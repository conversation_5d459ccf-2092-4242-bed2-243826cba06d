﻿@using System.Data;
@using System.Configuration;
@model Trace_AbilitySystem.Libs.DTOClass.DataAllTrace
@using Trace_AbilitySystem.Libs

@if (Model != null && Model.DataFlexAssy.FlexAssy_18_PanelAndSingleFPCBarcodeLink?.Rows.Count > 0)
{
    <h2 class="bd-title" id="18-panel-single-fpc-barcode-link">Panel & single FPC barcode link</h2>
    <table class="table table-bordered">
        <thead>
            <tr>
                <th scope="col">Control Item</th>
                <th scope="col">Control Value</th>
            </tr>
        </thead>
		<tbody>
			<tr>
				<td>ItemName</td>
				<td>@(ViewBag.ItemName)</td>
			</tr>
			<tr>
				<td>IndicationNo</td>
				<td>@(ViewBag.IndicationNo)</td>
			</tr>
			<tr>
				<td>PcsID</td>
				<td>@Model.DataFlexAssy.FlexAssy_18_PanelAndSingleFPCBarcodeLink.Rows[0]["ProductID"]</td>
			</tr>
			<tr>
				<td>BlockID</td>
				<td>@Model.DataFlexAssy.FlexAssy_18_PanelAndSingleFPCBarcodeLink.Rows[0]["BlockID"]</td>
			</tr>
			<tr>
				<td>DateTime</td>
				<td>@Model.DataFlexAssy.FlexAssy_18_PanelAndSingleFPCBarcodeLink.Rows[0]["DateTime"]</td>
			</tr>
			<tr>
				<td>Machine ID</td>
				<td>@Model.DataFlexAssy.FlexAssy_18_PanelAndSingleFPCBarcodeLink.Rows[0]["MachineID"]</td>
			</tr>
			<tr>
				<td>Operator ID</td>
				<td>@Model.DataFlexAssy.FlexAssy_18_PanelAndSingleFPCBarcodeLink.Rows[0]["OperatorID"]</td>
			</tr>
			@*<tr>
			<td>Fixture ID</td>
			<td>@Model.DataFlexAssy.FlexAssy_18_PanelAndSingleFPCBarcodeLink.Rows[0]["FixtureID"]</td>
		</tr>*@
			<tr>
				<td>Machine program</td>
				<td>@Model.DataFlexAssy.FlexAssy_18_PanelAndSingleFPCBarcodeLink.Rows[0]["ProgramName"]</td>
			</tr>
			<tr>
				<td>Panel & single FPC barcode</td>
				<td>@Model.DataFlexAssy.FlexAssy_18_PanelAndSingleFPCBarcodeLink.Rows[0]["PanelSingleFPBBarcode"]</td>
			</tr>
			@*<tr>
				<td>Machine maintenance & repair recording</td>
				<td><a onclick="viewDataMachineMaintenance(@Model.DataFlexAssy.FlexAssy_18_PanelAndSingleFPCBarcodeLink.Rows[0]["MachineMaintenanceID"])" href="javascript:">@Model.DataFlexAssy.FlexAssy_18_PanelAndSingleFPCBarcodeLink.Rows[0]["MachineMaintenanceDate"]</a></td>
		</tr>*@
			<tr>
				<td>First piece buyoff control (link)</td>
				<td>
					@if (ViewBag.Factory == "F5")
					{
						<a onclick="viewDataFirstPieceBuyoffControlNew('@Model.DataFlexAssy.FlexAssy_18_PanelAndSingleFPCBarcodeLink.Rows[0]["MachineID"]','@DataConvert.ConvertToString(ViewBag.IndicationNo)')" href="javascript:">
							@(Singleton_04_Machine.IFPBCheckingService.FPBChecking_GetResultByMachineID_IndicationNumber(DataConvert.ConvertToString(Model.DataFlexAssy.FlexAssy_18_PanelAndSingleFPCBarcodeLink.Rows[0]["MachineID"]),DataConvert.ConvertToString(ViewBag.IndicationNo)))
						</a>
					}
					else
					{
						<a onclick="viewDataFirstPieceBuyoffControlByTime('@Model.DataFlexAssy.FlexAssy_18_PanelAndSingleFPCBarcodeLink.Rows[0]["MachineID"]','@DataConvert.ToDateTime(Model.DataFlexAssy.FlexAssy_18_PanelAndSingleFPCBarcodeLink.Rows[0]["DateTime"]).ToString("yyyy-MM-dd HH:mm:ss")', '@DataConvert.ConvertToString(ViewBag.ItemName)')" href="javascript:">
							@(Singleton_04_Machine.IFPBCheckingService.FPBChecking_GetResultByMachineID_Time(DataConvert.ConvertToString(Model.DataFlexAssy.FlexAssy_18_PanelAndSingleFPCBarcodeLink.Rows[0]["MachineID"]), DataConvert.ToDateTime(Model.DataFlexAssy.FlexAssy_18_PanelAndSingleFPCBarcodeLink.Rows[0]["DateTime"]), @DataConvert.ConvertToString(ViewBag.ItemName)))
						</a>
					}
				</td>
			</tr>

			<tr>
				<td>Production condition (link)</td>
				<td>
					<a onclick="viewDataProductionCondition(@Model.DataFlexAssy.FlexAssy_18_PanelAndSingleFPCBarcodeLink.Rows[0]["ProductionConditionID"])" href="javascript:">
						@Singleton_03_Common.ICommon_CommonService.GetProductionContidtionResult(DataConvert.ConvertToString(Model.DataFlexAssy.FlexAssy_18_PanelAndSingleFPCBarcodeLink.Rows[0]["ProductionConditionID"]),
							DataConvert.ConvertToString(Model.DataFlexAssy.FlexAssy_18_PanelAndSingleFPCBarcodeLink.Rows[0]["ProductionConditionResult"]))
					</a>
				</td>
			</tr>
			<tr>
				<td>Temp/Humidity/Clearness Dashboard Link</td>
				<td>
					<a target="_blank" href="@Common.getCleanlink(ViewBag.Factory,1)">
						@Common.getCleanlink(ViewBag.Factory, 1)
					</a>
				</td>
			</tr>
		</tbody>
    </table>
}