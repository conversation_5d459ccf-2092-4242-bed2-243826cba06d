﻿using System;
using System.Data;
using System.Threading.Tasks;

namespace Trace_AbilitySystem.Libs.ITrace_02_FlexAssy_Services
{
    public interface IFlexAssy_08_LaserMarkingService
    {
        Task<int> FlexAssy_08_LaserMarking_ProcessData(string factory, DateTime createdDateSearch, int minutes, string connectionStringOption);
        int FlexAssy_08_LaserMarking_Insert(string productID, string blockID, DateTime dateTime, string machineID, string operatorID, string programName, string location, int count, string itemName, string grade,
            DateTime? machineMaintenanceDate, int? machineMaintenanceID, string productionConditionResult, int? productionConditionID, string profile, DateTime? firstPieceBuyoffControlDate, int? firstPieceBuyoffControlID, string connectionStringOption);
        int FlexAssy_08_LaserMarking_InsertLaserMarkingDetail(string blockID, string laserRange, string cameraAOffsetX, string cameraAOffsetY, string laserPower, string scanSpeed,
            string laserPulseCycle, string lineWidth, string sysbol, string mode, string rotation, string moduleH, string moduleW, string quietZone, string markModule, string spaceModule, string border, string connectionStringOption);

        DataTable FlexAssy_08_LaserMarking_GetByProductID(string productID, string connectionStringOption, out string connectionStringOk);
        DataTable FlexAssy_08_LaserMarking_GetListProductIDByBlockID(string blockID, string connectionStringOption, out string connectionStringOk);
        DataTable FlexAssy_08_LaserMarking_GetByListProductID_v2(DataTable listProductID, string connectionStringOption, out string connectionStringOk);
        DataTable FlexAssy_08_LaserMarking_GetByListProductID(DataTable listProductID, string connectionStringOption, out string connectionStringOk);
        DataTable FlexAssy_08_LaserMarking_GetListProductIDByListBlockID(DataTable listBlockID, string connectionStringOption);

        DataTable FlexAssy_08_LaserMarking_GetAllByListProductID(DataTable listProductID, string connectionStringOption);
        DataTable FlexAssy_08_LaserMarking_GetAllByBlockID(string blockID, string connectionStringOption);
        DataTable FlexAssy_08_LaserMarking_GetAllByListBlockID(DataTable listBlockID, string connectionStringOption);

        DataTable FlexAssy_08_LaserMarking_GetBlockIDByListProductID(DataTable listProductID, string connectionStringOption);

        DataTable FlexAssy_08_LaserMarking_GetListProductIDByIndiNumber(string IndiNumber, string connectionStringOption);
        Task<int> OEE_08_LaserMarking_ProcessData(string factory, DateTime rayonTimeSearch, int minutes, string connectionStringOption);
        Task<int> FlexAssy_08_LaserMarking_SecondSide_ProcessData(string factory, DateTime createdDateSearch, int minutes, string connectionStringOption);
        DataTable FlexAssy_08_LaserMarking_SecondSide_getByProductID(string productID, string connectionStringOption, out string connectionStringOk);
        DataTable FlexAssy_08_LaserMarking_GetOtherDataByListProductID(DataTable listProductID, string connectionStringOption);
        DataTable FlexAssy_08_LaserMarking_GetOtherDataByProductID(string ProductID, string connectionStringOption);
    }
}