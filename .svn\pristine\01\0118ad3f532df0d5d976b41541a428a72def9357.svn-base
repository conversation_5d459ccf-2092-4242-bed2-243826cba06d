﻿using System;
using System.Data;
using System.Threading.Tasks;

namespace Trace_AbilitySystem.Libs.ITrace_02_FlexAssy_Services
{
    public interface IFlexAssy_10_XrayInspectionService
    {
        Task<int> FlexAssy_10_XrayInspection_ProcessData(string factory, DateTime dataRegistrationDateSearch, int minutes, string connectionStringOption);
        Task<int> FlexAssy_10_XrayInspection_ProcessData_F4(string factory, DateTime dataRegistrationDateSearch, int minutes, string connectionStringOption);
        Task<int> FlexAssy_10_XrayInspection_ProcessData_F3(string factory, DateTime dataRegistrationDateSearch, int minutes, string connectionStringOption);
        Task SyncFlexAssy_10_XrayInspection(string stage, string factory, string LastValue, string connectionStringOptionBoard, string connectionStringOptionShopFloor);
        DataTable FlexAssy_10_XrayInspection_NG_Image_GetByBlockID(string BlockID, string connectionStringOption);
        DataTable FlexAssy_10_XrayInspection_NG_Image_GetByBlockID_v2(string blockID, string connectionStringOption, out string connectionStringOk);
        DataTable FlexAssy_10_XrayInspection_Component_GetByBlockID(string BlockID, string connectionStringOption);
        DataTable FlexAssy_10_XrayInspection_Component_GetByBlockID_v2(string blockID, string connectionStringOption, out string connectionStringOk);
        Task<int> FlexAssy_10_XrayInspection_NG_ProcessData(string factory, DateTime dataRegistrationDateSearch, string pathsave, int minutes, string connectionStringOption);
        Task<int> FlexAssy_10_XrayInspection_ProcessDataF3(string factory, DateTime dataRegistrationDateSearch, string pathsave, int minutes, string connectionStringOption);
        Task<int> FlexAssy_10_XrayInspection_ProcessDataF5(string factory, DateTime dataRegistrationDateSearch, int minutes, string connectionStringOption);
        int FlexAssy_10_XrayInspection_Insert(string blockID, DateTime? dateTime, string machineID, string operatorID, string programName, string partLocation, string result, string ngType, string finalResult,
            int? numberNGImage, DateTime? goldenSampleRecordChecking, DateTime? machineMaintenanceDate, int? machineMaintenanceID, string productionConditionResult, int? productionConditionID, DateTime? firstPieceBuyoffControlDate, int? firstPieceBuyoffControlID, string connectionStringOption);
        int FlexAssy_10_XrayInspection_Save(string blockID,string Lane, DateTime? dateTime, string machineID, string operatorID, string programName, string partLocation, string result, string ngType, string finalResult,
            int? numberNGImage, DateTime? goldenSampleRecordChecking, DateTime? machineMaintenanceDate, int? machineMaintenanceID, string productionConditionResult, int? productionConditionID, string connectionStringOption);
        int FlexAssy_10_XrayInspection_Sample_Insert(string blockID, DateTime? dateTime, string machineID, string operatorID, string programName, string partLocation, string result, string ngType, string finalResult,
            int? numberNGImage, DateTime? goldenSampleRecordChecking, DateTime? machineMaintenanceDate, int? machineMaintenanceID, string productionConditionResult, int? productionConditionID, string connectionStringOption);
        int FlexAssy_10_XrayInspection_Sample_Insert_v2(string blockID, DateTime? dateTime, string machineID, string operatorID, string programName, string partLocation, string result, string ngType,
            string finalResult, int? numberNGImage, DateTime? goldenSampleRecordChecking, DateTime? machineMaintenanceDate, int? machineMaintenanceID, string productionConditionResult, int? productionConditionID, string FileName, string connectionStringOption);
        int FlexAssy_10_XrayInspection_Sample_Save_F5(string blockID,string Lane, DateTime? dateTime, string machineID, string operatorID, string programName, string partLocation, string result, string ngType, string finalResult,
            int? numberNGImage, DateTime? goldenSampleRecordChecking, DateTime? machineMaintenanceDate, int? machineMaintenanceID, string productionConditionResult, int? productionConditionID, string connectionStringOption);
        int FlexAssy_10_XrayInspection_Update_GoldenSampleRecordChecking(string blockID, DateTime? goldenSampleRecordChecking, string connectionStringOption);
        int FlexAssy_10_XrayInspection_InsertSample(string blockID, string mcID, string testResult, string inspectionMachine, string programName, string createDate, string personRevisor, string lotCount, double? faultRate,
            string componentTotal, string pinTotal, string landTotal, string outComponentTotal, string personTester, DateTime? inspectionDateTime, string fileScanFolder, string movedFolder,
            string fileName, DateTime? dataRegistrationDate, string blockIDIndicationNumber, string blockIDCreatedBy, string blockIDCreatedByOperatorId, string blockIDCreatedByName, DateTime? blockIDCreatedDate, string connectionStringOption);

        // Log
        Task FlexAssy_10_XrayInspection_ProcessCopyLog(string factory, string pathSaveLog, string userName, string passWord);
        Task FlexAssy_10_XrayInspection_ProcessCopyImageF5(string factory, string pathSaveLog, string userName, string passWord);
        Task FlexAssy_10_XrayInspection_ProcessReadLog(string factory, int iD);
        Task ProcessLogShare_Security(int idLog, string networkPath, string factory, DateTime lastTime, string pathSaveLog, string machineID, string machineName);

        int FlexAssy_10_XrayInspection_ReadFileXRay(string factory, string machineID, int iD_LogFile, string fileName);
        int FlexAssy_10_XrayInspection_UpdateReadLog(string blockID, string partLocation, string result, string nGType, string finalResult, int? numberNGImage, int iD_LogFile, string connectionStringOption);

        int FlexAssy_10_XrayInspection_LogFile_Insert(string machineID, string fileNameRoot, string fileName, int lineRead, string connectionStringOption);
        int FlexAssy_10_XrayInspection_LogFile_UpdateLineRead(int iD, int lineRead, bool isReadOK, string connectionStringOption);
        int FlexAssy_10_XrayInspection_LogFile_UpdateFileNameRoot(int iD, string fileNameRoot, string connectionStringOption);

        DataTable FlexAssy_10_XrayInspection_LogFile_GetDataWithID(int iD, string connectionStringOption);
        DataTable FlexAssy_10_XrayInspection_LogFile_GetByID(int iD, string connectionStringOption);
        DataTable FlexAssy_10_XrayInspection_Sample_getLogFile(int ID, string connectionStringOption);
        DataTable FlexAssy_10_XrayInspection_Sample_getLogFile_F3(string MachineID, string ProgramName, DateTime DateTime, string connectionStringOption);
        DataTable FlexAssy_10_XrayInspection_GetByBlockID(string blockID, string connectionStringOption, out string connectionStringOk);
        DataTable FlexAssy_10_XrayInspection_GetByListBlockID(DataTable listBlockID, string connectionStringOption, out string connectionStringOk);
        DataTable FlexAssy_10_XrayInspection_Sample_GetByBlockID(string blockID, string connectionStringOption, out string connectionStringOk);
        DataTable FlexAssy_10_XrayInspection_Sample_GetByListBlockID(DataTable listBlockID, string connectionStringOption, out string connectionStringOk);
        DataTable FlexAssy_10_XrayInspection_Sample_GetDataWithMachineIDAndProgramName(string machineID, string ProgramName, DateTime dateTime, string connectionStringOption);
        DataTable FlexAssy_10_XrayInspection_Sample_GetDataWithDateTime(DateTime dateTime, string connectionStringOption);

        // Image2D
        int FlexAssy_10_XrayInspection_Image2D_Insert(string blockID, string image2D, string connectionStringOption);
        DataTable GetListFlexAssy_10_XrayInspection(string Datetime, string connectionStringOption);
        int FlexAssy_10_XrayInspection_Image2D_Save(string blockID,string Lane, string image2D, string connectionStringOption);
        DataTable FlexAssy_10_XrayInspection_Image2D_GetByBlockID(string blockID, int limit, string connectionStringOption);
        DataTable FlexAssy_10_XrayInspection_Image2D_GetByListBlockID(DataTable listBlockID, string connectionStringOption);
        DataTable FlexAssy_10_XrayInspection_NG_GetByBlockID(string blockID, string MachineID, string connectionStringOption);
        Task<int> OEE_10_XrayInspection_NG_ProcessData(string factory, DateTime DateTime, int minutes, string connectionStringOption);
        DataTable ParameterXay_GetByListBlockID(DataTable listBlockID, string connectionStringOption);
    }
}