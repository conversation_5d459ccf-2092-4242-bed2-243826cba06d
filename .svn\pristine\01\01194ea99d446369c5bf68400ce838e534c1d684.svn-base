﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Data.SqlClient;
using System.Threading.Tasks;
using Trace_AbilitySystem.Libs.DTOClass.SPCModels;
using Trace_AbilitySystem.Libs.Dataconnect;
using Trace_AbilitySystem.Libs.ISPC_Commom_Service;
using Trace_AbilitySystem.Libs.SPC_Utils;

namespace Trace_AbilitySystem.Libs.SPC_Commom_Service
{
    public class SPC_03_FAIService : ISPC_03_FAIService
    {
        private readonly DbExecute _db;
        private readonly string connectionStringOption = "SPC_F4";
        public SPC_03_FAIService()
        {
            _db = new SqlExecute();
        }
        public List<SystemConfigValueHistory> GetConfigChartByStage(string Stage, string connectionStringOption)
        {
            List<SystemConfigValueHistory> result = new List<SystemConfigValueHistory>();
            try
            {
                var paras = new SqlParameter[2];
                paras[0] = new SqlParameter("@StageName", Stage);
                paras[1] = new SqlParameter("@Status", 2);
                result = Extensions.TableToList<SystemConfigValueHistory>(_db.Execute_Table("Prc_SystemConfigValueHistory_GetByStage", paras, CommandType.StoredProcedure, connectionStringOption));
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp("Prc_SystemConfigValueHistory_GetByStage" + "\n" + ex.Message);
            }
            return result;
        }
        public decimal ChartDataInfor_Insert(string Stage, string BlockGroup, decimal itemNameConfigIdentity, string connectionStringOption)
        {
            List<SystemConfigValueHistory> result = new List<SystemConfigValueHistory>();
            try
            {
                var paras = new SqlParameter[3];
                paras[0] = new SqlParameter("@Stage", Stage);
                paras[1] = new SqlParameter("@PointNumber", BlockGroup);
                paras[2] = new SqlParameter("@ItemNameConfigIdentity", itemNameConfigIdentity);
                return _db.Execute_DecimalScalar("Prc_SPC_Alarm_ChartData_Infor_Insert", paras, CommandType.StoredProcedure, connectionStringOption);
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp("Prc_SPC_Alarm_ChartData_Infor_Insert" + "\n" + ex.Message);
                return -1;
            }
        }
        public void ChartData_Insert(decimal ChartInforID, decimal BlockID, decimal BlockIDInforID, decimal Volume, decimal Height, decimal Area, decimal ShiftX, decimal ShiftY, string connectionStringOption)
        {
            List<SystemConfigValueHistory> result = new List<SystemConfigValueHistory>();
            try
            {
                var paras = new SqlParameter[2];
                paras[0] = new SqlParameter("@ChartInforID", ChartInforID);
                paras[1] = new SqlParameter("@DataID", BlockID);
                _db.Execute_Modify("Prc_SPC_Alarm_ChartData_Insert", paras, CommandType.StoredProcedure, connectionStringOption);
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp("Prc_SPC_Alarm_ChartData_Insert" + "\n" + ex.Message);
            }
        }
        public int SPC_Point_NG_Insert(decimal ChartInforID, string Stage, string RuleNG, string ChartType, int status, string connectionStringOption)
        {
            List<SystemConfigValueHistory> result = new List<SystemConfigValueHistory>();
            try
            {
                var paras = new SqlParameter[5];
                paras[0] = new SqlParameter("@Stage", Stage);
                paras[1] = new SqlParameter("@RuleNG", RuleNG);
                paras[2] = new SqlParameter("@ChartInforID", ChartInforID);
                paras[3] = new SqlParameter("@ChartType", ChartType);
                paras[4] = new SqlParameter("@Status", status);
                return _db.Execute_Scalar("Prc_SPC_Point_NG_Insert", paras, CommandType.StoredProcedure, connectionStringOption);
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp("Prc_SPC_Point_NG_Insert" + "\n" + ex.Message);
                return -1;
            }
        }
        public void UpdateLastTime(string itemName,string group, string Stage, string Value, decimal ConfigIdentity, string connectionStringOption)
        {
            try
            {
                var paras = new SqlParameter[4];
                paras[0] = new SqlParameter("@ItemNameConfigIdentity", ConfigIdentity);
                paras[1] = new SqlParameter("@ItemName", itemName);
                paras[2] = new SqlParameter("@ValueData", Value);
                paras[3] = new SqlParameter("@StageName", Stage);
                _db.Execute_Modify("Prc_SystemConfigValueHistory_UpdateGetSampleLastTime", paras, CommandType.StoredProcedure, connectionStringOption);
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp("Prc_SystemConfigValueHistory_UpdateGetSampleLastTime" + "\n" + ex.Message);
            }
        }
        public List<SPC_03_OQC_FAI_DATAModel> OQC_FAI_Alarm_ChartData_GetByConfig(string ItemName, string DieID, decimal ItemNameConfigIdentity, string From, string connectionStringOption)
        {
            List<SPC_03_OQC_FAI_DATAModel> result = new List<SPC_03_OQC_FAI_DATAModel>();
            try
            {
                var paras = new SqlParameter[4];
                paras[1] = new SqlParameter("@ItemName", ItemName);
                paras[2] = new SqlParameter("@Group", DieID);
                paras[3] = new SqlParameter("@ItemNameConfigIdentity", ItemNameConfigIdentity);
                paras[4] = new SqlParameter("@InspectionFrom", From);
                result = Extensions.TableToList<SPC_03_OQC_FAI_DATAModel>(_db.Execute_Table("Prc_SPI_03_OQC_FAI_Alarm_ChartData_GetByConfig", paras, CommandType.StoredProcedure, connectionStringOption));
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp("Prc_SPI_03_OQC_FAI_Alarm_ChartData_GetByConfig" + "\n" + ex.Message);
            }
            return result;
        }

        public List<string> GetDieID(string ItemName,string connectionStringOption)
        {
            List<string> result = new List<string>();
            DataTable dtDieID = new DataTable();
            try
            {
                var paras = new SqlParameter[1];
                paras[0] = new SqlParameter("@ItemName", ItemName);
                dtDieID = _db.Execute_Table("Prc_SPC_03_OQC_FAI_PCSInfor_GetDieByItemName", paras, CommandType.StoredProcedure, connectionStringOption);
                if (dtDieID?.Rows.Count > 0)
                {
                    foreach (DataRow row in dtDieID.Rows)
                    {
                        if (row["DieID"] != null)
                        {
                            result.Add(row["DieID"].ToString());
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp("Prc_BlockIDInfor_GetAllMachineID" + "\n" + ex.Message);
            }
            return result;
        }
        public async Task asyncData(string Stage, string factory)
        {
            try
            {
                List<SystemConfigValueHistory> Configs = GetConfigChartByStage(Stage, factory);
                List<ChartConfig> chartConfigs = ChartConfig.ConfigToChartConfig(Configs);
                foreach (ChartConfig item in chartConfigs)
                {
                        List<SPC_03_OQC_FAI_DATAModel> dataCheck = GetICT_NET_DATA_BY_ConfigValue(item.ItemName, item.MESPoint, item.Group, DataConvert.ToDateTimeApp(item.GetSampleLastTime),
                            DataConvert.ConvertStringToInt(item.NumberBlock), DataConvert.ConvertStringToInt(item.SampleTime), DataConvert.ConvertStringToInt(item.BlockSpaceTime),
                            DataConvert.ConvertStringToInt(item.BlockSpaceNumber), DataConvert.ConvertStringToInt(item.IsSkip), item.Group, factory);
                        saveData(Stage, dataCheck, item, item.Group, factory);
                }
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp("SPC_asyncData" + "\n" + ex.Message);
            }
            await Task.Delay(500);
        }

        public async Task CheckRule(string Stage, string factory)
        {
            try
            {
                List<SystemConfigValueHistory> Configs = GetConfigChartByStage(Stage, factory);
                List<ChartConfig> chartConfigs = ChartConfig.ConfigToChartConfig(Configs);
                foreach (ChartConfig item in chartConfigs)
                {
                    List<SPC_03_OQC_FAI_DATAModel> dataCheck = OQC_FAI_Alarm_ChartData_GetByConfig(item.ItemName, item.Group, item.ItemNameConfigIdentity, item.GetSampleFirstTime, factory);
                    if (dataCheck?.Count > 0)
                    {
                        checkData(dataCheck, item, factory);
                    }
                }
            }
            catch (Exception ex)    
            {
                ManageLog.WriteErrorApp("SPC_CheckRule" + "\n" + ex.ToString());
            }
            await Task.Delay(500);
        }
        public void saveData(string stage, List<SPC_03_OQC_FAI_DATAModel> dataCheck, ChartConfig chartConfig, string machineid, string factory)
        {
            ChartData rs = new ChartData();
            try
            {
                if (dataCheck.Count > 0)
                {
                    dataCheck = dataCheck.OrderBy(x => x.BlockGroup).ToList();
                    List<GroupValue> lstGroupValue = new List<GroupValue>();
                    if (dataCheck.Count > 0)
                    {
                        decimal id = 0;
                        string query = "";
                        DateTime lasttime = DateTime.Now;
                        // tách dữ liệu theo nhóm
                        foreach (SPC_03_OQC_FAI_DATAModel item in dataCheck)
                        {
                            GroupValue GroupValue = lstGroupValue.Find(x => x.Group == item.BlockGroup);
                            if (GroupValue == null)
                            {
                                id = ChartDataInfor_Insert(stage, item.BlockGroup, chartConfig.ItemNameConfigIdentity, factory);
                                query += " EXEC [dbo].[Prc_SPC_Alarm_ChartData_Insert]";
                                query += " @ChartInforID = " + id + ",";
                                query += " @DataID = " + item.ID + ""+ Environment.NewLine;
                                lasttime = item.PunchingTime;
                            }
                            else
                            {
                                query += " EXEC [dbo].[Prc_SPC_Alarm_ChartData_Insert]";
                                query += " @ChartInforID = " + id + ",";
                                query += " @DataID = " + item.ID + "" + Environment.NewLine;
                                lasttime = item.PunchingTime;
                            }
                        }
                        _db.Execute_Modify(query, null, CommandType.Text, factory);
                        UpdateLastTime(chartConfig.ItemName,chartConfig.Group,stage, lasttime.ToString(), chartConfig.ItemNameConfigIdentity,factory);
                    }
                }
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorWeb(ex);
            }
        }

        public List<SPC_03_OQC_FAI_DATAModel> GetICT_NET_DATA_BY_ConfigValue(string ItemName, string MESPoint, string DieID, DateTime From, int numberBlock, int SampleTime, int BlockSpaceTime, int BlockSpaceNumber,
            int IsSkip, string Group, string connectionStringOption)
        {
            List<SPC_03_OQC_FAI_DATAModel> result = new List<SPC_03_OQC_FAI_DATAModel>();
            string store = "";
            try
            {
                if (IsSkip == 1)
                {
                    store = "Prc_SPC_03_OQC_FAI_Data_GetAllGroupIsSkipByTime";
                }
                else
                {
                    store = "Prc_SPC_03_OQC_FAI_Data_GetAllGroupNotSkipByTime";
                }
                var paras = new SqlParameter[10];
                paras[0] = new SqlParameter("@ItemName", ItemName);
                paras[1] = new SqlParameter("@DIEID", DieID);
                paras[2] = new SqlParameter("@InspectionFrom", From);
                paras[3] = new SqlParameter("@InspectionTo", DBNull.Value);
                paras[4] = new SqlParameter("@NumberBlock", numberBlock);
                paras[5] = new SqlParameter("@SampleTime", SampleTime);
                paras[6] = new SqlParameter("@BlockSpaceTime", BlockSpaceTime);
                paras[7] = new SqlParameter("@BlockSpaceNumber", BlockSpaceNumber);
                paras[8] = new SqlParameter("@MESPoint", MESPoint + ",");
                paras[9] = new SqlParameter("@Group", Group); 
                result = Extensions.TableToList<SPC_03_OQC_FAI_DATAModel>(_db.Execute_Table(store, paras, CommandType.StoredProcedure, connectionStringOption));
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp("GetSPI_DATA_BY_ItemName:" + store + "\n" + ex.Message);
            }
            return result;
        }

        public void checkData(List<SPC_03_OQC_FAI_DATAModel> dataCheck, ChartConfig item, string factory)
        {
            ChartData chartData = ChartCalc.OQCFAICalculator(dataCheck);
            decimal UCL = chartData.ArrUCL_R[0];
            decimal LCL = chartData.ArrLCL_R[0];
            decimal AVE = chartData.ArrRBar[0];
            decimal UCL2 = chartData.ArrUCL_x[0];
            decimal LCL2 = chartData.ArrLCL_x[0];
            decimal AVE2 = chartData.ArrX2Bar[0];
            List<decimal> ArrX2Bar = chartData.ArrX2Bar;
            // giới hạn R chart nếu có config
            if (item.UCLR != "" && item.AVER != "")
            {
                UCL = DataConvert.ConvertStringToDecimal(item.UCLR);
                AVE = DataConvert.ConvertStringToDecimal(item.AVER);
                LCL = AVE - (UCL - AVE);
            }
            // giới hạn xbar chart nếu có config
            if (item.UCLXBarR != "" && item.AVEXBarR != "")
            {
                UCL2 = DataConvert.ConvertStringToDecimal(item.UCLXBarR);
                AVE2 = DataConvert.ConvertStringToDecimal(item.AVEXBarR);
                LCL2 = AVE2 - (UCL2 - AVE2);
            }
            for (var i = 0; i < chartData.lstGroup.Count; i++)
            {
                chartData.ArrUCL_R[i] = UCL;
                chartData.ArrLCL_R[i] = LCL;
                chartData.ArrRBar[i] = AVE;
                chartData.ArrUCL_x[i] = UCL2;
                chartData.ArrLCL_x[i] = LCL2;
                ArrX2Bar[i] = AVE2;
            }
            checkRule(chartData.lstChartDataInforID, chartData.ArrR, UCL, LCL, AVE, item, "R-Chart", factory);
            checkRule(chartData.lstChartDataInforID, chartData.ArrXbar, UCL2, LCL2, AVE2, item, "X-Bar Của R", factory);
            UCL = chartData.ArrUCL_S[0];
            LCL = chartData.ArrLCL_S[0];
            AVE = chartData.ArrSBar[0];
            UCL2 = chartData.SArrUCL_x[0];
            LCL2 = chartData.SArrLCL_x[0];
            AVE2 = chartData.ArrX2Bar[0];
            ArrX2Bar = chartData.ArrX2Bar;
            // giới hạn S chart nếu có config
            if (item.UCLS != "" && item.AVES != "")
            {
                UCL = DataConvert.ConvertStringToDecimal(item.UCLS);
                AVE = DataConvert.ConvertStringToDecimal(item.AVES);
                LCL = AVE - (UCL - AVE);
            }
            // giới hạn xbar chart nếu có config
            if (item.UCLXBarS != "" && item.AVEXBarS != "")
            {
                UCL2 = DataConvert.ConvertStringToDecimal(item.UCLXBarS);
                AVE2 = DataConvert.ConvertStringToDecimal(item.AVEXBarS);
                LCL2 = AVE2 - (UCL2 - AVE2);
            }
            for (var i = 0; i < chartData.lstGroup.Count; i++)
            {
                chartData.ArrUCL_S[i] = UCL;
                chartData.ArrLCL_S[i] = LCL;
                chartData.ArrSBar[i] = AVE;
                chartData.SArrUCL_x[i] = UCL2;
                chartData.SArrLCL_x[i] = LCL2;
                ArrX2Bar[i] = AVE2;
            }
            checkRule(chartData.lstChartDataInforID, chartData.ArrS, UCL, LCL, AVE, item, "S-Chart", factory);
            checkRule(chartData.lstChartDataInforID, chartData.ArrXbar, UCL2, LCL2, AVE2, item, "X-Bar Của S", factory);
            UCL = chartData.XMArrUCL_R[0];
            LCL = chartData.XMArrLCL_R[0];
            AVE = chartData.XMArrRBar[0];
            UCL2 = chartData.XMArrUCL_x[0];
            LCL2 = chartData.XMArrLCL_x[0];
            AVE2 = chartData.ArrX2Bar[0];
            ArrX2Bar = chartData.ArrX2Bar;
            // giới hạn R chart nếu có config
            if (item.UCLR != "" && item.AVER != "")
            {
                UCL = DataConvert.ConvertStringToDecimal(item.UCLS);
                AVE = DataConvert.ConvertStringToDecimal(item.AVES);
                LCL = AVE - (UCL - AVE);
            }
            // giới hạn xbar chart nếu có config
            if (item.UCLXBarR != "" && item.AVEXBarR != "")
            {
                UCL2 = DataConvert.ConvertStringToDecimal(item.UCLXBarR);
                AVE2 = DataConvert.ConvertStringToDecimal(item.AVEXBarR);
                LCL2 = AVE2 - (UCL2 - AVE2);
            }
            for (var i = 0; i < chartData.lstGroup.Count; i++)
            {
                chartData.XMArrUCL_R[i] = UCL;
                chartData.XMArrLCL_R[i] = LCL;
                chartData.XMArrRBar[i] = AVE;
                chartData.XMArrUCL_x[i] = UCL2;
                chartData.XMArrLCL_x[i] = LCL2;
                ArrX2Bar[i] = AVE2;
            }
            checkRule(chartData.lstChartDataInforID, chartData.XMArrR, UCL, LCL, AVE, item, "XMR-Chart", factory);
            checkRule(chartData.lstChartDataInforID, chartData.ArrXbar, UCL2, LCL2, AVE2, item, "X-Bar Của XMR", factory);
        }
        public DataTable GetSPC_SystemConfigValue_GetByConfigName(string ConfigName, string Stage, string ItemName, string connectionStringOption)
        {
            var paras = new SqlParameter[3];
            paras[0] = new SqlParameter("@NameConfig", ConfigName);
            paras[1] = new SqlParameter("@StageName", Stage);
            paras[2] = new SqlParameter("@ItemName", ItemName);

            return _db.Execute_Table("Prc_SystemConfigValue_GetByConfigName", paras, CommandType.StoredProcedure, connectionStringOption);
        }
        public int ICT_NET_DataInsert(string InsertQuery, string connectionStringOption)
        {
            return _db.Execute_Modify(InsertQuery, null, CommandType.Text, connectionStringOption);
        }
        public void checkRule(List<decimal> lstChartDataInforID, List<decimal> MES, decimal UCL, decimal LCL, decimal AVE, ChartConfig Item, string ChartType, string factory)
        {
            List<string> Rule = new List<string>(); ;
            for (var i = 0; i < MES.Count; i++)
            {
                Rule.Add("");
            }
            var CheckRule1Result = ChartCalc.rule1(MES, UCL, LCL);
            for (var i = 0; i < MES.Count; i++)
            {
                if (CheckRule1Result[i] == 2)
                {
                    Rule[i] = ",1";
                }
            }
            var CheckRule2Result = ChartCalc.rule2(MES, UCL, LCL, AVE);
            for (var i = 0; i < MES.Count; i++)
            {
                if (CheckRule2Result[i] == 2)
                {
                    Rule[i] += ",2";
                }
            }

            var CheckRule3Result = ChartCalc.rule3(MES, UCL, LCL);
            for (var i = 0; i < MES.Count; i++)
            {
                if (CheckRule3Result[i] == 2)
                {
                    Rule[i] += ",3";
                }

            }
            var CheckRule4Result = ChartCalc.rule4(MES);
            for (var i = 0; i < MES.Count; i++)
            {
                if (CheckRule4Result[i] == 2)
                {
                    Rule[i] += ",4";
                }

            }
            var CheckRule5Result = ChartCalc.rule5(MES, UCL, LCL, AVE);
            for (var i = 0; i < MES.Count; i++)
            {
                if (CheckRule5Result[i] == 2)
                {
                    Rule[i] += ",5";
                }
            }

            var CheckRule6Result = ChartCalc.rule6(MES, UCL, LCL, AVE);
            for (var i = 0; i < MES.Count; i++)
            {
                if (CheckRule6Result[i] == 2)
                {
                    Rule[i] += ",6";
                }

            }
            var CheckRule7Result = ChartCalc.rule7(MES, UCL, LCL, AVE);
            for (var i = 0; i < MES.Count; i++)
            {
                if (CheckRule7Result[i] == 2)
                {
                    Rule[i] += ",7";
                }
            }
            var CheckRule8Result = ChartCalc.rule8(MES, UCL, LCL, AVE);
            for (var i = 0; i < MES.Count; i++)
            {
                if (CheckRule8Result[i] == 2)
                {
                    Rule[i] += ",8";
                }
            }
            for (int i = 0; i < Rule.Count; i++)
            {
                if (Rule[i] != "")
                {
                    int result = SPC_Point_NG_Insert(lstChartDataInforID[i], Item.StageName, Rule[i], ChartType, 0, factory);
                    if (result == 1)
                    {
                        Singleton_06_SPC.iSPC_CommonService.InsertMail(Item.ItemName, Item.MachineID, "OQC_FAI", factory);
                    }
                }
            }

        }
    }
}