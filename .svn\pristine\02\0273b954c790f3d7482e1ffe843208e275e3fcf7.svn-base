﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using Trace_AbilitySystem.Libs.Dataconnect;
using Trace_AbilitySystem.Libs.DTOClass;
using Trace_AbilitySystem.Libs.DTOClass.FlexAssyModel;
using Trace_AbilitySystem.Libs.ITrace_02_FlexAssy_Services;

namespace Trace_AbilitySystem.Libs.Trace_02_FlexAssy_Services
{
    public class FlexAssy_04_SolderPasteInspectionService : IFlexAssy_04_SolderPasteInspectionService
    {
        private readonly DbExecute _db;

        public FlexAssy_04_SolderPasteInspectionService()
        {
            _db = new SqlExecute();
        }

        public async Task<int> FlexAssy_04_SolderPasteInspection_ProcessData(string factory, DateTime dataRegistrationDateSearch, int minutes, string connectionStringOption)
        {
            string stage = "FlexAssy_04_SolderPasteInspection";
            string typeBarcode = "BlockID";

            try
            {
                DateTime dateNow = DateTime.Now;
                DateTime timeLast = dataRegistrationDateSearch;
                int recordNeedSyn = 0;
                int recordSyned = 0;

                DataTable dt = null;
                if (factory.Equals("F3"))
                {
                    dt = Singleton_02_FlexAssy.ISource_FlexAssy_MVT_Service.F3_FS_CKD_SPI_Summery_GetDataWithDataRegistrationDate(timeLast, minutes, connectionStringOption);
                }
                else
                {
                    dt = Singleton_02_FlexAssy.ISource_FlexAssy_MVT_Service.F4_FS_KY_SPI_Summery_GetDataWithDataRegistrationDate(timeLast, minutes, connectionStringOption);
                }

                if (dt == null)
                    return -1;

                if (dt.Rows.Count > 0)
                {
                    List<MachineMaintenance> machineMaintenances = new List<MachineMaintenance>();
                    List<ProductionCondition> productionConditions = new List<ProductionCondition>();
                    List<FirstPieceBuyOffControl> firstPieceBuyOffControls = new List<FirstPieceBuyOffControl>();
                    List<MachineOfStage> machineOfStages = new List<MachineOfStage>();
                    List<OperatorOfStage> operatorOfStages = new List<OperatorOfStage>();

                    string connectionStringOptionMachine = Singleton_03_Common.IDBInfoService.ConnectionStringOption(factory, "MachineDB");

                    //number of records to sync
                    recordNeedSyn = dt.Rows.Count;
                    for (int i = 0; i < recordNeedSyn; i++)
                    {
                        string blockID = dt.Rows[i]["BlockID"].ToString().Trim();
                        DateTime dateTime = Convert.ToDateTime(dt.Rows[i]["InspectionDateTime"]);
                        string machineID = null;
                        string operatorID = factory.Equals("F3") ? dt.Rows[i]["OperatorName"].ToString() : dt.Rows[i]["UserID"].ToString().Trim();
                        string programName = factory.Equals("F3") ? dt.Rows[i]["InspProgramName"].ToString() : dt.Rows[i]["PCBName"].ToString().Trim();
                        string result = factory.Equals("F3") ? dt.Rows[i]["NG_Cnt"].ToString() != "0" ? "FAIL" : "PASS" : dt.Rows[i]["PCBResult"].ToString().Trim();
                        int? panelQty = null;
                        int? passQty = null;
                        int? ngQty = null;
                        int? badMarkQty = null;
                        int? numberNGImage = null;

                        // find machineID
                        MachineMaintenance machineMaintenance = machineMaintenances.FirstOrDefault(x => x.McID.Equals(dt.Rows[i]["McID"].ToString()));
                        if (machineMaintenance != null)
                        {
                            machineID = machineMaintenance.MachineID;
                        }
                        else
                        {
                            DataTable dt1 = Singleton_02_FlexAssy.ISource_FlexAssy_MVT_Service.mdmEquipment_Copy_GetDataWithAssetNumber(dt.Rows[i]["McID"].ToString(), connectionStringOption);
                            if (dt1 == null)
                                return -1;
                            if (dt1.Rows.Count > 0)
                            {
                                machineID = dt1.Rows[0]["Name"].ToString().Trim();
                            }
                        }

                        // Find info machine maintenance date
                        List<MachineMaintenance> machineMaintenancesOther = Singleton_03_Common.ICommon_CommonService.GetMachine_Maintenance(dt.Rows[i]["McID"].ToString(), machineID, dateTime, out DateTime? machineMaintenanceDate, out int? machineMaintenanceID, machineMaintenances, connectionStringOptionMachine);
                        if (machineMaintenancesOther != null)
                            machineMaintenances.AddRange(machineMaintenancesOther);

                        // Find info production condition
                        ProductionCondition productionCondition = Singleton_03_Common.ICommon_CommonService.GetMachine_Condition(machineID, dateTime, programName, out string productionConditionResult, out int? productionConditionID, productionConditions, connectionStringOptionMachine);
                        if (productionCondition != null)
                            productionConditions.Add(productionCondition);

                        // find infor first piece buy off controll
                        FirstPieceBuyOffControl firstPieceBuyOffControl = Singleton_03_Common.ICommon_CommonService.GetMachine_FirstPieceBuyOff(factory, machineID, blockID, dateTime, out DateTime? firstPieceBuyoffControlDate, out int? firstPieceBuyoffControlID, firstPieceBuyOffControls, connectionStringOptionMachine);
                        if (firstPieceBuyOffControl != null)
                            firstPieceBuyOffControls.Add(firstPieceBuyOffControl);

                        // Insert data
                        int rs = FlexAssy_04_SolderPasteInspection_Insert(blockID, dateTime, machineID, operatorID, programName, result, panelQty, passQty, ngQty, badMarkQty, numberNGImage, firstPieceBuyoffControlDate, firstPieceBuyoffControlID,
                            machineMaintenanceDate, machineMaintenanceID, productionConditionResult, productionConditionID, factory);
                        if (rs == -9)
                            return -1;

                        // machineID, OperatorID definition makes at the stage
                        MachineOfStage machineOfStage = Singleton_03_Common.ICommon_CommonService.GetMachineOfStage(machineID, null, stage, factory, typeBarcode, machineOfStages);
                        if (machineOfStage != null)
                            machineOfStages.Add(machineOfStage);

                        OperatorOfStage operatorOfStage = Singleton_03_Common.ICommon_CommonService.GetOperatorOfStage(operatorID, null, stage, factory, typeBarcode, operatorOfStages);
                        if (operatorOfStage != null)
                            operatorOfStages.Add(operatorOfStage);

                        // update the time that the record is synchronized
                        timeLast = Convert.ToDateTime(dt.Rows[i]["DataRegistrationDate"]);

                        // count the number of synchronized records
                        recordSyned++;
                    }

                    // Update valuesearch
                    Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, stage, timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff"));
                }
                else
                {
                    timeLast = timeLast.AddMinutes(minutes) > dateNow ? timeLast : timeLast.AddMinutes(minutes);

                    // Update ValueSearch
                    Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, stage, timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff"));
                }

                ManageLog.WriteProcess(true, null, recordNeedSyn, recordSyned);
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp(stage + "\n" + ex.Message);

                return -1;
            }

            await Task.Delay(500);

            return 1;
        }

        public async Task<int> OEE_04_SolderPasteInspection_ProcessData_SEI_MES(string factory, DateTime lastTime, int minutes, string connectionStringOption)
        {
            string stage = "OEE_04_SolderPasteInspection";
            string typeBarcode = "BlockID";
            DateTime dateNow = DateTime.Now;
            DateTime timeLast = lastTime;
            try
            {
                int recordNeedSyn = 0;
                int recordSyned = 0;

                DataTable dt;
                dt = Singleton_02_FlexAssy.ISource_FlexAssy_SEI_MES_History_Service.GetData_FlexAssy_04_SolderPasteInspection_SEI_MES_ByLastTime(timeLast, minutes, connectionStringOption);

                if (dt == null)
                    return -1;

                if (dt.Rows.Count > 0)
                {
                    string connectionStringOptionBoard = Singleton_03_Common.IDBInfoService.ConnectionStringOption(factory, "BoardRegistrationDB");
                    List<DTOClass.SPCBase.OEE_Item_Product> lstOEE_Item_Product = new List<DTOClass.SPCBase.OEE_Item_Product>();
                    for (int i = 0; i < dt.Rows.Count; i++)
                    {
                        string MachineID = DataConvert.ConvertToString(dt.Rows[i]["MachineID"]).Trim();
                        string BlockID = DataConvert.ConvertToString(dt.Rows[i]["BlockID"]);
                        int goodQty = DataConvert.ConvertToInt(dt.Rows[i]["PASSQty"]);
                        int operatorCallQty = DataConvert.ConvertToInt(dt.Rows[i]["NGQty"]);
                        string ItemName = SingletonLocal.BoardRegisService.GetItemNameByBlockID(BlockID, out string IndicationNumber, connectionStringOptionBoard);
                        DateTime? _DateTime = null;
                        if (dt.Rows[i]["DateTime"] != DBNull.Value)
                            _DateTime = Convert.ToDateTime(dt.Rows[i]["DateTime"]);

                        if (_DateTime.Value.Hour < 6)
                        {
                            _DateTime = _DateTime.Value.AddDays(-1);
                            _DateTime = _DateTime.Value.Date;
                        }
                        else
                        {
                            _DateTime = _DateTime.Value.Date;
                        }
                        string Line = "";
                        DataTable dtMachineID = Singleton_04_Machine.ItMachineMtnService.tMachineList_GetByID(MachineID);
                        if (dtMachineID?.Rows.Count > 0)
                        {
                            Line = dtMachineID.Rows[0]["LineID"] + "";
                        }
                        int index = lstOEE_Item_Product.FindIndex(item => item.LineID == Line && item.MachineID == MachineID && item.IndicationNumber == IndicationNumber && _DateTime == item.DateTime);
                        if (index >= 0)
                        {
                            lstOEE_Item_Product[index].MachineID = MachineID;
                            lstOEE_Item_Product[index].LineID = Line;
                            lstOEE_Item_Product[index].ItemName = ItemName;
                            lstOEE_Item_Product[index].IndicationNumber = IndicationNumber;
                            lstOEE_Item_Product[index].DateTime = _DateTime;
                            lstOEE_Item_Product[index].TotalOKDay += goodQty;
                            lstOEE_Item_Product[index].TotalNGDay += operatorCallQty;
                        }
                        else
                        {
                            DTOClass.SPCBase.OEE_Item_Product New_OEE_Item_Product = new DTOClass.SPCBase.OEE_Item_Product();
                            New_OEE_Item_Product.MachineID = MachineID;
                            New_OEE_Item_Product.LineID = Line;
                            New_OEE_Item_Product.ItemName = ItemName;
                            New_OEE_Item_Product.IndicationNumber = IndicationNumber;
                            New_OEE_Item_Product.DateTime = _DateTime;
                            New_OEE_Item_Product.TotalOKDay = goodQty;
                            New_OEE_Item_Product.TotalNGDay = operatorCallQty;
                            lstOEE_Item_Product.Add(New_OEE_Item_Product);
                        }

                        timeLast = Convert.ToDateTime(dt.Rows[i]["CreatedDate"]);
                    }
                    //number of records to sync
                    for (int i = 0; i < lstOEE_Item_Product.Count; i++)
                    {
                        int rs = Singleton_06_SPC.iSPC_CommonService.OEE_Product_Save(lstOEE_Item_Product[i].ItemName, lstOEE_Item_Product[i].IndicationNumber, lstOEE_Item_Product[i].LineID, "SPI", lstOEE_Item_Product[i].MachineID,
                            lstOEE_Item_Product[i].TotalOKDay, lstOEE_Item_Product[i].TotalNGDay, lstOEE_Item_Product[i].DateTime, "NewSPC_" + factory);
                        if (rs == -9)
                            return -1;
                    }
                    Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, stage, timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff"));
                }
                else
                {
                    timeLast = timeLast.AddMinutes(minutes) > dateNow ? timeLast : timeLast.AddMinutes(minutes);

                    // Update ValueSearch
                    Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, stage, timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff"));
                }

                ManageLog.WriteProcess(true, null, recordNeedSyn, recordSyned);
            }
            catch (Exception ex)
            {
                timeLast = timeLast > dateNow ? timeLast : timeLast.AddMinutes(minutes);
                Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, stage, timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff"));
                ManageLog.WriteErrorApp(stage + "\n" + ex.Message);

                return -1;
            }
            await Task.Delay(500);

            return 1;
        }
        public async Task<int> FlexAssy_04_SolderPasteInspection_ProcessData_SEI_MES(string factory, DateTime lastTime, int minutes, string connectionStringOption)
        {
            string stage = "FlexAssy_04_SolderPasteInspection_SEI_MES";
            string typeBarcode = "BlockID";
            try
            {
                DateTime dateNow = DateTime.Now;
                DateTime timeLast = lastTime;
                int recordNeedSyn = 0;
                int recordSyned = 0;

                DataTable dt = Singleton_02_FlexAssy.ISource_FlexAssy_SEI_MES_History_Service.GetData_FlexAssy_04_SolderPasteInspection_SEI_MES_ByLastTime(timeLast, minutes, connectionStringOption);

                if (dt == null)
                    return -1;

                if (dt.Rows.Count > 0)
                {
                    //DataTable machineIDs = Singleton_03_Common.IMachineOfStageService.MachineOfStage_GetByFactoryAndStage(factory, stage);

                    List<MachineMaintenance> machineMaintenances = new List<MachineMaintenance>();
                    List<ProductionCondition> productionConditions = new List<ProductionCondition>();
                    List<FirstPieceBuyOffControl> firstPieceBuyOffControls = new List<FirstPieceBuyOffControl>();
                    List<MachineOfStage> machineOfStages = new List<MachineOfStage>();
                    List<OperatorOfStage> operatorOfStages = new List<OperatorOfStage>();

                    string connectionStringOptionMachine = Singleton_03_Common.IDBInfoService.ConnectionStringOption(factory, "MachineDB");

                    //number of records to sync
                    recordNeedSyn = dt.Rows.Count;
                    for (int i = 0; i < recordNeedSyn; i++)
                    {
                        string blockID = dt.Rows[i]["BlockID"].ToString().Trim();
                        DateTime dateTime = Convert.ToDateTime(dt.Rows[i]["DateTime"]);
                        string machineID = dt.Rows[i]["MachineID"].ToString().Trim();
                        string operatorID = dt.Rows[i]["OperatorID"].ToString().Trim();
                        string programName = dt.Rows[i]["ProgramName"].ToString().Trim();
                        string result = dt.Rows[i]["Result"].ToString().Trim();
                        int? panelQty = DataConvert.ConvertToInt(dt.Rows[i]["PanelQty"]);
                        int? PASSQty = DataConvert.ConvertToInt(dt.Rows[i]["PASSQty"]);
                        int? NGQty = DataConvert.ConvertToInt(dt.Rows[i]["NGQty"]);
                        int? BadMarkQty = DataConvert.ConvertToInt(dt.Rows[i]["BadMarkQty"]);
                        int? numberNGImage = DataConvert.ConvertToInt(dt.Rows[i]["NumberNGImage"]);
                        int ID_LogFile = DataConvert.ConvertToInt(dt.Rows[i]["ID_LogFile"]);
                        string Lane = dt.Rows[i]["Lane"].ToString().Trim();

                        // Find info machine maintenance date
                        List<MachineMaintenance> machineMaintenancesOther = Singleton_03_Common.ICommon_CommonService.GetMachine_Maintenance(null, machineID, dateTime, out DateTime? machineMaintenanceDate, out int? machineMaintenanceID, machineMaintenances, connectionStringOptionMachine);
                        if (machineMaintenancesOther != null)
                            machineMaintenances.AddRange(machineMaintenancesOther);

                        // Find info production condition
                        ProductionCondition productionCondition = Singleton_03_Common.ICommon_CommonService.GetMachine_Condition(machineID, dateTime, programName, out string productionConditionResult, out int? productionConditionID, productionConditions, connectionStringOptionMachine);
                        if (productionCondition != null)
                            productionConditions.Add(productionCondition);

                        // find infor first piece buy off controll
                        FirstPieceBuyOffControl firstPieceBuyOffControl = Singleton_03_Common.ICommon_CommonService.GetMachine_FirstPieceBuyOff(factory, machineID, blockID, dateTime, out DateTime? firstPieceBuyoffControlDate, out int? firstPieceBuyoffControlID, firstPieceBuyOffControls, connectionStringOptionMachine);
                        if (firstPieceBuyOffControl != null)
                            firstPieceBuyOffControls.Add(firstPieceBuyOffControl);

                        // Insert data
                        int rs = SEI_MES_FlexAssy_04_SolderPasteInspection_Insert(blockID, dateTime, machineID, operatorID, programName, result, panelQty, PASSQty, NGQty, BadMarkQty, numberNGImage,
                            firstPieceBuyoffControlDate, firstPieceBuyoffControlID, machineMaintenanceDate, machineMaintenanceID, productionConditionResult, productionConditionID, ID_LogFile, Lane, factory);
                        if (rs == -9)
                            return -1;


                        // machineID, OperatorID definition makes at the stage
                        MachineOfStage machineOfStage = Singleton_03_Common.ICommon_CommonService.GetMachineOfStage(machineID, null, stage, factory, typeBarcode, machineOfStages);
                        if (machineOfStage != null)
                            machineOfStages.Add(machineOfStage);

                        OperatorOfStage operatorOfStage = Singleton_03_Common.ICommon_CommonService.GetOperatorOfStage(operatorID, null, stage, factory, typeBarcode, operatorOfStages);
                        if (operatorOfStage != null)
                            operatorOfStages.Add(operatorOfStage);


                        // update the time that the record is synchronized
                        timeLast = Convert.ToDateTime(dt.Rows[i]["CreatedDate"]);

                        // count the number of synchronized records
                        recordSyned++;
                    }

                    // Update ValueSearch
                    if (DateTime.Compare(timeLast, lastTime) == 0)
                    {
                        timeLast = timeLast.AddMinutes(minutes) > dateNow ? timeLast : timeLast.AddMinutes(minutes);
                    }

                    // Update ValueSearch
                    Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, stage, timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff"));
                }
                else
                {
                    timeLast = timeLast.AddMinutes(minutes) > dateNow ? timeLast : timeLast.AddMinutes(minutes);

                    // Update ValueSearch
                    Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, stage, timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff"));
                }

                ManageLog.WriteProcess(true, null, recordNeedSyn, recordSyned);
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp(stage + "\n" + ex.Message);

                return -1;
            }

            await Task.Delay(500);

            return 1;
        }
        public async Task<int> FlexAssy_04_SolderPasteInspection_Detail_ProcessData_SEI_MES(string factory, DateTime lastTime, int minutes, string connectionStringOption)
        {
            string stage = "FlexAssy_04_SolderPasteInspection_Detail_SEI_MES";
            string typeBarcode = "BlockID";
            try
            {
                DateTime dateNow = DateTime.Now;
                DateTime timeLast = lastTime;
                int recordNeedSyn = 0;
                int recordSyned = 0;

                DataTable dt = Singleton_02_FlexAssy.ISource_FlexAssy_SEI_MES_History_Service.GetData_FlexAssy_04_SolderPasteInspection_Detail_SEI_MES_ByLastTime(timeLast, minutes, connectionStringOption);

                if (dt == null)
                    return -1;

                if (dt.Rows.Count > 0)
                {
                    List<MachineMaintenance> machineMaintenances = new List<MachineMaintenance>();
                    List<ProductionCondition> productionConditions = new List<ProductionCondition>();
                    List<FirstPieceBuyOffControl> firstPieceBuyOffControls = new List<FirstPieceBuyOffControl>();
                    List<MachineOfStage> machineOfStages = new List<MachineOfStage>();
                    List<OperatorOfStage> operatorOfStages = new List<OperatorOfStage>();

                    string connectionStringOptionMachine = Singleton_03_Common.IDBInfoService.ConnectionStringOption(factory, "MachineDB");

                    //number of records to sync
                    recordNeedSyn = dt.Rows.Count;
                    for (int i = 0; i < recordNeedSyn; i++)
                    {
                        string blockID = dt.Rows[i]["BlockID"].ToString().Trim();
                        string machineID = dt.Rows[i]["MachineID"].ToString().Trim();
                        DateTime dateTime = Convert.ToDateTime(dt.Rows[i]["DateTime"]);
                        string result = dt.Rows[i]["Result"].ToString().Trim();
                        int? numberNGImage = DataConvert.ConvertToInt(dt.Rows[i]["NumberNGImage"]);
                        string lane = dt.Rows[i]["Lane"].ToString().Trim();
                        int? cavityNumber = DataConvert.ConvertToInt(dt.Rows[i]["CavityNumber"]);
                        double? volumeMax = DataConvert.ConvertToDouble(dt.Rows[i]["VolumeMax"]);
                        double? volumeMin = DataConvert.ConvertToDouble(dt.Rows[i]["VolumeMin"]);
                        double? volumeAve = DataConvert.ConvertToDouble(dt.Rows[i]["VolumeAve"]);
                        double? heightMax = DataConvert.ConvertToDouble(dt.Rows[i]["HeightMax"]);
                        double? heightMin = DataConvert.ConvertToDouble(dt.Rows[i]["HeightMin"]);
                        double? heightAve = DataConvert.ConvertToDouble(dt.Rows[i]["HeightAve"]);
                        double? areaMax = DataConvert.ConvertToDouble(dt.Rows[i]["AreaMax"]);
                        double? areaMin = DataConvert.ConvertToDouble(dt.Rows[i]["AreaMin"]);
                        double? areaAve = DataConvert.ConvertToDouble(dt.Rows[i]["AreaAve"]);
                        double? shiftXMax = DataConvert.ConvertToDouble(dt.Rows[i]["ShiftXMax"]);
                        double? shiftXMin = DataConvert.ConvertToDouble(dt.Rows[i]["ShiftXMin"]);
                        double? shiftXAve = DataConvert.ConvertToDouble(dt.Rows[i]["ShiftXAve"]);
                        double? shiftYMax = DataConvert.ConvertToDouble(dt.Rows[i]["ShiftYMax"]);
                        double? shiftYMin = DataConvert.ConvertToDouble(dt.Rows[i]["ShiftYMin"]);
                        double? shiftYAve = DataConvert.ConvertToDouble(dt.Rows[i]["ShiftYAve"]);

                        // Insert data
                        int rs = SEI_MES_FlexAssy_04_SolderPasteInspection_Detail_Insert(blockID, machineID, dateTime, result, numberNGImage, lane, cavityNumber, volumeMax, volumeMin, volumeAve,
                            heightMax, heightMin, heightAve, areaMax, areaMin, areaAve, shiftXMax, shiftXMin, shiftXAve, shiftYMax, shiftYMin, shiftYAve, factory);
                        if (rs == -9)
                            return -1;


                        // machineID, OperatorID definition makes at the stage
                        MachineOfStage machineOfStage = Singleton_03_Common.ICommon_CommonService.GetMachineOfStage(machineID, null, stage, factory, typeBarcode, machineOfStages);
                        if (machineOfStage != null)
                            machineOfStages.Add(machineOfStage);

                        // update the time that the record is synchronized
                        timeLast = Convert.ToDateTime(dt.Rows[i]["CreatedDate"]);

                        // count the number of synchronized records
                        recordSyned++;
                    }

                    // Update ValueSearch
                    if (DateTime.Compare(timeLast, lastTime) == 0)
                    {
                        timeLast = timeLast.AddMinutes(minutes) > dateNow ? timeLast : timeLast.AddMinutes(minutes);
                    }

                    // Update ValueSearch
                    Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, stage, timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff"));
                }
                else
                {
                    timeLast = timeLast.AddMinutes(minutes) > dateNow ? timeLast : timeLast.AddMinutes(minutes);

                    // Update ValueSearch
                    Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, stage, timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff"));
                }

                ManageLog.WriteProcess(true, null, recordNeedSyn, recordSyned);
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp(stage + "\n" + ex.Message);

                return -1;
            }

            await Task.Delay(500);

            return 1;
        }
        public int FlexAssy_04_SolderPasteInspection_Insert(string blockID, DateTime dateTime, string machineID, string operatorID, string programName, string result, int? panelQty, int? passQty, int? ngQty,
            int? badMarkQty, int? numberNGImage, DateTime? firstPieceBuyoffControlDate, int? firstPieceBuyoffControlID, DateTime? machineMaintenanceDate, int? machineMaintenanceID,
            string productionConditionResult, int? productionConditionID, string connectionStringOption)
        {
            var paras = new SqlParameter[17];
            paras[0] = new SqlParameter("@BlockID", blockID);
            paras[1] = new SqlParameter("@DateTime", dateTime);
            paras[2] = new SqlParameter("@MachineID", machineID ?? (object)DBNull.Value);
            paras[3] = new SqlParameter("@OperatorID", operatorID ?? (object)DBNull.Value);
            paras[4] = new SqlParameter("@ProgramName", programName ?? (object)DBNull.Value);
            paras[5] = new SqlParameter("@Result", result ?? (object)DBNull.Value);
            paras[6] = new SqlParameter("@PanelQty", panelQty ?? (object)DBNull.Value);
            paras[7] = new SqlParameter("@PASSQty", passQty ?? (object)DBNull.Value);
            paras[8] = new SqlParameter("@NGQty", ngQty ?? (object)DBNull.Value);
            paras[9] = new SqlParameter("@BadMarkQty", badMarkQty ?? (object)DBNull.Value);
            paras[10] = new SqlParameter("@NumberNGImage", numberNGImage ?? (object)DBNull.Value);
            paras[11] = new SqlParameter("@FirstPieceBuyoffControlDate", firstPieceBuyoffControlDate ?? (object)DBNull.Value);
            paras[12] = new SqlParameter("@FirstPieceBuyoffControlID", firstPieceBuyoffControlID ?? (object)DBNull.Value);
            paras[13] = new SqlParameter("@MachineMaintenanceDate", machineMaintenanceDate ?? (object)DBNull.Value);
            paras[14] = new SqlParameter("@MachineMaintenanceID", machineMaintenanceID ?? (object)DBNull.Value);
            paras[15] = new SqlParameter("@ProductionConditionResult", productionConditionResult ?? (object)DBNull.Value);
            paras[16] = new SqlParameter("@ProductionConditionID", productionConditionID ?? (object)DBNull.Value);

            return _db.Execute_Modify("sp_sms_FlexAssy_04_SolderPasteInspection_Insert", paras, CommandType.StoredProcedure, connectionStringOption);
        }
        public int FlexAssy_04_SolderPasteInspection_Detail_Insert(List<FlexAssy_04_SolderPasteInspection_DetailModel> lstDetail, string connectionStringOption)
        {

            int ExcuteResult = 1;
            try
            {
                if (lstDetail != null)
                {
                    int count = 0;
                    string ExcuteQuery = "";
                    foreach (FlexAssy_04_SolderPasteInspection_DetailModel Detail in lstDetail)
                    {
                        ExcuteQuery += "EXEC [dbo].[sp_sms_FlexAssy_04_SolderPasteInspection_Detail_Insert]" + Environment.NewLine;
                        ExcuteQuery += "@BlockID = '" + Detail.BlockID + "'" + Environment.NewLine;
                        ExcuteQuery += ",@DateTime = '" + Detail.DateTime + "'" + Environment.NewLine;
                        ExcuteQuery += ",@MachineID = '" + Detail.MachineID + "'" + Environment.NewLine;
                        ExcuteQuery += ",@Result = '" + Detail.Result + "'" + Environment.NewLine;
                        ExcuteQuery += ",@NumberNGImage = '" + Detail.NumberNGImage + "'" + Environment.NewLine;
                        ExcuteQuery += ",@Lane = '" + Detail.Lane + "'" + Environment.NewLine;
                        ExcuteQuery += ",@CavityNumber = '" + Detail.CavityNumber + "'" + Environment.NewLine;
                        ExcuteQuery += ",@VolumeMax = '" + Detail.VolumeMax + "'" + Environment.NewLine;
                        ExcuteQuery += ",@VolumeMin = '" + Detail.VolumeMin + "'" + Environment.NewLine;
                        ExcuteQuery += ",@VolumeAve = '" + Detail.VolumeAve + "'" + Environment.NewLine;
                        ExcuteQuery += ",@HeightMax = '" + Detail.HeightMax + "'" + Environment.NewLine;
                        ExcuteQuery += ",@HeightMin = '" + Detail.HeightMin + "'" + Environment.NewLine;
                        ExcuteQuery += ",@HeightAve = '" + Detail.HeightAve + "'" + Environment.NewLine;
                        ExcuteQuery += ",@AreaMax = '" + Detail.AreaMax + "'" + Environment.NewLine;
                        ExcuteQuery += ",@AreaMin = '" + Detail.AreaMin + "'" + Environment.NewLine;
                        ExcuteQuery += ",@AreaAve = '" + Detail.AreaAve + "'" + Environment.NewLine;
                        ExcuteQuery += ",@ShiftXMax = '" + Detail.ShiftXMax + "'" + Environment.NewLine;
                        ExcuteQuery += ",@ShiftXMin = '" + Detail.ShiftXMin + "'" + Environment.NewLine;
                        ExcuteQuery += ",@ShiftXAve = '" + Detail.ShiftXAve + "'" + Environment.NewLine;
                        ExcuteQuery += ",@ShiftYMax = '" + Detail.ShiftYMax + "'" + Environment.NewLine;
                        ExcuteQuery += ",@ShiftYMin = '" + Detail.ShiftYMin + "'" + Environment.NewLine;
                        ExcuteQuery += ",@ShiftYAve = '" + Detail.ShiftYAve + "'" + Environment.NewLine;
                        if (count == 10)
                        {
                            ExcuteResult = _db.Execute_Modify(ExcuteQuery, null, CommandType.Text, connectionStringOption);
                            if (ExcuteResult == -9)
                            {
                                return -9;
                            }
                            count = 0;
                            ExcuteQuery = "";
                        }
                        count++;
                    }
                    if (ExcuteQuery.Length > 0)
                    {
                        ExcuteResult = _db.Execute_Modify(ExcuteQuery, null, CommandType.Text, connectionStringOption);
                        if (ExcuteResult == -9)
                        {
                            return -9;
                        }
                    }
                }
                return ExcuteResult;
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp("FlexAssy_04_SolderPasteInspection_ProcessReadLog_Insert_Detail:" + ex.Message);
                return -9;
            }
        }

        public async Task FlexAssy_04_SolderPasteInspection_ProcessCopyLog(string factory, string pathSaveLog, string userName, string passWord, string pathSaveLog_Medium, string userName_Medium, string password_Medium, int Active = 1)
        {
            if (string.IsNullOrEmpty(pathSaveLog))
                return;

            if (userName.Length > 0)
            {
                NetworkCredential credentials_Server = new NetworkCredential(userName, passWord);
                try
                {
                    using (new ConnectToSharedFolder(pathSaveLog, credentials_Server))
                    {
                        DataTable dt = Singleton_03_Common.ILogPathService.LogPath_GetByFactoryAndStage(factory, "FlexAssy_04_SolderPasteInspection", Active);
                        if (dt == null)
                            return;

                        if (dt.Rows.Count > 0)
                        {
                            for (int i = 0; i < dt.Rows.Count; i++)
                            {
                                int idLog = int.Parse(dt.Rows[i]["ID"].ToString());
                                string machineID = dt.Rows[i]["MachineID"].ToString().Trim();
                                string machineName = dt.Rows[i]["MachineName"].ToString().Trim();
                                DateTime lastTime = Convert.ToDateTime(dt.Rows[i]["LastTime"]);
                                string networkPath = dt.Rows[i]["LogAddress"].ToString().Trim();
                                string Line = dt.Rows[i]["Line"].ToString().Trim();
                                string GetType = "MultiFolder";
                                if (factory == "F4")
                                {
                                    GetType = "OneFolder";
                                }
                                string logNGImageAddress = dt.Rows[i]["LogNGImageAddress"].ToString().Trim();

                                if (!Common.CheckAddressExists(networkPath))
                                    continue;

                                if (dt.Rows[i]["Username"].ToString().Length > 0)
                                {
                                    try
                                    {
                                        NetworkCredential credentials = new NetworkCredential(dt.Rows[i]["Username"].ToString(), dt.Rows[i]["Password"].ToString());
                                        using (new ConnectToSharedFolder(networkPath, credentials))
                                        {
                                            await ProcessLogShare_Security(idLog, networkPath, factory, lastTime, pathSaveLog, machineID, machineName, logNGImageAddress, pathSaveLog_Medium, GetType);
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        ManageLog.WriteErrorApp("FlexAssy_04_SolderPasteInspection: " + networkPath + ";" + ex.Message);
                                        await ProcessLogShare_Security(idLog, networkPath, factory, lastTime, pathSaveLog, machineID, machineName, logNGImageAddress, pathSaveLog_Medium, GetType);
                                    }
                                }
                                else
                                {
                                    try
                                    {
                                        NetworkCredential credentials = new NetworkCredential("a", "");
                                        using (new ConnectToSharedFolder(networkPath, credentials))
                                        {
                                            await ProcessLogShare_Security(idLog, networkPath, factory, lastTime, pathSaveLog, machineID, machineName, logNGImageAddress, pathSaveLog_Medium, GetType);
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        ManageLog.WriteErrorApp("FlexAssy_04_SolderPasteInspection: " + networkPath + ";" + ex.Message);
                                        await ProcessLogShare_Security(idLog, networkPath, factory, lastTime, pathSaveLog, machineID, machineName, logNGImageAddress, pathSaveLog_Medium, GetType);
                                    }
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    ManageLog.WriteProcess(false, "Lỗi Không vào được", 0, 0);
                    ManageLog.WriteErrorApp("FlexAssy_04_SolderPasteInspection: " + pathSaveLog + ";" + ex.Message);
                }
            }
            else
            {
                DataTable dt = Singleton_03_Common.ILogPathService.LogPath_GetByFactoryAndStage(factory, "FlexAssy_04_SolderPasteInspection", Active);
                if (dt == null)
                    return;

                if (dt.Rows.Count > 0)
                {
                    for (int i = 0; i < dt.Rows.Count; i++)
                    {
                        int idLog = int.Parse(dt.Rows[i]["ID"].ToString());
                        string machineID = dt.Rows[i]["MachineID"].ToString().Trim();
                        string machineName = dt.Rows[i]["MachineName"].ToString().Trim();
                        DateTime lastTime = Convert.ToDateTime(dt.Rows[i]["LastTime"]);
                        string networkPath = dt.Rows[i]["LogAddress"].ToString().Trim();
                        string logNGImageAddress = dt.Rows[i]["LogNGImageAddress"].ToString().Trim();
                        string GetType = "MultiFolder";
                        if (factory == "F4")
                        {
                            GetType = "OneFolder";
                        }

                        if (!Common.CheckAddressExists(networkPath))
                            continue;

                        if (dt.Rows[i]["Username"].ToString().Length > 0)
                        {
                            NetworkCredential credentials = new NetworkCredential(dt.Rows[i]["Username"].ToString(), dt.Rows[i]["Password"].ToString());
                            try
                            {
                                using (new ConnectToSharedFolder(networkPath, credentials))
                                {
                                    await ProcessLogShare_Security(idLog, networkPath, factory, lastTime, pathSaveLog, machineID, machineName, logNGImageAddress, pathSaveLog_Medium, GetType);
                                }
                            }
                            catch (Exception ex)
                            {
                                await ProcessLogShare_Security(idLog, networkPath, factory, lastTime, pathSaveLog, machineID, machineName, logNGImageAddress, pathSaveLog_Medium, GetType);
                                ManageLog.WriteErrorApp("FlexAssy_04_SolderPasteInspection: " + networkPath + ";" + ex.Message);
                            }
                        }
                        else
                        {
                            NetworkCredential credentials = new NetworkCredential("a", "");
                            try
                            {
                                using (new ConnectToSharedFolder(networkPath, credentials))
                                {
                                    await ProcessLogShare_Security(idLog, networkPath, factory, lastTime, pathSaveLog, machineID, machineName, logNGImageAddress, pathSaveLog_Medium, GetType);
                                }
                            }
                            catch (Exception ex)
                            {
                                ManageLog.WriteErrorApp("FlexAssy_04_SolderPasteInspection: " + networkPath + ";" + ex.Message);

                                await ProcessLogShare_Security(idLog, networkPath, factory, lastTime, pathSaveLog, machineID, machineName, logNGImageAddress, pathSaveLog_Medium, GetType);
                            }
                        }
                    }
                }
            }

            await Task.Delay(500);
        }

        public DataTable FlexAssy_04_SolderPasteInspection_LogFile_GetDataWithID(int iD, int LastID, string connectionStringOption)
        {
            var paras = new SqlParameter[2];
            paras[0] = new SqlParameter("@ID", iD);
            paras[1] = new SqlParameter("@LastID", LastID);

            return _db.Execute_Table("sp_sms_FlexAssy_04_SolderPasteInspection_LogFile_GetDataWithID", paras, CommandType.StoredProcedure, connectionStringOption);
        }
        public async Task ProcessLogShare_Security(int idLog, string networkPath, string factory, DateTime lastTime, string pathSaveLog, string machineID, string machineName, string logNGImageAddress, string pathSaveLog_Medium, string GetType = "MultiFolder")
        {
            ManageLog.WriteProcess(false, machineID + "Start", 0, 0);
            if (!Common.DirectoryExistsTimeout(networkPath))
            {
                ManageLog.WriteErrorApp("FlexAssy_04_SolderPasteInspection: " + networkPath + " is not connect");
                return;
            }
            ManageLog.WriteProcess(false, networkPath + "Start", 0, 0);

            try
            {
                // Lấy các file dữ liệu log
                //lastTime = 2019-01-05 09:23:39.000
                //dateEnd = 2019-01-06 09:23:39.000
                DateTime dateNow = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 0, 0, 0);
                DateTime dateEnd = DateTime.Compare(lastTime.AddDays(1), dateNow) > 0 ? dateNow : lastTime.AddDays(1);
                lastTime = lastTime.AddMilliseconds(10);
                List<FileInfo> fileInfos = new List<FileInfo>();
                int error = 0;
                while (DateTime.Compare(dateEnd, dateNow) <= 0)
                {
                    error = 0;
                    if (factory.Equals("F5") || GetType == "OneFolder")
                    {
                        ManageLog.WriteProcess(false, "Trace 3" + GetType, 0, 0);
                        fileInfos = Common.GetAllFilesInOneFolder(factory.Equals("F3") ? ".csv" : ".txt", networkPath);
                    }
                    else
                    {
                        fileInfos = Common.GetListFileLogFromAnalysis2(factory.Equals("F3") ? ".csv" : ".txt", networkPath, lastTime, dateEnd);

                        List<FileInfo> fileInfosLastday = new List<FileInfo>();
                        fileInfosLastday = Common.GetListFileLogFromAnalysisLastDay(factory.Equals("F3") ? ".csv" : ".txt", networkPath, lastTime.AddDays(-1));
                        if (fileInfosLastday.Count > 0)
                        {
                            fileInfos.AddRange(fileInfosLastday);
                        }
                    }
                    ManageLog.WriteProcess(false, machineID + fileInfos.Count, 0, 0);

                    //ManageLog.WriteErrorApp("FlexAssy_04_SolderPasteInspection_ProcessReadLog: filecount " + networkPath +"\'"+ lastTime + "\'" + fileInfos.Count() + "");

                    if (fileInfos.Count > 0)
                    {
                        List<MachineMaintenance> machineMaintenances = new List<MachineMaintenance>();
                        List<ProductionCondition> productionConditions = new List<ProductionCondition>();
                        List<FirstPieceBuyOffControl> firstPieceBuyOffControls = new List<FirstPieceBuyOffControl>();

                        string connectionStringOptionMachine = Singleton_03_Common.IDBInfoService.ConnectionStringOption(factory, "MachineDB");

                        foreach (var item in fileInfos)
                        {
                            ManageLog.WriteProcess(false, machineID + item.FullName, 0, 0);
                            string pathSaveLogNew = $"{pathSaveLog_Medium}\\SPI\\{machineID}-{machineName}\\AutoExport\\{item.LastWriteTime.ToString("yyyyMM")}\\{item.LastWriteTime.ToString("yyyyMMdd")}"; // pathSaveLog + "\\SPI\\" + machineID  + "\\" + item.DirectoryName;
                            //string pathSaveLogNewImage = $"{pathSaveLog}\\AOI\\Post\\AutoExport\\{machineID}-{machineName}\\{item.LastWriteTime.ToString("yyyyMM")}\\{item.LastWriteTime.ToString("yyyyMMdd")}";
                            string pathSaveLogNewImage = $"{pathSaveLog}\\SPI\\{machineID}-{machineName}\\Auto" +
                                $"Export\\{item.LastWriteTime.ToString("yyyyMM")}\\{item.LastWriteTime.ToString("yyyyMMdd")}";

                            if (item.FullName.Contains("_NG"))
                            {
                                if (item.LastWriteTime.AddMinutes(5) > DateTime.Now)
                                {
                                    break;
                                }
                            }

                            // Copy du lieu ve F2
                            string fileName = Common.CopyFileToPcFromServer(item, pathSaveLogNew); // Copy file to Server
                            if (fileName == null)
                            {
                                error++;
                                break;
                            }

                            int rs = FlexAssy_04_SolderPasteInspection_LogFile_Insert(machineID, item.FullName, fileName, 0, factory); //Dia chi tren F4 : Common.Replace_PathSaveLog(pathSaveLog, fileName)
                            if (rs == -9)
                            {
                                error++;
                                break;
                            }

                            // Đọc luôn dữ liệu SPI của F4
                            if (factory.Equals("F4"))
                            {
                                if (logNGImageAddress == null)
                                {
                                    ManageLog.WriteErrorApp("FlexAssy_04_SolderPasteInspection: No define logNGImageAddress");
                                    {
                                        error++;
                                        break;
                                    }
                                }
                                int rs1 = FlexAssy_04_SolderPasteInspection_ReadFileSPI_F4(factory, rs, item.FullName, machineID, pathSaveLog, pathSaveLogNewImage, logNGImageAddress,
                                    machineMaintenances, productionConditions, firstPieceBuyOffControls, connectionStringOptionMachine); // Read File SPI
                                if (rs1 == -9)
                                {
                                    error++;
                                    break;
                                }
                            }
                            else if (factory.Equals("F3"))
                            {
                                int rs1 = FlexAssy_04_SolderPasteInspection_ReadFileSPI_F3(factory, rs, item.FullName, machineID, pathSaveLog, pathSaveLogNewImage, logNGImageAddress, networkPath,
                                    machineMaintenances, productionConditions, firstPieceBuyOffControls, connectionStringOptionMachine); // Read File SPI
                                if (rs1 == -9)
                                {
                                    error++;
                                    break;
                                }
                            }
                            else
                            {
                                int rs1 = FlexAssy_04_SolderPasteInspection_ReadFileSPI_F5(factory, rs, item.FullName, machineID, pathSaveLog, pathSaveLogNewImage, logNGImageAddress,
                                    machineMaintenances, productionConditions, firstPieceBuyOffControls, connectionStringOptionMachine); // Read File SPI
                                if (rs1 == -9)
                                {
                                    error++;
                                    break;
                                }
                            }

                            if (factory.Equals("F4"))
                            {
                                // Move file to Trace_READ_OK /NG
                                bool isMove = Common.MoveTo_Trace_READ_OK(networkPath, item, out string fileNameRoot, "SPI", machineID);
                                if (!isMove) // Loi move file
                                {
                                    error++;
                                    break;
                                }

                                // Move file thanh cong
                                rs = FlexAssy_04_SolderPasteInspection_LogFile_UpdateFileNameRoot(rs, fileNameRoot, factory);
                                if (rs == -9)
                                {
                                    error++;
                                    break;
                                }
                            }
                            else if (factory.Equals("F5"))
                            {
                                // Move file to Trace_READ_OK /NG
                                bool isMove = Common.MoveTo_Trace_READ_OKF5(networkPath, item, out string fileNameRoot, "SPI", machineID);
                                if (!isMove) // Loi move file
                                {
                                    error++;
                                    break;
                                }

                                // Move file thanh cong
                                rs = FlexAssy_04_SolderPasteInspection_LogFile_UpdateFileNameRoot(rs, fileNameRoot, factory);
                                if (rs == -9)
                                {
                                    error++;
                                    break;
                                }
                            }
                            else if (factory.Equals("F3"))
                            {
                                // Move file to Trace_READ_OK for F3
                                bool isMove = Common.MoveTo_Trace_READ_OK_SPI_F3(item);
                                if (!isMove) // Loi move file
                                {
                                    error++;
                                    break;
                                }
                            }

                            lastTime = item.LastWriteTime;
                            if (factory.Equals("F3"))
                            {
                                Singleton_03_Common.ILogPathService.LogPath_UpdateLastTime(idLog, lastTime); // Update thời gian của file cuối cùng vừa  lấy về
                            }

                            // await Task.Delay(500);
                        }
                    }
                    else
                    {
                        if (DateTime.Compare(lastTime, dateEnd) <= 0)
                            lastTime = dateEnd;
                    }

                    Singleton_03_Common.ILogPathService.LogPath_UpdateLastTime(idLog, lastTime); // Update thời gian của file cuối cùng vừa  lấy về

                    dateEnd = dateEnd.AddDays(1); //DateTime.Compare(lastTime.AddDays(1), dateNow) > 0 ? dateNow : lastTime.AddDays(1);

                    if (error > 0)
                    {
                        return;
                    }
                }
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp("FlexAssy_04_SolderPasteInspection: Copyfile to F2 " + ex.Message);
            }

            await Task.Delay(500);
        }
        public bool SPI_Result_CheckTableExist(string TableName, string connectionString)
        {
            bool bExist = false;
            DateTime dt = DateTime.Now;
            var paras = new SqlParameter[1];
            paras[0] = new SqlParameter("@TableName", TableName);
            var rs = _db.Execute_Table("sp_sms_Mes_History_CheckTableExist", paras, CommandType.StoredProcedure, connectionString);
            if (rs?.Rows?.Count > 0)
            {
                if (int.Parse(rs.Rows[0].ItemArray[0].ToString()) > 0)
                    bExist = true;
            }
            return bExist;
        }

        public int Result_SPI_CreateTable(string TableName, string connectionString)
        {
            var paras = new SqlParameter[1];
            paras[0] = new SqlParameter("@TableName", TableName);
            return _db.Execute_Modify("sp_sms_Mes_History_TblResultSPI_CreateTableIndex", paras, CommandType.StoredProcedure, connectionString);
        }

        public int FlexAssy_04_SolderPasteInspection_ReadFileSPI_F3(string factory, int iD_LogFile, string fileName, string machineID, string pathSaveLog, string pathSaveLogNew, string logNGImageAddress, string networkPath,
            List<MachineMaintenance> machineMaintenances, List<ProductionCondition> productionConditions, List<FirstPieceBuyOffControl> firstPieceBuyOffControls, string connectionStringOptionMachine)
        {
            StreamReader file = new StreamReader(fileName);
            using (var lineReader = new ReadFileText(file))
            {
                try
                {
                    IEnumerable<string> lines = lineReader;
                    string[] enumerable = lines as string[] ?? lines.ToArray();
                    string blockID = null;
                    DateTime? dateTime = null;
                    string programName = null;
                    string operatorID = null;
                    string result = null;

                    int panelQty = 0;
                    int passQty = 0;
                    int ngQty = 0;
                    int badMarkQty = 0;
                    int numberNGImage = 0;
                    string Lane = "";

                    string ItemName = "";
                    List<FlexAssy_04_SolderPasteInspection_DetailModel> lstDetail = new List<FlexAssy_04_SolderPasteInspection_DetailModel>();
                    int numberFolderImage = 2;

                    string dateFromName = Path.GetFileNameWithoutExtension(fileName);
                    string[] str = dateFromName.Split('_');
                    string inspectionDate = $"{str[1].Substring(0, 4)}-{str[1].Substring(4, 2)}-{str[1].Substring(6, 2)}";
                    dateFromName = $"{str[1].Substring(0, 4)}-{str[1].Substring(4, 2)}-{str[1].Substring(6, 2)} {str[1].Substring(8, 2)}:{str[1].Substring(10, 2)}:{str[1].Substring(12, 2)}";
                    string LineID = "";
                    DataTable dtMachine = Singleton_04_Machine.ItMachineMtnService.tMachineList_GetByID(machineID);
                    if (dtMachine?.Rows.Count > 0)
                    {
                        LineID = dtMachine.Rows[0]["LineID"] != null ? dtMachine.Rows[0]["LineID"].ToString() : "";
                    }
                    string sTableName = "tblResultSPI_" + $"{str[1].Substring(0, 4)}{str[1].Substring(4, 2)}";
                    string MESConnectionString = Singleton_03_Common.IDBInfoService.ConnectionStringOption(factory, "SEI-MES-HistoryDB_Local");
                    if (!SPI_Result_CheckTableExist(sTableName, MESConnectionString))
                    {
                        Result_SPI_CreateTable(sTableName, MESConnectionString);
                    }
                    string GetPadNo = "";
                    DataTable dtConfig;
                    string OldImagePath = "";
                    int PcsNGImage = 0;
                    int InserCount = 0;
                    string SPIDetailQuery = "";
                    List<string> lstQuery = new List<string>();
                    int listCount = 0;
                    lstQuery.Add("");

                    for (int i = 0; i < enumerable.Length; i++)
                    {
                        PcsNGImage = 0;
                        InserCount++;
                        if (i < 24)
                        {
                            if (i == 2)
                            {
                                operatorID = enumerable[i].Split(',')[1].Trim();
                                continue;
                            }

                            if (i == 3)
                            {
                                programName = enumerable[i].Split(',')[1].Trim();
                                continue;
                            }

                            if (i == 5)
                            {
                                blockID = enumerable[i].Split(',')[1].Trim();
                                dateTime = Convert.ToDateTime(dateFromName);
                                //spc Get Data
                                //SPCblock_ID = SPC_BlockID_Insert(blockID, dateFromName, inspectionDate, "SPC_F3");
                                string indiNumber = Singleton_02_FlexAssy.IFlexAssy_BlockIDService.FlexAssy_BlockID_GetByIndicationNumberByBlockID("F3", blockID);
                                DataTable dtWorkOrder = Singleton_02_FlexAssy.IFlexAssy_WorkOrderService.FlexAssy_WorkOrder_GetByWorkOrder(indiNumber, factory);
                                if (dtWorkOrder?.Rows.Count > 0)
                                {
                                    ItemName = dtWorkOrder.Rows[0]["ItemName"] != null ? dtWorkOrder.Rows[0]["ItemName"].ToString() : "";
                                }
                                continue;
                            }

                            if (i == 6)
                            {
                                // 2021-06-21 Improve SPI by tungud
                                //panelQty = int.Parse(enumerable[i].Split(',')[1].Trim());
                            }

                            if (i == 7)
                            {
                                // 2021-06-21 Improve SPI by tungud
                                //ngQty = int.Parse(enumerable[i].Split(',')[1].Trim());
                                result = enumerable[i].Split(',')[1].Trim() == "0" ? "PASS" : "FAIL";
                                if (result != "PASS")
                                {
                                    // Xử lý ảnh NG 
                                }
                                continue;
                            }
                            continue;
                        }
                        //spc Get Data Start
                        string[] strArr = enumerable[i].Trim().Split(',');
                        int PinNo = DataConvert.ConvertToInt(strArr[5]);
                        int PadNo = DataConvert.ConvertToInt(strArr[6]);
                        int CavityNumber = DataConvert.ConvertToInt(strArr[2]);
                        string PadResult = strArr[0];
                        if (PadResult.Equals("NG"))
                        {
                            PadResult = "FAIL";
                        }
                        else if (PadResult.Equals("BADMARK"))
                        {
                            PadResult = "BADMARK";
                        }
                        else
                        {
                            PadResult = "OK";
                        }
                        if (strArr.Count() > 53)
                        {
                            string Image = DataConvert.ConvertToString(strArr[53]).Trim();
                            string rsImage = "";

                            if (Image.Contains("Vp6000ds-01"))
                            {
                                rsImage = Image.Replace("\\\\Vp6000ds-01", "");
                            }
                            else if (Image.Contains("Vp6000ds-02"))
                            {
                                rsImage = Image.Replace("\\\\Vp6000ds-02", "");
                            }
                            else
                            {
                                rsImage = Image;
                            }

                            string ImagePath = logNGImageAddress + rsImage + ".jpg";
                            string padDefectType = "";

                            if (!Image.Equals("-") && strArr[0].Equals("NG"))
                            {
                                try
                                {
                                    string judgeVolume = DataConvert.ConvertToString(strArr[13]).Trim();
                                    if (judgeVolume.ToUpper().Trim().Equals("NG"))
                                    {
                                        padDefectType = "Volume";
                                    }

                                    string judgeHeight = DataConvert.ConvertToString(strArr[19]).Trim();
                                    if (judgeHeight.ToUpper().Trim().Equals("NG"))
                                    {
                                        if (!string.IsNullOrEmpty(padDefectType))
                                        {
                                            padDefectType = padDefectType + ",Height";
                                        }
                                        else
                                        {
                                            padDefectType = "Height";
                                        }
                                    }

                                    string judgeArea = DataConvert.ConvertToString(strArr[33]).Trim();
                                    if (judgeArea.ToUpper().Trim().Equals("NG"))
                                    {
                                        if (!string.IsNullOrEmpty(padDefectType))
                                        {
                                            padDefectType = padDefectType + ",Area";
                                        }
                                        else
                                        {
                                            padDefectType = "Area";
                                        }
                                    }

                                    string judgeXShift = DataConvert.ConvertToString(strArr[37]).Trim();
                                    if (judgeXShift.ToUpper().Trim().Equals("NG"))
                                    {
                                        if (!string.IsNullOrEmpty(padDefectType))
                                        {
                                            padDefectType = padDefectType + ",XShift";
                                        }
                                        else
                                        {
                                            padDefectType = "XShift";
                                        }
                                    }

                                    string judgeYShift = DataConvert.ConvertToString(strArr[41]).Trim();
                                    if (judgeYShift.ToUpper().Trim().Equals("NG"))
                                    {
                                        if (!string.IsNullOrEmpty(padDefectType))
                                        {
                                            padDefectType = padDefectType + ",YShift";
                                        }
                                        else
                                        {
                                            padDefectType = "YShift";
                                        }
                                    }
                                    string judgeBright = DataConvert.ConvertToString(strArr[46]).Trim();
                                    if (judgeBright.ToUpper().Trim().Equals("NG"))
                                    {
                                        if (!string.IsNullOrEmpty(padDefectType))
                                        {
                                            padDefectType = padDefectType + ",Bright";
                                        }
                                        else
                                        {
                                            padDefectType = "Bright";
                                        }
                                    }

                                    if (!OldImagePath.Contains(ImagePath))
                                    {
                                        // Copy ảnh lỗi về Server
                                        FileInfo item = new FileInfo(ImagePath);
                                        if (item != null)
                                        {
                                            string pathSaveLogNewImage = numberFolderImage == 1 ? $"{pathSaveLogNew.Replace("AutoExport", "Image2D")}\\{blockID}" : $"{pathSaveLogNew.Replace("AutoExport", "Image2D")}\\{blockID}_0{numberFolderImage}";

                                            string fileDesName = Common.CopyFileToPcFromServer(item, pathSaveLogNewImage); // Copy file to Server
                                            if (fileDesName == null)
                                            {
                                                ManageLog.WriteErrorApp("FlexAssy_04_SolderPasteInspection_ProcessReadLog: fileDesName is null " + networkPath + "");
                                                //return -9;
                                            }
                                            else
                                            {
                                                int rsSaveImage = FlexAssy_04_SolderPasteInspection_Image2DF3_Insert(blockID, machineID, CavityNumber, PadNo, padDefectType, Common.Replace_PathSaveLog(pathSaveLog, fileDesName), factory);
                                                if (rsSaveImage == -9)
                                                {
                                                    return -9;
                                                }
                                                else
                                                {
                                                    numberNGImage++;
                                                    PcsNGImage = 1;
                                                }
                                                OldImagePath += ImagePath;
                                            }
                                        }
                                        else
                                        {
                                            numberNGImage--;
                                        }
                                    }
                                }
                                catch (Exception ex)
                                {
                                    ManageLog.WriteErrorApp("FlexAssy_04_SolderPasteInspection_ProcessReadLog: " + iD_LogFile + "\n" + "Line:" + i + ex.Message);
                                }
                            }
                        }
                        if (PinNo < 0)
                        {
                            continue;
                        }
                        decimal Volume = DataConvert.ConvertToDecimal(strArr[8]);
                        decimal Height = DataConvert.ConvertToDecimal(strArr[14]);
                        decimal Area = DataConvert.ConvertToDecimal(strArr[28]);
                        decimal OffsetX = DataConvert.ConvertToDecimal(strArr[34]);
                        decimal OffsetY = DataConvert.ConvertToDecimal(strArr[38]);


                        FlexAssy_04_SolderPasteInspection_DetailModel.CalcDetailF3(ref lstDetail, CavityNumber, Volume, Height, Area, OffsetX, OffsetY, blockID, machineID
                            , Convert.ToDateTime(dateTime), Lane, PcsNGImage, PadResult, ref passQty, ref badMarkQty, ref ngQty, ref panelQty);

                        string GroupName = strArr[4];
                        if (i % 30 == 0)
                        {
                            listCount += 1;
                            lstQuery.Add(GenStringTransactionSPI_STORE(sTableName, Convert.ToDateTime(dateTime), machineID, blockID, GroupName, CavityNumber, PinNo, PadNo, PadResult, Volume, Area, Height, OffsetX, 0, OffsetY, 0));
                        }
                        else
                        {
                            lstQuery[listCount] += GenStringTransactionSPI_STORE(sTableName, Convert.ToDateTime(dateTime), machineID, blockID, GroupName, CavityNumber, PinNo, PadNo, PadResult, Volume, Area, Height, OffsetX, 0, OffsetY, 0);
                        }
                    }
                    ngQty = lstDetail.FindAll(item => item.Result == "FAIL").Count;
                    badMarkQty = lstDetail.FindAll(item => item.Result == "BADMARK").Count;
                    passQty = lstDetail.FindAll(item => item.Result == "OK").Count;

                    #region Sử lý thêm dữ liệu vào DB SMES_History
                    int SPI_ID = SEI_MES_FlexAssy_04_SolderPasteInspection_Save(blockID, Convert.ToDateTime(dateTime), machineID, operatorID, programName, result, panelQty, passQty, ngQty, badMarkQty,
                        numberNGImage, iD_LogFile, LineID, MESConnectionString);
                    if (SPI_ID == -9)
                    {
                        return -9;
                    }

                    for (int count = 0; count <= listCount; count++)
                    {
                        if (count == 0)
                        {
                            SPIDetailQuery = lstQuery[count];
                            SPIDetailQuery = SPIDetailQuery.Replace("#SPIID", SPI_ID + "");
                            SPIDetailQuery = "DELETE " + sTableName + " WHERE SPIID = " + SPI_ID + Environment.NewLine + SPIDetailQuery;
                            SPIDetailQuery = "DECLARE	@return_value int  " + Environment.NewLine + SPIDetailQuery;
                            SPIDetailQuery += " SELECT 'Return Value' = @return_value" + Environment.NewLine;
                        }
                        else
                        {
                            SPIDetailQuery = lstQuery[count];
                            SPIDetailQuery = SPIDetailQuery.Replace("#SPIID", SPI_ID + "");
                            SPIDetailQuery = "DECLARE	@return_value int  " + Environment.NewLine + SPIDetailQuery;
                            SPIDetailQuery += " SELECT 'Return Value' = @return_value" + Environment.NewLine;
                        }
                        int SPIDetailQueryrs = _db.Execute_Modify(SPIDetailQuery, null, CommandType.Text, MESConnectionString);
                        if (SPIDetailQueryrs == -9)
                        {
                            return -9;
                        }
                    }
                    #endregion

                    int rsSaveDetail = FlexAssy_04_SolderPasteInspection_Detail_Insert(lstDetail, factory);
                    if (rsSaveDetail == -9)
                    {
                        return -9;
                    }
                    if (string.IsNullOrEmpty(blockID))
                    {
                        int rs1 = FlexAssy_04_SolderPasteInspection_LogFile_UpdateLineRead(iD_LogFile, -1, false, factory);
                        if (rs1 == -9)
                            return -9;

                        ManageLog.WriteErrorApp("BlockId null: " + iD_LogFile + "\n" + fileName);
                        return 1;
                    }

                    // Find info machine maintenance date
                    List<MachineMaintenance> machineMaintenancesOther = Singleton_03_Common.ICommon_CommonService.GetMachine_Maintenance(null, machineID, Convert.ToDateTime(dateTime), out DateTime? machineMaintenanceDate, out int? machineMaintenanceID, machineMaintenances, connectionStringOptionMachine);
                    if (machineMaintenancesOther != null)
                        machineMaintenances.AddRange(machineMaintenancesOther);

                    // Find info production condition
                    ProductionCondition productionCondition = Singleton_03_Common.ICommon_CommonService.GetMachine_Condition(machineID, Convert.ToDateTime(dateTime), programName, out string productionConditionResult, out int? productionConditionID, productionConditions, connectionStringOptionMachine);
                    if (productionCondition != null)
                        productionConditions.Add(productionCondition);

                    // find infor first piece buy off controll
                    FirstPieceBuyOffControl firstPieceBuyOffControl = Singleton_03_Common.ICommon_CommonService.GetMachine_FirstPieceBuyOff(factory, machineID, blockID, Convert.ToDateTime(dateTime), out DateTime? firstPieceBuyoffControlDate, out int? firstPieceBuyoffControlID, firstPieceBuyOffControls, connectionStringOptionMachine);
                    if (firstPieceBuyOffControl != null)
                        firstPieceBuyOffControls.Add(firstPieceBuyOffControl);

                    int rs = FlexAssy_04_SolderPasteInspection_UpdateReadLogNew(blockID, dateTime, programName, operatorID, machineID, result, panelQty, passQty, ngQty, badMarkQty, numberNGImage,
                        firstPieceBuyoffControlDate, firstPieceBuyoffControlID, machineMaintenanceDate, machineMaintenanceID, productionConditionResult, productionConditionID, iD_LogFile, Lane, factory);
                    if (rs == -9)
                        return -9;

                    rs = FlexAssy_04_SolderPasteInspection_LogFile_UpdateLineRead(iD_LogFile, enumerable.Length, true, factory);
                    if (rs == -9)
                        return -9;
                    return 1;
                }
                catch (Exception ex)
                {
                    int rs = FlexAssy_04_SolderPasteInspection_LogFile_UpdateLineRead(iD_LogFile, -1, false, factory);
                    if (rs == -9)
                        return -9;

                    ManageLog.WriteErrorApp("FlexAssy_04_SolderPasteInspection_ProcessReadLog: " + iD_LogFile + "\n" + ex.Message);
                    return -2;
                }
            }
        }
        public int SEI_MES_FlexAssy_04_SolderPasteInspection_Save(string blockID, DateTime dateTime, string machineID, string operatorID, string programName, string result, int? panelQty, int? passQty, int? ngQty,
            int? badMarkQty, int? numberNGImage, int? id_logFile, string lane, string connectionStringOption)
        {
            var paras = new SqlParameter[13];
            paras[0] = new SqlParameter("@BlockID", blockID);
            paras[1] = new SqlParameter("@DateTime", dateTime);
            paras[2] = new SqlParameter("@MachineID", machineID ?? (object)DBNull.Value);
            paras[3] = new SqlParameter("@OperatorID", operatorID ?? (object)DBNull.Value);
            paras[4] = new SqlParameter("@ProgramName", programName ?? (object)DBNull.Value);
            paras[5] = new SqlParameter("@Result", result ?? (object)DBNull.Value);
            paras[6] = new SqlParameter("@PanelQty", panelQty ?? (object)DBNull.Value);
            paras[7] = new SqlParameter("@PASSQty", passQty ?? (object)DBNull.Value);
            paras[8] = new SqlParameter("@NGQty", ngQty ?? (object)DBNull.Value);
            paras[9] = new SqlParameter("@BadMarkQty", badMarkQty ?? (object)DBNull.Value);
            paras[10] = new SqlParameter("@NumberNGImage", numberNGImage ?? (object)DBNull.Value);
            paras[11] = new SqlParameter("@ID_LogFile", id_logFile ?? (object)DBNull.Value);
            paras[12] = new SqlParameter("@Lane", lane ?? (object)DBNull.Value);

            return _db.Execute_Scalar("sp_sms_FlexAssy_04_SolderPasteInspection_Insert_SEI_MES", paras, CommandType.StoredProcedure, connectionStringOption);
        }
        string GenStringTransactionSPI_STORE(string TableName, DateTime dt, string machineID, string BlockID, string GroupName, int PanelNumber, int PinNo, int PadID, string PadResult, decimal Volume, decimal Area, decimal Height, decimal OffsetX, decimal BridgeDistance, decimal OffsetY, decimal BridgeHeight)
        {
            string sql = string.Empty;
            try
            {
                sql = " EXEC	@return_value = [dbo].[sp_sms_Mes_History_TblResultSPI_InsertDynamic_Retry_20221116]"
                             + @"@BlockID = N'" + BlockID + "',"

                + @"@BlockDatetime = N'" + dt.ToString("yyyy-MM-dd HH:mm:ss") + "',"
                + @"@MachineID = N'" + machineID + "',"
                + @"@GroupName = N'" + GroupName + "',"
                + @"@PanelNumber = " + PanelNumber + ","
                + @"@PadID = " + PadID + ","
                + @"@PinNo = " + PinNo + ","
                + @"@PadResult = N'" + PadResult + "',"
                + @"@Volume = N'" + Volume + "',"
                + @"@Area = N'" + Area + "',"
                + @"@Height = N'" + Height + "',"
                + @"@OffsetX = N'" + OffsetX + "',"
                + @"@OffsetY = N'" + OffsetY + "',"
                + @"@BridgeHeight = N'" + BridgeHeight + "',"
                + @"@BridgeDistance = N'" + BridgeDistance + "',"
                + @"@TableName = N'" + TableName + "',"
                + @"@SpiID = #SPIID" + Environment.NewLine;
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp(ex.ToString());
            }
            return sql;
        }
        public int FlexAssy_04_SolderPasteInspection_ReadFileSPI_F4(string factory, int iD_LogFile, string fileName, string machineID, string pathSaveLog, string pathSaveLogNew, string logNGImageAddress,
            List<MachineMaintenance> machineMaintenances, List<ProductionCondition> productionConditions, List<FirstPieceBuyOffControl> firstPieceBuyOffControls, string connectionStringOptionMachine)
        {
            StreamReader file = new StreamReader(fileName);
            using (var lineReader = new ReadFileText(file))
            {
                try
                {
                    IEnumerable<string> lines = lineReader;
                    string[] enumerable = lines as string[] ?? lines.ToArray();

                    string blockID = null;
                    string dateTime = null;
                    string programName = null;
                    string operatorID = null;
                    string result = null;
                    int panelQty = 0;
                    int passQty = 0;
                    int ngQty = 0;
                    int badMarkQty = 0;
                    int numberNGImage = 0;
                    int addNGImageOK = 0;
                    string Lane = "";
                    List<FileInfo> images = new List<FileInfo>();
                    List<FlexAssy_04_SolderPasteInspection_DetailModel> lstDetail = new List<FlexAssy_04_SolderPasteInspection_DetailModel>();
                    int numberFolderImage = 2;
                    bool isPad = false;
                    decimal SPCblock_ID = 0;
                    decimal SPCBlockInfoID = 0;
                    string SPIData = "";
                    string ItemName = "";

                    string dateFromName = Path.GetFileNameWithoutExtension(fileName);
                    string[] str = dateFromName.Split('_');
                    string inspectionDate = $"{str[1].Substring(0, 4)}-{str[1].Substring(4, 2)}-{str[1].Substring(6, 2)}";
                    dateFromName = $"{str[1].Substring(0, 4)}-{str[1].Substring(4, 2)}-{str[1].Substring(6, 2)} {str[1].Substring(8, 2)}:{str[1].Substring(10, 2)}:{str[1].Substring(12, 2)}";
                    string LineID = "";
                    DataTable dtMachine = Singleton_04_Machine.ItMachineMtnService.tMachineList_GetByID(machineID);
                    if (dtMachine?.Rows.Count > 0)
                    {
                        LineID = dtMachine.Rows[0]["LineID"] != null ? dtMachine.Rows[0]["LineID"].ToString() : "";
                    }
                    string GetPadNo = "";
                    DataTable dtConfig; //GetSPC_ConfigValue("GetPadNo", "SPI", "SPC_F4");
                    //if (dtConfig?.Rows.Count > 0)
                    //{
                    //    GetPadNo = dtConfig.Rows[0]["ValueData"] != null ? dtConfig.Rows[0]["ValueData"].ToString() : "";
                    //}
                    //get SPI GetPadNo Config
                    for (int i = 0; i < enumerable.Length; i++)
                    {
                        if (i == 2)
                        {
                            string[] strArr = enumerable[i].Trim().Split(',');
                            if (strArr.Length > 9 || strArr.Length < 8)
                                break;

                            blockID = strArr[6];
                            programName = strArr[1];
                            operatorID = strArr[7];
                            if (strArr.Length == 9)
                            {
                                string[] strArr1 = enumerable[i - 1].Trim().Split(',');
                                if (strArr1[8].ToString().Equals("InspectionDateTime"))
                                {
                                    dateTime = strArr[8];
                                }
                                else
                                {
                                    Lane = strArr[8];
                                }
                            }
                            else
                            {
                                dateTime = $"{dateFromName}";
                            }
                            result = strArr[0];
                            inspectionDate += " " + strArr[5];
                            SPCblock_ID = SPC_BlockID_Insert(blockID, dateTime, inspectionDate, "SPC_F4");
                            string indiNumber = Singleton_02_FlexAssy.IFlexAssy_BlockIDService.FlexAssy_BlockID_GetByIndicationNumberByBlockID("F4", blockID);
                            DataTable dtWorkOrder = Singleton_02_FlexAssy.IFlexAssy_WorkOrderService.FlexAssy_WorkOrder_GetByWorkOrder(indiNumber, factory);
                            if (dtWorkOrder?.Rows.Count > 0)
                            {
                                ItemName = dtWorkOrder.Rows[0]["ItemName"] != null ? dtWorkOrder.Rows[0]["ItemName"].ToString() : "";
                            }
                            SPCBlockInfoID = SPC_BlockIDInfo_Insert(machineID, LineID, ItemName, operatorID, indiNumber, "SPC_F4");
                            dtConfig = GetSPC_SystemConfigValue_GetByConfigName("GetPadNo", "SPI", ItemName, "SPC_F4");
                            if (dtConfig?.Rows.Count > 0)
                            {
                                GetPadNo = "," + (dtConfig.Rows[0]["ValueData"] != null ? dtConfig.Rows[0]["ValueData"].ToString() : "") + ",";
                            }
                            // xóa image cũ
                            FlexAssy_04_SolderPasteInspection_Image2D_Delete(blockID, machineID, factory);
                            continue;
                        }
                        if (isPad)
                        {
                            string[] strArr = enumerable[i].Trim().Split(',');
                            if (strArr.Length >= 11)
                            {
                                if (!DataConvert.IsNumber(strArr[0]))
                                {
                                    continue;
                                }
                                int numberNGImageOfPcs = 0;
                                int padDefectType = DataConvert.ConvertToInt(strArr[4].Trim());
                                if (padDefectType < 0 && images.Count > 0)
                                {
                                    // Copy ảnh lỗi về Server
                                    FileInfo item = images.FirstOrDefault(x => x.Name.Contains($"{strArr[0]}_{strArr[2]}"));
                                    if (item != null)
                                    {
                                        numberNGImageOfPcs = 1;
                                    }
                                }
                                int PadNo = DataConvert.ConvertToInt(strArr[0]);
                                int CavityNumber = DataConvert.ConvertToInt(strArr[3]);
                                string PadResult = strArr[5];
                                decimal Volume = DataConvert.ConvertToDecimal(strArr[6]);
                                decimal Height = DataConvert.ConvertToDecimal(strArr[7]);
                                decimal Area = DataConvert.ConvertToDecimal(strArr[8]);
                                decimal OffsetX = DataConvert.ConvertToDecimal(strArr[9]);
                                decimal OffsetY = DataConvert.ConvertToDecimal(strArr[10]);
                                decimal OffsetX2 = DataConvert.ConvertToDecimal(strArr[9]) * 1000;
                                decimal OffsetY2 = DataConvert.ConvertToDecimal(strArr[10]) * 1000;
                                FlexAssy_04_SolderPasteInspection_DetailModel.CalcDetailF4(ref lstDetail, CavityNumber, Volume, Height, Area, OffsetX2, OffsetY2, blockID, machineID
                                    , Convert.ToDateTime(dateTime), Lane, numberNGImageOfPcs, PadResult);
                                if (GetPadNo.Contains("," + PadNo + ","))
                                {
                                    SPIData += "EXEC [dbo].[Prc_SPC_01_SPI_DATA_Insert]" + Environment.NewLine;
                                    SPIData += " @value = N'(" + SPCblock_ID + ", " + SPCBlockInfoID + ", " + PadNo + ", " + Volume + ", " + Height + ", " + Area + ", " + OffsetX + ", " + OffsetY + ", GETDATE())'" + Environment.NewLine;
                                }
                            }
                        }
                        if (numberNGImage > 0 && numberNGImage != addNGImageOK)
                        {
                            string[] strArr = enumerable[i].Trim().Split(',');
                            try
                            {
                                int padDefectType = int.Parse(strArr[4].Trim());
                                if (padDefectType < 0 && images.Count > 0)
                                {
                                    // Copy ảnh lỗi về Server
                                    FileInfo item = images.FirstOrDefault(x => x.Name.Contains($"{strArr[0]}_{strArr[2]}"));
                                    if (item != null)
                                    {
                                        string pathSaveLogNewImage = numberFolderImage == 1 ? $"{pathSaveLogNew.Replace("AutoExport", "Image2D")}\\{blockID}" : $"{pathSaveLogNew.Replace("AutoExport", "Image2D")}\\{blockID}_0{numberFolderImage}";

                                        string fileDesName = Common.CopyFileToPcFromServer(item, pathSaveLogNewImage); // Copy file to Server
                                        if (fileDesName == null)
                                        {
                                            ManageLog.WriteErrorApp("FlexAssy_04_SolderPasteInspection_ProcessReadLog: fileDesName is null");
                                            return -9;
                                        }

                                        int padNo = int.Parse(strArr[0].Trim());
                                        int panelNo = int.Parse(strArr[3].Trim());
                                        int rsNG = Singleton_02_FlexAssy.IFlexAssy_04_SolderPasteInspectionService.FlexAssy_04_SolderPasteInspection_Image2D_Insert(blockID, machineID, panelNo, padNo, padDefectType, Common.Replace_PathSaveLog(pathSaveLog, fileDesName), factory);
                                        if (rsNG == -9)
                                            return -9;
                                        addNGImageOK++;

                                        //if (numberNGImage == addNGImageOK)
                                        //    break;
                                    }
                                    else
                                    {
                                        numberNGImage--;
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                ManageLog.WriteErrorApp("FlexAssy_04_SolderPasteInspection_ProcessReadLog: " + iD_LogFile + "\n" + "Line:" + i + ex.Message);
                            }

                            continue;
                        }
                        if (enumerable[i].Trim().Equals("Pad"))
                        {
                            isPad = true;
                            if (Common.DirectoryExistsTimeout(logNGImageAddress))
                            {
                                // Xử lý ảnh lỗi
                                string folderNGImage = $"{logNGImageAddress}\\{blockID}";
                                while (true)
                                {
                                    string folderNGImageLast = $"{logNGImageAddress}\\{blockID}_0{numberFolderImage}";
                                    if (Common.DirectoryExistsTimeout(folderNGImageLast))
                                    {
                                        numberFolderImage++;
                                    }
                                    else
                                    {
                                        numberFolderImage--;
                                        break;
                                    }
                                }
                                folderNGImage = numberFolderImage == 1 ? $"{logNGImageAddress}\\{blockID}" : $"{logNGImageAddress}\\{blockID}_0{numberFolderImage}";

                                if (Common.DirectoryExistsTimeout(folderNGImage))
                                {
                                    List<string> extension = new List<string> { ".png", ".jpg", ".bmp", ".jpeg" };
                                    images = new DirectoryInfo(folderNGImage).GetFiles("*").Where(item => extension.Any(x => x == item.Extension.ToLower())).ToList();
                                    numberNGImage = images.Count;
                                    if (numberNGImage != 0)
                                    {
                                        i += 1;// nhay qua dong header
                                    }
                                }
                            }

                            continue;
                        }

                        if (i > 4 && isPad == false)
                        {
                            string[] strArr = enumerable[i].Trim().Split(',');
                            panelQty++;

                            if (strArr[2].Equals("PASS"))
                            {
                                if (strArr[3].Equals("NormalPanel"))
                                    passQty++;
                            }
                            else
                                ngQty++;

                            if (strArr[3].Equals("BadMarkPanel"))
                            {
                                int CavityNumber = DataConvert.ConvertToInt(strArr[0]);
                                badMarkQty++;
                                FlexAssy_04_SolderPasteInspection_DetailModel.CalcDetailF4(ref lstDetail, CavityNumber, 0, 0, 0, 0, 0, blockID, machineID
                                    , Convert.ToDateTime(dateTime), Lane, 0, "BADMARK");
                            }
                        }
                    }
                    if (!string.IsNullOrEmpty(SPIData))
                    {
                        SPC_SPI_Data_Insert(SPIData, "SPC_F4");
                    }
                    int rsSaveDetail = FlexAssy_04_SolderPasteInspection_Detail_Insert(lstDetail, factory);
                    if (rsSaveDetail == -9)
                    {
                        return -9;
                    }
                    if (dateTime == null)
                    {
                        int rsErrorFile = FlexAssy_04_SolderPasteInspection_LogFile_UpdateLineRead(iD_LogFile, -1, false, factory);
                        if (rsErrorFile == -9)
                            return -9;

                        ManageLog.WriteErrorApp("FlexAssy_04_SolderPasteInspection_ProcessReadLog: " + iD_LogFile + "\n" + " File " + fileName + " SPI is Error");
                        return -2;
                    }
                    else
                    {
                        // Find info machine maintenance date
                        List<MachineMaintenance> machineMaintenancesOther = Singleton_03_Common.ICommon_CommonService.GetMachine_Maintenance(null, machineID, Convert.ToDateTime(dateTime), out DateTime? machineMaintenanceDate, out int? machineMaintenanceID, machineMaintenances, connectionStringOptionMachine);
                        if (machineMaintenancesOther != null)
                            machineMaintenances.AddRange(machineMaintenancesOther);

                        // Find info production condition
                        ProductionCondition productionCondition = Singleton_03_Common.ICommon_CommonService.GetMachine_Condition(machineID, Convert.ToDateTime(dateTime), programName, out string productionConditionResult, out int? productionConditionID, productionConditions, connectionStringOptionMachine);
                        if (productionCondition != null)
                            productionConditions.Add(productionCondition);

                        // find infor first piece buy off controll
                        FirstPieceBuyOffControl firstPieceBuyOffControl = Singleton_03_Common.ICommon_CommonService.GetMachine_FirstPieceBuyOff(factory, machineID, blockID, Convert.ToDateTime(dateTime), out DateTime? firstPieceBuyoffControlDate, out int? firstPieceBuyoffControlID, firstPieceBuyOffControls, connectionStringOptionMachine);
                        if (firstPieceBuyOffControl != null)
                            firstPieceBuyOffControls.Add(firstPieceBuyOffControl);

                        int rs = FlexAssy_04_SolderPasteInspection_UpdateReadLogNew(blockID, Convert.ToDateTime(dateTime), programName, operatorID, machineID, result, panelQty, passQty, ngQty, badMarkQty, numberNGImage,
                            firstPieceBuyoffControlDate, firstPieceBuyoffControlID, machineMaintenanceDate, machineMaintenanceID, productionConditionResult, productionConditionID, iD_LogFile, Lane, factory);
                        if (rs == -9)
                            return -9;

                        rs = FlexAssy_04_SolderPasteInspection_LogFile_UpdateLineRead(iD_LogFile, enumerable.Length, true, factory);
                        if (rs == -9)
                            return -9;
                        //string Line = "";
                        //DateTime DateTime = DataConvert.ToDateTime(dateTime);

                        //if (DateTime.Hour < 6)
                        //{
                        //    DateTime = DateTime.AddDays(-1);
                        //    DateTime = DateTime.Date;
                        //}
                        //else
                        //{
                        //    DateTime = DateTime.Date;
                        //}
                        //DataTable dtMachineID = Singleton_04_Machine.ItMachineMtnService.tMachineList_GetByID(machineID);
                        //if (dtMachineID?.Rows.Count > 0)
                        //{
                        //    Line = dtMachineID.Rows[0]["LineID"] + "";
                        //}
                        //// Insert data
                        //ItemName = (ItemName + "").Split('-')[0];
                        //rs = Singleton_06_SPC.iSPC_CommonService.OEE_Item_Product_Save(ItemName, Line, "SPI", machineID, passQty, ngQty, DataConvert.ToDateTime(dateTime), "NewSPC_" + factory);
                    }

                    return 1;
                }
                catch (Exception ex)
                {
                    int rs = FlexAssy_04_SolderPasteInspection_LogFile_UpdateLineRead(iD_LogFile, -1, false, factory);
                    if (rs == -9)
                        return -9;

                    ManageLog.WriteErrorApp("FlexAssy_04_SolderPasteInspection_ProcessReadLog: " + iD_LogFile + "\n" + ex.Message);
                    return -2;
                }
            }
        }
        public int FlexAssy_04_SolderPasteInspection_ReadFileSPI_F5(string factory, int iD_LogFile, string fileName, string machineID, string pathSaveLog, string pathSaveLogNew, string logNGImageAddress,
            List<MachineMaintenance> machineMaintenances, List<ProductionCondition> productionConditions, List<FirstPieceBuyOffControl> firstPieceBuyOffControls, string connectionStringOptionMachine)
        {
            string stage = "FlexAssy_04_SolderPasteInspection";
            string typeBarcode = "BlockID";
            StreamReader file = new StreamReader(fileName);
            using (var lineReader = new ReadFileText(file))
            {
                try
                {
                    IEnumerable<string> lines = lineReader;
                    string[] enumerable = lines as string[] ?? lines.ToArray();

                    string blockID = null;
                    string dateTime = null;
                    string programName = null;
                    string operatorID = null;
                    string result = null;
                    string MachineLane = machineID;
                    int panelQty = 0;
                    int passQty = 0;
                    int ngQty = 0;
                    int badMarkQty = 0;
                    int numberNGImage = 0;
                    int addNGImageOK = 0;
                    string Lane = "";
                    List<FileInfo> images = new List<FileInfo>();
                    List<FlexAssy_04_SolderPasteInspection_DetailModel> ListDetail = new List<FlexAssy_04_SolderPasteInspection_DetailModel>();
                    int numberFolderImage = 2;
                    bool isPad = false;
                    //decimal SPCblock_ID = 0;
                    //decimal SPCBlockInfoID = 0;
                    //string SPIData = "";
                    //string ItemName = "";

                    string dateFromName = Path.GetFileNameWithoutExtension(fileName);
                    string[] str = dateFromName.Split('_');
                    string inspectionDate = $"{str[1].Substring(0, 4)}-{str[1].Substring(4, 2)}-{str[1].Substring(6, 2)}";
                    dateFromName = $"{str[1].Substring(0, 4)}-{str[1].Substring(4, 2)}-{str[1].Substring(6, 2)} {str[1].Substring(8, 2)}:{str[1].Substring(10, 2)}:{str[1].Substring(12, 2)}";
                    for (int i = 0; i < enumerable.Length; i++)
                    {
                        if (i == 2)
                        {
                            string[] strArr = enumerable[i].Trim().Split(',');
                            if (strArr.Length > 9 || strArr.Length < 8)
                                break;

                            blockID = strArr[6];
                            programName = strArr[1];
                            operatorID = strArr[7];
                            if (strArr.Length == 9)
                            {
                                Lane = strArr[8];
                            }
                            if (factory == "F5")
                            {
                                string LandID = programName.Substring(programName.Length - 5, 1);
                                Lane = "Lane " + LandID;
                                MachineLane = machineID + LandID;
                            }
                            result = strArr[0];
                            inspectionDate += " " + strArr[5];
                            dateTime = $"{dateFromName}";
                            continue;
                        }
                        if (numberNGImage > 0 && numberNGImage != addNGImageOK)
                        {
                            string[] strArr = enumerable[i].Trim().Split(',');
                            try
                            {
                                int padDefectType = int.Parse(strArr[4].Trim());
                                if (padDefectType < 0 && images.Count > 0)
                                {
                                    // Copy ảnh lỗi về Server
                                    FileInfo item = images.FirstOrDefault(x => x.Name.Contains($"{strArr[0]}_{strArr[2]}"));
                                    if (item != null)
                                    {
                                        string pathSaveLogNewImage = numberFolderImage == 1 ? $"{pathSaveLogNew.Replace("AutoExport", "Image2D")}\\{blockID}" : $"{pathSaveLogNew.Replace("AutoExport", "Image2D")}\\{blockID}_0{numberFolderImage}";

                                        string fileDesName = Common.CopyFileToPcFromServer(item, pathSaveLogNewImage); // Copy file to Server
                                        if (fileDesName == null)
                                        {
                                            ManageLog.WriteErrorApp("FlexAssy_04_SolderPasteInspection_ProcessReadLog: fileDesName is null");
                                            return -9;
                                        }

                                        int padNo = int.Parse(strArr[0].Trim());
                                        int panelNo = int.Parse(strArr[3].Trim());
                                        int rsNG = Singleton_02_FlexAssy.IFlexAssy_04_SolderPasteInspectionService.FlexAssy_04_SolderPasteInspection_Image2D_Insert(blockID, machineID, panelNo, padNo, padDefectType, Common.Replace_PathSaveLog(pathSaveLog, fileDesName), factory);
                                        if (rsNG == -9)
                                            return -9;
                                        addNGImageOK++;

                                        //if (numberNGImage == addNGImageOK)
                                        //    break;
                                    }
                                    else
                                    {
                                        numberNGImage--;
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                ManageLog.WriteErrorApp("FlexAssy_04_SolderPasteInspection_ProcessReadLog: " + iD_LogFile + "\n" + "Line:" + i + ex.Message);
                            }

                            continue;
                        }
                        if (enumerable[i].Trim().Equals("Pad"))
                        {
                            isPad = true;
                            try
                            {
                                if (Common.DirectoryExistsTimeout(logNGImageAddress))
                                {
                                    // Xử lý ảnh lỗi
                                    string folderNGImage = $"{logNGImageAddress}\\{blockID}";
                                    while (true)
                                    {
                                        string folderNGImageLast = $"{logNGImageAddress}\\{blockID}_0{numberFolderImage}";
                                        if (Common.DirectoryExistsTimeout(folderNGImageLast))
                                        {
                                            numberFolderImage++;
                                        }
                                        else
                                        {
                                            numberFolderImage--;
                                            break;
                                        }
                                    }
                                    folderNGImage = numberFolderImage == 1 ? $"{logNGImageAddress}\\{blockID}" : $"{logNGImageAddress}\\{blockID}_0{numberFolderImage}";

                                    if (Common.DirectoryExistsTimeout(folderNGImage))
                                    {
                                        List<string> extension = new List<string> { ".png", ".jpg", ".bmp", ".jpeg" };
                                        images = new DirectoryInfo(folderNGImage).GetFiles("*").Where(item => extension.Any(x => x == item.Extension.ToLower())).ToList();
                                        numberNGImage = images.Count;
                                        if (numberNGImage != 0)
                                        {
                                            i += 1;// nhay qua dong header
                                        }
                                    }
                                }
                                continue;
                            }
                            catch (Exception ex)
                            {
                                ManageLog.WriteErrorApp(ex.ToString());
                            }
                        }

                        if (i > 4 && isPad == false)
                        {
                            string[] strArr = enumerable[i].Trim().Split(',');
                            panelQty++;

                            if (strArr[2].Equals("PASS"))
                            {
                                if (strArr[3].Equals("NormalPanel"))
                                    passQty++;
                            }
                            else
                                ngQty++;

                            if (strArr[3].Equals("BadMarkPanel"))
                                badMarkQty++;
                        }
                    }
                    //if (!string.IsNullOrEmpty(SPIData))
                    //{
                    //    SPC_SPI_Data_Insert(SPIData, "SPC_F4");
                    //}
                    if (dateTime == null)
                    {
                        int rsErrorFile = FlexAssy_04_SolderPasteInspection_LogFile_UpdateLineRead(iD_LogFile, -1, false, factory);
                        if (rsErrorFile == -9)
                            return -9;

                        ManageLog.WriteErrorApp("FlexAssy_04_SolderPasteInspection_ProcessReadLog: " + iD_LogFile + "\n" + " File " + fileName + " SPI is Error");
                        return -2;
                    }
                    else
                    {
                        // Find info machine maintenance date
                        List<MachineMaintenance> machineMaintenancesOther = Singleton_03_Common.ICommon_CommonService.GetMachine_Maintenance(null, machineID, Convert.ToDateTime(dateTime), out DateTime? machineMaintenanceDate, out int? machineMaintenanceID, machineMaintenances, connectionStringOptionMachine);
                        if (machineMaintenancesOther != null)
                            machineMaintenances.AddRange(machineMaintenancesOther);

                        // Find info production condition
                        ProductionCondition productionCondition = Singleton_03_Common.ICommon_CommonService.GetMachine_Condition(machineID, Convert.ToDateTime(dateTime), programName, out string productionConditionResult, out int? productionConditionID, productionConditions, connectionStringOptionMachine);
                        if (productionCondition != null)
                            productionConditions.Add(productionCondition);

                        // find infor first piece buy off controll
                        FirstPieceBuyOffControl firstPieceBuyOffControl = Singleton_03_Common.ICommon_CommonService.GetMachine_FirstPieceBuyOff(factory, machineID, blockID, Convert.ToDateTime(dateTime), out DateTime? firstPieceBuyoffControlDate, out int? firstPieceBuyoffControlID, firstPieceBuyOffControls, connectionStringOptionMachine);
                        if (firstPieceBuyOffControl != null)
                            firstPieceBuyOffControls.Add(firstPieceBuyOffControl);

                        int rs = FlexAssy_04_SolderPasteInspection_UpdateReadLogNew(blockID, Convert.ToDateTime(dateTime), programName, operatorID, MachineLane, result, panelQty, passQty, ngQty, badMarkQty, numberNGImage,
                            firstPieceBuyoffControlDate, firstPieceBuyoffControlID, machineMaintenanceDate, machineMaintenanceID, productionConditionResult, productionConditionID, iD_LogFile, Lane, factory);
                        if (rs == -9)
                            return -9;

                        rs = FlexAssy_04_SolderPasteInspection_LogFile_UpdateLineRead(iD_LogFile, enumerable.Length, true, factory);
                        if (rs == -9)
                            return -9;
                        List<MachineOfStage> machineOfStages = new List<MachineOfStage>();
                        List<OperatorOfStage> operatorOfStages = new List<OperatorOfStage>();

                        // machineID, OperatorID definition makes at the stage
                        MachineOfStage machineOfStage = Singleton_03_Common.ICommon_CommonService.GetMachineOfStage(MachineLane, null, stage, factory, typeBarcode, machineOfStages);
                        if (machineOfStage != null)
                            machineOfStages.Add(machineOfStage);

                        OperatorOfStage operatorOfStage = Singleton_03_Common.ICommon_CommonService.GetOperatorOfStage(operatorID, null, stage, factory, typeBarcode, operatorOfStages);
                        if (operatorOfStage != null)
                            operatorOfStages.Add(operatorOfStage);
                    }
                    return 1;
                }
                catch (Exception ex)
                {
                    int rs = FlexAssy_04_SolderPasteInspection_LogFile_UpdateLineRead(iD_LogFile, -1, false, factory);
                    if (rs == -9)
                        return -9;

                    ManageLog.WriteErrorApp("FlexAssy_04_SolderPasteInspection_ProcessReadLog: " + iD_LogFile + "\n" + ex.Message);
                    return -2;
                }
            }
        }

        public int FlexAssy_04_SolderPasteInspection_SPC_ReadFileSPI_F3(string fileName, string machineID)
        {
            StreamReader file = new StreamReader(fileName);
            using (var lineReader = new ReadFileText(file))
            {
                try
                {
                    IEnumerable<string> lines = lineReader;
                    string[] enumerable = lines as string[] ?? lines.ToArray();
                    string blockID = null;
                    DateTime? dateTime = null;
                    string programName = null;
                    string operatorID = null;
                    string result = null;

                    decimal SPCblock_ID = 0;
                    decimal SPCBlockInfoID = 0;
                    string SPIData = "";
                    string ItemName = "";
                    List<FlexAssy_04_SolderPasteInspection_DetailModel> lstDetail = new List<FlexAssy_04_SolderPasteInspection_DetailModel>();
                    string dateFromName = Path.GetFileNameWithoutExtension(fileName);
                    string[] str = dateFromName.Split('_');
                    string inspectionDate = $"{str[1].Substring(0, 4)}-{str[1].Substring(4, 2)}-{str[1].Substring(6, 2)}";
                    dateFromName = $"{str[1].Substring(0, 4)}-{str[1].Substring(4, 2)}-{str[1].Substring(6, 2)} {str[1].Substring(8, 2)}:{str[1].Substring(10, 2)}:{str[1].Substring(12, 2)}";
                    string LineID = "";
                    DataTable dtMachine = Singleton_04_Machine.ItMachineMtnService.tMachineList_GetByID(machineID);
                    if (dtMachine?.Rows.Count > 0)
                    {
                        LineID = dtMachine.Rows[0]["LineID"] != null ? dtMachine.Rows[0]["LineID"].ToString() : "";
                    }
                    string GetPadNo = "";
                    DataTable dtConfig;
                    for (int i = 0; i < enumerable.Length; i++)
                    {
                        if (i == 2)
                        {
                            operatorID = enumerable[i].Split(',')[1].Trim();
                            continue;
                        }

                        if (i == 3)
                        {
                            programName = enumerable[i].Split(',')[1].Trim();
                            continue;
                        }

                        if (i == 5)
                        {
                            blockID = enumerable[i].Split(',')[1].Trim();
                            dateTime = Convert.ToDateTime(dateFromName);
                            //spc Get Data
                            SPCblock_ID = SPC_BlockID_Insert(blockID, dateFromName, inspectionDate, "SPC_F3");
                            string indiNumber = Singleton_02_FlexAssy.IFlexAssy_BlockIDService.FlexAssy_BlockID_GetByIndicationNumberByBlockID("F3", blockID);
                            DataTable dtWorkOrder = Singleton_02_FlexAssy.IFlexAssy_WorkOrderService.FlexAssy_WorkOrder_GetByWorkOrder(indiNumber, "F3");
                            if (dtWorkOrder?.Rows.Count > 0)
                            {
                                ItemName = dtWorkOrder.Rows[0]["ItemName"] != null ? dtWorkOrder.Rows[0]["ItemName"].ToString() : "";
                            }
                            SPCBlockInfoID = SPC_BlockIDInfo_Insert(machineID, LineID, ItemName, operatorID, indiNumber, "SPC_F3");
                            dtConfig = GetSPC_SystemConfigValue_GetByConfigName("GetPadNo", "SPI", ItemName, "SPC_F3");
                            if (dtConfig?.Rows.Count > 0)
                            {
                                GetPadNo = "," + (dtConfig.Rows[0]["ValueData"] != null ? dtConfig.Rows[0]["ValueData"].ToString() : "") + ",";
                            }
                            //spc Get Data end
                            continue;
                        }

                        if (i == 6)
                        {
                            // 2021-06-21 Improve SPI by tungud
                            //panelQty = int.Parse(enumerable[i].Split(',')[1].Trim());
                        }

                        if (i == 7)
                        {
                            // 2021-06-21 Improve SPI by tungud
                            //ngQty = int.Parse(enumerable[i].Split(',')[1].Trim());
                            result = enumerable[i].Split(',')[1].Trim() == "0" ? "PASS" : "FAIL";
                            if (result != "PASS")
                            {
                                // Xử lý ảnh NG 
                            }
                            continue;
                        }

                        if (i < 24)
                            continue;
                        //spc Get Data Start
                        string[] strArr = enumerable[i].Trim().Split(',');
                        int PinNo = DataConvert.ConvertToInt(strArr[5]);
                        if (PinNo < 0)
                        {
                            continue;
                        }
                        int PadNo = DataConvert.ConvertToInt(strArr[6]);
                        int CavityNumber = DataConvert.ConvertToInt(strArr[2]);
                        string Volume = strArr[8];
                        string Height = strArr[14];
                        string Area = strArr[28];
                        string OffsetX = strArr[34];
                        string OffsetY = strArr[38];
                        if (GetPadNo.Contains("," + PadNo + ","))
                        {
                            SPIData += "EXEC [dbo].[Prc_SPC_01_SPI_DATA_Insert]" + Environment.NewLine;
                            SPIData += " @value = N'(" + SPCblock_ID + ", " + SPCBlockInfoID + ", " + PadNo + ", " + Volume + ", " + Height + ", " + Area + ", " + OffsetX + ", " + OffsetY + ", GETDATE())'" + Environment.NewLine;
                        }
                    }

                    if (!string.IsNullOrEmpty(SPIData))
                    {
                        SPC_SPI_Data_Insert(SPIData, "SPC_F3");
                    }
                    return 1;
                }
                catch (Exception ex)
                {
                    ManageLog.WriteLogApp("StrongError.Log", ex.ToString());
                    return -9;
                }
            }
        }
        public int FlexAssy_04_SolderPasteInspection_SPC_ReadFileSPI_F4(string fileName, string machineID)
        {
            StreamReader file = new StreamReader(fileName);
            using (var lineReader = new ReadFileText(file))
            {
                try
                {
                    IEnumerable<string> lines = lineReader;
                    string[] enumerable = lines as string[] ?? lines.ToArray();

                    string blockID = null;
                    string dateTime = null;
                    string programName = null;
                    string operatorID = null;
                    string result = null;
                    string Lane = "";
                    List<FileInfo> images = new List<FileInfo>();
                    List<FlexAssy_04_SolderPasteInspection_DetailModel> lstDetail = new List<FlexAssy_04_SolderPasteInspection_DetailModel>();
                    bool isPad = false;
                    decimal SPCblock_ID = 0;
                    decimal SPCBlockInfoID = 0;
                    string SPIData = "";
                    string ItemName = "";

                    string dateFromName = Path.GetFileNameWithoutExtension(fileName);
                    string[] str = dateFromName.Split('_');
                    string inspectionDate = $"{str[1].Substring(0, 4)}-{str[1].Substring(4, 2)}-{str[1].Substring(6, 2)}";
                    dateFromName = $"{str[1].Substring(0, 4)}-{str[1].Substring(4, 2)}-{str[1].Substring(6, 2)} {str[1].Substring(8, 2)}:{str[1].Substring(10, 2)}:{str[1].Substring(12, 2)}";
                    string LineID = "";
                    DataTable dtMachine = Singleton_04_Machine.ItMachineMtnService.tMachineList_GetByID(machineID);
                    if (dtMachine?.Rows.Count > 0)
                    {
                        LineID = dtMachine.Rows[0]["LineID"] != null ? dtMachine.Rows[0]["LineID"].ToString() : "";
                    }
                    string GetPadNo = "";
                    DataTable dtConfig;
                    for (int i = 0; i < enumerable.Length; i++)
                    {
                        if (i == 2)
                        {
                            string[] strArr = enumerable[i].Trim().Split(',');
                            if (strArr.Length > 9 || strArr.Length < 8)
                                break;

                            blockID = strArr[6];
                            programName = strArr[1];
                            operatorID = strArr[7];
                            if (strArr.Length == 9)
                            {
                                Lane = strArr[8];
                            }
                            result = strArr[0];
                            inspectionDate += " " + strArr[5];
                            dateTime = $"{dateFromName}";
                            SPCblock_ID = SPC_BlockID_Insert(blockID, dateTime, inspectionDate, "SPC_F4");
                            string indiNumber = Singleton_02_FlexAssy.IFlexAssy_BlockIDService.FlexAssy_BlockID_GetByIndicationNumberByBlockID("F4", blockID);
                            DataTable dtWorkOrder = Singleton_02_FlexAssy.IFlexAssy_WorkOrderService.FlexAssy_WorkOrder_GetByWorkOrder(indiNumber, "F4");
                            if (dtWorkOrder?.Rows.Count > 0)
                            {
                                ItemName = dtWorkOrder.Rows[0]["ItemName"] != null ? dtWorkOrder.Rows[0]["ItemName"].ToString() : "";
                            }
                            SPCBlockInfoID = SPC_BlockIDInfo_Insert(machineID, LineID, ItemName, operatorID, indiNumber, "SPC_F4");
                            dtConfig = GetSPC_SystemConfigValue_GetByConfigName("GetPadNo", "SPI", ItemName, "SPC_F4");
                            if (dtConfig?.Rows.Count > 0)
                            {
                                GetPadNo = "," + (dtConfig.Rows[0]["ValueData"] != null ? dtConfig.Rows[0]["ValueData"].ToString() : "") + ",";
                            }
                            continue;
                        }
                        if (isPad)
                        {
                            string[] strArr = enumerable[i].Trim().Split(',');
                            if (strArr.Length >= 11)
                            {
                                if (!DataConvert.IsNumber(strArr[0]))
                                {
                                    continue;
                                }
                                int PadNo = DataConvert.ConvertToInt(strArr[0]);
                                int CavityNumber = DataConvert.ConvertToInt(strArr[3]);
                                string PadResult = strArr[5];
                                string Volume = strArr[6];
                                string Height = strArr[7];
                                string Area = strArr[8];
                                string OffsetX = strArr[9];
                                string OffsetY = strArr[10];
                                if (GetPadNo.Contains("," + PadNo + ","))
                                {
                                    SPIData += "EXEC [dbo].[Prc_SPC_01_SPI_DATA_Insert]" + Environment.NewLine;
                                    SPIData += " @value = N'(" + SPCblock_ID + ", " + SPCBlockInfoID + ", " + PadNo + ", " + Volume + ", " + Height + ", " + Area + ", " + OffsetX + ", " + OffsetY + ", GETDATE())'" + Environment.NewLine;
                                }
                            }
                        }
                    }
                    if (!string.IsNullOrEmpty(SPIData))
                    {
                        SPC_SPI_Data_Insert(SPIData, "SPC_F4");
                    }
                    return 1;
                }
                catch (Exception ex)
                {
                    ManageLog.WriteLogApp("StrongError.Log", ex.ToString());
                    return -9;
                }
            }
        }
        public int FlexAssy_04_SolderPasteInspection_UpdateReadLog(string blockID, DateTime? dateTime, string programName, string operatorID, string machineID, string result, int panelQty, int pASSQty, int ngQty, int badMarkQty,
            int numberNgImage, DateTime? firstPieceBuyoffControlDate, int? firstPieceBuyoffControlID, DateTime? machineMaintenanceDate, int? machineMaintenanceID,
            string productionConditionResult, int? productionConditionID, int iD_LogFile, string connectionStringOption)
        {
            var paras = new SqlParameter[19];
            paras[0] = new SqlParameter("@BlockID", blockID);
            paras[1] = new SqlParameter("@DateTime", dateTime ?? (object)DBNull.Value);
            paras[2] = new SqlParameter("@ProgramName", programName ?? (object)DBNull.Value);
            paras[3] = new SqlParameter("@OperatorID", operatorID ?? (object)DBNull.Value);
            paras[4] = new SqlParameter("@MachineID", machineID ?? (object)DBNull.Value);
            paras[5] = new SqlParameter("@Result", result ?? (object)DBNull.Value);
            paras[6] = new SqlParameter("@PanelQty", panelQty);
            paras[7] = new SqlParameter("@PASSQty", pASSQty);
            paras[8] = new SqlParameter("@NGQty", ngQty);
            paras[9] = new SqlParameter("@BadMarkQty", badMarkQty);
            paras[10] = new SqlParameter("@NumberNGImage", numberNgImage);
            paras[11] = new SqlParameter("@FirstPieceBuyoffControlDate", firstPieceBuyoffControlDate ?? (object)DBNull.Value);
            paras[12] = new SqlParameter("@FirstPieceBuyoffControlID", firstPieceBuyoffControlID ?? (object)DBNull.Value);
            paras[13] = new SqlParameter("@MachineMaintenanceDate", machineMaintenanceDate ?? (object)DBNull.Value);
            paras[14] = new SqlParameter("@MachineMaintenanceID", machineMaintenanceID ?? (object)DBNull.Value);
            paras[15] = new SqlParameter("@ProductionConditionResult", productionConditionResult ?? (object)DBNull.Value);
            paras[16] = new SqlParameter("@ProductionConditionID", productionConditionID ?? (object)DBNull.Value);
            paras[17] = new SqlParameter("@ID_LogFile", iD_LogFile);

            return _db.Execute_Modify("sp_sms_FlexAssy_04_SolderPasteInspection_UpdateReadLog", paras, CommandType.StoredProcedure, connectionStringOption);
        }
        public int FlexAssy_04_SolderPasteInspection_UpdateReadLogNew(string blockID, DateTime? dateTime, string programName, string operatorID, string machineID, string result, int panelQty, int pASSQty, int ngQty, int badMarkQty,
            int numberNgImage, DateTime? firstPieceBuyoffControlDate, int? firstPieceBuyoffControlID, DateTime? machineMaintenanceDate, int? machineMaintenanceID,
            string productionConditionResult, int? productionConditionID, int iD_LogFile, string Lane, string connectionStringOption)
        {
            var paras = new SqlParameter[19];
            paras[0] = new SqlParameter("@BlockID", blockID);
            paras[1] = new SqlParameter("@DateTime", dateTime ?? (object)DBNull.Value);
            paras[2] = new SqlParameter("@ProgramName", programName ?? (object)DBNull.Value);
            paras[3] = new SqlParameter("@OperatorID", operatorID ?? (object)DBNull.Value);
            paras[4] = new SqlParameter("@MachineID", machineID ?? (object)DBNull.Value);
            paras[5] = new SqlParameter("@Result", result ?? (object)DBNull.Value);
            paras[6] = new SqlParameter("@PanelQty", panelQty);
            paras[7] = new SqlParameter("@PASSQty", pASSQty);
            paras[8] = new SqlParameter("@NGQty", ngQty);
            paras[9] = new SqlParameter("@BadMarkQty", badMarkQty);
            paras[10] = new SqlParameter("@NumberNGImage", numberNgImage);
            paras[11] = new SqlParameter("@FirstPieceBuyoffControlDate", firstPieceBuyoffControlDate ?? (object)DBNull.Value);
            paras[12] = new SqlParameter("@FirstPieceBuyoffControlID", firstPieceBuyoffControlID ?? (object)DBNull.Value);
            paras[13] = new SqlParameter("@MachineMaintenanceDate", machineMaintenanceDate ?? (object)DBNull.Value);
            paras[14] = new SqlParameter("@MachineMaintenanceID", machineMaintenanceID ?? (object)DBNull.Value);
            paras[15] = new SqlParameter("@ProductionConditionResult", productionConditionResult ?? (object)DBNull.Value);
            paras[16] = new SqlParameter("@ProductionConditionID", productionConditionID ?? (object)DBNull.Value);
            paras[17] = new SqlParameter("@Lane", Lane);
            paras[18] = new SqlParameter("@ID_LogFile", iD_LogFile);

            return _db.Execute_Modify("sp_sms_FlexAssy_04_SolderPasteInspection_UpdateReadLogNew", paras, CommandType.StoredProcedure, connectionStringOption);
        }
        public int FlexAssy_04_SolderPasteInspection_LogFile_Insert(string machineID, string fileNameRoot, string fileNameMedium, int lineRead, string connectionStringOption)
        {
            var paras = new SqlParameter[5];
            paras[0] = new SqlParameter("@Rs", SqlDbType.Int) { Direction = ParameterDirection.Output };
            paras[1] = new SqlParameter("@MachineID", machineID ?? (object)DBNull.Value);
            paras[2] = new SqlParameter("@FileNameRoot", fileNameRoot ?? (object)DBNull.Value);
            paras[3] = new SqlParameter("@FileNameMedium", fileNameMedium ?? (object)DBNull.Value);
            paras[4] = new SqlParameter("@LineRead", lineRead);

            var rs = _db.Execute_Modify("sp_sms_FlexAssy_04_SolderPasteInspection_LogFile_Insert", paras, CommandType.StoredProcedure, connectionStringOption);
            if (rs != -9) return (int)paras[0].Value;
            return rs;
        }

        public int FlexAssy_04_SolderPasteInspection_LogFile_UpdateLineRead(int iD, int lineRead, bool isReadOK, string connectionStringOption)
        {
            var paras = new SqlParameter[3];
            paras[0] = new SqlParameter("@ID", iD);
            paras[1] = new SqlParameter("@LineRead", lineRead);
            paras[2] = new SqlParameter("@IsReadOK", isReadOK);

            return _db.Execute_Modify("sp_sms_FlexAssy_04_SolderPasteInspection_LogFile_UpdateLineRead", paras, CommandType.StoredProcedure, connectionStringOption);
        }
        public int FlexAssy_04_SolderPasteInspection_LogFile_UpdateFileNameRoot(int iD, string fileNameRoot, string connectionStringOption)
        {
            var paras = new SqlParameter[2];
            paras[0] = new SqlParameter("@ID", iD);
            paras[1] = new SqlParameter("@FileNameRoot", fileNameRoot);

            return _db.Execute_Modify("sp_sms_FlexAssy_04_SolderPasteInspection_LogFile_UpdateFileNameRoot", paras, CommandType.StoredProcedure, connectionStringOption);
        }
        public int FlexAssy_04_SolderPasteInspection_LogFile_UpdateFileName(int iD, string fileName, string connectionStringOption)
        {
            var paras = new SqlParameter[2];
            paras[0] = new SqlParameter("@ID", iD);
            paras[1] = new SqlParameter("@FileName", fileName);

            return _db.Execute_Modify("sp_sms_FlexAssy_04_SolderPasteInspection_LogFile_UpdateFileName", paras, CommandType.StoredProcedure, connectionStringOption);
        }
        public DataTable FlexAssy_04_SolderPasteInspection_LogFile_GetDataWithID(int iD, string connectionStringOption)
        {
            var paras = new SqlParameter[1];
            paras[0] = new SqlParameter("@ID", iD);

            return _db.Execute_Table("sp_sms_FlexAssy_04_SolderPasteInspection_LogFile_GetDataWithID", paras, CommandType.StoredProcedure, connectionStringOption);
        }
        public DataTable FlexAssy_04_SolderPasteInspection_GetByBlockID(string blockID, string connectionStringOption, out string connectionStringOk)
        {
            var paras = new SqlParameter[1];
            paras[0] = new SqlParameter("@BlockID", blockID ?? (object)DBNull.Value);

            string connectionStringDefault = null;
            DataTable dt;
            switch (connectionStringOption)
            {
                case "F3":
                    dt = _db.Execute_Table("sp_sms_FlexAssy_04_SolderPasteInspection_GetByBlockID", paras, CommandType.StoredProcedure, connectionStringOption);
                    break;

                case "F4":
                    dt = _db.Execute_Table("sp_sms_FlexAssy_04_SolderPasteInspection_GetByBlockID", paras, CommandType.StoredProcedure, connectionStringOption);
                    break;

                case "F5":
                    dt = _db.Execute_Table("sp_sms_FlexAssy_04_SolderPasteInspection_GetByBlockID", paras, CommandType.StoredProcedure, connectionStringOption);
                    break;
                default:
                    connectionStringDefault = "F3";
                    dt = _db.Execute_Table("sp_sms_FlexAssy_04_SolderPasteInspection_GetByBlockID", paras, CommandType.StoredProcedure, "F3");
                    if (dt == null || dt?.Rows.Count == 0)
                    {
                        connectionStringDefault = "F4";
                        paras = new SqlParameter[1];
                        paras[0] = new SqlParameter("@BlockID", blockID ?? (object)DBNull.Value);

                        dt = _db.Execute_Table("sp_sms_FlexAssy_04_SolderPasteInspection_GetByBlockID", paras, CommandType.StoredProcedure, "F4");
                    }
                    if (dt == null || dt?.Rows.Count == 0)
                    {
                        connectionStringDefault = "F5";
                        paras = new SqlParameter[1];
                        paras[0] = new SqlParameter("@BlockID", blockID ?? (object)DBNull.Value);

                        dt = _db.Execute_Table("sp_sms_FlexAssy_04_SolderPasteInspection_GetByBlockID", paras, CommandType.StoredProcedure, "F5");
                    }
                    break;
            }

            connectionStringOk = connectionStringOption ?? (dt?.Rows.Count > 0 ? connectionStringDefault : null);
            return dt;
        }
        public DataTable FlexAssy_04_SolderPasteInspection_Detail_GetByProductID(string ProductID, string connectionStringOption, out string connectionStringOk)
        {
            var paras = new SqlParameter[1];
            paras[0] = new SqlParameter("@ProductID", ProductID ?? (object)DBNull.Value);

            string connectionStringDefault = null;
            DataTable dt;
            switch (connectionStringOption)
            {
                case "F3":
                    dt = _db.Execute_Table("sp_sms_FlexAssy_04_SolderPasteInspection_Detail_GetByProductID", paras, CommandType.StoredProcedure, connectionStringOption);
                    break;

                case "F4":
                    dt = _db.Execute_Table("sp_sms_FlexAssy_04_SolderPasteInspection_Detail_GetByProductID", paras, CommandType.StoredProcedure, connectionStringOption);
                    break;
                default:
                    connectionStringDefault = "F3";
                    dt = _db.Execute_Table("sp_sms_FlexAssy_04_SolderPasteInspection_Detail_GetByProductID", paras, CommandType.StoredProcedure, "F3");
                    if (dt == null || dt?.Rows.Count == 0)
                    {
                        connectionStringDefault = "F4";
                        paras = new SqlParameter[1];
                        paras[0] = new SqlParameter("@ProductID", ProductID ?? (object)DBNull.Value);

                        dt = _db.Execute_Table("sp_sms_FlexAssy_04_SolderPasteInspection_Detail_GetByProductID", paras, CommandType.StoredProcedure, "F4");
                    }
                    break;
            }

            connectionStringOk = connectionStringOption ?? (dt?.Rows.Count > 0 ? connectionStringDefault : null);
            return dt;
        }
        public DataTable SMES_FlexAssy_04_SolderPasteInspection_GetByListBlockID(DataTable listBlockID, string connectionStringOption)
        {
            var paras = new SqlParameter[1];
            paras[0] = new SqlParameter("@ListBlockID", listBlockID ?? (object)DBNull.Value);
            DataTable dt;
            dt = _db.Execute_Table("sp_sms_FlexAssy_04_SolderPasteInspection_GetByListBlockID", paras, CommandType.StoredProcedure, connectionStringOption);
            return dt;
        }
        public DataTable FlexAssy_04_SolderPasteInspection_GetByListBlockID(DataTable listBlockID, string connectionStringOption, out string connectionStringOk)
        {
            var paras = new SqlParameter[1];
            paras[0] = new SqlParameter("@ListBlockID", listBlockID ?? (object)DBNull.Value);

            string connectionStringDefault = null;
            DataTable dt;
            switch (connectionStringOption)
            {
                case "F3":
                    dt = _db.Execute_Table("sp_sms_FlexAssy_04_SolderPasteInspection_GetByListBlockID", paras, CommandType.StoredProcedure, connectionStringOption);
                    break;

                case "F4":
                    dt = _db.Execute_Table("sp_sms_FlexAssy_04_SolderPasteInspection_GetByListBlockID", paras, CommandType.StoredProcedure, connectionStringOption);
                    break;

                case "F5":
                    dt = _db.Execute_Table("sp_sms_FlexAssy_04_SolderPasteInspection_GetByListBlockID", paras, CommandType.StoredProcedure, connectionStringOption);
                    break;
                default:
                    connectionStringDefault = "F3";
                    dt = _db.Execute_Table("sp_sms_FlexAssy_04_SolderPasteInspection_GetByListBlockID", paras, CommandType.StoredProcedure, "F3");
                    if (dt == null || dt?.Rows.Count == 0)
                    {
                        connectionStringDefault = "F4";
                        paras = new SqlParameter[1];
                        paras[0] = new SqlParameter("@ListBlockID", listBlockID ?? (object)DBNull.Value);

                        dt = _db.Execute_Table("sp_sms_FlexAssy_04_SolderPasteInspection_GetByListBlockID", paras, CommandType.StoredProcedure, "F4");
                    }
                    if (dt == null || dt?.Rows.Count == 0)
                    {
                        connectionStringDefault = "F5";
                        paras = new SqlParameter[1];
                        paras[0] = new SqlParameter("@ListBlockID", listBlockID ?? (object)DBNull.Value);

                        dt = _db.Execute_Table("sp_sms_FlexAssy_04_SolderPasteInspection_GetByListBlockID", paras, CommandType.StoredProcedure, "F5");
                    }
                    break;
            }

            connectionStringOk = connectionStringOption ?? (dt?.Rows.Count > 0 ? connectionStringDefault : null);
            return dt;
        }
        public DataTable FlexAssy_04_SolderPasteInspection_Detail_GetByListProductID(DataTable listProductID, string connectionStringOption, out string connectionStringOk)
        {
            var paras = new SqlParameter[1];
            paras[0] = new SqlParameter("@ListProductID", listProductID ?? (object)DBNull.Value);

            string connectionStringDefault = null;
            DataTable dt;
            switch (connectionStringOption)
            {
                case "F3":
                    dt = _db.Execute_Table("sp_sms_FlexAssy_04_SolderPasteInspection_Detail_GetByListProductID", paras, CommandType.StoredProcedure, connectionStringOption);
                    break;

                case "F4":
                    dt = _db.Execute_Table("sp_sms_FlexAssy_04_SolderPasteInspection_Detail_GetByListProductID", paras, CommandType.StoredProcedure, connectionStringOption);
                    break;
                default:
                    connectionStringDefault = "F3";
                    dt = _db.Execute_Table("sp_sms_FlexAssy_04_SolderPasteInspection_Detail_GetByListProductID", paras, CommandType.StoredProcedure, "F3");
                    if (dt == null || dt?.Rows.Count == 0)
                    {
                        connectionStringDefault = "F4";
                        paras = new SqlParameter[1];
                        paras[0] = new SqlParameter("@ListProductID", listProductID ?? (object)DBNull.Value);

                        dt = _db.Execute_Table("sp_sms_FlexAssy_04_SolderPasteInspection_Detail_GetByListProductID", paras, CommandType.StoredProcedure, "F4");
                    }
                    break;
            }

            connectionStringOk = connectionStringOption ?? (dt?.Rows.Count > 0 ? connectionStringDefault : null);
            return dt;
        }
        // Image 2D
        public int FlexAssy_04_SolderPasteInspection_Image2D_Insert(string blockID, string machineID, int? panelNo, int? padNo, int? padDefectType, string image2D, string connectionStringOption)
        {
            var paras = new SqlParameter[6];
            paras[0] = new SqlParameter("@BlockID", blockID ?? (object)DBNull.Value);
            paras[1] = new SqlParameter("@MachineID", machineID ?? (object)DBNull.Value);
            paras[2] = new SqlParameter("@PanelNo", panelNo ?? (object)DBNull.Value);
            paras[3] = new SqlParameter("@PadNo", padNo ?? (object)DBNull.Value);
            paras[4] = new SqlParameter("@PadDefectType", padDefectType ?? (object)DBNull.Value);
            paras[5] = new SqlParameter("@Image2D", image2D ?? (object)DBNull.Value);

            return _db.Execute_Modify("sp_sms_FlexAssy_04_SolderPasteInspection_Image2D_Insert", paras, CommandType.StoredProcedure, connectionStringOption);
        }
        public DataTable FlexAssy_04_SolderPasteInspection_Image2D_GetByBlockID(string blockID, int limit, string connectionStringOption)
        {
            var paras = new SqlParameter[2];
            paras[0] = new SqlParameter("@BlockID", blockID ?? (object)DBNull.Value);
            paras[1] = new SqlParameter("@Limit", limit);

            return _db.Execute_Table("sp_sms_FlexAssy_04_SolderPasteInspection_Image2D_GetByBlockID", paras, CommandType.StoredProcedure, connectionStringOption);
        }
        public DataTable FlexAssy_04_SolderPasteInspection_Image2D_GetByListBlockID(DataTable listBlockID, string connectionStringOption)
        {
            if (listBlockID == null || listBlockID.Rows.Count == 0)
                return null;

            var paras = new SqlParameter[1];
            paras[0] = new SqlParameter("@ListBlockID", listBlockID ?? (object)DBNull.Value);

            return _db.Execute_Table("sp_sms_FlexAssy_04_SolderPasteInspection_Image2D_GetByListBlockID", paras, CommandType.StoredProcedure, connectionStringOption);
        }
        public int SPC_SPI_Data_Insert(string command, string connectionStringOption)
        {
            return _db.Execute_Modify(command, null, CommandType.Text, connectionStringOption);
        }
        public decimal SPC_BlockID_Insert(string BlockID, string DateBlock, string InspectionDate, string connectionStringOption)
        {
            var paras = new SqlParameter[3];
            paras[0] = new SqlParameter("@BlockID", BlockID ?? (object)DBNull.Value);
            paras[1] = new SqlParameter("@DateBlock", DataConvert.ObjectToDateTime(DateBlock));
            paras[2] = new SqlParameter("@InspectionDate", DataConvert.ObjectToDateTime(InspectionDate));

            return _db.Execute_DecimalScalar("Prc_BlockID_Insert", paras, CommandType.StoredProcedure, connectionStringOption);
        }
        public decimal SPC_BlockIDInfo_Insert(string McID, string LineID, string ItemName, string Operator, string Indi, string connectionStringOption)
        {
            var paras = new SqlParameter[5];
            paras[0] = new SqlParameter("@McID", McID);
            paras[1] = new SqlParameter("@LineID", LineID);
            paras[2] = new SqlParameter("@ItemName", ItemName);
            paras[3] = new SqlParameter("@Operator", Operator);
            paras[4] = new SqlParameter("@IndiNumBer", Indi);

            return _db.Execute_DecimalScalar("Prc_BlockIDInfo_Insert", paras, CommandType.StoredProcedure, connectionStringOption);
        }
        public DataTable GetSPC_SystemConfigValue_GetByConfigName(string ConfigName, string Stage, string ItemName, string connectionStringOption)
        {
            var paras = new SqlParameter[3];
            paras[0] = new SqlParameter("@NameConfig", ConfigName);
            paras[1] = new SqlParameter("@StageName", Stage);
            paras[2] = new SqlParameter("@ItemName", ItemName);

            return _db.Execute_Table("Prc_SystemConfigValue_GetByConfigName", paras, CommandType.StoredProcedure, connectionStringOption);
        }
        public DataTable GetSPC_ConfigValue(string ConfigName, string Stage, string connectionStringOption)
        {
            var paras = new SqlParameter[2];
            paras[0] = new SqlParameter("@NameConfig", ConfigName);
            paras[1] = new SqlParameter("@StageName", Stage);

            return _db.Execute_Table("Prc_SystemConfigValue_GetByConfigName", paras, CommandType.StoredProcedure, connectionStringOption);
        }
        /// <summary>
        /// FlexAssy_04_SolderPasteInspection_ProcessReadLog
        /// </summary>
        /// <param name="factory"></param>
        /// <param name="iD"></param>
        /// <param name="pathSaveLog"></param>
        /// <param name="pathSynMedium"></param>
        /// <param name="pathLinkTo_Medium"></param>
        /// <returns></returns>
        public async Task FlexAssy_04_SolderPasteInspection_ProcessMediumToF2(string factory, int iD, string pathSaveLog, string pathSynMedium, string pathLinkTo_Medium)
        {
            try
            {
                int lastID = iD;
                int recordNeedSyn = 0;
                int recordSyned = 0;

                DataTable dt = FlexAssy_04_SolderPasteInspection_LogFile_GetDataWithID(iD, factory);
                if (dt == null)
                    return;

                if (dt.Rows.Count > 0)
                {
                    //number of records to sync
                    recordNeedSyn = dt.Rows.Count;
                    for (int i = 0; i < recordNeedSyn; i++)
                    {
                        if (dt.Rows[i]["FileNameMedium"] == DBNull.Value)
                        {
                            // update the ID that the record is synchronized
                            lastID = int.Parse(dt.Rows[i]["ID"].ToString());
                            // count the number of synchronized records
                            recordSyned++;
                            continue;
                        }

                        int iD_LogFile = int.Parse(dt.Rows[i]["ID"].ToString());
                        string machineID = dt.Rows[i]["MachineID"].ToString().Trim();
                        string fileNameRoot = dt.Rows[i]["FileNameRoot"].ToString().Trim();
                        string fileNameMedium = dt.Rows[i]["FileNameMedium"].ToString().Replace(pathSynMedium, pathLinkTo_Medium); //\\10.126.22.249\ContactAngle\LOGS_NO_DELETE => \\10.212.7.120\ContactAngle\LOGS_NO_DELETE

                        if (!Common.DirectoryExistsTimeout(Path.GetDirectoryName(fileNameMedium)))
                        {
                            ManageLog.WriteErrorApp("FlexAssy_04_SolderPasteInspection_ProcessMediumToF2: Directory Not Exists or Timeout:" + fileNameMedium);
                            continue;
                        }

                        if (!File.Exists(fileNameMedium))
                        {
                            // update the ID that the record is synchronized
                            lastID = int.Parse(dt.Rows[i]["ID"].ToString());

                            // count the number of synchronized records
                            recordSyned++;

                            // Update Value Search
                            Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, "FlexAssy_04_SolderPasteInspection_ProcessReadLog", lastID.ToString());
                            continue;
                        }

                        string pathSaveLogNew = $"{pathSaveLog}{Path.GetDirectoryName(fileNameMedium).Replace(pathLinkTo_Medium, "")}";

                        string fileName_F2 = null;
                        try
                        {
                            // Copy file du lieu ve F2
                            fileName_F2 = Common.CopyFileToPcFromServer(new FileInfo(fileNameMedium), pathSaveLogNew);
                            if (fileName_F2 == null)
                            {
                                break;
                            }
                        }
                        catch (Exception)
                        {
                            //ManageLog.WriteErrorApp("continues");
                            continue;
                        }
                        string fileNameSample = Common.Replace_PathSaveLog(pathSaveLog, fileName_F2);
                        //ManageLog.WriteErrorApp(fileNameSample);

                        //ManageLog.WriteErrorApp(dt.Rows[i]["FileNameMedium"].ToString() + "\n" + fileNameMedium + "\n" + pathSaveLogNew + "\n" + pathSynMedium + "\n" + pathLinkTo_Medium + "\n" + fileName_F2 + "\n" + fileNameSample);

                        // Update duong dan ICT F2
                        int rs = FlexAssy_04_SolderPasteInspection_LogFile_UpdateFileName(iD_LogFile, fileNameSample, factory);
                        if (rs == -9)
                            break;
                        //ManageLog.WriteErrorApp(iD_LogFile.ToString());

                        // update the ID that the record is synchronized
                        lastID = int.Parse(dt.Rows[i]["ID"].ToString());

                        // Update Value Search
                        Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, "FlexAssy_04_SolderPasteInspection_ProcessReadLog", lastID.ToString());

                        // count the number of synchronized records
                        recordSyned++;

                        File.Delete(fileNameMedium);
                    }

                    // Update Value Search
                    Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, "FlexAssy_04_SolderPasteInspection_ProcessReadLog", lastID.ToString());
                }

                ManageLog.WriteProcess(true, null, recordNeedSyn, recordSyned);
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp("FlexAssy_04_SolderPasteInspection Copy Log To F2: " + iD + "\n" + ex.Message);
            }

            await Task.Delay(500);
        }

        public int FlexAssy_04_SolderPasteInspection_Image2DF3_Insert(string blockID, string machineID, int? cavityNumber, int? padNo, string padDefectType, string image2D, string connectionStringOption)
        {
            var paras = new SqlParameter[6];
            paras[0] = new SqlParameter("@BlockID", blockID ?? (object)DBNull.Value);
            paras[1] = new SqlParameter("@MachineID", machineID ?? (object)DBNull.Value);
            paras[2] = new SqlParameter("@CavityNumber", cavityNumber ?? (object)DBNull.Value);
            paras[3] = new SqlParameter("@PadNo", padNo ?? (object)DBNull.Value);
            paras[4] = new SqlParameter("@PadDefectType", padDefectType ?? (object)DBNull.Value);
            paras[5] = new SqlParameter("@Image2D", image2D ?? (object)DBNull.Value);

            return _db.Execute_Modify("sp_sms_FlexAssy_04_SolderPasteInspection_Image2D_Insert", paras, CommandType.StoredProcedure, connectionStringOption);
        }

        //SEI_MES
        public int SEI_MES_FlexAssy_04_SolderPasteInspection_Insert(string blockID, DateTime dateTime, string machineID, string operatorID, string programName, string result, int? panelQty, int? passQty, int? ngQty,
            int? badMarkQty, int? numberNGImage, DateTime? firstPieceBuyoffControlDate, int? firstPieceBuyoffControlID, DateTime? machineMaintenanceDate, int? machineMaintenanceID,
            string productionConditionResult, int? productionConditionID, int? ID_LogFile, string Lane, string connectionStringOption)
        {
            var paras = new SqlParameter[19];
            paras[0] = new SqlParameter("@BlockID", blockID);
            paras[1] = new SqlParameter("@DateTime", dateTime);
            paras[2] = new SqlParameter("@MachineID", machineID ?? (object)DBNull.Value);
            paras[3] = new SqlParameter("@OperatorID", operatorID ?? (object)DBNull.Value);
            paras[4] = new SqlParameter("@ProgramName", programName ?? (object)DBNull.Value);
            paras[5] = new SqlParameter("@Result", result ?? (object)DBNull.Value);
            paras[6] = new SqlParameter("@PanelQty", panelQty ?? (object)DBNull.Value);
            paras[7] = new SqlParameter("@PASSQty", passQty ?? (object)DBNull.Value);
            paras[8] = new SqlParameter("@NGQty", ngQty ?? (object)DBNull.Value);
            paras[9] = new SqlParameter("@BadMarkQty", badMarkQty ?? (object)DBNull.Value);
            paras[10] = new SqlParameter("@NumberNGImage", numberNGImage ?? (object)DBNull.Value);
            paras[11] = new SqlParameter("@FirstPieceBuyoffControlDate", firstPieceBuyoffControlDate ?? (object)DBNull.Value);
            paras[12] = new SqlParameter("@FirstPieceBuyoffControlID", firstPieceBuyoffControlID ?? (object)DBNull.Value);
            paras[13] = new SqlParameter("@MachineMaintenanceDate", machineMaintenanceDate ?? (object)DBNull.Value);
            paras[14] = new SqlParameter("@MachineMaintenanceID", machineMaintenanceID ?? (object)DBNull.Value);
            paras[15] = new SqlParameter("@ProductionConditionResult", productionConditionResult ?? (object)DBNull.Value);
            paras[16] = new SqlParameter("@ProductionConditionID", productionConditionID ?? (object)DBNull.Value);
            paras[17] = new SqlParameter("@ID_LogFile", ID_LogFile ?? (object)DBNull.Value);
            paras[18] = new SqlParameter("@Lane", Lane ?? (object)DBNull.Value);
            return _db.Execute_Modify("sp_sms_SEI_MES_FlexAssy_04_SolderPasteInspection_Insert", paras, CommandType.StoredProcedure, connectionStringOption);
        }

        public int SEI_MES_FlexAssy_04_SolderPasteInspection_Detail_Insert(string blockID, string machineID, DateTime dateTime, string result, int? numberNGImage, string lane, double? cavityNumber, double? volumeMax, double? volumeMin, double? volumeAve,
            double? heightMax, double? heightMin, double? heightAve, double? areaMax, double? areaMin, double? areaAve, double? shiftXMax, double? shiftXMin, double? shiftXAve, double? shiftYMax, double? shiftYMin,
            double? shiftYAve, string connectionStringOption)
        {
            var paras = new SqlParameter[22];
            paras[0] = new SqlParameter("@BlockID", blockID);
            paras[2] = new SqlParameter("@MachineID", machineID ?? (object)DBNull.Value);
            paras[1] = new SqlParameter("@DateTime", dateTime);
            paras[3] = new SqlParameter("@Result", result ?? (object)DBNull.Value);
            paras[4] = new SqlParameter("@NumberNGImage", numberNGImage ?? (object)DBNull.Value);
            paras[5] = new SqlParameter("@Lane", lane ?? (object)DBNull.Value);
            paras[6] = new SqlParameter("@CavityNumber", cavityNumber);
            paras[7] = new SqlParameter("@VolumeMax", volumeMax ?? (object)DBNull.Value);
            paras[8] = new SqlParameter("@VolumeMin", volumeMin ?? (object)DBNull.Value);
            paras[9] = new SqlParameter("@VolumeAve", volumeAve ?? (object)DBNull.Value);
            paras[10] = new SqlParameter("@HeightMax", heightMax ?? (object)DBNull.Value);
            paras[11] = new SqlParameter("@HeightMin", heightMin ?? (object)DBNull.Value);
            paras[12] = new SqlParameter("@HeightAve", heightAve ?? (object)DBNull.Value);
            paras[13] = new SqlParameter("@AreaMax", areaMax ?? (object)DBNull.Value);
            paras[14] = new SqlParameter("@AreaMin", areaMin ?? (object)DBNull.Value);
            paras[15] = new SqlParameter("@AreaAve", areaAve ?? (object)DBNull.Value);
            paras[16] = new SqlParameter("@ShiftXMax", shiftXMax ?? (object)DBNull.Value);
            paras[17] = new SqlParameter("@ShiftXMin", shiftXMin ?? (object)DBNull.Value);
            paras[18] = new SqlParameter("@ShiftXAve", shiftXAve ?? (object)DBNull.Value);
            paras[19] = new SqlParameter("@ShiftYMax", shiftYMax ?? (object)DBNull.Value);
            paras[20] = new SqlParameter("@ShiftYMin", shiftYMin ?? (object)DBNull.Value);
            paras[21] = new SqlParameter("@ShiftYAve", shiftYAve ?? (object)DBNull.Value);

            return _db.Execute_Modify("sp_sms_SEI_MES_FlexAssy_04_SolderPasteInspection_Detail_Insert", paras, CommandType.StoredProcedure, connectionStringOption);
        }
        public int FlexAssy_04_SolderPasteInspection_Image2D_Delete(string BlockID, string MachineID, string connectionStringOption)
        {
            var paras = new SqlParameter[2];
            paras[0] = new SqlParameter("@BlockID", BlockID);
            paras[1] = new SqlParameter("@MachineID", MachineID);
            return _db.Execute_Modify("sp_sms_FlexAssy_04_SolderPasteInspection_Image2D_Delete", paras, CommandType.StoredProcedure, connectionStringOption);
        }
    }
}