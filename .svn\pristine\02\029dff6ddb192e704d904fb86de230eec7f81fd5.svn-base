﻿using System;
using System.Configuration;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Timers;
using System.Windows.Forms;
using Trace_AbilitySystem.Libs;
using Timer = System.Timers.Timer;

namespace Trace_AbilitySystem_FlexAssy_DataSyn
{
    public partial class FrmProcess : Form
    {
        private string _serviceName = "Trace_AbilitySystem_FlexAssy_DataSyn";
        private Timer _timer;
        private int _interval = 30;
        private int _startService = 0;
        private string _factory = "F3";
        private string _process = "2";
        string StrStage = "";

        public FrmProcess()
        {
            if (Common.CheckProcessOpenByPath(_serviceName))
            {
                DialogHelper.Info("Process " + _serviceName + " is running");
                Environment.Exit(0);
            }
            else
                InitializeComponent();
        }
        public void ExportToCsv()
        {
            var sb = new StringBuilder();
            // Ghi dữ liệu từng dòng
            for (int i = 1; i <= 500000; i++)
            {
                string Data = "";
                for (int j = 0; j < 300; j++)
                {
                    Data += "abcd1234dskljfklasdjfklsdalkasdjfklasd,";
                }
                sb.AppendLine(Data);
                if (i % 1000 == 0)
                {
                    File.AppendAllText(@"C:\Users\<USER>\Desktop\Trace\New folder\Test.txt", sb.ToString(), Encoding.UTF8);
                    sb.Clear();
                }
            }

            // Ghi vào file
        }
        private void FrmProcess_Load(object sender, EventArgs e)
        {
            _process = ConfigurationSettings.AppSettings["Process"].ToString();
            StrStage = ConfigurationSettings.AppSettings["Stage"].ToString();
            _factory = ConfigurationSettings.AppSettings["Factory"].ToString();
            _serviceName = _serviceName + "_" + _factory;
            notifyIcon.BalloonTipIcon = ToolTipIcon.Info;
            notifyIcon.BalloonTipText = $"{_factory}: FlexAssy DataSyn To F2 Process: " + _process;
            notifyIcon.BalloonTipTitle = $"{_factory}: FlexAssy DataSyn To F2: " + _process;
            notifyIcon.Text = $"{_factory}: FlexAssy DataSyn To F2: " + _process;
            notifyIcon.ShowBalloonTip(1000);

            ManageLog.WriteProcess(false, "Start Service FlexAssy " + _factory, 0, 0);

            DataTable dt = Singleton_03_Common.IServiceSettingService.ServiceSetting_GetByServiceName(_serviceName);
            if (dt?.Rows.Count > 0)
            {
                _interval = int.Parse(dt.Rows[0]["IntervalSecond"].ToString());
            }
            this.Text = _factory + ": Data Syn" + StrStage;
            label1.Text = _factory + ": Data Syn" + StrStage;
            richTextBox.Invoke(new Action(() => richTextBox.SelectionColor = Color.Black));
            _startService = _interval;
            AppendRichTextBox(richTextBox, "Processing timeout loop: " + _startService);

            _timer = new Timer { Interval = 1000 };
            _timer.Elapsed += Timer_Tick;
            _timer.Enabled = true;
            _timer.Start();
        }

        /// <summary>
        ///     Process Data From Database
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private async void Timer_Tick(object sender, ElapsedEventArgs e)
        {
            _timer.Enabled = false;
            _timer.Stop();

            try
            {
                _startService -= 1;

                if (_startService <= 0)
                {
                    string Stage = ConfigurationManager.AppSettings["Stage"];

                    Singleton_03_Common.IServiceInfo_Interface.UpdateServiceRunning(_serviceName + $"_{_process}");

                    int length = 0;
                    richTextBox.Invoke(new Action(() => length = richTextBox.Lines.Length));
                    richTextBox.Invoke(new Action(() => ChangeLineRichTextBox(richTextBox, richTextBox.Lines.Length - 1, "....................Processing DATA......................." + Environment.NewLine)));
                    richTextBox.Invoke(new Action(() => richTextBox.SelectionColor = Color.Green));

                    await Singleton_02_FlexAssy.IFlexAssy_CommonService.ProcessService(richTextBox, _factory, StrStage);
                    // Process Log
                    //label1.Invoke(new Action(() => label1.Text = _factory + " - Sync Data only for process number: " + _process.ToString()));
                    //if (_process == "3")
                    //{
                    //    await Singleton_02_FlexAssy.IFlexAssy_CommonService.ProcessService4(richTextBox, _factory);
                    //    await Singleton_02_FlexAssy.IFlexAssy_CommonService.ProcessService3(richTextBox, _factory);
                    //}
                    //else if (_process == "2")
                    //{
                    //    await Singleton_02_FlexAssy.IFlexAssy_CommonService.ProcessService2(richTextBox, _factory);
                    //}
                    //else if (_process == "97")
                    //{
                    //    ////for only
                    //    label1.Invoke(new Action(() => label1.Text = _factory + " - Sync Data only for: ORT"));
                    //    await Singleton_02_FlexAssy.IFlexAssy_CommonService.ProcessServiceOrt(richTextBox, _factory);
                    //}
                    //else if (_process == "99")
                    //{
                    //    //for iqc
                    //    label1.Invoke(new Action(() => label1.Text = _factory + " - Sync Data only for: IQC"));
                    //    await Singleton_02_FlexAssy.IFlexAssy_CommonService.ProcessServiceIqc(richTextBox, _factory);
                    //}
                    //else
                    //{
                    //    await Singleton_02_FlexAssy.IFlexAssy_CommonService.ProcessService(richTextBox, _factory);
                    //}

                    DataTable dt = Singleton_03_Common.IServiceSettingService.ServiceSetting_GetByServiceName(_serviceName);
                    if (dt?.Rows.Count > 0)
                    {
                        _interval = int.Parse(dt.Rows[0]["IntervalSecond"].ToString());
                    }
                    _startService = _interval;

                    richTextBox.Invoke(new Action(() => richTextBox.SelectionColor = Color.Black));
                    AppendRichTextBox(richTextBox, "Processing timeout loop: " + _startService);
                }
                else
                {
                    int length = 0;
                    richTextBox.Invoke(new Action(() => length = richTextBox.Lines.Length));
                    richTextBox.Invoke(new Action(() => ChangeLineRichTextBox(richTextBox, richTextBox.Lines.Length - 1, "Processing timeout loop: " + _startService)));
                }
            }
            catch (Exception ex)
            {
                richTextBox.Clear();
                ManageLog.WriteErrorApp("Timer_Tick: " + _serviceName + "\n" + ex.Message);
            }

            _timer.Interval = 1000;
            _timer.Enabled = true;
            _timer.Start();
        }

        private void RichTextBox_TextChanged(object sender, System.EventArgs e)
        {
            richTextBox.SelectionStart = richTextBox.Text.Length;
            richTextBox.ScrollToCaret();
        }

        public void InvokeMethod(RichTextBox RTB, string msg)
        {
            RTB.AppendText(msg);
            if (RTB.Lines.Count() >= 100)
            {
                RTB.Clear();
            }
        }

        public void AppendRichTextBox(RichTextBox RTB, string msg)
        {
            if (RTB.InvokeRequired)
            {
                RTB.BeginInvoke(new InvokeDelegate(InvokeMethod), RTB, msg);
            }
            else
            {
                InvokeMethod(RTB, msg);
            }
        }

        private delegate void InvokeDelegate(RichTextBox RTB, string msg);

        private void ChangeLineRichTextBox(RichTextBox RTB, int line, string text)
        {
            if (line < 0)
            {
                RTB.Clear();
                return;
            }

            int s1 = RTB.GetFirstCharIndexFromLine(line);
            int s2 = line < RTB.Lines.Count() - 1 ? RTB.GetFirstCharIndexFromLine(line + 1) - 1 : RTB.Text.Length;
            RTB.Select(s1, s2 - s1);
            RTB.SelectedText = text;
        }

        //Disable close button
        private const int CpDisableCloseButton = 0x200;
        protected override CreateParams CreateParams
        {
            get
            {
                CreateParams cp = base.CreateParams;
                cp.ClassStyle |= CpDisableCloseButton;
                return cp;
            }
        }

        private void NotifyIcon1_DoubleClick(object sender, EventArgs e)
        {
            Show();
            WindowState = FormWindowState.Normal;
        }

        private void FrmProcess_Resize(object sender, EventArgs e)
        {
            if (FormWindowState.Minimized == WindowState)
            {
                notifyIcon.Visible = true;
                notifyIcon.ShowBalloonTip(500);
                Hide();
            }
            else if (FormWindowState.Normal == WindowState)
            {
                notifyIcon.Visible = false;
            }
        }

        private void FrmProcess_FormClosing(object sender, FormClosingEventArgs e)
        {
            DialogResult window = MessageBox.Show("Close the program: " + _serviceName + "?", "Are you sure?", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);

            e.Cancel = window == DialogResult.No;
        }
    }
}