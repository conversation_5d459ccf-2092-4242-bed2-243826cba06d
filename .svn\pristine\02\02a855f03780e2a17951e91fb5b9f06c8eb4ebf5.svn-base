﻿
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Collections;
using System.Drawing;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Text.RegularExpressions;
using System.Windows.Forms;
using Trace_AbilitySystem.Libs;
using Trace_AbilitySystem.Libs.Trace_02_FlexAssy_Services;
using Trace_AbilitySystem.Libs.DTOClass.ShopFloor;

namespace Trace_AblilitySystem_Flexssy_Interface_OQC
{

    public partial class FrmOQCDefinePunching : Form
    {
        private string c_sPunch0 = "Chọn Punching";
        private List<TblComponentCheck> _lstCompCheck;
        private List<TblTimeCondition> _lstTimeCondition;
        //private bool _bCheckDuplicate = false;
        // Declare the delegate prototype to send data back to the form
        delegate void ReceivedData(string data);
        private string _connectionStringMain;
        private string _connectionStringIDLink;
        private string _connectionStringShopfloor;
        private string _connectionStringBoard;
        private string _connectionStringTrace;
        private string _connectionStringTrace_Common;
        private string _connectionStringPMTT_Trace;
        private string StorageAddress;
        private string _pathCsvLocal;
        private string _pathCsvServer;
        private bool _isEdit = false;
        private bool autoAddData = false;
        private int _lengthProductID = 17;
        private int _nProcessCode;
        string _sTestID;
        string _sTestType;
        string _PunchingType;
        string _sTestName;
        public string _ItemName;
        //Manager for serial port Handy + Fix
        private string _strHandy;
        private string _sPreProductID = string.Empty;
        private readonly SerialPortManager _spManagerHandyManager;
        private string _IPAddressFix;
        private Socket m_sock;
        private byte[] m_byBuff = new byte[256];    // Recieved data buffer
        private event ReceivedData addData;         // Add Message Event handler for Form
        List<string> _lstProductID;
        string _preBlockID = string.Empty;
        int GradeCheckRate = 5;
        int IsInputTime = 0;
        Boolean inputInforFinish = false;
        Boolean isPaste = false;
        Boolean checkDuplication = false;
        string[] arrGradeCheckRate;
        int DetailID = -1;
        int oldNumber = -1;

        private string _productionLineTest = string.Empty;
        public FrmOQCDefinePunching(string ProcessName, string sTestType, string sTestID)
        {
            InitializeComponent();
            try
            {
                ManageLog.WriteLogApp("Log.txt", "Program Start");
                _sTestID = sTestID;
                _sTestType = sTestType;
                // Com Handy
                _spManagerHandyManager = new SerialPortManager();
                _spManagerHandyManager.NewSerialDataRecieved += SpManager_NewSerialDataRecieved_Handy;

                addData = new ReceivedData(ReceivedDataFix);

                InitGridDataTable(dataGridView);
                InitSearchGridDataTable(dgvSearch);
                ManageLog.WriteLogApp("Log.txt", "Program Start 1");
                lbProcess.Text = ProcessName;
                lbTestType.Text = _sTestType;
                _sTestName = lbTestType.Text;
                ManageLog.WriteLogApp("Log.txt", "Program Start 2");
                _lstProductID = new List<string>();
                lbVersion.Text = OQC_Version.c_sVersion;
                lbVersionDate.Text = OQC_Version.c_sVersionDate;

            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp(ex);
            }
        }
        public FrmOQCDefinePunching(string ProcessName, string sTestType, string sTestID, string PunchingType)
        {
            InitializeComponent();
            try
            {
                ManageLog.WriteLogApp("Log.txt", "Program Start");
                _sTestID = sTestID;
                _sTestType = sTestType;
                _PunchingType = PunchingType;
                // Com Handy
                _spManagerHandyManager = new SerialPortManager();
                _spManagerHandyManager.NewSerialDataRecieved += SpManager_NewSerialDataRecieved_Handy;

                addData = new ReceivedData(ReceivedDataFix);

                InitGridDataTable(dataGridView);
                InitSearchGridDataTable(dgvSearch);
                ManageLog.WriteLogApp("Log.txt", "Program Start 1");
                lbProcess.Text = ProcessName;
                lbTestType.Text = _sTestType;
                _sTestName = lbTestType.Text;
                ManageLog.WriteLogApp("Log.txt", "Program Start 2");
                _lstProductID = new List<string>();
                lbVersion.Text = OQC_Version.c_sVersion;
                lbVersionDate.Text = OQC_Version.c_sVersionDate;

            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp(ex);
            }
        }

        private void FrmOBADefine_Load(object sender, EventArgs e)
        {
            try
            {
                var account = MySharedInfo.CurrentUser;
                if (account != null)
                {
                    txtOperatorID.Text = account.OperatorID;
                    txtOperatorID.Enabled = false;
                    txtNumber.Enabled = true;
                    txtNumber.Focus();
                }
                ManageLog.WriteLogApp("Log.txt", "Program Start 3");
                lbItemName.Text = "---";
                _connectionStringMain = Singleton.IniFile.GetDbConnectionString();
                _connectionStringIDLink = Singleton.IniFile.GetDbConnectionStringIDLink();
                _connectionStringShopfloor = Singleton.IniFile.GetDbConnectionStringShopfloor();
                _connectionStringBoard = Singleton.IniFile.GetDbConnectionString_Board();
                _connectionStringTrace = Singleton.IniFile.GetDbConnectionString_Trace();
                _connectionStringPMTT_Trace = Singleton.IniFile.GetDbConnectionString_PMTT_Trace();
                _connectionStringTrace_Common = Singleton.IniFile.GetDbConnectionStringTrace_Common();
                string sLineSetting = Singleton.IniFile.GetSetting("Line", "Punch");
                cbxLineID.Items.Clear();
                cbxLineID.Items.Add(c_sPunch0);
                if (!String.IsNullOrEmpty(sLineSetting))
                {
                    string[] sLine = sLineSetting.Split(',');
                    foreach (string item in sLine)
                        cbxLineID.Items.Add(item);
                }
                if (!int.TryParse(Singleton.IniFile.GetSetting("FormSetting", "LengthProductID"), out _lengthProductID))
                {
                    _lengthProductID = 17;
                }
                txtProductId.MaxLength = _lengthProductID;
                if (!int.TryParse(Singleton.IniFile.GetSetting("FormSetting", "TimerConnect"), out int TimerConnectTime))
                {
                    timerConnect.Interval = TimerConnectTime;
                }

                _pathCsvLocal = Singleton.IniFile.GetSetting("FormSetting", "PathCsvLocal");
                _pathCsvServer = Singleton.IniFile.GetSetting("FormSetting", "PathCsvServer");
                StorageAddress = Singleton.IniFile.GetSetting("FormSetting", "StorageAddress");
                lbPathCsvLocal.Text = "(Local) Path Export CSV: " + _pathCsvLocal;
                lbPathCsvServer.Text = "(Server) Path Export CSV: " + _pathCsvServer;
                if (_spManagerHandyManager != null) _spManagerHandyManager.StartListening("Handy");
                else MessageBox.Show("Bạn chưa khởi tạo Handy");
                _IPAddressFix = Singleton.IniFile.GetSetting("FormSetting", "IPAddressFix");
                timerConnect.Enabled = true;
                timerConnect.Start();
                btConnectBarcode.Text = " Connect Barcode";
                btConnectBarcode.BackColor = Color.Red;
                btConnectBarcode.Enabled = false;
                btnAutoAddData.Visible = false;
                btRegist.BackColor = Color.Red;
                CheckConnect();
                GetComponentCheck();
                getTestInfor();
            }
            catch (Exception ex)
            {
                DialogHelper.Error(ex.Message);
            }
        }
        private void ConnectBarcode(string IpAddress)
        {
            ClearGrid();
            Cursor cursor = Cursor.Current;
            Cursor.Current = Cursors.WaitCursor;
            try
            {
                // Close the socket if it is still open
                if (m_sock != null && m_sock.Connected)
                {
                    m_sock.Shutdown(SocketShutdown.Both);
                    System.Threading.Thread.Sleep(10);
                    m_sock.Close();
                }

                // Create the socket object
                m_sock = new Socket(AddressFamily.InterNetwork, SocketType.Stream, ProtocolType.Tcp);

                // Define the Server address and port
                IPEndPoint epServer = new IPEndPoint(IPAddress.Parse(IpAddress), 9004);

                // Connect to the server blocking method and setup callback for recieved data

                //m_sock.Connect(epServer);
                //SetupRecieveCallback(m_sock);

                // Connect to server non-Blocking method
                m_sock.Blocking = false;
                AsyncCallback onconnect = new AsyncCallback(OnConnect);
                m_sock.BeginConnect(epServer, onconnect, m_sock);
                btConnectBarcode.BackColor = Color.Lime;
            }
            catch (Exception ex)
            {
                btConnectBarcode.BackColor = Color.Yellow;
                lblFixBCRStatus.Text = @"NG";
                lblFixBCRStatus.BackColor = Color.Red;
                ManageLog.WriteErrorApp(ex.Message);
                MessageBox.Show("Connect đầu đọc barcode thất bại!");
            }
            Cursor.Current = cursor;
        }
        public void OnConnect(IAsyncResult ar)
        {
            // Socket was the passed in object
            Socket sock = (Socket)ar.AsyncState;

            // Check if we were sucessfull
            try
            {
                //sock.EndConnect( ar );
                if (sock.Connected)
                {
                    SetupRecieveCallback(sock);
                }
                else
                    ManageLog.WriteErrorApp("ERROR");
                //MessageBox.Show(this, "Unable to connect to remote machine", "Connect Failed!");
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp(ex);
                // MessageBox.Show(this, ex.Message, "Unusual error during Connect!");
            }
        }

        /// <summary>
        ///     Check connect
        /// </summary>
        /// <returns></returns>
        private bool CheckConnect()
        {
            var cnnHandy = true;
            var cnnDbSm = true;
            var cnnFix = true;

            cnnHandy = _spManagerHandyManager.StartListening("Handy");
            //cnnDbSm =  Common.CheckConnectServer(_connectionStringMain)
            //    && Common.CheckConnectServer(_connectionStringIDLink)
            //      && Common.CheckConnectServer(_connectionStringShopfloor);
            //cnnFix = true;// QRFix1.IsConnected();

            lblHandyStatus.Text = cnnHandy ? @"OK" : @"NG";
            lblFixBCRStatus.Text = cnnFix ? @"OK" : @"NG";
            lbSaoMai.Text = cnnDbSm ? @"OK" : @"NG";

            lblHandyStatus.BackColor = cnnHandy ? Color.Lime : Color.Red;
            lblFixBCRStatus.BackColor = cnnFix ? Color.Lime : Color.Red;
            lbSaoMai.BackColor = cnnDbSm ? Color.Lime : Color.Red;

            if (cnnFix && cnnDbSm && cnnHandy)
            {
                return true;
            }
            return false;
        }
        /// <summary>
		/// Get the new data and send it out to all other connections. 
		/// Note: If not data was recieved the connection has probably 
		/// died.
		/// </summary>
		/// <param name="ar"></param>
		public void OnRecievedData(IAsyncResult ar)
        {
            // Socket was the passed in object
            Socket sock = (Socket)ar.AsyncState;

            // Check if we got any data
            try
            {
                int nBytesRec = sock.EndReceive(ar);
                if (nBytesRec > 0)
                {
                    // Wrote the data to the List
                    string sRecieved = Encoding.ASCII.GetString(m_byBuff, 0, nBytesRec);
                    // WARNING : The following line is NOT thread safe. Invoke is
                    //m_lbRecievedData.Items.Add( sRecieved );

                    Invoke(addData, new string[] { sRecieved });
                    // If the connection is still usable restablish the callback
                    SetupRecieveCallback(sock);
                }
                else
                {
                    // If no data was recieved then the connection is probably dead
                    Console.WriteLine("Client {0}, disconnected", sock.RemoteEndPoint);
                    sock.Shutdown(SocketShutdown.Both);
                    sock.Close();
                }
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp(ex);
                //MessageBox.Show(this, ex.Message, "Unusual error druing Recieve!");
            }
        }
        /// <summary>
		/// Setup the callback for recieved data and loss of conneciton
		/// </summary>
		public void SetupRecieveCallback(Socket sock)
        {
            try
            {
                AsyncCallback recieveData = new AsyncCallback(OnRecievedData);
                sock.BeginReceive(m_byBuff, 0, m_byBuff.Length, SocketFlags.None, recieveData, sock);
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp(ex);
                //MessageBox.Show(this, ex.Message, "Setup Recieve Callback failed!");
            }
        }

        public void ReceivedDataFix(string data)
        {
            if (String.IsNullOrEmpty(data)) return;
            string[] sRes = data.Split('\r');
            if (sRes.Count() > 0)
            {

                foreach (string s in sRes)
                {
                    if (!string.IsNullOrEmpty(s))
                    {
                        string sProDuctID = s.Replace("\r\n", "").Replace("\r", "");
                        if (!_sPreProductID.Equals(sProDuctID))
                        {
                            _sPreProductID = sProDuctID;
                            txtProductId.Text = sProDuctID;
                            CheckProductID(sProDuctID);
                        }
                    }
                }
            }

        }

        private void Disconnect()
        {
            if (m_sock != null && m_sock.Connected)
            {
                m_sock.Shutdown(SocketShutdown.Both);
                m_sock.Close();
            }
        }
        private DataGridViewCellStyle GetHyperLinkStyleForGridCell()
        {
            // Set the Font and Uderline into the Content of the grid cell .  
            {
                DataGridViewCellStyle l_objDGVCS = new DataGridViewCellStyle();
                System.Drawing.Font l_objFont = new System.Drawing.Font(FontFamily.GenericSansSerif, 8, FontStyle.Underline);
                l_objDGVCS.Font = l_objFont;
                l_objDGVCS.ForeColor = Color.Blue;
                return l_objDGVCS;
            }
        }
        private void dgvSearch_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {
            string factory = Singleton.IniFile.GetSetting("FormSetting", "Factory");
            if (dgvSearch.Columns[dgvSearch.CurrentCell.ColumnIndex].HeaderText.Contains("Action"))
            {
                if (!String.IsNullOrWhiteSpace(dgvSearch.CurrentRow.Cells[10].Value.ToString()))
                {
                    tabPage1.Focus();
                    DataTable dt = Singleton_02_FlexAssy.FlexAssy_33_IPQC_OQC_ORT_OBAService.GetTestDetailByDetailID(DataConvert.ConvertToInt(dgvSearch.CurrentRow.Cells[10].Value.ToString()), _connectionStringMain);
                }
            }
        }
        private void InitSearchGridDataTable(DataGridView dtGridView)
        {
            try
            {
                var columnHeaders = new List<string> { "IndicationNumber", "ItemCode", "ItemName", "ProductionDateTest", "LineId", "InputTest", "InputOperator", "TestName", "Action" };
                var columnHeadersWidth = new List<int> { 200, 200, 100, 100, 100, 100, 100, 100, 150, 300 };
                dtGridView.ColumnCount = columnHeaders.Count;
                dtGridView.ColumnHeadersVisible = true;

                // Thiết lập Style cho Header
                DataGridViewCellStyle columnHeaderStyle = new DataGridViewCellStyle
                {
                    BackColor = Color.Azure,
                    Font = new Font("Tahoma", 11, FontStyle.Bold),
                    Alignment = DataGridViewContentAlignment.MiddleCenter
                };

                dtGridView.ColumnHeadersDefaultCellStyle = columnHeaderStyle;
                dtGridView.ColumnHeadersDefaultCellStyle.BackColor = Color.Azure;
                dtGridView.EnableHeadersVisualStyles = false;
                dtGridView.RowHeadersVisible = false;
                dtGridView.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
                dtGridView.ColumnHeadersHeight = 30;

                for (int i = 0; i < columnHeaders.Count; i++)
                {
                    if (dtGridView.Columns.Count < i)
                    {
                        dtGridView.Columns.Add(columnHeaders[i], columnHeaders[i]);
                    }

                    dtGridView.Columns[i].HeaderText = columnHeaders[i];
                    dtGridView.Columns[i].SortMode = DataGridViewColumnSortMode.NotSortable;
                    if (dtGridView.Columns[i].DefaultCellStyle == null)
                        dtGridView.Columns[i].DefaultCellStyle = new DataGridViewCellStyle();
                    dtGridView.Columns[i].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                    dtGridView.Columns[i].Width = columnHeadersWidth[i];
                }
                dtGridView.Columns[9].DefaultCellStyle = GetHyperLinkStyleForGridCell();
                // Remove row default
                dtGridView.AllowUserToAddRows = false;
                dtGridView.AutoGenerateColumns = false;

                // Auto resize height multi line cell
                dtGridView.DefaultCellStyle.WrapMode = DataGridViewTriState.True;

                dtGridView.RowsDefaultCellStyle.SelectionBackColor = Color.DeepSkyBlue;
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp(ex.Message);
            }
        }
        private void InitGridDataTable(DataGridView dtGridView)
        {
            try
            {
                var columnHeaders = new List<string> { "No", "ProductID", "Result", "BlockID", "ItemCode", "ItemName", "ProductionDateTest", "PunchingMachineID", "MachineID", "FixtureID", "InputTest", "InputOperator", "TestName", "Comment", "ResultLink" };
                var columnHeadersWidth = new List<int> { 80, 150, 150, 100, 100, 200, 100, 150, 150, 150, 250, 350 };
                dtGridView.ColumnCount = columnHeaders.Count;
                dtGridView.ColumnHeadersVisible = true;
                dtGridView.AllowUserToAddRows = false;
                // Thiết lập Style cho Header
                DataGridViewCellStyle columnHeaderStyle = new DataGridViewCellStyle
                {
                    BackColor = Color.Azure,
                    Font = new Font("Tahoma", 11, FontStyle.Bold),
                    Alignment = DataGridViewContentAlignment.MiddleCenter
                };

                dtGridView.ColumnHeadersDefaultCellStyle = columnHeaderStyle;
                dtGridView.ColumnHeadersDefaultCellStyle.BackColor = Color.Azure;
                dtGridView.EnableHeadersVisualStyles = false;
                dtGridView.RowHeadersVisible = false;
                dtGridView.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
                dtGridView.ColumnHeadersHeight = 30;

                for (int i = 0; i < columnHeaders.Count; i++)
                {
                    if (dtGridView.Columns.Count < i)
                    {
                        dtGridView.Columns.Add(columnHeaders[i], columnHeaders[i]);
                    }

                    dtGridView.Columns[i].HeaderText = columnHeaders[i];
                    dtGridView.Columns[i].SortMode = DataGridViewColumnSortMode.NotSortable;
                    if (dtGridView.Columns[i].DefaultCellStyle == null)
                        dtGridView.Columns[i].DefaultCellStyle = new DataGridViewCellStyle();
                    dtGridView.Columns[i].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                    dtGridView.Columns[i].Width = columnHeadersWidth[i];
                }
                dtGridView.Columns[columnHeaders.Count - 1].AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill;
                //dtGridView.Columns[5].Visible = false;

                // Remove row default
                dtGridView.AllowUserToAddRows = false;
                dtGridView.AutoGenerateColumns = false;

                // Auto resize height multi line cell
                dtGridView.DefaultCellStyle.WrapMode = DataGridViewTriState.True;

                dtGridView.RowsDefaultCellStyle.SelectionBackColor = Color.DeepSkyBlue;
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp(ex.Message);
            }
        }
        private void AddRowQC(DataGridView dtGridView, string productID, string BlockID, string itemCode, string itemName, DateTime productiondatetest, string LineID, string MachineID, string operatorID, DateTime dateTime, string comment, string FixtureID, string Result, string ResultLink)
        {
            try
            {

                var productionDate = productiondatetest.ToString();
                // "ProductID","BlockID", "ItemCode", "ItemName", "MachineID", "InputTest", "InputOperator",  "TestName","Comment"
                dtGridView.Rows.Insert(dtGridView.Rows.Count, new string[] {(dtGridView.Rows.Count+1).ToString(),
                productID,
                Result,
                BlockID,
                itemCode,
                itemName,
                productionDate,
                LineID,
                MachineID,
                FixtureID,
                dateTime.ToString("yyyy/MM/dd HH:mm:ss"),
                operatorID,
                _sTestName,
                comment,
                ResultLink
                });
                dtGridView.Rows[0].Height = 30;
                dtGridView.Rows[0].DefaultCellStyle.Font = new Font("Tahoma", 10, FontStyle.Regular);
                dtGridView.Rows[0].DefaultCellStyle.BackColor = Color.White;
                dtGridView.Rows[0].ReadOnly = true;
                dtGridView.Rows[0].DefaultCellStyle.BackColor = !(string.IsNullOrEmpty(comment)) ? Color.Red : Color.White;
                dtGridView.ClearSelection();
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp(ex);
            }
        }

        private void SpManager_NewSerialDataRecieved_Handy(object sender, SerialDataEventArgs e)
        {
            if (InvokeRequired)
            {
                // Using this.Invoke causes deadlock when closing serial port, and BeginInvoke is good practice anyway.
                BeginInvoke(new EventHandler<SerialDataEventArgs>(SpManager_NewSerialDataRecieved_Handy), sender, e);
                return;
            }

            // This application is connected to a GPS sending ASCCI characters, so data is converted to text
            var tbData = Encoding.ASCII.GetString(e.Data);
            _strHandy += tbData;

            // Đọc dữ liệu cho tới khi gặp ký tự '\r'
            if (_strHandy.Contains("\r"))
            {
                if (_strHandy.StartsWith("OP"))
                {
                    if (txtOperatorID.Enabled == true)
                    {
                        _strHandy = _strHandy.Replace("\r", "").Replace("\n", "").Trim().Replace("OP", "");
                        InputOPID(_strHandy);
                        _strHandy = "";
                    }
                }
                else if ((_strHandy.Contains("000B") || _strHandy.Contains("V")) && SingletonLocal.BoardRegisService.CheckExist(_strHandy.Trim(), out string ItemName, _connectionStringBoard))
                {
                    if (txtIndi.Enabled == true)
                    {
                        _strHandy = _strHandy.Replace("\r", "").Replace("\n", "").Trim();
                        SetIndi(_strHandy);
                        _strHandy = "";
                    }
                }
                else if (_strHandy.Contains("MO") || _strHandy.Contains("QC") || _strHandy.Contains("QA") || _strHandy.Contains("PE")
                    || _strHandy.Contains("LE") || _strHandy.Contains("RG") || _strHandy.Contains("FTR") || _strHandy.Contains("AF")
                    || _strHandy.Contains("VT") || _strHandy.Contains("PK"))
                {
                    if (txtMachineID.Enabled == true)
                    {
                        _strHandy = _strHandy.Replace("\r", "").Replace("\n", "").Trim();
                        txtMachineID.Text = _strHandy;
                        _strHandy = "";
                    }
                }
                else if (txtFixtureQty.Enabled == true)
                {
                    _strHandy = _strHandy.Replace("\r", "").Replace("\n", "").Trim();
                    ProcessHandyData(_strHandy);
                    _strHandy = "";
                }
            }
        }
        public void testhandy()
        {
            _strHandy += "";

            // Đọc dữ liệu cho tới khi gặp ký tự '\r'
            if (_strHandy.Contains("\r"))
            {
                if (_strHandy.StartsWith("OP"))
                {
                    _strHandy = _strHandy.Replace("\r", "").Replace("\n", "").Trim().Replace("OP", "");
                    InputOPID(_strHandy);
                    _strHandy = "";
                }
                else if ((_strHandy.Contains("000B") || _strHandy.Contains("V")) && SingletonLocal.BoardRegisService.CheckExist(_strHandy, out string ItemName, _connectionStringBoard))
                {
                    _strHandy = _strHandy.Replace("\r", "").Replace("\n", "").Trim();
                    SetIndi(_strHandy);
                    _strHandy = "";
                }
                else if (_strHandy.Contains("MO") || _strHandy.Contains("QC") || _strHandy.Contains("QA"))
                {
                    _strHandy = _strHandy.Replace("\r", "").Replace("\n", "").Trim();
                    txtMachineID.Text = _strHandy;
                    _strHandy = "";
                }
                else
                {
                    _strHandy = _strHandy.Replace("\r", "").Replace("\n", "").Trim();
                    if (txtfixtureid.Text.Length == 0)
                    {
                        txtfixtureid.Text = _strHandy;
                        _strHandy = "";
                    }
                    else
                    {
                        string fixtures = txtfixtureid.Text.Replace(Environment.NewLine, ";");
                        if (txtfixtureid.Text.ToUpper().Contains(_strHandy.ToUpper()))
                        {
                            MessageBox.Show("Fixture: " + _strHandy + " đã được khai báo!!");
                            return;
                        }
                        else
                        {
                            txtfixtureid.Text += _strHandy + Environment.NewLine;
                            _strHandy = "";
                        }
                    }
                }
            }
        }
        private void InputOPID(string ID)
        {
            txtOperatorID.Text = ID;
            _strHandy = "";

            if (txtOperatorID.Text.Trim().Length != 5)
            {
                DialogHelper.Warning("OperatorID không đúng định dạng/ OperatorID wrong format");
                return;
            }
            else
            {
                txtOperatorID.Enabled = false;
                txtNumber.Enabled = true;
                txtNumber.Focus();
            }
            if (txtNumber.Text.Trim().Length == 0 || txtNumber.Text.Trim() == "0")
            {
                txtNumber.Enabled = true;
                txtNumber.Focus();
            }
        }
        //private void SpManager_NewSerialDataRecieved_Fix(object sender, SerialDataEventArgs e)
        //{
        //    if (InvokeRequired)
        //    {
        //        // Using this.Invoke causes deadlock when closing serial port, and BeginInvoke is good practice anyway.
        //        BeginInvoke(new EventHandler<SerialDataEventArgs>(SpManager_NewSerialDataRecieved_Fix), sender, e);
        //        return;
        //    }

        //    // This application is connected to a GPS sending ASCCI characters, so data is converted to text
        //    var tbData = Encoding.ASCII.GetString(e.Data);
        //    _strFix += tbData;

        //    //Đọc dữ liệu cho tới khi gặp ký tự '\r'
        //    if (_strFix.Contains("\r"))
        //    {
        //        if (txtOperatorID.Text.Trim().Length == 0 || txtNumber.Text.Trim().Length == 0)
        //        {
        //            lbStatusProduct.Text = @"Chưa nhập đủ thông tin đầu vào/ Not enough input information yet";
        //            _strFix = "";
        //            return;
        //        }

        //        _strFix = _strFix.Replace("\r", "").Replace("\n", "").Trim();
        //        txtProductId.Text = _strFix;
        //        _strFix = "";

        //        if (txtProductId.Text.Trim().Length == _lengthProductID)
        //        {
        //            CheckProductID(_strFix.Trim());
        //        }
        //        else
        //        {
        //            lbStatusProduct.Text = @"ProductID có độ dài: " + txtProductId.Text.Trim().Length + " != " + _lengthProductID + "/ ProductID has length: " + txtProductId.Text.Trim().Length + " != " + _lengthProductID;
        //            lbStatusProduct.ForeColor = Color.Red;
        //        }
        //    }
        //}

        private void TxtOperatorID_TextChanged(object sender, EventArgs e)
        {
            var cursorPosition = txtOperatorID.SelectionStart;
            txtOperatorID.Text = Regex.Replace(txtOperatorID.Text, "[^0-9]", "");
            txtOperatorID.SelectionStart = cursorPosition;
        }

        private void TxtOperatorID_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                if (txtOperatorID.Text.Trim().Length != 5 && txtOperatorID.Text.Trim().Length != 6)
                {
                    DialogHelper.Warning("OperatorID không đúng định dạng/ OperatorID wrong format");
                    txtOperatorID.Focus();

                    return;
                }

                txtOperatorID.Enabled = false;
                txtNumber.Enabled = true;
                txtNumber.Focus();
            }
        }

        private void TxtNumber_TextChanged(object sender, EventArgs e)
        {
            var cursorPosition = txtNumber.SelectionStart;
            txtNumber.Text = Regex.Replace(txtNumber.Text, "[^0-9]", "");
            txtNumber.SelectionStart = cursorPosition;
        }

        private void TxtNumber_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                //int nCount = DataConvert.ConvertStringToInt(txtNumber.Text.Trim());
                if (string.IsNullOrEmpty(txtNumber.Text.Trim()))
                {
                    DialogHelper.Warning("Bạn chưa nhập số lượng Quantity. Vui lòng nhập lại.");
                    txtNumber.SelectAll();
                    txtNumber.Focus();
                }
                else if (txtNumber.Text.Trim() == "0")
                {
                    DialogHelper.Warning("Số lượng Quantity phải lớn hơn 0. Vui lòng nhập lại.");
                    txtNumber.SelectAll();
                    txtNumber.Focus();
                }
                else
                {
                    txtNumber.Enabled = false;
                    txtIndi.Enabled = true;
                    txtIndi.Focus();
                }
            }
        }

        private bool Check_TextNumber()
        {
            if (txtOperatorID.Text.Trim().Length != 5 && txtOperatorID.Text.Trim().Length != 6)
            {
                _sPreProductID = string.Empty;
                DialogHelper.Warning("OperatorID không đúng định dạng/ OperatorID wrong format");
                return false;
            }

            if (txtNumber.Text.Trim().Length == 0 || txtNumber.Text.Trim() == "0")
            {
                DialogHelper.Warning("Số lượng ProductID cần định nghĩa QC Test phải > 0/ The number of ProductIDs needed to define QC Test must be > 0");
                txtNumber.SelectAll();
                txtNumber.Focus();

                return false;
            }
            if (txtIndi.Text.Trim().Length == 0)
            {
                _sPreProductID = string.Empty;
                DialogHelper.Warning("Bạn chưa nhập Indi");
                txtIndi.Enabled = true;
                txtIndi.Focus();
                return false;
            }
            if (txtOperatorID.Text.Trim().Length != 5 && txtOperatorID.Text.Trim().Length != 6)
            {
                _sPreProductID = string.Empty;
                DialogHelper.Warning("OperatorID không đúng định dạng/ OperatorID wrong format");
                txtOperatorID.Focus();

                return false;
            }
            if (string.IsNullOrEmpty(txtItemCode.Text.Trim()))
            {
                _sPreProductID = string.Empty;
                DialogHelper.Warning("Chưa nhập ItemCode/ ItemCode must not empty");
                txtItemCode.Enabled = true;
                txtItemCode.Focus();
                return false;
            }
            //if (string.IsNullOrEmpty(cbxLineID.Text.Trim()) || cbxLineID.Text == c_sPunch0)
            //{
            //    _sPreProductID = string.Empty;
            //    DialogHelper.Warning("Chưa nhập Punch MachineID/  Punch MachineID must not empty");
            //    cbxLineID.Enabled = true;
            //    cbxLineID.Focus();
            //    return false ;
            //}
            if (_isEdit)
            {
                if (int.Parse(txtNumber.Text) < int.Parse(lbTotalInput.Text))
                {
                    DialogHelper.Warning("Số lượng ProductID cần sửa không được nhỏ hơn số lượng bạn vừa nhập vào/ The number of ProductID to fix must not be less than the number you just entered");
                    txtNumber.SelectAll();
                    txtNumber.Focus();

                    return false;
                }

                if (int.Parse(txtNumber.Text) == int.Parse(lbTotalInput.Text))
                {
                    DialogHelper.Info("Đã nhập đủ số lượng: " + txtNumber.Text + " ProductID cần định nghĩa / Entered sufficient quantities: " + txtNumber.Text + " ProductID needs QC definition");
                    Reset();

                    return false;
                }

                _isEdit = false;
                txtNumber.Enabled = false;
                //txtProductId.Focus();

                return true;
            }

            _isEdit = false;
            txtNumber.Enabled = false;
            return true;
        }
        private void TxtProductId_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.V && e.Control)
            {
                isPaste = true;
                txtProductId.Text = Clipboard.GetText().Trim().Replace("\r\n", ";").Replace("\n", ";").Replace("\r", ";").Replace("\t", ";").Replace("; ;", ";").Replace(";;", ";").Replace("; ", ";");
            }
            if (e.KeyCode == Keys.Enter)
            {
                //checkDuplication = true;
                if (txtIndi.Text.Trim().Length == 0)
                {
                    DialogHelper.Warning("Indi không được để trống / Indi can't be empty");
                    txtProductId.Text = string.Empty;
                    return;

                }
                if (txtProductId.Text.Trim().Contains(";"))
                {
                    string[] arrProduct = txtProductId.Text.Trim().Split(';');
                    foreach (string productid in arrProduct)
                    {
                        if (inputInforFinish)
                        {
                            CheckProductID(productid);
                        }
                        else
                        {
                            break;
                        }
                    }
                    inputInforFinish = true;
                }
                else
                {
                    CheckProductID(txtProductId.Text.Trim());
                    inputInforFinish = true;
                }
            }
        }
        private void toolStripMenuItemDelete_Click(object sender, EventArgs e)
        {
            if (dataGridView.SelectedRows?.Count > 0)
            {
                if (MessageBox.Show("Bạn có muốn xóa không?", "Đang xóa...", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                {
                    int rowIndex = dataGridView.SelectedRows[0].Index;
                    string productId = dataGridView.Rows[rowIndex].Cells[0].Value.ToString();
                    lbTotal.Text = (int.Parse(lbTotal.Text) - 1).ToString();
                    dataGridView.Rows.RemoveAt(rowIndex);
                    _lstProductID.Remove(productId);
                }
            }
        }

        private DateTime _productionDateTest = DateTime.Now;
        private void CheckProductID(string sProductID, string Result = "", string ResultLink = "")
        {
            try
            {
                if (_lstProductID.Contains(sProductID.Trim()))
                {
                    //checkDuplication = false;
                    //_lstProductID = new List<string>();
                    //DialogHelper.Warning("Có ProductID: " + sProductID + " đã tồn tại, vui lòng kiểm tra lại");

                    tbNG.Text += sProductID.Trim() + " (trùng lặp)" + Environment.NewLine;
                    return;
                }
                if (sProductID.Trim().Length == 0)
                {
                    DialogHelper.Warning("Bạn nhập thiếu số lượng, vui lòng nhập đủ số lượng cần khai báo");
                    txtProductId.Focus();
                    return;
                }
                else
                {
                    //if (sProductID.Trim().Length != _lengthProductID)
                    //{
                    //    ManageLog.WriteLogApp("barcode.txt", sProductID);
                    //    lbStatusProduct.Text = @"ProductID có độ dài: " + sProductID.Trim().Length + " != " + _lengthProductID
                    //        + "/ ProductID has length: " + sProductID.Trim().Length + " != " + _lengthProductID;
                    //    lbStatusProduct.ForeColor = Color.Red;
                    //    return;
                    //}
                    if (SingletonLocal.OQC_IPQC_ORT_OBA_Local_Service.CheckProductExist(_sTestID, sProductID.Trim(), out string Message, _connectionStringMain))
                    {
                        tbNG.Text += sProductID.Trim() + Environment.NewLine;
                        lbStatusProduct.Text = Message;
                        lbStatusProduct.ForeColor = Color.Red;
                        inputInforFinish = false;
                        //txtProductId.Text = "";
                        //txtProductId.Focus();
                    }
                    else
                    {

                        int nNumProduct = DataConvert.ConvertStringToInt(lbTotalInput.Text);
                        if (nNumProduct < int.Parse(txtNumber.Text))
                        {
                            string comment = string.Empty;
                            string BlockID = SingletonLocal.OQC_IPQC_ORT_OBA_Local_Service.CheckBlock(sProductID, _connectionStringIDLink);
                            if (string.IsNullOrEmpty(_preBlockID))
                            {
                                _preBlockID = BlockID;
                            }
                            else
                            {
                                if (_sTestID.Equals(AccountRole.c_ipqc_OutlinePunchingDimension))
                                {
                                    if (!_preBlockID.Equals(BlockID))
                                    {
                                        comment += "ProductID " + sProductID + " không thuộc Block " + _preBlockID + ".";
                                    }
                                }
                            }
                            DataTable dt_getdatetime = null;
                            dt_getdatetime = SingletonLocal.OQC_IPQC_ORT_OBA_Local_Service.GetMachineByBlockID_CompName(BlockID, _PunchingType, _connectionStringShopfloor);
                            string machineID = string.Empty;
                            if (dt_getdatetime == null)
                            {
                                lbStatusProduct.Text = "Không tìm thấy " + _PunchingType + " Machine";
                                lbStatusProduct.ForeColor = Color.Red;
                                inputInforFinish = false;
                                return;
                            }
                            else if (dt_getdatetime.Rows.Count < 1)
                            {
                                lbStatusProduct.Text = "Không tìm thấy " + _PunchingType + " Machine";
                                lbStatusProduct.ForeColor = Color.Red;
                                inputInforFinish = false;
                                return;
                            }
                            if (dt_getdatetime?.Rows[0]["MachineID"] != null)
                            {
                                machineID = dt_getdatetime?.Rows[0]["MachineID"].ToString();
                                cbxLineID.Items.Add(machineID);
                                cbxLineID.SelectedIndex = cbxLineID.FindStringExact(machineID);
                                if(IsInputTime!=1)
                                {
                                    _productionDateTest = (DateTime)dt_getdatetime?.Rows[0]["CreatedDate"];
                                }    
                            }
                            else
                            {
                                lbStatusProduct.Text = "Không Lấy được dữ liệu";
                                lbStatusProduct.ForeColor = Color.Red;
                                inputInforFinish = false;
                                return;
                            }
                            bool bMatchIndi = SingletonLocal.OQC_IPQC_ORT_OBA_Local_Service.CheckProductIndi(sProductID.Trim(), txtIndi.Text.Trim(), _connectionStringIDLink);
                            if (!bMatchIndi)
                            {
                                tbNG.Text += sProductID.Trim() + Environment.NewLine;
                                lbStatusProduct.Text = "ProductID:" + sProductID.Trim() + " không Thuộc Indi:" + txtIndi.Text;
                                lbStatusProduct.ForeColor = Color.Red;
                                inputInforFinish = false;
                                return;
                            }
                            if (!SingletonLocal.OQC_IPQC_ORT_OBA_Local_Service.CheckBlockPunchingData(BlockID, cbxLineID.Text.Trim(), out string PunchID, _connectionStringShopfloor))
                            {
                                tbNG.Text += sProductID.Trim() + Environment.NewLine;
                                lbStatusProduct.Text = "ProductID " + sProductID + " không được làm tại máy " + cbxLineID.Text.Trim() + ".";
                                lbStatusProduct.ForeColor = Color.Red;
                                inputInforFinish = false;
                                return;
                            }
                            if (_sTestID == AccountRole.ID_OBA2Dbarcodegrade || _sTestID == AccountRole.ID_OQC2Dbarcodegrade)
                            {
                                string GradeMachineID = Singleton.IniFile.GetSetting("FormSetting", "GradeMachineID");
                                DataTable cognext = Singleton_02_FlexAssy.IFlexAssy_23_CognexService.Cognex_GetByProductID_MachineID(sProductID, GradeMachineID, _connectionStringPMTT_Trace);
                                if (cognext != null && cognext.Rows.Count > 0)
                                {
                                    Result = DataConvert.ConvertToString(cognext.Rows[0]["GradeLevel"]);
                                }
                                else
                                {
                                    tbNG.Text += sProductID.Trim() + Environment.NewLine;
                                    lbStatusProduct.Text = "ProductID:" + sProductID.Trim() + " không lấy được thông tin Grade Check";
                                    lbStatusProduct.ForeColor = Color.Red;
                                    inputInforFinish = false;
                                    return;
                                }
                            }
                            _lstProductID.Add(sProductID);
                            AddRowQC(dataGridView, sProductID.Trim(), BlockID, txtItemCode.Text.Trim(), txtItemName.Text.Trim(), _productionDateTest,
                                PunchID, txtMachineID.Text.Trim(), txtOperatorID.Text.Trim(), DateTime.Now, comment, txtfixtureid.Text.Trim(), Result, ResultLink);
                            lbTotal.Text = (int.Parse(lbTotal.Text) + 1).ToString();
                            lbTotalInput.Text = (int.Parse(lbTotalInput.Text) + 1).ToString();
                            nNumProduct = DataConvert.ConvertStringToInt(lbTotalInput.Text);
                            lbStatusProduct.Text = "Thêm mới thành công / Add productID Success.";
                            lbStatusProduct.ForeColor = Color.Green;
                            txtProductId.Text = "";
                            txtProductId.Focus();
                            if (nNumProduct >= int.Parse(txtNumber.Text))
                            {
                                bool bError = false;
                                var comTmp = string.Empty;
                                for (int i = 0; i < dataGridView.Rows.Count; i++)
                                {
                                    string s = dataGridView.Rows[i].Cells[dataGridView.Columns.Count - 2].Value.ToString();
                                    if (!string.IsNullOrEmpty(s))
                                    {
                                        bError = true;
                                        comTmp += s + Environment.NewLine;
                                    }
                                }

                                if (!bError)
                                {
                                    DialogHelper.Info("Đã nhập đủ số lượng: " + txtNumber.Text + " ProductID cần định nghĩa Test/ Entered sufficient quantities: "
                                        + txtNumber.Text + " ProductID needs QC Test definition" + Environment.NewLine +
                                        "Hệ thống sẽ tiến hành đăng kí test.");
                                    RegisTest();
                                    return;
                                }
                                else
                                {
                                    DialogHelper.Warning("Đã nhập đủ số lượng: " + txtNumber.Text + " ProductID cần định nghĩa Test/ Entered sufficient quantities: "
                                        + txtNumber.Text + " ProductID needs QC Test definition" + Environment.NewLine +
                                        "Xảy ra lỗi: " + comTmp);
                                    inputInforFinish = false;
                                    return;
                                }
                            }
                        }
                        else
                        {
                            btRegist.BackColor = Color.Lime;
                            lbStatusProduct.Text = "Đã nhập đủ sản phẩm, không thể nhập thêm";
                            lbStatusProduct.ForeColor = Color.Red;
                            if (nNumProduct >= int.Parse(txtNumber.Text))
                            {
                                DialogHelper.Info("Đã nhập đủ số lượng: " + txtNumber.Text + " ProductID cần định nghĩa Test/ Entered sufficient quantities: "
                                    + txtNumber.Text + " ProductID needs QC Test definition" + Environment.NewLine +
                                    "Hệ thống sẽ tiến hành đăng kí test.");
                                RegisTest();
                                return;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp(ex.Message);
            }
        }
        private void SetNG(DataGridViewRow row)
        {
            row.DefaultCellStyle.BackColor = Color.Red;
        }
        public void RegisTest()
        {
            try
            {
                int nNumProduct = DataConvert.ConvertStringToInt(txtNumber.Text);
                if (nNumProduct > int.Parse(lbTotalInput.Text))
                {
                    DialogHelper.Warning("Chưa nhập đủ số lượng: " + txtNumber.Text + " ProductID cần định nghĩa Test/ Entered not sufficient quantities: "
                        + txtNumber.Text + " ProductID needs QC Test definition" + Environment.NewLine +
                        "Cảnh báo");
                    return;
                }
                string stt = string.Empty;
                string sProductID = string.Empty;
                string BlockID = string.Empty;
                string preBlockID = string.Empty;
                bool bError = false;
                for (int i = 0; i < dataGridView.Rows.Count; i++)
                {
                    string s = dataGridView.Rows[i].Cells[dataGridView.Columns.Count - 2].Value.ToString();
                    if (!string.IsNullOrEmpty(s))
                    {

                        bError = true;
                    }
                }
                for (int i = 0; i < dataGridView.Rows.Count; i++)
                {
                    preBlockID = string.Empty;
                    sProductID = dataGridView.Rows[i].Cells[1].Value.ToString();
                    BlockID = SingletonLocal.OQC_IPQC_ORT_OBA_Local_Service.CheckBlock(sProductID, _connectionStringIDLink);
                    //if (_sTestID != AccountRole.c_oqc_measurement)
                    //{
                    if (string.IsNullOrEmpty(preBlockID))
                    {
                        preBlockID = BlockID;
                        dataGridView.Rows[i].Cells[3].Value = BlockID;
                    }
                }
                if (bError) return;
                if (!SingletonLocal.OQC_IPQC_ORT_OBA_Local_Service.CheckBlockPunchingData(preBlockID, cbxLineID.Text.Trim(), out string PuchingMachineID, _connectionStringShopfloor))
                {
                    lbStatusProduct.Text = "Block:" + preBlockID + " không có dữ liệu punching/ Block: " + preBlockID.Trim() + " does not have punching data";
                    lbStatusProduct.ForeColor = Color.Red;
                    DialogHelper.Error("Block:" + preBlockID + " không có dữ liệu punching/ Block: " + preBlockID.Trim() + " does not have punching data");
                    return;
                }

                if (!PuchingMachineID.Equals(cbxLineID.Text.Trim()))
                {
                    for (int i = 0; i < dataGridView.Rows.Count; i++)
                    {
                        SetNG(dataGridView.Rows[i]);
                        dataGridView.Rows[i].Cells[5].Value = PuchingMachineID;
                    }
                    lbStatusProduct.Text = "Block:" + preBlockID + "không được làm tại máy " + cbxLineID.Text.Trim() + " / Block: "
                        + preBlockID.Trim() + " were not punched in " + cbxLineID.Text.Trim();
                    lbStatusProduct.ForeColor = Color.Red;
                    DialogHelper.Error("Block:" + preBlockID + "không được làm tại máy " + cbxLineID.Text.Trim() + " / Block: "
                        + preBlockID.Trim() + " were not punched in " + cbxLineID.Text.Trim());
                    bError = true;
                }
                if (bError)
                {
                    DialogHelper.Error("Mẫu khai báo không cùng Block hoặc không cùng Indi. Vui lòng kiểm tra và khai báo lại.");
                    return;
                }
                bool bSuccess = true;
                DataTable table = new DataTable();
                table.Columns.Add("No", typeof(string));
                table.Columns.Add("ProductID", typeof(string));
                table.Columns.Add("BlockID", typeof(string));
                table.Columns.Add("IndicationNumber", typeof(string));
                table.Columns.Add("ProductionDateTest", typeof(DateTime));
                table.Columns.Add("Process", typeof(string));
                table.Columns.Add("TestType", typeof(string));
                table.Columns.Add("ItemCode", typeof(string));
                table.Columns.Add("ItemName", typeof(string));
                table.Columns.Add("LineID", typeof(string));
                table.Columns.Add("Operator", typeof(string));
                table.Columns.Add("Result", typeof(string));
                table.Columns.Add("ResultLink", typeof(string));
                table.Columns.Add("CreatedDate", typeof(DateTime));

                decimal nDetailID = SingletonLocal.OQC_IPQC_ORT_OBA_Local_Service.GetDetailID(_connectionStringMain) + 1;
                bool blnResult = true;
                float total = dataGridView.Rows.Count;
                float totalB = 0;
                if (_sTestID == AccountRole.ID_OBA2Dbarcodegrade || _sTestID == AccountRole.ID_OQC2Dbarcodegrade)
                {
                    autoAddData = true;
                }
                for (int i = 0; i < dataGridView.Rows.Count; i++)
                {
                    stt = dataGridView.Rows[i].Cells[0].Value.ToString();
                    sProductID = dataGridView.Rows[i].Cells[1].Value.ToString();
                    preBlockID = dataGridView.Rows[i].Cells[3].Value.ToString();
                    string Result = dataGridView.Rows[i].Cells[2].Value.ToString();
                    string ResultLink = dataGridView.Rows[i].Cells[13].Value.ToString();
                    DateTime ProductionDateTest = Convert.ToDateTime(dataGridView.Rows[i].Cells[6].Value.ToString());
                    //if (ResultLink != null && ResultLink != "")
                    //{
                    //    ResultLink = ResultLink.Replace("\\\\", "http://");
                    //    ResultLink = ResultLink.Replace("\\", "/");
                    //    ResultLink = ResultLink.Replace("WebMachine/", "");
                    //}
                    if (autoAddData == true)
                    {
                        if (_sTestID == AccountRole.ID_OBA2Dbarcodegrade || _sTestID == AccountRole.ID_OQC2Dbarcodegrade)
                        {
                            if (Result != "A")
                            {
                                if (Result == "B")
                                {
                                    totalB += 1;
                                }
                                else
                                {
                                    blnResult = false;
                                }
                            }
                        }
                        else
                        {
                            if (Result.ToUpper() != "PASS")
                            {
                                blnResult = false;
                            }
                        }
                    }

                    int rs = SingletonLocal.OQC_IPQC_ORT_OBA_Local_Service.InsertProductDetail(sProductID, nDetailID, _connectionStringMain);
                    if (rs < 0)
                    {
                        bSuccess = false;
                    }
                    else
                    {
                        table.Rows.Add(stt, sProductID, preBlockID, txtIndi.Text.Trim(), ProductionDateTest, lbProcess.Text.Trim(), lbTestType.Text.Trim(), txtItemCode.Text.Trim(),
                            txtItemName.Text.Trim(), cbxLineID.Text.Trim(), txtOperatorID.Text.Trim(), Result, ResultLink, DateTime.Now);
                    }
                }

                if (bSuccess)
                {
                    Common.IPQC_OQC_WriteCsvIndiBlock(table, txtIndi.Text.Trim(), txtItemCode.Text.Trim(), _sTestName, _pathCsvLocal);
                    Common.IPQC_OQC_WriteCsvIndiBlock(table, txtIndi.Text.Trim(), txtItemCode.Text.Trim(), _sTestName, _pathCsvServer);
                    if (autoAddData)
                    {
                        string PathCsvResultServer = Singleton.IniFile.GetSetting("FormSetting", "PathCsvResultServer");
                        string _ResultLink = Common.IPQC_OQC_WriteCsvIndiBlock(table, txtIndi.Text.Trim(), txtItemCode.Text.Trim(), _sTestName, PathCsvResultServer);
                        if ((totalB / total * 100) > GradeCheckRate)
                        {
                            blnResult = false;
                        }
                        string strResult = blnResult ? "OK" : "NG";
                        bSuccess = bSuccess && SingletonLocal.OQC_IPQC_ORT_OBA_Local_Service.InsertTestResultIndi(txtIndi.Text.Trim(), txtItemCode.Text.Trim(), txtItemName.Text, txtMachineID.Text.Trim(), txtfixtureid.Text.Trim().Replace(Environment.NewLine, ";"),
                            cbxLineID.Text.Trim(), txtOperatorID.Text.Trim(), _sTestID, _sTestName, nDetailID, _productionDateTest, strResult, _ResultLink, _connectionStringMain);
                    }
                    else
                    {
                        bSuccess = bSuccess && SingletonLocal.OQC_IPQC_ORT_OBA_Local_Service.InsertTestIndi_v134(txtIndi.Text.Trim(), txtItemCode.Text.Trim(), txtItemName.Text, txtMachineID.Text.Trim(), txtfixtureid.Text.Trim().Replace(Environment.NewLine, ";"),
                            cbxLineID.Text.Trim(), txtOperatorID.Text.Trim(), _sTestID, _sTestName, nDetailID, _productionDateTest, _connectionStringMain);
                    }
                    if (bSuccess)
                    {
                        DialogHelper.Info("Đăng kí dữ liệu thành công!");
                        lbStatusProduct.Text = "Đăng kí dữ liệu thành công";
                        lbStatusProduct.ForeColor = Color.Black;
                        Reset();
                        Disconnect();
                    }
                    else
                    {
                        DialogHelper.Info("Đăng kí dữ liệu Thất bại!");
                        lbStatusProduct.Text = "Đăng kí dữ liệu Thất bại!";
                        lbStatusProduct.ForeColor = Color.Black;
                    }
                }
                else
                {
                    DialogHelper.Info("Đăng kí dữ liệu vào DB thất bại, vui lòng kiểm tra lại");
                }
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp(ex.StackTrace);
            }

        }
        private void DataGridView_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {
            //if (e.ColumnIndex == 5 && e.RowIndex != -1)
            //{
            //    if (MessageBox.Show("Bạn có muốn xóa không?", "Đang xóa...", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            //    {
            //        int rowIndex = e.RowIndex;
            //        string productId = dataGridView.Rows[rowIndex].Cells[0].Value.ToString();
            //        lbTotal.Text = (int.Parse(lbTotal.Text) - 1).ToString();
            //        dataGridView.Rows.RemoveAt(rowIndex);
            //    }
            //}
        }

        private void BtnReset_Click(object sender, EventArgs e)
        {
            if (txtOperatorID.Text.Trim().Length == 5 || txtOperatorID.Text.Trim().Length == 6)
            {
                ClearGrid();
                Reset();
            }
            else
            {
                ClearGrid();
                Reset();
                txtNumber.Enabled = false;
            }
        }
        private void ClearGrid()
        {
            _lstProductID = new List<string>();
            dataGridView.ClearSelection();
            dataGridView.Rows.Clear();
            dataGridView.Refresh();
        }
        private void Reset()
        {
            autoAddData = false;
            txtProductId.Text = "";
            lbStatusProduct.Text = "";
            lbTotal.Text = "0";
            txtfixtureid.Text = "";
            lbTotalInput.Text = "0";
            lbItemName.Text = "---";
            txtNumber.Text = "";
            txtNumber.Enabled = true;
            txtNumber.Focus();
            tbNG.Text = "";
            txtItemCode.Text = "";
            txtItemName.Text = "";
            txtIndi.Text = "";
            txtIndi.Enabled = false;
            cbxLineID.SelectedIndex = 0;
            //cbxLineID.Enabled = true;
            _lstProductID = new List<string>();
            _sPreProductID = string.Empty;
            btConnectBarcode.BackColor = Color.Red;
            btConnectBarcode.Enabled = false;
            txtMachineID.Enabled = false;
            btnAutoAddData.Visible = false;
            btRegist.BackColor = Color.Red;
            _preBlockID = string.Empty;
        }

        private void LbResetOperatorID_Click(object sender, EventArgs e)
        {
            txtOperatorID.Text = "";
            txtOperatorID.Enabled = true;
        }

        private void LbEdit_Click(object sender, EventArgs e)
        {
            if (txtOperatorID.Text.Trim().Length == 0 || txtNumber.Text.Trim().Length == 0)
            {
                DialogHelper.Warning("Bạn không thể sửa số lượng do chưa nhập đủ thông tin đầu vào/ You cannot edit the quantity because you have not entered enough information");
                return;
            }

            _isEdit = true;
            txtNumber.Enabled = true;
            txtNumber.SelectAll();
            txtNumber.Focus();
        }
        private void FrmOBADefine_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (e.CloseReason == CloseReason.UserClosing)
            {
                DialogResult result = MessageBox.Show(@"Thoát chương trình/ Exit Program?.", @"Thoát/Exit", MessageBoxButtons.YesNo);
                if (result == DialogResult.Yes)
                {
                    timerConnect.Stop();
                    timerConnect.Enabled = false;
                    if (_spManagerHandyManager != null) _spManagerHandyManager.StopListening();
                    Disconnect();
                    //Environment.Exit(0);
                    //Application.Exit();
                }
                else
                {
                    e.Cancel = true;
                }
            }
            else
            {
                e.Cancel = true;
            }
        }

        private void btRegist_Click(object sender, EventArgs e)
        {
            RegisTest();
        }
        private void btTestOpID_Click(object sender, EventArgs e)
        {
            InputOPID("12345");
        }
        private void txtItemCode_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                if (string.IsNullOrEmpty(txtItemCode.Text.Trim()))
                {
                    MessageBox.Show("Itemcode không được để trống!" + Environment.NewLine + "Itemcode must not be empty!");
                    return;
                }
                else
                {//tbItemCode.ReadOnly = true;
                    txtItemCode.Enabled = false;
                    txtItemName.Enabled = true;
                    txtItemName.Focus();
                }

            }
        }
        private void txtItemName_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                if (string.IsNullOrEmpty(txtItemName.Text.Trim()))
                {
                    MessageBox.Show("ItemName không được để trống!" + Environment.NewLine + "ItemName must not be empty!");
                    return;
                }
                else
                {
                    //tbItemCode.ReadOnly = true;
                    txtItemName.Enabled = false;
                    cbxLineID.Text = c_sPunch0;
                    //cbxLineID.Enabled = true;
                    //cbxLineID.Focus();
                }
            }
        }
        private void cbxLineID_SelectedIndexChanged(object sender, EventArgs e)
        {
            cbxLineID.Enabled = false;
            //btConnectBarcode.Text = " Connect Barcode";
            //btConnectBarcode.BackColor = Color.Yellow;
            btConnectBarcode.Enabled = true;
            if (_sTestID == AccountRole.ID_OQCICTtest || _sTestID == AccountRole.ID_OBAICTtest || _sTestID == AccountRole.c_oqc_Heating4WTest || _sTestID == AccountRole.ID_OQCFunctiontest || _sTestID == AccountRole.ID_OBAFunctiontest) { btnAutoAddData.Visible = true; }
            if (_sTestID == AccountRole.ID_OBAVisualInspection)
            {
                label19.Visible = true;
                txtAlbagID.Visible = true;
            }
        }
        //private void txtLineID_KeyDown(object sender, KeyEventArgs e)
        //{
        //    if (e.KeyCode == Keys.Enter)
        //    {
        //        //tbItemCode.ReadOnly = true;
        //        cbxLineID.Enabled = false;
        //        txtProductId.Enabled = true;
        //        txtProductId.Focus();
        //    }
        //}
        private void btTestProduct_Click(object sender, EventArgs e)
        {
            CheckProductID(txtProductId.Text.Trim());
        }
        private void Search()
        {
            try
            {
                this.dgvSearch.Rows.Clear();
                DataTable dt = new DataTable();
                if (txtSearch.Text.Trim().Length == 0)
                    return;
                dt = SingletonLocal.OQC_IPQC_ORT_OBA_Local_Service.IPQC_OQC_Search(txtSearch.Text.Trim(), _connectionStringMain);
                if (dt?.Rows.Count > 0)
                {
                    for (int i = 0; i < dt.Rows.Count; i++)
                    {
                        var IndicationNumber = dt.Rows[i]["IndicationNumber"];
                        var itemcode = dt.Rows[i]["ItemCode"];
                        var itemname = dt.Rows[i]["ItemName"];
                        var lineid = dt.Rows[i]["LineId"];
                        var inputtest = Convert.ToString(dt.Rows[i]["InputTest"]);
                        var finishtest = Convert.ToString(dt.Rows[i]["FinishTest"]);
                        var result = dt.Rows[i]["Result"];
                        var resultlink = dt.Rows[i]["ResultLink"];
                        var operatorid = dt.Rows[i]["InputOperator"];
                        var finishid = dt.Rows[i]["FinishOperator"];
                        var testid = dt.Rows[i]["TestID"];
                        var testname = dt.Rows[i]["TestName"];
                        var detailid = dt.Rows[i]["DetailID"];
                        if (testid.ToString() == AccountRole.ID_OBAVisualInspection || testid.ToString() == AccountRole.ID_OQCVisualInspection)
                        {
                            Object[] row = { IndicationNumber, itemname, lineid, inputtest, finishtest, result, resultlink, operatorid, finishid, testid, testname, detailid, "ADD Product" };
                            this.dgvSearch.Rows.Add(row);
                        }
                        else
                        {
                            Object[] row = { IndicationNumber, itemname, lineid, inputtest, finishtest, result, resultlink, operatorid, finishid, testid, testname, detailid, "" };
                            this.dgvSearch.Rows.Add(row);
                        }
                    }
                    lbTotal.Text = dt.Rows.Count.ToString();
                }
                else
                {
                    DialogHelper.Info("Không tìm thấy dữ liệu nào/ Data not found");
                }
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp(ex);
            }
        }
        private void txtSearch_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                Search();
            }
        }

        private void btSearch_Click(object sender, EventArgs e)
        {
            Search();
        }

        private void btAccountManage_Click(object sender, EventArgs e)
        {
            frmAccount frm = new frmAccount();
            frm.ShowDialog();
        }

        private void timerConnect_Tick(object sender, EventArgs e)
        {
            CheckConnect();
        }

        private void btConnectBarcode_Click(object sender, EventArgs e)
        {
            if (!Check_TextNumber())
            {
                btConnectBarcode.BackColor = Color.Red;
                return;
            }
            if (int.Parse(txtNumber.Text) == int.Parse(lbTotalInput.Text))
            {
                DialogHelper.Info("Đã nhập đủ số lượng: " + txtNumber.Text + " ProductID cần Input Test/ Entered sufficient quantities: "
                    + txtNumber.Text + " ProductID needs OBA definition");
                Reset();
                return;
            }
            ConnectBarcode(_IPAddressFix);
        }

        private void lbChangeLine_Click(object sender, EventArgs e)
        {
            //cbxLineID.Enabled= true;
        }

        string SplitItemCode(string Indi)
        {
            string ItemCode = string.Empty;

            try
            {
                Indi = Indi.ToUpper();
                if (Indi.Length < 7) return ItemCode;
                if (Indi.Length >= 9 && Indi[8] != 'V')
                    ItemCode = Indi.Substring(0, 6);
                else ItemCode = Indi.Substring(0, 7);
                return ItemCode;
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp(ex);
                ManageLog.WriteErrorApp(Indi);
            }
            return ItemCode;
        }
        bool SetIndi(string Indi)
        {
            //check Indi
            bool bCheckIndi = false;
            if (string.IsNullOrEmpty(Indi)) return bCheckIndi;
            bCheckIndi = SingletonLocal.BoardRegisService.CheckExist(txtIndi.Text.Trim(), out string ItemName, _connectionStringBoard);
            if (bCheckIndi)
            {
                if (!ItemName.ToUpper().Contains(_ItemName.Trim().ToUpper()))
                {
                    MessageBox.Show("đang nhập dữ liệu cho ItemName:" + _ItemName);
                    bCheckIndi = false;
                }
                else
                {
                    string ItemCode = SplitItemCode(txtIndi.Text.Trim());
                    txtItemCode.Text = ItemCode;
                    txtItemCode.Enabled = false;
                    txtItemName.Text = ItemName;
                    txtItemName.Enabled = false;
                    txtIndi.Enabled = false;
                    cbxLineID.SelectedIndex = 0;
                    //cbxLineID.Enabled = true;
                    cbxLineID.Enabled = false;
                    cbxLineID.Focus();
                }
            }
            else
            {
                DialogHelper.Warning("Indi không tồn tại / Indi does not exists");
                txtIndi.Focus();
            }
            return bCheckIndi;
        }
        private void txtIndi_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                if (string.IsNullOrEmpty(txtIndi.Text.Trim()))
                {
                    DialogHelper.Warning("Chưa nhập Indi/ Indi must not empty");
                    txtIndi.Enabled = true;
                    txtIndi.Focus();
                    return;
                }

                if (SetIndi(txtIndi.Text.Trim()))
                {
                    //Check nếu Indi Number đã được đăng ký test thuộc ORT thì không được đăng ký test khác. 

                    var tmpTest = SingletonLocal.OQC_IPQC_ORT_OBA_Local_Service.CheckExistIndiNumber(txtIndi.Text.Trim(), _sTestID, _connectionStringMain);
                    if (tmpTest)
                    {
                        if (!(MessageBox.Show("Indi đã đăng ký test với ORT, bạn có muốn đăng ký test thêm không?", "Confirm", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes))
                        {
                            txtIndi.Text = string.Empty;
                            txtItemCode.Text = string.Empty;
                            txtItemName.Text = string.Empty;
                            txtIndi.Focus();
                            return;
                        }
                        //DialogHelper.Warning("Indi đã được đăng ký test với ORT!/Indi was already registed with ORT!");
                        //txtIndi.Text = string.Empty;
                        //txtItemCode.Text = string.Empty;
                        //txtItemName.Text = string.Empty;
                        //txtIndi.Focus();
                        //return;
                    }

                    txtIndi.Enabled = false;

                    if (IsInputTime == 1)
                    {
                        dtpInputTime.Enabled = true;
                        dtpInputTime.Focus();
                    }
                    else
                    {
                        txtMachineID.Enabled = true;
                        txtMachineID.Focus();
                        txtMachineID.SelectAll();
                    }
                }
                else
                {
                    return;
                }
                _productionDateTest = SingletonLocal.OQC_IPQC_ORT_OBA_Local_Service.GetProductionDateTestByIndi(txtIndi.Text.Trim(), "FlexAssy_32_PunchingInfor", "PunchingDate", _connectionStringTrace);
                if (_productionDateTest == DateTime.MinValue)
                {
                    DialogHelper.Warning("PunchingInfor không tồn tại / PunchingInfor does not exists");
                    return;
                }
                dtpInputTime.Value = _productionDateTest;
                getShift(_productionDateTest);


                //if (label3.Text.Contains("Line"))
                //{
                //    //Get line và ProductionDate trên 8.42
                //    var tmpProductDate = SingletonLocal.OQC_IPQC_ORT_OBA_Local_Service.GetProductionDateTest(txtIndi.Text.Trim(), _connectionStringTrace);
                //    var tmpProductLine = SingletonLocal.OQC_IPQC_ORT_OBA_Local_Service.GetProductionLine(txtIndi.Text.Trim(), _connectionStringTrace);
                //    if (!string.IsNullOrEmpty(tmpProductDate))
                //    {
                //        _productionDateTest = Convert.ToDateTime(tmpProductDate.ToString().Trim());
                //    }
                //    else
                //    {
                //        DialogHelper.Warning("ProductionDate không tồn tại/ ProductionDate does not exists");
                //        txtIndi.Focus();
                //        return;
                //    }

                //    if (!string.IsNullOrEmpty(tmpProductLine))
                //    {
                //        _productionLineTest = tmpProductLine;
                //        cbxLineID.SelectedIndex = cbxLineID.FindStringExact(_productionLineTest);
                //    }
                //    else
                //    {
                //        DialogHelper.Warning("ProductionLine không tồn tại / ProductionLine does not exists");
                //        txtIndi.Focus();
                //        return;
                //    }
                //}

                //txtMachineID.Enabled = true;
                //txtMachineID.Focus();
                //txtMachineID.SelectAll();
            }
        }

        private void txtIndi_TextChanged(object sender, EventArgs e)
        {

        }

        private void txtProductId_TextChanged(object sender, EventArgs e)
        {

        }

        private void txtItemCode_TextChanged(object sender, EventArgs e)
        {

        }

        private void groupBox1_Enter(object sender, EventArgs e)
        {

        }
        private void txtFixtureQty_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                txtfixtureid.Enabled = true;
                txtfixtureid.Focus();
            }
        }

        private void txtFixtureQty_TextChanged(object sender, EventArgs e)
        {
            var cursorPosition = txtFixtureQty.SelectionStart;
            txtFixtureQty.Text = Regex.Replace(txtFixtureQty.Text, "[^0-9]", "");
            txtFixtureQty.SelectionStart = cursorPosition;
        }

        public string MultiTextFormat(string text)
        {
            string TextValue = text;
            while (TextValue.Contains(Environment.NewLine + Environment.NewLine))
            {
                TextValue = TextValue.Replace(Environment.NewLine + Environment.NewLine, Environment.NewLine);
            }
            return TextValue;
        }
        public string[] getTextArr(string text)
        {
            string[] ArrResult = null;
            text = text.Trim();
            text = text.Replace("\r\n", ";");
            ArrResult = text.Split(';');
            return ArrResult;
        }
        public Boolean checkTextExist(string text)
        {
            Boolean blnResult = false;
            string[] TextArr = getTextArr(text);
            ArrayList newTextArr = new ArrayList();
            if (TextArr != null)
            {
                if (TextArr.Length > 1)
                {
                    for (int i = 0; i < TextArr.Length; i++)
                    {
                        if (newTextArr.IndexOf(TextArr[i]) < 0)
                        {
                            newTextArr.Add(TextArr[i]);
                        }
                        else
                        {
                            blnResult = true;
                        }
                    }
                }
            }
            if (blnResult == true)
            {
                txtfixtureid.Clear();
                int count = 0;
                foreach (string Item in newTextArr)
                {
                    count++;
                    if (count == newTextArr.Count)
                    {
                        txtfixtureid.AppendText(Item);
                    }
                    else
                    {
                        txtfixtureid.AppendText(Item + Environment.NewLine);
                    }
                }
            }
            return blnResult;
        }
        string oldFixture = "";
        private void txtfixtureid_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                string FixtureValue = MultiTextFormat(txtfixtureid.Text.Trim());
                if (string.IsNullOrEmpty(FixtureValue) || FixtureValue.ToUpper() == "NA")
                {
                    txtfixtureid.Enabled = false;
                    txtFixtureQty.Enabled = false;
                    txtfixtureid.Text = FixtureValue;
                    btConnectBarcode.BackColor = Color.Yellow;
                    btConnectBarcode.Enabled = true;
                    btConnectBarcode.Focus();
                    if (_sTestID == AccountRole.ID_OQCICTtest || _sTestID == AccountRole.ID_OBAICTtest || _sTestID == AccountRole.c_oqc_Heating4WTest || _sTestID == AccountRole.ID_OQCFunctiontest || _sTestID == AccountRole.ID_OBAFunctiontest) { btnAutoAddData.Visible = true; btnAutoAddData.Enabled = true; }
                    if (_sTestID == AccountRole.ID_OBAVisualInspection)
                    {
                        label19.Visible = true;
                        txtAlbagID.Visible = true;
                    }
                    inputInforFinish = true;
                    return;
                }
                string[] FixtureArr = getTextArr(txtfixtureid.Text.Trim());
                string[] FixtureArrOld = getTextArr(oldFixture);
                txtfixtureid.Text = "";
                foreach (string Fixture in FixtureArr)
                {
                    if(!FixtureArrOld.Contains(Fixture))
                    {
                        ProcessHandyData(Fixture);
                    }
                }
                oldFixture = txtfixtureid.Text;
            }
        }

        private void txtfixtureid_KeyDown()
        {
            string invoiceValue = MultiTextFormat(txtfixtureid.Text.Trim());
            if (string.IsNullOrEmpty(invoiceValue))
            {
                txtfixtureid.Enabled = false;
                txtFixtureQty.Enabled = false;
                txtfixtureid.Text = invoiceValue;
                btConnectBarcode.BackColor = Color.Yellow;
                btConnectBarcode.Enabled = true;
                btConnectBarcode.Focus();
                inputInforFinish = true;
                if (_sTestID == AccountRole.ID_OQCICTtest || _sTestID == AccountRole.ID_OBAICTtest || _sTestID == AccountRole.c_oqc_Heating4WTest || _sTestID == AccountRole.ID_OQCFunctiontest || _sTestID == AccountRole.ID_OBAFunctiontest) { btnAutoAddData.Visible = true; }
                if (_sTestID == AccountRole.ID_OBAVisualInspection)
                {
                    label19.Visible = true;
                    txtAlbagID.Visible = true;
                }
            }
            else
            {
                if (checkTextExist(txtfixtureid.Text))
                {
                    DialogHelper.Warning("Invoice bị trùng. Đã xóa Invoice trùng");
                    invoiceValue = MultiTextFormat(txtfixtureid.Text.Trim());
                }
                int invoiceQty = 0;
                if (invoiceValue.Contains(Environment.NewLine))
                {
                    invoiceQty = invoiceValue.Split('\n').Length;
                }
                else
                {
                    invoiceQty = 1;
                }
                if (invoiceQty == int.Parse(txtFixtureQty.Text))
                {
                    txtfixtureid.Enabled = false;
                    txtFixtureQty.Enabled = false;
                    txtfixtureid.Text = invoiceValue;
                    btConnectBarcode.BackColor = Color.Yellow;
                    btConnectBarcode.Enabled = true;
                    btConnectBarcode.Focus();
                    if (_sTestID == AccountRole.ID_OQCICTtest || _sTestID == AccountRole.ID_OBAICTtest || _sTestID == AccountRole.c_oqc_Heating4WTest || _sTestID == AccountRole.ID_OQCFunctiontest || _sTestID == AccountRole.ID_OBAFunctiontest) { btnAutoAddData.Visible = true; }
                    if (_sTestID == AccountRole.ID_OBAVisualInspection)
                    {
                        label19.Visible = true;
                        txtAlbagID.Visible = true;
                    }
                }
            }
        }

        private void btnAutoAddData_Click(object sender, EventArgs e)
        {
            if (txtIndi.Text.Trim().Length == 0)
            {
                DialogHelper.Warning("Indi không được để trống / Indi can't be empty");
                txtProductId.Text = string.Empty;
                return;
            }
            if (_sTestID == AccountRole.ID_OQCICTtest || _sTestID == AccountRole.ID_OBAICTtest || _sTestID == AccountRole.c_oqc_Heating4WTest || _sTestID == AccountRole.ID_OQCFunctiontest || _sTestID == AccountRole.ID_OBAFunctiontest)
            {
                string ProgramType = "";
                switch (_sTestID)
                {
                    case AccountRole.ID_OQCICTtest:
                        ProgramType = "OQC";
                        break;
                    case AccountRole.ID_OBAICTtest:
                        ProgramType = "OBA";
                        break;
                    case AccountRole.c_oqc_Heating4WTest:
                        ProgramType = "H4W";
                        break;
                    case AccountRole.ID_OQCFunctiontest:
                        ProgramType = "OQCFT";
                        break;
                    case AccountRole.ID_OBAFunctiontest:
                        ProgramType = "OBAFT";
                        break;
                }
                DataTable dtPcs = Singleton_02_FlexAssy.IFlexAssy_22_ICTService.FlexAssy_22_ICT_RetestCycle_GetByIndiNumber_ProgramType(txtIndi.Text.Trim(), ProgramType, _connectionStringTrace);

                try
                {
                    if (dtPcs?.Rows.Count <= 0)
                    {
                        DialogHelper.Warning("Không tìm thấy dữ liệu " + ProgramType + " ICT");
                    }
                    btnAutoAddData.Enabled = false;
                    autoAddData = true;
                    Dictionary<string, int> dicProductNG = new Dictionary<string, int>();
                    for (int i = 0; i < dtPcs.Rows.Count && _lstProductID.Count < DataConvert.ConvertToInt(txtNumber.Text); i++)
                    {
                        string ProductID = DataConvert.ConvertToString(dtPcs.Rows[i]["ProductID"]);
                        string Result = DataConvert.ConvertToString(dtPcs.Rows[i]["Result"]);
                        string ResultLog = DataConvert.ConvertToString(dtPcs.Rows[i]["FileName_Pcs"]);
                        if (string.IsNullOrEmpty(ResultLog))
                        {
                            ResultLog = DataConvert.ConvertToString(dtPcs.Rows[i]["FileName_Block"]);
                        }
                        if (!string.IsNullOrEmpty(ResultLog))
                        {
                            ResultLog = StorageAddress + ResultLog;
                        }
                        if (Result.ToUpper() != "PASS")
                        {
                            if (dicProductNG.ContainsKey(ProductID))
                            {
                                dicProductNG[ProductID] = i;
                            }
                            else
                            {
                                dicProductNG.Add(ProductID, i);
                            }
                        }
                        else if (dicProductNG.ContainsKey(ProductID))
                        {
                            continue;
                        }
                        else
                        {
                            CheckProductID(ProductID, Result, ResultLog);
                        }
                    }
                    if (_lstProductID.Count < DataConvert.ConvertToInt(txtNumber.Text))
                    {
                        foreach (KeyValuePair<string, int> item in dicProductNG)
                        {
                            if (_lstProductID.Count < DataConvert.ConvertToInt(txtNumber.Text))
                            {
                                string ProductID = DataConvert.ConvertToString(dtPcs.Rows[item.Value]["ProductID"]);
                                string Result = DataConvert.ConvertToString(dtPcs.Rows[item.Value]["Result"]);
                                string ResultLog = DataConvert.ConvertToString(dtPcs.Rows[item.Value]["FileName_Pcs"]);
                                CheckProductID(ProductID, Result, ResultLog);
                            }
                        }
                    }
                }
                catch (Exception)
                {
                }
            }
            if (_sTestID == AccountRole.ID_OBAVisualInspection)
            {

            }
        }

        private void txtAlbagID_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                if (txtIndi.Text.Trim().Length == 0)
                {
                    DialogHelper.Warning("Indi không được để trống / Indi can't be empty");
                    txtAlbagID.Text = string.Empty;
                    return;
                }
                if (_sTestID == AccountRole.ID_OBAVisualInspection)
                {
                    string factory = Singleton.IniFile.GetSetting("FormSetting", "Factory");
                    string connectionStringOption = Singleton_03_Common.IDBInfoService.ConnectionStringOption(factory, "ShipmentDBLocal", Singleton.IniFile.GetDbConnectionStringTrace_Common());
                    DataTable dtPcs = Singleton_02_FlexAssy.ISource_FlexAssy_Shipment_Service.AlBagProduct_GetDataWithAlBagID(txtAlbagID.Text.Trim(), connectionStringOption);
                    try
                    {
                        for (int i = 0; i < dtPcs.Rows.Count && _lstProductID.Count < DataConvert.ConvertToInt(txtNumber.Text); i++)
                        {
                            string ProductID = DataConvert.ConvertToString(dtPcs.Rows[i]["ProductID"]);
                            CheckProductID(ProductID);
                        }
                    }
                    catch (Exception)
                    {
                    }
                }
            }
        }
        private void txtMachineID_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                txtMachineID.Enabled = false;
                txtFixtureQty.Enabled = true;
                txtFixtureQty.Focus();
                txtFixtureQty.SelectAll();
            }
        }
        private void txtProductId_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (isPaste)
            {
                e.Handled = true;
                isPaste = false;
            }
        }
        public List<string> GetPatternList_Update20210326(string barCode, out string message)
        {
            string rs = string.Empty;
            message = string.Empty;
            List<string> lstResult = new List<string>();
            try
            {
                //Xac ding thuoc pattern nao
                var lstpatterns = Singleton_02_FlexAssy.ISource_FlexAssy_ShopFloor_Service.GetAllPatterns(_connectionStringShopfloor);
                if (lstpatterns != null)
                {
                    int iDigit;
                    foreach (var pattern in lstpatterns)
                    {
                        bool flg = false;
                        if (barCode.Trim().Length == pattern.PatternCode.Trim().Length)
                        {
                            for (int i = 0; i < barCode.Length; i++)
                            {
                                if (barCode[i] == pattern.PatternCode[i])
                                {
                                    flg = true;
                                }
                                else if (pattern.PatternCode[i].ToString().Equals("#") || pattern.PatternCode[i].ToString().Equals("?") || pattern.PatternCode[i].ToString().Equals("!"))
                                {
                                    if (pattern.PatternCode[i].ToString().Equals("?"))
                                    {
                                        flg = true;
                                    }
                                    else if (pattern.PatternCode[i].ToString().Equals("#") && int.TryParse(barCode[i].ToString(), out iDigit))
                                    {
                                        flg = true;
                                    }
                                    else if (pattern.PatternCode[i].ToString().Equals("!") && !int.TryParse(barCode[i].ToString(), out iDigit))
                                    {
                                        flg = true;
                                    }
                                    else
                                    {
                                        flg = false;
                                        break;
                                    }
                                }
                                else
                                {
                                    flg = false;
                                    break;
                                }
                            }
                            if (flg)
                            {
                                rs = pattern.PatternCode + "," + pattern.PatternType + "," + pattern.UserFor;
                                lstResult.Add(rs);
                            }
                        }
                        else
                        {
                            //Lỗi độ dài pattern
                            message = "Can not find suitable pattern.";
                        }
                    }
                }
                else
                {
                    //Lỗi kết nối DB
                    message = "Error connect to DB!";
                }
                if (lstResult.Count == 0)
                {
                    //Khong tim thay pattern
                    message = "Can not find pattern.";
                }
                else
                {
                    message = "";
                }
                return lstResult;
                //return rs;
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp(ex);
                return new List<string>();
            }
        }
        private void ProcessHandyData(string strHandy)
        {
            var message = string.Empty;
            string rsPattern = string.Empty;

            /*Dùng cách này nếu check theo cách mới (không dùng Pattern) */
            var comp = Singleton_02_FlexAssy.ISource_FlexAssy_ShopFloor_Service.Component_GetDataWithCompID(strHandy, _connectionStringShopfloor);
            if (comp != null && comp?.Rows.Count > 0)
            {
                string CompID = comp.Rows[0]["CompID"] + "";
                if (!string.IsNullOrEmpty(CompID))
                {
                    FillCompnentIDStart(CompID, strHandy, ref message, _connectionStringShopfloor);
                    if (!string.IsNullOrEmpty(message))
                    {
                        MessageBox.Show(message);
                    }
                    return;
                }
            }
            else
            {
                MessageBox.Show(strHandy + " không tồn tại trong hệ thống");
            }    
        }
        private void GetComponentCheck()
        {
            _lstCompCheck = new List<TblComponentCheck>();
            _lstCompCheck = Singleton_02_FlexAssy.ISource_FlexAssy_ShopFloor_Service.GetAllComponentCheck(_connectionStringShopfloor);

            _lstTimeCondition = new List<TblTimeCondition>();
            _lstTimeCondition = Singleton_02_FlexAssy.ISource_FlexAssy_ShopFloor_Service.GetAllTimeCondition(_connectionStringShopfloor);
            //Tesst();
        }
        private void FillCompnentIDStart(string compType, string compID, ref string message, string Connection)
        {
            try
            {
                if (!string.IsNullOrEmpty(message)) return;

                /* Check component duoc khai bao chua */
                var currentDate = DateTime.Now;
                var comp = Singleton_02_FlexAssy.ISource_FlexAssy_ShopFloor_Service.GetComponentByID(compID, Connection);
                if (comp == null)
                {
                    message = "Component: " + compID + " chưa được khai báo!";
                    return;
                }

                // check tool Premaintaince _lstCompCheck
                string Status = "OK";
                string _TimeControl = "-";
                string _TimeF = "";
                Color _color = Color.White;

                // Update maintain PreMatainTool
                var data_CompCheck = _lstCompCheck.FirstOrDefault(x => x.CompType.ToUpper().Equals(comp.CompTypeID.ToUpper()));
                if (data_CompCheck != null)
                {
                    Singleton_02_FlexAssy.ISource_FlexAssy_ShopFloor_Service.UpdateTimeMaintainceByCompID(data_CompCheck, comp.CompID, !string.IsNullOrEmpty(txtOperatorID.Text.Trim()) ? txtOperatorID.Text.Trim() : "N/A", _connectionStringShopfloor);
                }
                else
                {
                    message = "không lấy được Condition của Comptype : " + comp.CompTypeID.ToUpper();
                    return;
                }
                string MessPmtool = Singleton_02_FlexAssy.ISource_FlexAssy_ShopFloor_Service.CheckPreMatainTool(comp.CompID, _connectionStringShopfloor);
                if (!string.IsNullOrEmpty(MessPmtool))
                {
                    if (!MessPmtool.Contains("*NG*"))
                    {
                        if (MessPmtool.Contains("*Warning*"))
                        {
                            Status = "ALARM";
                            _color = Color.Yellow;
                            _TimeControl = (MessPmtool.Split('*')[0]).Split('#')[1];
                            _TimeF = "(" + (MessPmtool.Split('*')[0]).Split('#')[0] + ")";
                        }
                        else
                        {
                            Status = "OK";
                            _color = Color.White;
                            _TimeControl = (MessPmtool.Split('*')[0]).Split('#')[1];
                            _TimeF = "(" + (MessPmtool.Split('*')[0]).Split('#')[0] + ")";
                        }
                    }
                    else
                    {
                        message = compID + " Hết hạn sử dụng";
                        return;
                    }
                }
                //Get cac dieu kien can tinh cua component
                var tmpCheck = _lstCompCheck.FirstOrDefault(compCheck => compCheck.CompType.ToUpper() == comp.CompTypeID.ToUpper());
                bool expiresDate = tmpCheck.ExpiresDate;
                bool usageCtrl = tmpCheck.UsageControl;
                //Get du lieu su dung moi nhat cua component
                var shopFloorDetail = new TblShopFloorDetail();
                shopFloorDetail = Singleton_02_FlexAssy.ISource_FlexAssy_ShopFloor_Service.GetLastValue(compID, _connectionStringShopfloor); //Lấy số liệu trong bảng Detail
                if (shopFloorDetail != null)
                {
                    if (expiresDate)
                    {
                        if (currentDate > comp.ExpiresDate)
                        {
                            message = comp.CompID + " đã quá hạn sử dụng!";
                            return;
                        }
                    }

                    if (usageCtrl)
                    {
                        if (shopFloorDetail.Quantity >= comp.Quantity)
                        {
                            message = comp.CompID + " đã sử dụng quá số lần cho phép!";
                            return;
                        }
                    }
                }
                txtfixtureid.Text += Environment.NewLine + compID;
                txtfixtureid_KeyDown();
                _strHandy = "";
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp(ex);
            }
        }
        private void dateTimePicker1_ValueChanged(object sender, EventArgs e)
        {
            dtpInputTime.CustomFormat = "yyyy-MM-dd";
        }

        public void getTestInfor()
        {
            try
            {
                string factory = Singleton.IniFile.GetSetting("FormSetting", "Factory");
                DataTable dtInfor = Singleton_03_Common.ShiftDefinition.GetTestInfor(_ItemName, factory, DataConvert.ConvertToInt(_sTestID), Singleton.IniFile.GetDbConnectionStringTrace_Common());
                if (dtInfor?.Rows.Count > 0)
                {
                    IsInputTime = DataConvert.ConvertToInt(dtInfor.Rows[0]["IsInputTime"]);
                    if (IsInputTime == 1)
                    {
                        dtpInputTime.Visible = true;
                        cbxShift.Visible = true;
                    }
                }
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp(ex);
            }
        }
        public void GetProductionDateTest()
        {
            DataTable dt = Singleton_03_Common.ShiftDefinition.LoadAllShift(_connectionStringTrace_Common);
            int iSShift = 0;
            if (dt?.Rows.Count > 0)
            {
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    if (DataConvert.ConvertToString(dt.Rows[i]["WorkItem"]) != "SHIFT")
                    {
                        continue;
                    }
                    iSShift = 1;
                    string Shift = DataConvert.ConvertToString(dt.Rows[i]["ShiftName"]);
                    if (cbxShift.SelectedIndex == 0 && Shift == "Shift 1")
                    {
                        _productionDateTest = DataConvert.ObjectToDateTime(dtpInputTime.Value.ToString("yyyy-MM-dd ") + dt.Rows[i]["StartTime"]);
                    }
                    if (cbxShift.SelectedIndex == 1 && Shift == "Shift 2")
                    {
                        _productionDateTest = DataConvert.ObjectToDateTime(dtpInputTime.Value.ToString("yyyy-MM-dd ") + dt.Rows[i]["StartTime"]);
                    }
                    if (cbxShift.SelectedIndex == 2 && Shift == "Shift 3")
                    {
                        _productionDateTest = DataConvert.ObjectToDateTime(dtpInputTime.Value.ToString("yyyy-MM-dd ") + dt.Rows[i]["StartTime"]);
                    }
                }
            }
            if (iSShift == 0)
            {
                if (cbxShift.SelectedIndex == 0)
                {
                    _productionDateTest = DataConvert.ObjectToDateTime(dtpInputTime.Value.ToString("yyyy-MM-dd 06:00:00"));
                }
                if (cbxShift.SelectedIndex == 1)
                {
                    _productionDateTest = DataConvert.ObjectToDateTime(dtpInputTime.Value.ToString("yyyy-MM-dd 14:00:00"));
                }
                if (cbxShift.SelectedIndex == 2)
                {
                    _productionDateTest = DataConvert.ObjectToDateTime(dtpInputTime.Value.ToString("yyyy-MM-dd 22:00:00"));
                }
            }
        }
        public void getShift(DateTime productionTime)
        {
            DataTable dt = Singleton_03_Common.ShiftDefinition.LoadAllShift(_connectionStringTrace_Common);
            int iSShift = 0;
            if (dt?.Rows.Count > 0)
            {
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    if (DataConvert.ConvertToString(dt.Rows[i]["WorkItem"]) != "SHIFT")
                    {
                        continue;
                    }
                    iSShift = 1;
                    DateTime StartTime = DataConvert.ObjectToDateTime(productionTime.ToString("yyyy-MM-dd ") + dt.Rows[i]["StartTime"]);
                    DateTime EndTime = DataConvert.ObjectToDateTime(productionTime.ToString("yyyy-MM-dd ") + dt.Rows[i]["EndTime"]);
                    if (EndTime < StartTime)
                    {
                        EndTime = EndTime.AddDays(1);
                    }
                    if (productionTime > StartTime && productionTime < EndTime)
                    {
                        string Shift = DataConvert.ConvertToString(dt.Rows[i]["ShiftName"]);
                        switch (Shift)
                        {
                            case "Shift 1":
                                cbxShift.SelectedIndex = 0;
                                break;
                            case "Shift 2":
                                cbxShift.SelectedIndex = 1;
                                break;
                            case "Shift 3":
                                cbxShift.SelectedIndex = 2;
                                break;
                        }
                    }
                }
            }
            if (iSShift == 0)
            {
                int Hour = productionTime.Hour;
                if (Hour >= 6 && Hour < 14)
                {
                    cbxShift.SelectedIndex = 0;
                }
                else if (Hour >= 14 && Hour < 22)
                {
                    cbxShift.SelectedIndex = 1;
                }
                else if (Hour >= 22 && Hour < 6)
                {
                    cbxShift.SelectedIndex = 2;
                }
            }
        }
        private void dtpInputTime_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                cbxShift.Enabled = true;
                cbxShift.Focus();
                dtpInputTime.Enabled = false;
            }
        }
        private void cbxShift_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                txtMachineID.Enabled = true;
                txtMachineID.Focus();
                txtMachineID.SelectAll();
                GetProductionDateTest();
                cbxShift.Enabled = false;
            }
        }
    }
}