﻿@using System.Data;
@using System.Configuration;
@model Trace_AbilitySystem.Libs.DTOClass.DataAllTrace
@using Trace_AbilitySystem.Libs

@if (Model != null && Model.DataFlexAssy.FlexAssy_19_PunchingLinking?.Rows.Count > 0)
{

    <h2 class="bd-title" id="19-punching-linking">Punching Linking</h2>
    <table class="table table-bordered">
        <thead>
            <tr>
                <th scope="col">Control Item</th>
                <th scope="col">Control Value</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>ItemName</td>
                <td>@(ViewBag.ItemName)</td>
            </tr>
            <tr>
                <td>IndicationNo</td>
                <td>@Model.DataFlexAssy.FlexAssy_19_PunchingLinking.Rows[0]["IndiSMT"]</td>
            </tr>
            <tr>
                <td>BlockID</td>
                <td>@Model.DataFlexAssy.FlexAssy_19_PunchingLinking.Rows[0]["BlockID"]</td>
            </tr>
            <tr>
                <td>BlockIDMap</td>
                <td>@Model.DataFlexAssy.FlexAssy_19_PunchingLinking.Rows[0]["BlockIDMap"]</td>
            </tr>
            <tr>
                <td>DateTime</td>
                <td>@Model.DataFlexAssy.FlexAssy_19_PunchingLinking.Rows[0]["Createddate"]</td>
            </tr>
            <tr>
                <td>Operator ID</td>
                <td>@Model.DataFlexAssy.FlexAssy_19_PunchingLinking.Rows[0]["OperationID"]</td>
            </tr>

        </tbody>
    </table>
}
@if (Model != null && Model.DataFlexAssy.FlexAssy_19_Punching?.Rows.Count > 0)
{
    for (int i = 0; i < Model.DataFlexAssy.FlexAssy_19_Punching.Rows.Count; i++)
    {
        @*<h2 class="bd-title" id="19-punching">Punching @(Model.DataFlexAssy.FlexAssy_19_Punching.Rows.Count > 1 ? (i + 1).ToString() : "")</h2>*@
        if (i < 3)
        {
            <h2 class="bd-title" id="19-punching">Punching @(Model.DataFlexAssy.FlexAssy_19_Punching.Rows.Count > 1 ? (i + 1).ToString() : "")</h2>
        }
        else
        {
            <h2 class="bd-title" id="19-punching">Punching 3</h2>
        }
        <table class="table table-bordered">
            <thead>
                <tr>
                    <th scope="col">Control Item</th>
                    <th scope="col">Control Value</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>ItemName</td>
                    <td>@(ViewBag.ItemName)</td>
                </tr>
                <tr>
                    <td>IndicationNo</td>
                    <td>@(ViewBag.IndicationNo)</td>
                </tr>
                <tr>
                    <td>BlockID</td>
                    <td>@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["BlockID"]</td>
                </tr>
                <tr>
                    <td>DateTime</td>
                    <td>@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["DateTime"]</td>
                </tr>
                @*<tr>
            <td>Machine ID</td>
            <td>@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["MachineID"]</td>
        </tr>*@
                <tr>
                    <td>Operator ID</td>
                    <td>@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["OperatorID"]</td>
                </tr>
                @{
                    string Name = "Punch die ID";
                    string Name2 = "Punch die maintain time";
                    if (DataConvert.ConvertToString(Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["PunchDieID"]).Contains("U"))
                    {
                        Name = "Upper die ID";
                        Name2 = "Upper die maintain time";
                    }
                }
                <tr>
                    <td>@Name</td>
                    @if (Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["PunchDieID"] + "" == "")
                    {
                        <td>N/A</td>
                    }
                    else
                    {
                        <td><a href="javascript:" onclick="viewDataByCompID('@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["PunchDieID"]', '@(ViewBag.Factory)', '@(ViewBag.ItemName)', '')">@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["PunchDieID"]</a></td>
                    }
                </tr>
                <tr>
                    <td>@Name2</td>
                    @if (Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["PunchDieID"] + "" == "")
                    {
                        <td>N/A</td>
                    }
                    else
                    {
                        <td><a onclick="viewDataToolCleaningDate(@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["PunchDieManualCleaningID"], '@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["PunchDieID"]')" href="javascript:">@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["PunchDieManualCleaningTime"]</a></td>
                    }

                </tr>
                <tr>
                    <td>Lower die ID</td>
                    @if (Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["DieLowerID"] + "" == "")
                    {
                        <td>N/A</td>
                    }
                    else
                    {
                        <td><a href="javascript:" onclick="viewDataByCompID('@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["DieLowerID"]', '@(ViewBag.Factory)', '@(ViewBag.ItemName)', '')">@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["DieLowerID"]</a></td>
                    }
                </tr>
                <tr>
                    <td>Lower die maintain time</td>
                    @if (Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["DieLowerID"] + "" == "")
                    {
                        <td>N/A</td>
                    }
                    else
                    {
                        <td><a onclick="viewDataToolCleaningDate(@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["DieLowerManualCleaningID"], '@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["DieLowerID"]')" href="javascript:">@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["DieLowerManualCleaningTime"]</a></td>
                    }
                </tr>
                <tr>
                    <td>Liftup board ID</td>
                    @if (Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["LiftupBoardID"] + "" == "")
                    {
                        <td>N/A</td>
                    }
                    else
                    {
                        <td><a href="javascript:" onclick="viewDataByCompID('@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["LiftupBoardID"]', '@(ViewBag.Factory)', '@(ViewBag.ItemName)', '')">@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["LiftupBoardID"]</a></td>
                    }
                </tr>
                <tr>
                    <td>Liftup board maintain time</td>
                    @if (Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["LiftupBoardID"] + "" == "")
                    {
                        <td>N/A</td>
                    }
                    else
                    {
                        <td><a onclick="viewDataToolCleaningDate(@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["LiftupBoardManualCleaningID"], '@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["LiftupBoardID"]')" href="javascript:">@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["LiftupBoardManualCleaningTime"]</a></td>
                    }
                </tr>
                <tr>
                    <td>Blade ID</td>
                    @if (Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["BladeID"] + "" == "")
                    {
                        <td>N/A</td>
                    }
                    else
                    {
                        <td><a href="javascript:" onclick="viewDataByCompID('@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["BladeID"]', '@(ViewBag.Factory)', '@(ViewBag.ItemName)', '')">@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["BladeID"]</a></td>
                    }
                </tr>
                <tr>
                    <td>Blade maintain time</td>
                    @if (Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["BladeID"] + "" == "")
                    {
                        <td>N/A</td>
                    }
                    else
                    {
                        <td><a onclick="viewDataToolCleaningDate(@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["BladeManualCleaningID"], '@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["BladeID"]')" href="javascript:">@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["BladeManualCleaningTime"]</a></td>
                    }
                </tr>
                @if (ViewBag.Factory == "F4")
                {
                    if (i == 0)
                    {
                        <tr>
                            <td>Partial Punching Loading Suction Jig ID</td>
                            @if (Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["PartialPunchingLoadingSuctionJigID"] + "" == "")
                            {
                                <td>N/A</td>
                            }
                            else
                            {
                                <td><a href="javascript:" onclick="viewDataByCompID('@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["PartialPunchingLoadingSuctionJigID"]', '@(ViewBag.Factory)', '@(ViewBag.ItemName)', '')">@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["PartialPunchingLoadingSuctionJigID"]</a></td>
                            }
                        </tr>
                        <tr>
                            <td>Partial Punching Loading Suction Jig maintain time</td>
                            @if (Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["PartialPunchingLoadingSuctionJigID"] + "" == "")
                            {
                                <td>N/A</td>
                            }
                            else
                            {
                                <td><a onclick="viewDataToolCleaningDate(@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["PartialPunchingLoadingSuctionJigManualCleaningID"], '@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["PartialPunchingLoadingSuctionJigID"]')" href="javascript:">@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["PartialPunchingLoadingSuctionJigManualCleaningTime"]</a></td>
                            }
                        </tr>
                        <tr>
                            <td>Partial Punching Unloading SuctionJig ID</td>
                            @if (Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["PartialPunchingUnloadingSuctionJigID"] + "" == "")
                            {
                                <td>N/A</td>
                            }
                            else
                            {
                                <td><a href="javascript:" onclick="viewDataByCompID('@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["PartialPunchingUnloadingSuctionJigID"]', '@(ViewBag.Factory)', '@(ViewBag.ItemName)', '')">@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["PartialPunchingUnloadingSuctionJigID"]</a></td>
                            }
                        </tr>
                        <tr>
                            <td>Partial Punching Unloading Suction Jig maintain time</td>
                            @if (Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["PartialPunchingUnloadingSuctionJigID"] + "" == "")
                            {
                                <td>N/A</td>
                            }
                            else
                            {
                                <td><a onclick="viewDataToolCleaningDate(@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["PartialPunchingUnloadingSuctionJigManualCleaningID"], '@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["PartialPunchingUnloadingSuctionJigID"]')" href="javascript:">@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["PartialPunchingUnloadingSuctionJigManualCleaningTime"]</a></td>
                            }
                        </tr>
                        <tr>
                            <td>Slide Conveyor Transfer Loading Positioning Jig ID</td>
                            @if (Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["SlideConveyorTransferLoadingPositioningJigID"] + "" == "")
                            {
                                <td>N/A</td>
                            }
                            else
                            {
                                <td><a href="javascript:" onclick="viewDataByCompID('@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["SlideConveyorTransferLoadingPositioningJigID"]', '@(ViewBag.Factory)', '@(ViewBag.ItemName)', '')">@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["SlideConveyorTransferLoadingPositioningJigID"]</a></td>
                            }
                        </tr>
                        <tr>
                            <td>Slide Conveyor Transfer Loading Positioning Jig maintain time</td>
                            @if (Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["SlideConveyorTransferLoadingPositioningJigID"] + "" == "")
                            {
                                <td>N/A</td>
                            }
                            else
                            {
                                <td><a onclick="viewDataToolCleaningDate(@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["SlideConveyorTransferLoadingPositioningJigManualCleaningID"], '@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["SlideConveyorTransferLoadingPositioningJigID"]')" href="javascript:">@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["SlideConveyorTransferLoadingPositioningJigManualCleaningTime"]</a></td>
                            }
                        </tr>
                    }
                    else
                    {
                        <tr>
                            <td>Outline Punching Loading Suction Jig ID</td>
                            @if (Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["OutlinePunchingLoadingSuctionJigID"] + "" == "")
                            {
                                <td>N/A</td>
                            }
                            else
                            {
                                <td><a href="javascript:" onclick="viewDataByCompID('@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["OutlinePunchingLoadingSuctionJigID"]', '@(ViewBag.Factory)', '@(ViewBag.ItemName)', '')">@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["OutlinePunchingLoadingSuctionJigID"]</a></td>
                            }
                        </tr>
                        <tr>
                            <td>Outline Punching Loading Suction Jig maintain time</td>
                            @if (Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["OutlinePunchingLoadingSuctionJigID"] + "" == "")
                            {
                                <td>N/A</td>
                            }
                            else
                            {
                                <td><a onclick="viewDataToolCleaningDate(@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["OutlinePunchingLoadingSuctionJigManualCleaningID"], '@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["OutlinePunchingLoadingSuctionJigID"]')" href="javascript:">@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["OutlinePunchingLoadingSuctionJigManualCleaningTime"]</a></td>
                            }
                        </tr>
                        <tr>
                            <td>Outline Punching Press Suction Jig ID</td>
                            @if (Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["OutlinePunchingPressSuctionJigID"] + "" == "")
                            {
                                <td>N/A</td>
                            }
                            else
                            {
                                <td><a href="javascript:" onclick="viewDataByCompID('@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["OutlinePunchingPressSuctionJigID"]', '@(ViewBag.Factory)', '@(ViewBag.ItemName)', '')">@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["OutlinePunchingPressSuctionJigID"]</a></td>
                            }
                        </tr>
                        <tr>
                            <td>Outline Punching Press Suction Jig maintain time</td>
                            @if (Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["OutlinePunchingPressSuctionJigID"] + "" == "")
                            {
                                <td>N/A</td>
                            }
                            else
                            {
                                <td><a onclick="viewDataToolCleaningDate(@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["OutlinePunchingPressSuctionJigManualCleaningID"], '@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["OutlinePunchingPressSuctionJigID"]')" href="javascript:">@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["OutlinePunchingPressSuctionJigManualCleaningTime"]</a></td>
                            }
                        </tr>
                        <tr>
                            <td>Outline Punching Unloading Positioning Jig ID</td>
                            @if (Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["OutlinePunchingUnloadingPositioningJigID"] + "" == "")
                            {
                                <td>N/A</td>
                            }
                            else
                            {
                                <td><a href="javascript:" onclick="viewDataByCompID('@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["OutlinePunchingUnloadingPositioningJigID"]', '@(ViewBag.Factory)', '@(ViewBag.ItemName)', '')">@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["OutlinePunchingUnloadingPositioningJigID"]</a></td>
                            }
                        </tr>
                        <tr>
                            <td>Outline Punching Unloading Positioning Jig maintain time</td>
                            @if (Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["OutlinePunchingUnloadingPositioningJigID"] + "" == "")
                            {
                                <td>N/A</td>
                            }
                            else
                            {
                                <td><a onclick="viewDataToolCleaningDate(@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["OutlinePunchingUnloadingPositioningJigManualCleaningID"], '@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["OutlinePunchingUnloadingPositioningJigID"]')" href="javascript:">@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["OutlinePunchingUnloadingPositioningJigManualCleaningTime"]</a></td>
                            }
                        </tr>
                    }
                }
                <tr>
                    <td>First piece buyoff control (link)</td>
                    <td>
                        @if (ViewBag.Factory == "F5")
                        {
                            <a onclick="viewDataFirstPieceBuyoffControlNew('@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["MachineID"]','@DataConvert.ConvertToString(ViewBag.IndicationNo)')" href="javascript:">
                                @(Singleton_04_Machine.IFPBCheckingService.FPBChecking_GetResultByMachineID_IndicationNumber(DataConvert.ConvertToString(Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["MachineID"]),DataConvert.ConvertToString(ViewBag.IndicationNo)))
                            </a>
                        }
                        else
                        {
                            <a onclick="viewDataFirstPieceBuyoffControlByTime('@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["MachineID"]','@DataConvert.ToDateTime(Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["DateTime"]).ToString("yyyy-MM-dd HH:mm:ss")', '@DataConvert.ConvertToString(ViewBag.ItemName)')" href="javascript:">
                                @(Singleton_04_Machine.IFPBCheckingService.FPBChecking_GetResultByMachineID_Time(DataConvert.ConvertToString(Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["MachineID"]), DataConvert.ToDateTime(Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["DateTime"]), @DataConvert.ConvertToString(ViewBag.ItemName)))
                            </a>
                        }
                    </td>
                </tr>

                @if (ViewBag.Factory == "F5")
                {
                    <tr>
                        <td>Production condition (link)</td>
                        <td>
                            <a onclick="viewDataProductionCondition(@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["ProductionConditionID"])" href="javascript:">
                                @Singleton_03_Common.ICommon_CommonService.GetProductionContidtionResult(DataConvert.ConvertToString(Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["ProductionConditionID"]),
             DataConvert.ConvertToString(Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["ProductionConditionResult"]))
                            </a>
                        </td>
                    </tr>
                }

                <tr>
                    <td>Machine ID</td>
                    <td>@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["MachineID"]</td>
                </tr>
                <tr>
                    <td>Machine maintenance & repair recording</td>
                    <td><a onclick="viewDataMachineMaintenance(@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["MachineMaintenanceID"])" href="javascript:">@Model.DataFlexAssy.FlexAssy_19_Punching.Rows[i]["MachineMaintenanceDate"]</a></td>
                </tr>
                <tr>
                    <td>Temp/Humidity/Clearness Dashboard Link</td>
                    <td>
                        <a target="_blank" href="@Common.getCleanlink(ViewBag.Factory,1)">
                            @Common.getCleanlink(ViewBag.Factory, 1)
                        </a>
                    </td>
                </tr>
            </tbody>
        </table>
    }
}

@if (Model != null && Model.DataFlexAssy.FlexAssy_19_PunchingLinking_PunDataManual?.Rows.Count > 0)
{
    <h2 class="bd-title" id="19-punching-linking-product">Punching Linking Of Product</h2>
    <table class="table table-bordered">
        <thead>
            <tr>
                <th scope="col">Control Item</th>
                <th scope="col">Control Value</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>ItemName</td>
                <td>@(ViewBag.ItemName)</td>
            </tr>
            <tr>
                <td>ProductID</td>
                <td>@Model.DataFlexAssy.FlexAssy_19_PunchingLinking_PunDataManual.Rows[0]["ProductID"]</td>
            </tr>
            <tr>
                <td>BlockID</td>
                <td>@Model.DataFlexAssy.FlexAssy_19_PunchingLinking_PunDataManual.Rows[0]["BlockID"]</td>
            </tr>
            <tr>
                <td>Location</td>
                <td>@Model.DataFlexAssy.FlexAssy_19_PunchingLinking_PunDataManual.Rows[0]["Location"]</td>
            </tr>
            <tr>
                <td>DateTime</td>
                <td>@Model.DataFlexAssy.FlexAssy_19_PunchingLinking_PunDataManual.Rows[0]["CreatedDate"]</td>
            </tr>
            <tr>
                <td>Operator ID</td>
                <td>@Model.DataFlexAssy.FlexAssy_19_PunchingLinking_PunDataManual.Rows[0]["OperatorID"]</td>
            </tr>

        </tbody>
    </table>
    <br />

}