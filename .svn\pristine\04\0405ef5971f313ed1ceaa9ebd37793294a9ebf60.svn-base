﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
namespace Trace_AbilitySystem.Libs.Dataconnect
{
    public class SqlExecute : DbExecute
    {
        private readonly string _connectionStringF1;
        private readonly string _connectionStringPismo;
        private readonly string _connectionStringPismoTest;
        private readonly string _connectionStringCommonMasterTest;

        private readonly string _connectionStringPismoCommon;
        private readonly string _connectionStringF3;
        private readonly string _connectionStringF4;
        private readonly string _connectionStringF5;
        private readonly string _connectionStringSPC_F3;
        private readonly string _connectionStringSPC_F4;
        private readonly string _connectionStringNewSPC_F4;
        private readonly string _connectionStringNewSPC_F3;
        private readonly string _connectionStringICTData_F4;
        private readonly string _connectionStringICTData_F3;
        private readonly string _connectionStringLocal;
        private readonly string _connectionStringLocal2;
        private readonly string _connectionStringCommon;
        private readonly string _connectionStringMachine;
        private readonly string _connectionStringErpData;
        private readonly string _connectionStringSMES;

        private readonly string _connectionStringLocalMail_F3;
        private readonly string _connectionStringLocalMail_F4;

        private readonly string _connectionStringLocalShopFloor_F3;
        private readonly string _connectionStringLocalShopFloor_F4;
        private readonly string _connectionStringLocalShopFloor_F5;
        private readonly string _ConnectionStringBroadRegF5;

        private SqlConnection _cnn;
        private object updateStatusConnectionLock = new object();
        public SqlExecute()
        {
            _connectionStringF1 = ConfigurationManager.ConnectionStrings["ConnectionStringF1"]?.ConnectionString;
            _connectionStringPismo = ConfigurationManager.ConnectionStrings["ConnectionStringPismo"]?.ConnectionString;
            _connectionStringPismoTest = ConfigurationManager.ConnectionStrings["ConnectionStringTest"]?.ConnectionString;
            _connectionStringCommonMasterTest = ConfigurationManager.ConnectionStrings["ConnectionStringCommonMaster"]?.ConnectionString;

            _connectionStringPismoCommon = ConfigurationManager.ConnectionStrings["ConnectionStringPismoCommon"]?.ConnectionString;
            _connectionStringF3 = ConfigurationManager.ConnectionStrings["ConnectionStringF3"]?.ConnectionString;
            _connectionStringF4 = ConfigurationManager.ConnectionStrings["ConnectionStringF4"]?.ConnectionString;
            _connectionStringF5 = ConfigurationManager.ConnectionStrings["ConnectionStringF5"]?.ConnectionString;
            _connectionStringLocal = ConfigurationManager.ConnectionStrings["ConnectionStringLocal"]?.ConnectionString;
            _connectionStringLocal2 = ConfigurationManager.ConnectionStrings["ConnectionStringLocal2"]?.ConnectionString;
            _connectionStringCommon = ConfigurationManager.ConnectionStrings["ConnectionStringCommon"]?.ConnectionString;
            _connectionStringMachine = ConfigurationManager.ConnectionStrings["ConnectionStringMachine"]?.ConnectionString;
            _connectionStringErpData = ConfigurationManager.ConnectionStrings["ConnectionStringErpData"]?.ConnectionString;
            _connectionStringSMES = ConfigurationManager.ConnectionStrings["ConnectionStringSMES"]?.ConnectionString;
            _connectionStringLocalMail_F3 = ConfigurationManager.ConnectionStrings["ConnectionStringLocalMail_F3"]?.ConnectionString;
            _connectionStringLocalMail_F4 = ConfigurationManager.ConnectionStrings["ConnectionStringLocalMail_F4"]?.ConnectionString;
            _connectionStringSPC_F3 = ConfigurationManager.ConnectionStrings["ConnectionStringSPCF3"]?.ConnectionString;
            _connectionStringSPC_F4 = ConfigurationManager.ConnectionStrings["ConnectionStringSPCF4"]?.ConnectionString;
            _connectionStringNewSPC_F4 = ConfigurationManager.ConnectionStrings["ConnectionStringNewSPCF4"]?.ConnectionString;
            _connectionStringNewSPC_F3 = ConfigurationManager.ConnectionStrings["ConnectionStringNewSPCF3"]?.ConnectionString;
            _connectionStringICTData_F4 = ConfigurationManager.ConnectionStrings["ConnectionStringICTDataF4"]?.ConnectionString;
            _connectionStringICTData_F3 = ConfigurationManager.ConnectionStrings["ConnectionStringICTDataF3"]?.ConnectionString;
            _ConnectionStringBroadRegF5 = ConfigurationManager.ConnectionStrings["ConnectionStringBroadRegF5"]?.ConnectionString;


            _connectionStringLocalShopFloor_F3 = ConfigurationManager.ConnectionStrings["ConnectionStringLocalShopFloor_F3"]?.ConnectionString;
            _connectionStringLocalShopFloor_F4 = ConfigurationManager.ConnectionStrings["ConnectionStringLocalShopFloor_F4"]?.ConnectionString;
            _connectionStringLocalShopFloor_F5 = ConfigurationManager.ConnectionStrings["ConnectionStringLocalShopFloor_F5"]?.ConnectionString;
        }

        public override DataTable Execute_Table(string text, SqlParameter[] paras, CommandType cmdType, string connectionStringOption)
        {
            lock (updateStatusConnectionLock)
            {
                if (connectionStringOption == null) return new DataTable();
                if (ConnectString(connectionStringOption) == null) return new DataTable();
                using (_cnn = new SqlConnection(ConnectString(connectionStringOption)))
                {
                    try
                    {
                        if (_cnn.State != ConnectionState.Open)
                        {
                            _cnn.Open();
                        }
                        //DateTime dt1 = DateTime.Now;
                        SqlCommand cmd = new SqlCommand(text, _cnn) { CommandType = cmdType };
                        if (paras != null)
                            cmd.Parameters.AddRange(paras);
                        DataTable table = new DataTable();
                        table.Load(cmd.ExecuteReader());
                        //DateTime dt2 = DateTime.Now;
                        //ManageLog.WriteErrorApp(text +":"+ (dt2-dt1).TotalMilliseconds.ToString() + " ms");
                        return table;
                    }
                    catch (Exception ex)
                    {
                        ManageLog.WriteErrorApp(ex.ToString() + "\n" + text + "\n" + connectionStringOption);
                        ManageLog.WriteErrorWeb(ex.ToString() + "\n" + text + "\n" + connectionStringOption);
                        return null;
                    }
                    finally
                    {
                        _cnn.Close();
                    }
                }
            }
        }

        public override int Execute_Modify_Bulk(DataTable dt, string TableName, string connectionStringOption)
        {
            try
            {
                if (dt == null || dt.Rows.Count == 0)
                    return 1;

                using (var connection = new SqlConnection(ConnectString(connectionStringOption)))
                {
                    connection.Open();
                    using (var bulkCopy = new SqlBulkCopy(connection, SqlBulkCopyOptions.CheckConstraints, null))
                    {
                        bulkCopy.DestinationTableName = TableName;

                        foreach (DataColumn column in dt.Columns)
                        {
                            bulkCopy.ColumnMappings.Add(column.ColumnName, column.ColumnName);
                        }

                        bulkCopy.WriteToServer(dt);
                    }
                }
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp(ex.ToString() + "\n" + connectionStringOption);
                return -9;
            }
            return 1;
        }
        public override int Execute_Modify(string text, SqlParameter[] paras, CommandType cmdType, string connectionStringOption)
        {
            lock (updateStatusConnectionLock)
            {
                using (_cnn = new SqlConnection(ConnectString(connectionStringOption)))
                {
                    try
                    {
                        if (_cnn.State != ConnectionState.Open)
                        {
                            _cnn.Open();
                        }
                        //SqlCommand cmd = new SqlCommand(text, _cnn)
                        //{
                        //    CommandType = cmdType
                        //};
                        //if (paras != null)
                        //    cmd.Parameters.AddRange(paras);
                        //_cnn.Open();
                        SqlCommand cmd = new SqlCommand();

                        // cmd.CommandType=
                        cmd.Connection = _cnn;
                        cmd.CommandType = cmdType;
                        cmd.CommandText = text;
                        cmd.CommandTimeout = 9999;
                        if (paras != null)
                            cmd.Parameters.AddRange(paras);
                        return cmd.ExecuteNonQuery();
                    }
                    catch (Exception ex)
                    {
                        ManageLog.WriteErrorApp(ex.ToString() + "\n" + text + "\n" + connectionStringOption);
                        // Not connect
                        return -9;
                    }
                    finally
                    {
                        _cnn.Close();
                    }
                }
            }
        }

        public override int Execute_Scalar(string text, SqlParameter[] paras, CommandType cmdType, string connectionStringOption)
        {

            lock (updateStatusConnectionLock)
            {
                using (_cnn = new SqlConnection(ConnectString(connectionStringOption)))
                {
                    try
                    {
                        SqlCommand cmd = new SqlCommand(text, _cnn) { CommandType = cmdType };
                        if (paras != null)
                            cmd.Parameters.AddRange(paras);
                        _cnn.Open();
                        return DataConvert.ConvertToInt(cmd.ExecuteScalar());
                    }
                    catch (Exception ex)
                    {
                        ManageLog.WriteErrorApp(ex.ToString() + "\n" + text + "\n" + connectionStringOption);
                        return -9;
                    }
                    finally
                    {
                        _cnn.Close();
                    }
                }
            }
        }
        public override decimal Execute_DecimalScalar(string text, SqlParameter[] paras, CommandType cmdType, string connectionStringOption)
        {
            lock (updateStatusConnectionLock)
            {
                using (_cnn = new SqlConnection(ConnectString(connectionStringOption)))
                {
                    try
                    {
                        SqlCommand cmd = new SqlCommand(text, _cnn) { CommandType = cmdType };
                        if (paras != null)
                            cmd.Parameters.AddRange(paras);
                        _cnn.Open();
                        return DataConvert.ConvertToDecimal(cmd.ExecuteScalar());
                    }
                    catch (Exception ex)
                    {
                        ManageLog.WriteErrorApp(ex.ToString() + "\n" + text + "\n" + connectionStringOption);
                        return -9;
                    }
                    finally
                    {
                        _cnn.Close();
                        _cnn.Dispose();
                    }
                }
            }
        }

        public string ConnectString(string connectionStringOption)
        {
            string strConnectString;
            switch (connectionStringOption)
            {
                case "F1":
                    strConnectString = _connectionStringF1;
                    break;
                case "Pismo":
                    strConnectString = _connectionStringPismo;
                    break;
                case "PismoTest":
                    strConnectString = _connectionStringPismoTest;
                    break;
                case "CommonMaster":
                    strConnectString = _connectionStringCommonMasterTest;
                    break;
                case "PismoCommon":
                    strConnectString = _connectionStringPismoCommon;
                    break;
                case "F3":
                    strConnectString = _connectionStringF3;
                    break;
                case "F4":
                    strConnectString = _connectionStringF4;
                    break;
                case "F5":
                    strConnectString = _connectionStringF5;
                    break;
                case "SPC_F3":
                    strConnectString = _connectionStringSPC_F3;
                    break;
                case "SPC_F4":
                    strConnectString = _connectionStringSPC_F4;
                    break;
                case "NewSPC_F4":
                    strConnectString = _connectionStringNewSPC_F4;
                    break;
                case "NewSPC_F3":
                    strConnectString = _connectionStringNewSPC_F3;
                    break;
                case "ICTData_F4":
                    strConnectString = _connectionStringICTData_F4;
                    break;
                case "ICTData_F3":
                    strConnectString = _connectionStringICTData_F3;
                    break;
                case "Local":
                    strConnectString = _connectionStringLocal;
                    break;
                case "LocalMail_F3":
                    strConnectString = _connectionStringLocalMail_F3;
                    break;
                case "LocalMail_F4":
                    strConnectString = _connectionStringLocalMail_F4;
                    break;
                case "Local2":
                    strConnectString = _connectionStringLocal2;
                    break;
                case "Common":
                    strConnectString = _connectionStringCommon;
                    break;
                case "Machine":
                    strConnectString = _connectionStringMachine;
                    break;
                case "ErpData":
                    strConnectString = _connectionStringErpData;
                    break;
                case "LocalShopFloor_F3":
                    strConnectString = _connectionStringLocalShopFloor_F3;
                    break;
                case "LocalShopFloor_F4":
                    strConnectString = _connectionStringLocalShopFloor_F4;
                    break;
                case "LocalShopFloor_F5":
                    strConnectString = _connectionStringLocalShopFloor_F5;
                    break;
                case "BroadRegF5":
                    strConnectString = _ConnectionStringBroadRegF5;
                    break;
                case "ConnectionStringSMES":
                    strConnectString = _connectionStringSMES;
                    break;
                default:
                    strConnectString = connectionStringOption;
                    break;
            }

            return strConnectString;
        }
    }
}