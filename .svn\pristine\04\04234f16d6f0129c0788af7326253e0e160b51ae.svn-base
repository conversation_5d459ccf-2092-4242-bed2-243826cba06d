﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trace_AbilitySystem.Libs.ITrace_07_Pismo_Auto_Service
{
    public interface IFlexAssy_06_CognexService2025
    {
        Task<int> FlexAssy_Cognex_ProcessData2025(string factory, DateTime createdDateSearch, int minutes, string connectionStringOption);
        DataTable Cognex_GetDataWithCreatedDate2025(DateTime createdDate, int minutes, string connectionStringOption);
        Task<int> FlexAssy_Cognex_Detail_ProcessData2025(string factory, DateTime createdDateSearch, int minutes, string connectionStringOption);
        int FlexAssy_Cognex_Insert2025(int PKID, DateTime dateTime, string BlockID, string ProgramName, string PanelID, string GradePanelID, string MachineIDLaser, string FileName, string ErrorMap, string LotNumber, string PanelNumber, string ItemName, string MachineID, string MachineType, string TableLaser, string Side, string ErrorMapConvert, string FullName, bool SyncPcsID, string OperatorID, string connectionStringOption);
        int FlexAssy_Cognex_Detail_Insert2025(int PKID, int CognexPkid, int Location, DateTime? CreatedDate, string ProductID, string Result, string LocationPanel, string CavityNumber, int LocationInkJet, string GradeLevel, string DefectCode, string DefectDescription, string connectionStringOption);
        DataTable Cognex_Detail_GetDataWithCreatedDate2025(DateTime createdDate, int minutes, string connectionStringOption);
        DataTable FlexAssy_06_Cognex_GetByProductID(string productID, string connectionStringOption);
        DataTable FlexAssy_06_Cognex_GetByBlockID(string blockID, string connectionStringOption);
        DataTable FlexAssy_06_Cognex_GetByListBlockID(DataTable listBlockID, string connectionStringOption, out string connectionStringOk);
    }
}
