﻿using System;
using System.Data;

namespace Trace_AbilitySystem.Libs.ITrace_01_BareFlex_Services
{
    public interface IBareFlex_07_8_1_LayupService
    {
        int BareFlex_07_8_1_Layup_Insert(string workOrder, DateTime dateTime, string productName, string machineID, string operatorID, DateTime? firstPieceBuyoffControlDate, int? firstPieceBuyoffControlID, DateTime? machineMaintenanceDate, int? machineMaintenanceID);
        DataTable BareFlex_07_8_1_Layup_GetByWorkOrder(string workOrder);
        DataTable BareFlex_07_8_1_Layup_GetByListWorkOrder(DataTable listWorkOrder);
    }
}