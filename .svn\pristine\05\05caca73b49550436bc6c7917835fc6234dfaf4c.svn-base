﻿@using System.Data;
@using Trace_AbilitySystem.Libs.DTOClass;
@model Trace_AbilitySystem.Libs.DTOClass.DataAllTrace
@{
    DataTable dtStageActiveBareFlex = (DataTable)ViewBag.StageActiveBareFlex;
    ExportStage exportStage = (ExportStage)ViewBag.ExportStage;
    SequencyOfStageModels sequencyOfStageModels = (SequencyOfStageModels)ViewBag.SequencyOfStageModels;
}
@if (Model != null)
{
    <h2 class="bd-title" id="" style="font-weight: bold; font-size: 20px; color: #0081cc;margin-top:50px; text-decoration:none">I. BARE FLEX</h2>
    <hr />
}
@(Html.Partial("~/Views/BareFlex/ProcessBlock_Data.cshtml"))


@if (Model != null)
{
    <script type="text/javascript">
        $(document).ready(function () {
            try {
                var AreaSelect = window.localStorage.getItem("AreaSelect");
                if (AreaSelect != undefined) {
                    if (AreaSelect.includes("#")) {
                        var Item = AreaSelect + "anchor"
                        window.document.getElementById(Item).classList.add("anchorActive");
                        element = document.getElementById(AreaSelect.replace('#', ''));
                        var positiony = element.offsetTop - element.scrollTop;
                        $('html, body').animate({
                            scrollTop: positiony
                        }, 200);
                    }
                }
            }
            catch (err) {
                console.log(err.message);
            }
        });
    </script>
}