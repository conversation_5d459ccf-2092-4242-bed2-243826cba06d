﻿using System.Data;
using Trace_AbilitySystem.Libs.Dataconnect;
using Trace_AbilitySystem.Libs.ITrace_02_FlexAssy_Services;

namespace Trace_AbilitySystem.Libs.Trace_02_FlexAssy_Services
{
    public class Source_FlexAssy_AOI_F4Service : ISource_FlexAssy_AOI_F4Service
    {
        private readonly DbExecute _db;

        public Source_FlexAssy_AOI_F4Service()
        {
            _db = new SqlExecute();
        }

        public DataTable KY_AOIDB_TB_AOIPCB_GetByPcbID(string pcbID, string connectionStringOption)
        {
            string sql = "SELECT TOP(1) PCBGUID, ResultDBName, ImageDBName FROM TB_AOIPCB WHERE BarCode = '" + pcbID + "'";
            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }

        public DataTable KY_Result_Week_TB_AOIDefect_GetByPCBGUID(string pcbGUID, string connectionStringOption)
        {
            string sql = "SELECT ComponentGUID FROM TB_AOIDefect WHERE PCBGUID = '" + pcbGUID + "'";
            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }

        public DataTable KY_Image_Week_TB_Image2D_GetByComponentGUID(string comonentGUID, string connectionStringOption)
        {
            string sql = "SELECT Image2D FROM TB_Image2D WHERE ComponentGUID = '" + comonentGUID + "'";
            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }

        public DataTable TB_AOIResult_GetByBarCode(string barcode, string connectionStringOption)
        {
            string sql = "SELECT TOP(1) PCBGUID, StartDateTime FROM TB_AOIResult WHERE BarCode = '" + barcode + "' ORDER BY StartDateTime DESC";
            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }

        public int TB_AOIPanel_CountWithPCBGUID(string pcbGUID, string connectionStringOption)
        {
            string sql = "SELECT COUNT(*) FROM TB_AOIPanel WHERE PCBGUID = '" + pcbGUID + "' AND PanelBadMarkType = 0";
            return _db.Execute_Scalar(sql, null, CommandType.Text, connectionStringOption);
        }
    }
}