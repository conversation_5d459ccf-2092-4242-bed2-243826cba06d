﻿@using System.Data;
@using System.Configuration;
@using Trace_AbilitySystem.Libs
@model Trace_AbilitySystem.Libs.DTOClass.DataAllTrace

@if (Model != null && Model.DataFlexAssy.FlexAssy_25_AVI?.Rows.Count > 0)
{
    //int index = Model.DataFlexAssy.FlexAssy_25_AVI.Rows.Count - 1;
    int index = 0;
    <h2 class="bd-title" id="25-avi">AVI</h2>
    <table class="table table-bordered">
        <thead>
            <tr>
                <th scope="col">Control Item</th>
                <th scope="col">Control Value</th>
            </tr>
        </thead>
		<tbody>
			<tr>
				<td>ItemName</td>
				<td>@Model.DataFlexAssy.FlexAssy_25_AVI.Rows[index]["ItemName"]</td>
			</tr>
			<tr>
				<td>IndicationNo</td>
				<td>@Model.DataFlexAssy.FlexAssy_25_AVI.Rows[index]["IndicationNumber"]</td>
			</tr>
			<tr>
				<td>PcsID</td>
				<td>@Model.DataFlexAssy.FlexAssy_25_AVI.Rows[index]["ProductID"]</td>
			</tr>
			<tr>
				<td>DateTime</td>
				<td>@Model.DataFlexAssy.FlexAssy_25_AVI.Rows[index]["DateTime"]</td>
			</tr>
			<tr>
				<td>ItemCode</td>
				<td>@Model.DataFlexAssy.FlexAssy_25_AVI.Rows[index]["ItemCode"]</td>
			</tr>
			<tr>
				<td>AVI MachineID</td>
				<td>@Model.DataFlexAssy.FlexAssy_25_AVI.Rows[index]["MachineID"]</td>
			</tr>
			<tr>
				<td>ProgramName</td>
				<td>@Model.DataFlexAssy.FlexAssy_25_AVI.Rows[index]["ProgramName"]</td>
			</tr>
			<tr>
				<td>Result</td>
				<td>@Model.DataFlexAssy.FlexAssy_25_AVI.Rows[index]["Result"]</td>
			</tr>
			<tr>
				<td>LaneID</td>
				<td>@Model.DataFlexAssy.FlexAssy_25_AVI.Rows[index]["LaneID"]</td>
			</tr>
			<tr>
				<td>JigID</td>
				<td>@Model.DataFlexAssy.FlexAssy_25_AVI.Rows[index]["JigID"]</td>
			</tr>
			<tr>
				<td>LoaderHand</td>
				<td>@Model.DataFlexAssy.FlexAssy_25_AVI.Rows[index]["LoaderHand"]</td>
			</tr>
			<tr>
				<td>LoaderBuffer</td>
				<td>@Model.DataFlexAssy.FlexAssy_25_AVI.Rows[index]["LoaderBuffer"]</td>
			</tr>
			<tr>
				<td>UnLoaderHand</td>
				<td>@Model.DataFlexAssy.FlexAssy_25_AVI.Rows[index]["UnLoaderHand"]</td>
			</tr>
			<tr>
				<td>UnLoaderBuffer</td>
				<td>@Model.DataFlexAssy.FlexAssy_25_AVI.Rows[index]["UnLoaderBuffer"]</td>
			</tr>
			<tr>
				<td>Location</td>
				<td>@Model.DataFlexAssy.FlexAssy_25_AVI.Rows[index]["PositionIndex"]</td>
			</tr>
			<tr>
				<td>DefectCode</td>
				<td>@Model.DataFlexAssy.FlexAssy_25_AVI.Rows[index]["DefectCode"]</td>
			</tr>
			@if (Model.DataFlexAssy.FlexAssy_25_AVI.Rows[index]["AVIType"] + "" == "VRS")
			{
				<tr>
					<td>VRSByPicture</td>
					<td>@Model.DataFlexAssy.FlexAssy_25_AVI.Rows[index]["strVRSByPicture"]</td>
				</tr>
				<tr>
					<td>VRSByCCD</td>
					<td>@Model.DataFlexAssy.FlexAssy_25_AVI.Rows[index]["strVRSByCCD"]</td>
				</tr>
				<tr>
					<td>VRSByMS</td>
					<td>@Model.DataFlexAssy.FlexAssy_25_AVI.Rows[index]["strVRSByMS"]</td>
				</tr>
			}
			else
			{

				<tr>
					<td>VRSByPicture</td>
					<td>N/A</td>
				</tr>
				<tr>
					<td>VRSByCCD</td>
					<td>N/A</td>
				</tr>
				<tr>
					<td>VRSByMS</td>
					<td>N/A</td>
				</tr>
			}
			<tr>
				<td>Golden sample checking record</td>
				@{
					string strMs = Model.DataFlexAssy.FlexAssy_25_AVI.Rows[index]["MS_Result"] + " (" + Model.DataFlexAssy.FlexAssy_25_AVI.Rows[index]["MS_Time"] + ")";
				}
				@if (Model.DataFlexAssy.FlexAssy_25_AVI.Rows[index]["MS_Result"] + "" == "")
				{
					<td></td>
				}
				else
				{
					<td>@strMs</td>
				}
			</tr>
			<tr>
				<td>OperatorID</td>
				<td>@Model.DataFlexAssy.FlexAssy_25_AVI.Rows[index]["OperatorID"]</td>
			</tr>
			<tr>
				<td>First piece buyoff control (link)</td>
				<td>
					@if (ViewBag.Factory == "F5")
					{
						<a onclick="viewDataFirstPieceBuyoffControlNew('@Model.DataFlexAssy.FlexAssy_25_AVI.Rows[index]["MachineID"]','@DataConvert.ConvertToString(ViewBag.IndicationNo)')" href="javascript:">
							@(Singleton_04_Machine.IFPBCheckingService.FPBChecking_GetResultByMachineID_IndicationNumber(DataConvert.ConvertToString(Model.DataFlexAssy.FlexAssy_25_AVI.Rows[index]["MachineID"]),DataConvert.ConvertToString(ViewBag.IndicationNo)))
						</a>
					}
					else
					{
						<a onclick="viewDataFirstPieceBuyoffControlByTime('@Model.DataFlexAssy.FlexAssy_25_AVI.Rows[index]["MachineID"]','@DataConvert.ToDateTime(Model.DataFlexAssy.FlexAssy_25_AVI.Rows[index]["DateTime"]).ToString("yyyy-MM-dd HH:mm:ss")', '@DataConvert.ConvertToString(Model.DataFlexAssy.FlexAssy_25_AVI.Rows[index]["ItemName"])')" href="javascript:">
							@(Singleton_04_Machine.IFPBCheckingService.FPBChecking_GetResultByMachineID_Time(DataConvert.ConvertToString(Model.DataFlexAssy.FlexAssy_25_AVI.Rows[index]["MachineID"]), DataConvert.ToDateTime(Model.DataFlexAssy.FlexAssy_25_AVI.Rows[index]["DateTime"]), @DataConvert.ConvertToString(Model.DataFlexAssy.FlexAssy_25_AVI.Rows[index]["ItemName"])))
						</a>
					}
				</td>
			</tr>
			@{
				DataTable dtPdt = Singleton_04_Machine.ItProductionControlService.tProductionControl_GetIDs(
				Model.DataFlexAssy.FlexAssy_25_AVI.Rows[index]["MachineID"].ToString(),
				Model.DataFlexAssy.FlexAssy_25_AVI.Rows[index]["ProgramName"].ToString(),
				Convert.ToDateTime(Model.DataFlexAssy.FlexAssy_25_AVI.Rows[index]["DateTime"]));
			}
			<tr>
				<td>Production condition (link)</td>
				<td>
					<a onclick="viewDataProductionCondition(@Model.DataFlexAssy.FlexAssy_25_AVI.Rows[index]["ProductionConditionID"])" href="javascript:">
						@Singleton_03_Common.ICommon_CommonService.GetProductionContidtionResult(DataConvert.ConvertToString(Model.DataFlexAssy.FlexAssy_25_AVI.Rows[index]["ProductionConditionID"]),
						DataConvert.ConvertToString(Model.DataFlexAssy.FlexAssy_25_AVI.Rows[index]["ProductionConditionResult"]))
					</a>
				</td>
			</tr>
			<tr>
				<td>Machine maintenance date (link)</td>
				<td><a onclick="viewDataMachineMaintenance(@Model.DataFlexAssy.FlexAssy_25_AVI.Rows[index]["MachineMaintenanceID"])" href="javascript:">@Model.DataFlexAssy.FlexAssy_25_AVI.Rows[index]["MachineMaintenanceDate"]</a></td>
			</tr>
			@if (!string.IsNullOrEmpty(@Model.DataFlexAssy.FlexAssy_25_AVI.Rows[index]["FileNameServer"].ToString()))
			{
				<tr>
					<td>Log File of pcs</td>
					<td><a target="_blank" href="@($"{ConfigurationManager.AppSettings["Log_Address"]}{@Model.DataFlexAssy.FlexAssy_25_AVI.Rows[index]["FileNameServer"].ToString()}")">@($"{ConfigurationManager.AppSettings["Log_Address"]}{@Model.DataFlexAssy.FlexAssy_25_AVI.Rows[index]["FileNameServer"].ToString()}")</a></td>
				</tr>
			}
			else
			{
				<tr>
					<td>Log File of pcs</td>
					<td>
					</td>
				</tr>
			}
			<tr>
				<td>History</td>
				<td><a onclick="View_AVI_History('@Model.DataFlexAssy.FlexAssy_25_AVI.Rows[index]["ProductID"]')" href="javascript:">View History</a></td>
			</tr>
			<tr>
				<td>Temp/Humidity/Clearness Dashboard Link</td>
				<td>
					<a target="_blank" href="@Common.getCleanlink(ViewBag.Factory,2)">
						@Common.getCleanlink(ViewBag.Factory, 2)
					</a>
				</td>
			</tr>
		</tbody>
    </table>
}
