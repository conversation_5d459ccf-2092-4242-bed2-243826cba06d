﻿using System;
using System.Data;
using System.Threading.Tasks;

namespace Trace_AbilitySystem.Libs.ITrace_02_FlexAssy_Services
{
    public interface IFlexAssy_20_Sorting_DetailService
    {
        Task<int> FlexAssy_20_Sorting_Detail_ProcessData(string factory, DateTime createdDateSearch, int minutes, string connectionStringOption);
        DateTime? FlexAssy_20_Sorting_Detail_DataSyn(DateTime dateTime,int Minute, string connectionStringOption);
        DataTable FlexAssy_20_Sorting_Detail_GetByProductID(string pcsID, string connectionStringOption, out string connectionStringOk);
        DataTable FlexAssy_20_Sorting_Detail_GetByListProductID(DataTable pcsID, string connectionStringOption, out string connectionStringOk);
    }
}