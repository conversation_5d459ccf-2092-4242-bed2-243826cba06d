﻿using System;
using System.Windows.Forms;
using Trace_AbilitySystem.Libs;
using BlockNG;
using System.Configuration;

namespace Trace_AblilitySystem_Flexssy_Interface_IPQC_Define
{
    public partial class frmLogin : Form
    {
        private string _connectionStringMain;
        public frmLogin()
        {
            InitializeComponent();
            _connectionStringMain = ConfigurationManager.ConnectionStrings["ConnectionStringBoardRegistration_F1"].ConnectionString;
        }

        private void btLogIn_Click(object sender, EventArgs e)
        {
            //this.Hide();
            //frmMain frmt = new frmMain();
            //DialogResult rest = frmt.ShowDialog();
            //if (rest == DialogResult.OK || rest == DialogResult.Cancel)
            //{
            //    this.Show();
            //    this.tbPassword.Clear();
            //}
            //return;

            if (tbUsername.Text.Trim() == string.Empty)
            {
                MessageBox.Show("Bạn chưa nhập username");
                return;
            }
            if (tbPassword.Text.Trim() == string.Empty)
            {
                MessageBox.Show("Bạn chưa nhập password");
                return;
            }
            Account acc = SingletonLocal.AccountService.GetByOperatorIDAndPassWord(tbUsername.Text.Trim(), 
                Common.Md5Endcoding(tbPassword.Text.Trim()), _connectionStringMain);
            if (acc != null)
            {
                this.Hide();
                MySharedInfo.CurrentUser = acc;
                frmMain frm = new frmMain();
                DialogResult res = frm.ShowDialog();
                if (res == DialogResult.OK || res == DialogResult.Cancel)
                {
                    this.Show();
                    this.tbPassword.Clear();
                }
            }
            else
            {
                MessageBox.Show("Tài khoản đăng nhập không hợp lệ, vui lòng kiểm tra lại.");
                return;
            }                
        }

        private void tbUsername_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                if (tbUsername.Text.Trim() == string.Empty)
                {
                    MessageBox.Show("Bạn chưa nhập username");
                    return;
                }
                tbPassword.Focus();
            }
        }

        private void tbPassword_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                if (tbPassword.Text.Trim() == string.Empty)
                {
                    MessageBox.Show("Bạn chưa nhập password");
                    return;
                }
                else
                {
                    btLogIn.PerformClick();
                }
            }
        }

        private void frmLogin_Load(object sender, EventArgs e)
        {
        }
    }
}
