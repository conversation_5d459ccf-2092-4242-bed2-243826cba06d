﻿using System;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using System.Windows.Forms;
using Trace_AbilitySystem.Libs.Dataconnect;
using Trace_AbilitySystem.Libs.ITrace_07_Pismo_Service;


namespace Trace_AbilitySystem.Libs.Trace_07_Pismo_Services
{
    public class FlexAssy_ItemlotServices : IFlexAssy_ItemlotService
    {
        private readonly DbExecute _db;
        private readonly string connectionStringOption = "F1";

        public FlexAssy_ItemlotServices()
        {
            _db = new SqlExecute();
        }
        public async Task FlexAssy_WorkOrder_ProcessData(string factory, DateTime createdDateSearch, string connectionStringOption)
        {
            try
            {
                DateTime dateNow = DateTime.Now;
                DateTime timeLast = createdDateSearch;
                int recordNeedSyn = 0;
                int recordSyned = 0;

                DataTable dt = Singleton_07_Pismo.IFlexAssy_ItemlotService.Itemlot_GetDataWithCreatedDate(timeLast, connectionStringOption);
                if (dt == null)
                    return;

                if (dt.Rows.Count > 0)
                {
                    recordNeedSyn = dt.Rows.Count;
                    for (int i = 0; i < recordNeedSyn; i++)
                    {
                        int itemlotpkid =int.Parse(dt.Rows[i]["PKID"]+ "");
                        int itemnamepkid = int.Parse(dt.Rows[i]["ItemNamePkid"]+ "");
                        int itemcodepkid = int.Parse(dt.Rows[i]["ItemCodePkid"]+ "");
                        DateTime dateTime = Convert.ToDateTime(dt.Rows[i]["CreateDate"]);
                        string itemName = dt.Rows[i]["ItemName"]+ "";
                        string itemCode = dt.Rows[i]["ItemCode"]+ "";
                        string itemLot = dt.Rows[i]["ItemLot"]+ "";
                        int operatorPkid =int.Parse(dt.Rows[i]["OperatorPkid"]+ "");

                        int rs = FlexAssy_Itemlot_Insert(itemlotpkid,itemnamepkid,itemcodepkid, dateTime, itemName, itemCode, itemLot, operatorPkid, "Pismo");
                        if (rs == -9)
                            break;

                        timeLast = Convert.ToDateTime(dt.Rows[i]["CreateDate"]);
                        recordSyned++;
                    }

                    // Update ValueSearch
                    Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, "Pismo_FlexAssy_Itemlot", timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff"));
                }

                ManageLog.WriteProcess(true, null, recordNeedSyn, recordSyned);
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp("FlexAssy_WorkOrder" + "\n" + ex.Message);
            }

            await Task.Delay(500);
        }
        public int FlexAssy_Itemlot_Insert(int itemlotpkid,int itemnamepkid,int itemcodepkid, DateTime dateTime, string itemName, string itemCode, string itemLot,int operatorPkid, string connectionStringOption)
        {
            var paras = new SqlParameter[8];
            paras[0] = new SqlParameter("@ItemlotPkid", itemlotpkid);
            paras[1] = new SqlParameter("@ItemNamePkid", itemnamepkid);
            paras[2] = new SqlParameter("@ItemCodePkid", itemcodepkid);
            paras[3] = new SqlParameter("@DateTime", dateTime);
            paras[4] = new SqlParameter("@ItemName", itemName);
            paras[5] = new SqlParameter("@ItemCode", itemCode);
            paras[6] = new SqlParameter("@ItemLot", itemLot);
            paras[7] = new SqlParameter("@OperatorPkid", operatorPkid);

            return _db.Execute_Modify("_FlexAssy_Itemlot_Insert", paras, CommandType.StoredProcedure, connectionStringOption);
        }
        public DataTable Itemlot_GetDataWithCreatedDate(DateTime createdDate, string connectionStringOption)
        {
            var paras = new SqlParameter[1];
            paras[0] = new SqlParameter("@CreatedDate", createdDate);
            return _db.Execute_Table("sms_FlexAssy_Itemlot_GetDataWithCreatedDate", paras, CommandType.StoredProcedure, connectionStringOption);
        }
    }
}
