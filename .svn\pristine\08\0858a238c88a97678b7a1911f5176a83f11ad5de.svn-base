﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trace_AbilitySystem.Libs.Dataconnect;
using Trace_AbilitySystem.Libs.ITrace_07_Pismo_Auto_Service;

namespace Trace_AbilitySystem.Libs.Trace_07_Pismo_Auto_Services
{
    public class Source_PISMO_Auto_TraceDB : ISource_PISMO_Auto_TraceDB
    {
        private readonly DbExecute _db;

        public Source_PISMO_Auto_TraceDB()
        {
            _db = new SqlExecute();
        }
        public DataTable TblAVI_GetDataTimeEnd2025(DateTime timeLast, int minutes, string connectionStringOption)
        {
            string strWhere = "A.CreatedDate > '" + timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff") + "' " +
                " AND A.CreatedDate <= '" + timeLast.AddMinutes(minutes).ToString("yyyy-MM-dd HH:mm:ss.fff") + "'";
            string sql = "SELECT A.*, B.BlockID FROM [PISMO_Auto_TraceDB].[dbo].[TblAVI] AS A left join[PISMO_CommonMaster].[dbo].[Define_Block] AS B ON A.PKID = B.[PKID] WHERE " + strWhere + " ORDER BY A.CreatedDate ASC";

            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }
        public DataTable TblAVI_Detail_GetDataTimeEnd2025(DateTime timeLast, int minutes, string connectionStringOption)
        {
            string strWhere = "CreatedDate > '" + timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff") + "' " +
                " AND CreatedDate <= '" + timeLast.AddMinutes(minutes).ToString("yyyy-MM-dd HH:mm:ss.fff") + "'";
            string sql = "SELECT * FROM TblAVI_Detail WHERE " + strWhere + " ORDER BY CreatedDate ASC";

            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }
        public DataTable TblAVI_SUS_GetDataTimeEnd2025(DateTime timeLast, int minutes, string connectionStringOption)
        {
            string strWhere = "CreatedDate > '" + timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff") + "' " +
                " AND CreatedDate <= '" + timeLast.AddMinutes(minutes).ToString("yyyy-MM-dd HH:mm:ss.fff") + "'";
            string sql = "SELECT * FROM TblAVI_SUS WHERE " + strWhere + " ORDER BY CreatedDate ASC";

            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }

        public DataTable TblAVI_SUS_Detail_GetDataTimeEnd2025(DateTime timeLast, int minutes, string connectionStringOption)
        {
            string strWhere = "CreatedDate > '" + timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff") + "' " +
                " AND CreatedDate <= '" + timeLast.AddMinutes(minutes).ToString("yyyy-MM-dd HH:mm:ss.fff") + "'";
            string sql = "SELECT * FROM TblAVI_SUS_Detail WHERE " + strWhere + " ORDER BY CreatedDate ASC";

            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }
        public DataTable TblLaser_GetDataTimeEnd2025(DateTime timeLast, int minutes, string connectionStringOption)
        {
            string strWhere = "A.CreatedDate > '" + timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff") + "' " +
                " AND A.CreatedDate <= '" + timeLast.AddMinutes(minutes).ToString("yyyy-MM-dd HH:mm:ss.fff") + "'";
            string sql = "SELECT A.*, B.BlockID FROM [PISMO_Auto_TraceDB].[dbo].[TblLaser] AS A left join[PISMO_CommonMaster].[dbo].[Define_Block] AS B ON A.PKID = B.[PKID] WHERE " + strWhere + " ORDER BY A.CreatedDate ASC";

            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }

        public DataTable TblLaser_Detail_GetDataTimeEnd2025(DateTime timeLast, int minutes, string connectionStringOption)
        {
            string strWhere = "CreatedDate > '" + timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff") + "' " +
                " AND CreatedDate <= '" + timeLast.AddMinutes(minutes).ToString("yyyy-MM-dd HH:mm:ss.fff") + "'";
            string sql = "SELECT * FROM TblLaser_Detail WHERE " + strWhere + " ORDER BY CreatedDate ASC";

            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }

        public DataTable TblCognex_GetDataTimeEnd2025(DateTime timeLast, int minutes, string connectionStringOption)
        {
            string strWhere = "A.CreatedDate > '" + timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff") + "' " +
                " AND A.CreatedDate <= '" + timeLast.AddMinutes(minutes).ToString("yyyy-MM-dd HH:mm:ss.fff") + "'";
            string sql = "SELECT A.*, B.BlockID FROM [PISMO_Auto_TraceDB].[dbo].[TblCognex] AS A left join[PISMO_CommonMaster].[dbo].[Define_Block] AS B ON A.PKID = B.[PKID] WHERE " + strWhere + " ORDER BY A.CreatedDate ASC";

            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }

        public DataTable TblCognex_Detail_GetDataTimeEnd2025(DateTime timeLast, int minutes, string connectionStringOption)
        {
            string strWhere = "CreatedDate > '" + timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff") + "' " +
                " AND CreatedDate <= '" + timeLast.AddMinutes(minutes).ToString("yyyy-MM-dd HH:mm:ss.fff") + "'";
            string sql = "SELECT * FROM TblCognex_Detail WHERE " + strWhere + " ORDER BY CreatedDate ASC";

            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }
        public DataTable TblCoper_GetDataTimeEnd2025(DateTime timeLast, int minutes, string connectionStringOption)
        {
            string strWhere = "A.CreatedDate > '" + timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff") + "' " +
                " AND A.CreatedDate <= '" + timeLast.AddMinutes(minutes).ToString("yyyy-MM-dd HH:mm:ss.fff") + "'";
            string sql = "SELECT A.*, B.BlockID FROM [PISMO_Auto_TraceDB].[dbo].[TblCoper] AS A left join[PISMO_CommonMaster].[dbo].[Define_Block] AS B ON A.PKID = B.[PKID] WHERE " + strWhere + " ORDER BY A.CreatedDate ASC";

            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }

        public DataTable TblCoper_Detail_GetDataTimeEnd2025(DateTime timeLast, int minutes, string connectionStringOption)
        {
            string strWhere = "CreatedDate > '" + timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff") + "' " +
                " AND CreatedDate <= '" + timeLast.AddMinutes(minutes).ToString("yyyy-MM-dd HH:mm:ss.fff") + "'";
            string sql = "SELECT * FROM TblCoper_Detail WHERE " + strWhere + " ORDER BY CreatedDate ASC";

            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }
        public DataTable TblECheck_GetDataTimeEnd2025(DateTime timeLast, int minutes, string connectionStringOption)
        {
            string strWhere = "A.CreatedDate > '" + timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff") + "' " +
                " AND A.CreatedDate <= '" + timeLast.AddMinutes(minutes).ToString("yyyy-MM-dd HH:mm:ss.fff") + "'";
            string sql = "SELECT A.*, B.BlockID FROM [PISMO_Auto_TraceDB].[dbo].[TblECheck] AS A left join[PISMO_CommonMaster].[dbo].[Define_Block] AS B ON A.PKID = B.[PKID] WHERE " + strWhere + " ORDER BY A.CreatedDate ASC";

            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }

        public DataTable TblECheck_Detail_GetDataTimeEnd2025(DateTime timeLast, int minutes, string connectionStringOption)
        {
            string strWhere = "CreatedDate > '" + timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff") + "' " +
                " AND CreatedDate <= '" + timeLast.AddMinutes(minutes).ToString("yyyy-MM-dd HH:mm:ss.fff") + "'";
            string sql = "SELECT * FROM TblECheck_Detail WHERE " + strWhere + " ORDER BY CreatedDate ASC";

            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }
        public DataTable TblTraceAOI_GetDataTimeEnd2025(DateTime timeLast, int minutes, string connectionStringOption)
        {
            string strWhere = "A.CreatedDate > '" + timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff") + "' " +
                " AND A.CreatedDate <= '" + timeLast.AddMinutes(minutes).ToString("yyyy-MM-dd HH:mm:ss.fff") + "'";
            string sql = "SELECT A.*, B.BlockID FROM [PISMO_Auto_TraceDB].[dbo].[TblTraceAOI] AS A left join[PISMO_CommonMaster].[dbo].[Define_Block] AS B ON A.PKID = B.[PKID] WHERE " + strWhere + " ORDER BY A.CreatedDate ASC";

            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }

        public DataTable TblTraceAOI_Detail_GetDataTimeEnd2025(DateTime timeLast, int minutes, string connectionStringOption)
        {
            string strWhere = "CreatedDate > '" + timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff") + "' " +
                " AND CreatedDate <= '" + timeLast.AddMinutes(minutes).ToString("yyyy-MM-dd HH:mm:ss.fff") + "'";
            string sql = "SELECT * FROM TblTraceAOI_Detail WHERE " + strWhere + " ORDER BY CreatedDate ASC";

            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }
        public DataTable TblViaAOI_GetDataTimeEnd2025(DateTime timeLast, int minutes, string connectionStringOption)
        {
            string strWhere = "A.CreatedDate > '" + timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff") + "' " +
                " AND A.CreatedDate <= '" + timeLast.AddMinutes(minutes).ToString("yyyy-MM-dd HH:mm:ss.fff") + "'";
            string sql = "SELECT A.*, B.BlockID FROM [PISMO_Auto_TraceDB].[dbo].[TblViaAOI] AS A left join[PISMO_CommonMaster].[dbo].[Define_Block] AS B ON A.PKID = B.[PKID] WHERE " + strWhere + " ORDER BY A.CreatedDate ASC";

            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }

        public DataTable TblViaAOI_Detail_GetDataTimeEnd2025(DateTime timeLast, int minutes, string connectionStringOption)
        {
            string strWhere = "CreatedDate > '" + timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff") + "' " +
                " AND CreatedDate <= '" + timeLast.AddMinutes(minutes).ToString("yyyy-MM-dd HH:mm:ss.fff") + "'";
            string sql = "SELECT * FROM TblViaAOI_Detail WHERE " + strWhere + " ORDER BY CreatedDate ASC";

            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }
        public DataTable FlexAssy_03_LaserSUS_GetByListBlockID(DataTable listBlockID, string connectionStringOption, out string connectionStringOk)
        {
            var paras = new SqlParameter[1];
            paras[0] = new SqlParameter("@ListBlockID", listBlockID ?? (object)DBNull.Value);

            string connectionStringDefault = null;
            DataTable dt;
            switch (connectionStringOption)
            {
                case "Pismo":
                    dt = _db.Execute_Table("sp_sms_FlexAssy_03_LaserSUS_GetByListBlockID", paras, CommandType.StoredProcedure, connectionStringOption);
                    break;


                default:
                    connectionStringDefault = "Pismo";
                    dt = _db.Execute_Table("sp_sms_FlexAssy_03_LaserSUS_GetByListBlockID", paras, CommandType.StoredProcedure, "Pismo");

                    break;
            }
            connectionStringOk = connectionStringOption ?? (dt?.Rows.Count > 0 ? connectionStringDefault : null);
            return dt;
        }
        public DataTable FlexAssy_04_QcGate_GetByListProductID(DataTable listProductID, string connectionStringOption, out string connectionStringOk)
        {
            var paras = new SqlParameter[1];
            paras[0] = new SqlParameter("@listProductID", listProductID ?? (object)DBNull.Value);

            string connectionStringDefault = null;
            DataTable dt;
            switch (connectionStringOption)
            {
                case "Pismo":
                    dt = _db.Execute_Table("sp_sms_FlexAssy_04_QcGate_GetByListProductID", paras, CommandType.StoredProcedure, connectionStringOption);
                    break;


                default:
                    connectionStringDefault = "Pismo";
                    dt = _db.Execute_Table("sp_sms_FlexAssy_04_QcGate_GetByListProductID", paras, CommandType.StoredProcedure, "Pismo");

                    break;
            }
            connectionStringOk = connectionStringOption ?? (dt?.Rows.Count > 0 ? connectionStringDefault : null);
            return dt;
        }
    }
}
