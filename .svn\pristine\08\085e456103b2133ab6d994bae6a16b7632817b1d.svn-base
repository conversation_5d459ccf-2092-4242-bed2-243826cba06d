﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trace_AbilitySystem.Libs.Dataconnect;
using Trace_AbilitySystem.Libs.Trace_07_Pismo_Auto_Services;

namespace Trace_AbilitySystem.Libs.ITrace_07_Pismo_Auto_Service
{
    public class FlexAssy_TraceAOIService : IFlexAssy_TraceAOIServices
    {
        private readonly DbExecute _db;
        private string _connectionStringOption = "PismoTest";

        public FlexAssy_TraceAOIService()
        {
            _db = new SqlExecute();
        }
        public async Task<int> FlexAssy_TraceAOI_ProcessData(string factory, DateTime createdDateSearch, int minutes, string connectionStringOption)
        {
            try
            {
                DateTime dateNow = DateTime.Now;
                DateTime timeLast = createdDateSearch;
                int recordNeedSyn = 0;
                int recordSyned = 0;

                DataTable dt = Singleton_07_Pismo.ISource_PISMO_Auto_TraceDB.TblTraceAOI_GetDataTimeEnd2025(timeLast, minutes, connectionStringOption);
                if (dt == null)
                    return -1;

                if (dt.Rows.Count > 0)
                {
                    recordNeedSyn = dt.Rows.Count;
                    for (int i = 0; i < recordNeedSyn; i++)
                    {
                        int PKID = int.Parse(dt.Rows[i]["PKID"].ToString().Trim());
                        string BlockID = dt.Rows[i]["BlockID"].ToString().Trim();
                        string LotNumber = dt.Rows[i]["LotNumber"].ToString().Trim();
                        string PanelNumber = dt.Rows[i]["PanelNumber"].ToString().Trim();
                        string ItemName = dt.Rows[i]["ItemName"].ToString().Trim();
                        string Model = dt.Rows[i]["Model"].ToString().Trim();
                        string ProcessType = dt.Rows[i]["ProcessType"].ToString().Trim();
                        string MachineID = dt.Rows[i]["MachineID"].ToString().Trim();
                        string MachineType = dt.Rows[i]["MachineType"].ToString().Trim();
                        DateTime DateTime_AOI = Convert.ToDateTime(dt.Rows[i]["DateTime_AOI"]);
                        DateTime DateTime_CVR = Convert.ToDateTime(dt.Rows[i]["DateTime_CVR"]);
                        string LayerName = dt.Rows[i]["LayerName"].ToString().Trim();
                        string Side = dt.Rows[i]["Side"].ToString().Trim();
                        string ErrorMap = dt.Rows[i]["ErrorMap"].ToString().Trim();
                        string ErrorMapConvert = dt.Rows[i]["ErrorMapConvert"].ToString().Trim();
                        string FileName = dt.Rows[i]["FileName"].ToString().Trim();
                        string FullName = dt.Rows[i]["FullName"].ToString().Trim();
                        bool SyncPcsID = bool.Parse(dt.Rows[i]["SyncPcsID"].ToString().Trim());
                        string OperatorID = dt.Rows[i]["OperatorID"].ToString().Trim();
                        DateTime CreatedDate = Convert.ToDateTime(dt.Rows[i]["CreatedDate"]);

                        int rs = FlexAssy_TraceAOI_Insert(PKID, BlockID, LotNumber, PanelNumber, ItemName, Model, ProcessType, MachineID, MachineType, DateTime_AOI, DateTime_CVR, LayerName, Side, ErrorMap, ErrorMapConvert, FileName, FullName, SyncPcsID, OperatorID, CreatedDate, "Pismo");
                        if (rs == -9)
                            return -1;

                        timeLast = Convert.ToDateTime(dt.Rows[i]["CreatedDate"]);
                        recordSyned++;
                    }
                    if (timeLast == createdDateSearch)
                    {
                        timeLast = timeLast.AddMinutes(minutes) > dateNow ? timeLast : timeLast.AddMinutes(minutes);
                    }
                    // Update ValueSearch
                    Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, "Pismo_TraceAOI2025", timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff"));
                }
                else
                {
                    timeLast = timeLast.AddMinutes(minutes) > dateNow ? timeLast : timeLast.AddMinutes(minutes);

                    // Update ValueSearch
                    Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, "Pismo_TraceAOI2025", timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff"));
                }

                ManageLog.WriteProcess(true, null, recordNeedSyn, recordSyned);
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp("Pismo_TraceAOI2025" + "\n" + ex.Message);
                return -1;
            }

            await Task.Delay(500);

            return 1;
        }
        public async Task<int> FlexAssy_TraceAOI_Detail_ProcessData2025(string factory, DateTime createdDateSearch, int minutes, string connectionStringOption)
        {
            try
            {
                DateTime dateNow = DateTime.Now;
                DateTime timeLast = createdDateSearch;
                int recordNeedSyn = 0;
                int recordSyned = 0;

                DataTable dt = Singleton_07_Pismo.ISource_PISMO_Auto_TraceDB.TblTraceAOI_Detail_GetDataTimeEnd2025(timeLast, minutes, connectionStringOption);
                if (dt == null)
                    return -1;

                if (dt.Rows.Count > 0)
                {
                    recordNeedSyn = dt.Rows.Count;
                    for (int i = 0; i < recordNeedSyn; i++)
                    {
                        int PKID = int.Parse(dt.Rows[i]["PKID"].ToString().Trim());
                        int TraceAOIPkid = int.Parse(dt.Rows[i]["TraceAOIPkid"].ToString().Trim());
                        int Location = int.Parse(dt.Rows[i]["Location"].ToString().Trim());
                        string LocationPanel = dt.Rows[i]["LocationPanel"].ToString().Trim();
                        string CavityNumber = dt.Rows[i]["CavityNumber"].ToString().Trim();
                        int LocationInkJet = int.Parse(dt.Rows[i]["LocationInkJet"].ToString().Trim());
                        string ProductID = dt.Rows[i]["ProductID"].ToString().Trim();
                        string Result = dt.Rows[i]["Result"].ToString().Trim();
                        string DefectCode = dt.Rows[i]["DefectCode"].ToString().Trim();
                        string DefectDescription = dt.Rows[i]["DefectDescription"].ToString().Trim();
                        string PictureLink = dt.Rows[i]["PictureLink"].ToString().Trim();
                        DateTime CreatedDate = Convert.ToDateTime(dt.Rows[i]["CreatedDate"]);
                        DateTime? SyncPcsDate = null;
                        if (dt.Rows[i]["SyncPcsDate"] != DBNull.Value)
                        {
                            SyncPcsDate = Convert.ToDateTime(dt.Rows[i]["SyncPcsDate"]);
                        }

                        int rs = FlexAssy_TraceAOI_Detail_Insert2025(PKID, TraceAOIPkid, Location, LocationPanel, CavityNumber, LocationInkJet, ProductID, Result, DefectCode, DefectDescription, PictureLink, CreatedDate, SyncPcsDate, "Pismo");
                        if (rs == -9)
                            return -1;

                        timeLast = Convert.ToDateTime(dt.Rows[i]["CreatedDate"]);
                        recordSyned++;
                    }
                    if (timeLast == createdDateSearch)
                    {
                        timeLast = timeLast.AddMinutes(minutes) > dateNow ? timeLast : timeLast.AddMinutes(minutes);
                    }
                    // Update ValueSearch
                    Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, "Pismo_TraceAOI_Detail2025", timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff"));
                }
                else
                {
                    timeLast = timeLast.AddMinutes(minutes) > dateNow ? timeLast : timeLast.AddMinutes(minutes);

                    // Update ValueSearch
                    Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, "Pismo_TraceAOI_Detail2025", timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff"));
                }

                ManageLog.WriteProcess(true, null, recordNeedSyn, recordSyned);
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp("Pismo_TraceAOI_Detail2025" + "\n" + ex.Message);
                return -1;
            }

            await Task.Delay(500);

            return 1;
        }
        public DataTable TraceAOI_GetDataWithCreatedDate(DateTime createdDate, int minutes, string connectionStringOption)
        {
            var paras = new SqlParameter[2];
            paras[0] = new SqlParameter("@CreatedDate", createdDate);
            paras[1] = new SqlParameter("@Minutes", minutes);
            return _db.Execute_Table("sms_FlexAssy_01_TraceAOI_GetDataWithCreatedDate", paras, CommandType.StoredProcedure, _connectionStringOption);
        }
        public DataTable TraceAOI_MapNG_GetDataWithMapId(int BlockMapId, string connectionStringOption)
        {
            string sql = "SELECT * FROM BlockNG Where BlockMapId ='" + BlockMapId + "'";
            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }
        public int FlexAssy_TraceAOI_Insert(int PKID,  string BlockID, string LotNumber, string PanelNumber, string ItemName, string Model, string ProcessType, string MachineID, string MachineType, DateTime DateTime_AOI, DateTime DateTime_CVR, string LayerName, string Side, string ErrorMap, string ErrorMapConvert, string FileName, string FullName, bool SyncPcsID, string OperatorID, DateTime CreatedDate, string connectionStringOption)
        {
            var paras = new SqlParameter[20];
            paras[0] = new SqlParameter("@BlockID", BlockID);
            paras[1] = new SqlParameter("@LotNumber", LotNumber);
            paras[2] = new SqlParameter("@PanelNumber", PanelNumber);
            paras[3] = new SqlParameter("@ItemName", ItemName);
            paras[4] = new SqlParameter("@Model", Model);
            paras[5] = new SqlParameter("@ProcessType", ProcessType);
            paras[6] = new SqlParameter("@MachineID", MachineID);
            paras[7] = new SqlParameter("@MachineType", MachineType);
            paras[8] = new SqlParameter("@DateTime_AOI", DateTime_AOI);
            paras[9] = new SqlParameter("@DateTime_CVR", DateTime_CVR);
            paras[10] = new SqlParameter("@LayerName", LayerName);
            paras[11] = new SqlParameter("@Side", Side);
            paras[12] = new SqlParameter("@ErrorMap", ErrorMap);
            paras[13] = new SqlParameter("@ErrorMapConvert", ErrorMapConvert);
            paras[14] = new SqlParameter("@FileName", FileName);
            paras[15] = new SqlParameter("@FullName", FullName);
            paras[16] = new SqlParameter("@SyncPcsID", SyncPcsID);
            paras[17] = new SqlParameter("@OperatorID", OperatorID);
            paras[18] = new SqlParameter("@CreatedDate", CreatedDate);
            paras[19] = new SqlParameter("@PKID", PKID);

            return _db.Execute_Modify("_FlexAssy_TraceAOI_Insert2025", paras, CommandType.StoredProcedure, _connectionStringOption);
        }
        public int FlexAssy_TraceAOI_Detail_Insert2025(int PKID, int TraceAOIPkid, int Location, string LocationPanel, string CavityNumber, int LocationInkJet, string ProductID, string Result, string DefectCode, string DefectDescription, string PictureLink, DateTime CreatedDate, DateTime? SyncPcsDate, string connectionStringOption)
        {
            var paras = new SqlParameter[13];
            paras[0] = new SqlParameter("@PKID", PKID);
            paras[1] = new SqlParameter("@TraceAOIPkid", TraceAOIPkid);
            paras[2] = new SqlParameter("@Location", Location);
            paras[3] = new SqlParameter("@LocationPanel", LocationPanel);
            paras[4] = new SqlParameter("@CavityNumber", CavityNumber);
            paras[5] = new SqlParameter("@LocationInkJet", LocationInkJet);
            paras[6] = new SqlParameter("@ProductID", ProductID);
            paras[7] = new SqlParameter("@Result", Result);
            paras[8] = new SqlParameter("@DefectCode", DefectCode);
            paras[9] = new SqlParameter("@DefectDescription", DefectDescription);
            paras[10] = new SqlParameter("@PictureLink", PictureLink);
            paras[11] = new SqlParameter("@CreatedDate", CreatedDate);
            paras[12] = new SqlParameter("@SyncPcsDate", SyncPcsDate);

            return _db.Execute_Modify("_FlexAssy_TraceAOI_Detail_Insert2025", paras, CommandType.StoredProcedure, _connectionStringOption);
        }
        public DataTable TraceAOI_Detail_GetDataWithCreatedDate2025(DateTime createdDate, int minutes, string connectionStringOption)
        {
            var paras = new SqlParameter[2];
            paras[0] = new SqlParameter("@CreatedDate", createdDate);
            paras[1] = new SqlParameter("@Minutes", minutes);
            return _db.Execute_Table("sms_FlexAssy_TraceAOI_Detail_GetDataWithCreatedDate", paras, CommandType.StoredProcedure, _connectionStringOption);
        }
        public DataTable FlexAssy_TraceAOI_GetByBlockID(string blockID, string connectionStringOption)
        {
            var paras = new SqlParameter[1];
            paras[0] = new SqlParameter("@BlockID", blockID);
            var dt = _db.Execute_Table("sp_sms_FlexAssy_TraceAOI_GetByBlockID", paras, CommandType.StoredProcedure, "Pismo");
            return dt;
        }

        public DataTable FlexAssy_TraceAOI_GetIndicationByBlockID(string blockID, string connectionStringOption)
        {
            string sql = "SELECT TOP 1 * FROM [dbo].[LogVerification] WHERE BlockID like '" + blockID + "%'";
            var dt = _db.Execute_Table(sql, null, CommandType.Text, _connectionStringOption); ;
            return dt;
        }
        public DataTable FlexAssy_TraceAOI_GetByProductID(string productID, string connectionStringOption)
        {
            var paras = new SqlParameter[1];
            paras[0] = new SqlParameter("@ProductID", productID ?? (object)DBNull.Value);
            var dt = _db.Execute_Table("sp_sms_FlexAssy_TraceAOI_Detail_GetByProductID", paras, CommandType.StoredProcedure, "Pismo");
            return dt;
        }
        public DataTable FlexAssy_TraceAOI_GetByPkID(int PKID)
        {
            var paras = new SqlParameter[1];
            paras[0] = new SqlParameter("@PKID", PKID);
            var dt = _db.Execute_Table("sp_sms_FlexAssy_TraceAOI_GetByPkID", paras, CommandType.StoredProcedure, "Pismo");
            return dt;
        }
        public DataTable FlexAssy_TraceAOI_GetByListBlockID(DataTable listBlockID, string connectionStringOption, out string connectionStringOk)
        {
            var paras = new SqlParameter[1];
            paras[0] = new SqlParameter("@ListBlockID", listBlockID ?? (object)DBNull.Value);

            string connectionStringDefault = null;
            DataTable dt;
            switch (connectionStringOption)
            {
                case "Pismo":
                    dt = _db.Execute_Table("sp_sms_FlexAssy_TraceAOI_GetByListBlockID", paras, CommandType.StoredProcedure, connectionStringOption);
                    break;


                default:
                    connectionStringDefault = "Pismo";
                    dt = _db.Execute_Table("sp_sms_FlexAssy_TraceAOI_GetByListBlockID", paras, CommandType.StoredProcedure, "Pismo");

                    break;
            }
            connectionStringOk = connectionStringOption ?? (dt?.Rows.Count > 0 ? connectionStringDefault : null);
            return dt;
        }
    }
}
