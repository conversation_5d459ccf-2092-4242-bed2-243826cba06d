﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Trace_AbilitySystem.Libs
{
    public class EthernetQR
    {
        private const int READER_COUNT = 1;      // number of readers to connect
        private const int RECV_DATA_MAX = 1024;
        private ClientSocket[] clientSocketInstance;
        public EthernetQR()
        {
            clientSocketInstance = new ClientSocket[READER_COUNT];
            int readerIndex = 0;
            int CommandPort = 9004;
            int DataPort = 9004;
            //
            // Fix 1
            //
            byte[] ip1 = { 192, 168, 100, 201 };
            clientSocketInstance[readerIndex++] = new ClientSocket(ip1, CommandPort, DataPort);
            
        }

        public EthernetQR(string fixAddress)
        {
            clientSocketInstance = new ClientSocket[READER_COUNT];
            int readerIndex = 0;
            int CommandPort = 9004;
            int DataPort = 9004;
            //
            // Fix 1
            //
            byte[] ip1 = IPAddress.Parse(fixAddress).GetAddressBytes();
            clientSocketInstance[readerIndex++] = new ClientSocket(ip1, CommandPort, DataPort);

        }

        public bool Connect()
        {
            bool rs = false;
            for (int i = 0; i < READER_COUNT; i++)
            {
                //
                // Connect to the command port.
                //
                try
                {
                    clientSocketInstance[i].readerCommandEndPoint.Port = 9004;
                    clientSocketInstance[i].readerDataEndPoint.Port = 9004;
                    //
                    // Close the socket if opened.
                    //
                    if (clientSocketInstance[i].commandSocket != null)
                    {
                        clientSocketInstance[i].commandSocket.Close();
                    }

                    //
                    // Create a new socket.
                    //
                    clientSocketInstance[i].commandSocket = new Socket(AddressFamily.InterNetwork, SocketType.Stream, ProtocolType.Tcp);

                    clientSocketInstance[i].commandSocket.Connect(clientSocketInstance[i].readerCommandEndPoint);

                    rs = true;//clientSocketInstance[i].readerCommandEndPoint.ToString() + " Connected.";
                }
                catch (ArgumentOutOfRangeException ex)
                {
                    //
                    // Catch exceptions and show the message.
                    //
                    rs = false;//clientSocketInstance[i].readerCommandEndPoint.ToString() + " Failed to connect.";
                    ManageLog.WriteErrorApp(ex);
                    clientSocketInstance[i].commandSocket = null;
                    continue;
                }
                catch (SocketException ex)
                {
                    //
                    // Catch exceptions and show the message.
                    //
                    rs = false;//clientSocketInstance[i].readerCommandEndPoint.ToString() + " Failed to connect.";
                    ManageLog.WriteErrorApp(ex.Message);
                    clientSocketInstance[i].commandSocket = null;
                    continue;
                }

                //
                // Connect to the data port.
                //
                try
                {
                    //
                    // Close the socket if opend.
                    //
                    if (clientSocketInstance[i].dataSocket != null)
                    {
                        clientSocketInstance[i].dataSocket.Close();
                    }
                    if (clientSocketInstance[i].readerCommandEndPoint.Port == clientSocketInstance[i].readerDataEndPoint.Port)
                    {
                        clientSocketInstance[i].dataSocket = clientSocketInstance[i].commandSocket;
                    }
                    else
                    {
                        //
                        // Create a new socket.
                        //
                        clientSocketInstance[i].dataSocket = new Socket(AddressFamily.InterNetwork, SocketType.Stream, ProtocolType.Tcp);
                        //msg = clientSocketInstance[i].readerDataEndPoint.ToString() + " Connecting..";
                        clientSocketInstance[i].dataSocket.Connect(clientSocketInstance[i].readerDataEndPoint);
                        rs = true; // clientSocketInstance[i].readerDataEndPoint.ToString() + " Connected.";
                    }
                    //
                    // Set 100 milliseconds to receive timeout.
                    //
                    clientSocketInstance[i].dataSocket.ReceiveTimeout = 100;
                }

                catch (SocketException ex)
                {
                    //
                    // Catch exceptions and show the message.
                    //
                    rs = false;//clientSocketInstance[i].readerDataEndPoint.ToString() + " Failed to connect.";
                    ManageLog.WriteErrorApp(ex);
                    clientSocketInstance[i].dataSocket = null;
                    continue;
                }
            }
            return rs;
        }
        public string Disconnect()
        {
            string msg = null;
            for (int i = 0; i < READER_COUNT; i++)
            {
                if (clientSocketInstance[i].commandSocket != null)
                {
                    clientSocketInstance[i].commandSocket.Close();
                    clientSocketInstance[i].commandSocket = null;
                    msg = clientSocketInstance[i].readerCommandEndPoint.ToString() + " Disconnected.";
                }
                if (clientSocketInstance[i].dataSocket != null)
                {
                    clientSocketInstance[i].dataSocket.Close();
                    clientSocketInstance[i].dataSocket = null;
                    msg = clientSocketInstance[i].readerDataEndPoint.ToString() + " Disconnected.";
                }
            }
            return msg;
        }
        public string Scan()
        {
            string value = null;
            string lon = "LON\r";   // CR is terminator
            Byte[] command = ASCIIEncoding.ASCII.GetBytes(lon);

            for (int i = 0; i < READER_COUNT; i++)
            {
                if (clientSocketInstance[i].commandSocket != null)
                {
                    clientSocketInstance[i].commandSocket.Send(command);
                }
                else
                {
                    ManageLog.WriteErrorApp(clientSocketInstance[i].readerCommandEndPoint.ToString() + " is disconnected.");
                }
            }
            return value;
        }
        public void StopScan()
        {
            string loff = "LOFF\r"; // CR is terminator
            byte[] command = Encoding.ASCII.GetBytes(loff);

            for (int i = 0; i < READER_COUNT; i++)
            {
                if (clientSocketInstance[i].commandSocket != null)
                {
                    clientSocketInstance[i].commandSocket.Send(command);
                }
                else
                {
                    ManageLog.WriteErrorApp(clientSocketInstance[i].readerCommandEndPoint.ToString() + " is disconnected.");
                }
            }
        }        

        public string LoadConfig(string productType)
        {            
            string proType = string.Empty;
            if (productType.Equals("Program1"))
            {
                proType = "1";
            }
            else if (productType.Equals("Program2"))
            {
                proType = "2";
            }
            else if (productType.Equals("Program3"))
            {
                proType = "3";
            }
            else if (productType.Equals("Program4"))
            {
                proType = "4";
            }
            else if (productType.Equals("Program5"))
            {
                proType = "5";
            }
            else if (productType.Equals("Program6"))
            {
                proType = "6";
            }
            else if (productType.Equals("Program7"))
            {
                proType = "7";
            }
            else if (productType.Equals("Program8"))
            {
                proType = "8";
            }

            string value = null;
            string cmd = "BLOAD," + proType + "\r\n";   // CR is terminator
            Byte[] command = ASCIIEncoding.ASCII.GetBytes(cmd);

            for (int i = 0; i < READER_COUNT; i++)
            {
                if (clientSocketInstance[i].commandSocket != null)
                {
                    clientSocketInstance[i].commandSocket.Send(command);
                }
                else
                {
                    ManageLog.WriteErrorApp(clientSocketInstance[i].readerCommandEndPoint.ToString() + " is disconnected.");
                }
            }

            Thread.Sleep(1000);
            /// Receive data
            Byte[] recvBytes = new Byte[RECV_DATA_MAX];
            int recvSize = 0;

            for (int i = 0; i < READER_COUNT; i++)
            {
                if (clientSocketInstance[i].dataSocket != null)
                {
                    try
                    {
                        recvSize = clientSocketInstance[i].dataSocket.Receive(recvBytes);
                    }
                    catch (SocketException)
                    {
                        //
                        // Catch the exception, if cannot receive any data.
                        //
                        recvSize = 0;
                    }
                }
                else
                {
                    ManageLog.WriteErrorApp(clientSocketInstance[i].readerDataEndPoint.ToString() + " is disconnected.");
                    continue;
                }

                if (recvSize == 0)
                {
                    //System.Windows.Forms.MessageBox.Show(clientSocketInstance[i].readerDataEndPoint.ToString() + " has no data.");
                }
                else
                {
                    recvBytes[recvSize] = 0;
                    value = Encoding.GetEncoding("Shift_JIS").GetString(recvBytes);
                }
            }
            return value;
        }

        public string AddCommandSub()
        {
            string value = string.Empty;
            //using 8 bank
            for (int bank = 0; bank < 8; bank++)
            {
                string commentCode = "WB," + (bank + 1).ToString("D2") + "310,1\r\n";
                Byte[] cmd = ASCIIEncoding.ASCII.GetBytes(commentCode);
                for (int i = 0; i < READER_COUNT; i++)
                {
                    if (clientSocketInstance[i].commandSocket != null)
                    {
                        clientSocketInstance[i].commandSocket.Send(cmd);
                    }
                    else
                    {
                        ManageLog.WriteErrorApp(clientSocketInstance[i].readerCommandEndPoint.ToString() + " is disconnected.");
                    }
                }
                Thread.Sleep(50);
                value = ReceiveData();
                value = value.Replace('\0', ' ').Replace('\r', ' ').Trim();
                if (value.Contains("OK"))
                {
                    value = "OK";
                }
                else
                {
                    return "ER";
                }
            }
            return value;
        }
        public string ReceiveData()
        {
            Byte[] recvBytes = new Byte[RECV_DATA_MAX];
            int recvSize = 0;

            for (int i = 0; i < READER_COUNT; i++)
            {
                if (clientSocketInstance[i].dataSocket != null)
                {
                    try
                    {
                        recvSize = clientSocketInstance[i].dataSocket.Receive(recvBytes);
                    }
                    catch (SocketException)
                    {
                        //
                        // Catch the exception, if cannot receive any data.
                        //
                        recvSize = 0;
                    }
                }
                else
                {
                    //MessageBox.Show(clientSocketInstance[i].readerDataEndPoint.ToString() + " is disconnected.");
                    continue;
                }

                if (recvSize == 0)
                {
                    //MessageBox.Show(clientSocketInstance[i].readerDataEndPoint.ToString() + " has no data.");
                }
                else
                {
                    //
                    // Show the receive data after converting the receive data to Shift-JIS.
                    // Terminating null to handle as string.
                    //
                    recvBytes[recvSize] = 0;
                    //MessageBox.Show(clientSocketInstance[i].readerDataEndPoint.ToString() + "\r\n" + Encoding.GetEncoding("Shift_JIS").GetString(recvBytes));
                }
            }

            return Encoding.GetEncoding("Shift_JIS").GetString(recvBytes);

        }

        public bool IsConnected()
        {
            try
            {
                return !(clientSocketInstance[0].dataSocket.Poll(50, SelectMode.SelectRead) && clientSocketInstance[0].dataSocket.Available == 0);
            }
            catch (SocketException)
            {
                return false;
            }
        }
    }
}
