﻿using System;
using System.Data;
using System.Data.SqlClient;
using Trace_AbilitySystem.Libs.Dataconnect;
using Trace_AbilitySystem.Libs.ITrace_03_Common_Services;

namespace Trace_AbilitySystem.Libs.Trace_03_Common_Services
{
    public class SequencyOfStageService : ISequencyOfStageService
    {
        private readonly DbExecute _db;
        private readonly string connectionStringOption = "Common";

        public SequencyOfStageService()
        {
            _db = new SqlExecute();
        }

        public int SequencyOfStage_Insert(string frontOrBack, string sequency, string stage, string stageName, string machineID, string machineName, string itemName)
        {
            var paras = new SqlParameter[7];
            paras[0] = new SqlParameter("@FrontOrBack", frontOrBack ?? (object)DBNull.Value);
            paras[1] = new SqlParameter("@Sequency", sequency ?? (object)DBNull.Value);
            paras[2] = new SqlParameter("@Stage", stage ?? (object)DBNull.Value);
            paras[3] = new SqlParameter("@StageName", stageName ?? (object)DBNull.Value);
            paras[4] = new SqlParameter("@MachineID", machineID ?? (object)DBNull.Value);
            paras[5] = new SqlParameter("@MachineName", machineName ?? (object)DBNull.Value);
            paras[6] = new SqlParameter("@ItemName", itemName ?? (object)DBNull.Value);

            return _db.Execute_Modify("sp_sms_SequencyOfStage_Insert", paras, CommandType.StoredProcedure, connectionStringOption);
        }

        public int SequencyOfStage_Update(int iD, string frontOrBack, string sequency, string stage, string stageName, string machineID, string machineName, string itemName)
        {
            var paras = new SqlParameter[8];
            paras[0] = new SqlParameter("@ID", iD);
            paras[1] = new SqlParameter("@FrontOrBack", frontOrBack ?? (object)DBNull.Value);
            paras[2] = new SqlParameter("@Sequency", sequency ?? (object)DBNull.Value);
            paras[3] = new SqlParameter("@Stage", stage ?? (object)DBNull.Value);
            paras[4] = new SqlParameter("@StageName", stageName ?? (object)DBNull.Value);
            paras[5] = new SqlParameter("@MachineID", machineID ?? (object)DBNull.Value);
            paras[6] = new SqlParameter("@MachineName", machineName ?? (object)DBNull.Value);
            paras[7] = new SqlParameter("@ItemName", itemName ?? (object)DBNull.Value);

            return _db.Execute_Modify("sp_sms_SequencyOfStage_Update", paras, CommandType.StoredProcedure, connectionStringOption);
        }

        public int SequencyOfStage_Delete(int iD)
        {
            var paras = new SqlParameter[1];
            paras[0] = new SqlParameter("@ID", iD);

            return _db.Execute_Modify("sp_sms_SequencyOfStage_Delete", paras, CommandType.StoredProcedure, connectionStringOption);
        }

        public DataTable SequencyOfStage_GetByID(int iD)
        {
            var paras = new SqlParameter[1];
            paras[0] = new SqlParameter("@ID", iD);

            return _db.Execute_Table("sp_sms_SequencyOfStage_GetByID", paras, CommandType.StoredProcedure, connectionStringOption);
        }

        public DataTable SequencyOfStage_GetByFrontOrBackAndItemName(string frontOrBack, string itemName)
        {
            var paras = new SqlParameter[2];
            paras[0] = new SqlParameter("@FrontOrBack", frontOrBack ?? (object)DBNull.Value);
            paras[1] = new SqlParameter("@ItemName", itemName ?? (object)DBNull.Value);

            return _db.Execute_Table("sp_sms_SequencyOfStage_GetByFrontOrBackAndItemName", paras, CommandType.StoredProcedure, connectionStringOption);
        }

        public DataTable SequencyOfStage_GetByAll()
        {
            return _db.Execute_Table("sp_sms_SequencyOfStage_GetByAll", null, CommandType.StoredProcedure, connectionStringOption);
        }
        public DataTable SequencyOfStage_GetByAll(string itemName)
        {
            var paras = new SqlParameter[1];
            paras[0] = new SqlParameter("@ItemName", itemName ?? (object)DBNull.Value);

            return _db.Execute_Table("sp_sms_SequencyOfStage_GetByAllByItemName", paras, CommandType.StoredProcedure, connectionStringOption);
        }

    }
}