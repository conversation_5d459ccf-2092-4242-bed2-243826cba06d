﻿@using System.Configuration;
@using System.Data;
@using Trace_AbilitySystem.Libs
@model Trace_AbilitySystem.Libs.DTOClass.DataAllTrace
@if (Model != null && Model.DataFlexAssy.FlexAssy_22_ORT_ICT?.Rows.Count > 0)
{
    var counter = 0;
    <!doctype html>
    <html lang="en">
    <body>

        <h2 class="bd-title" id="22-ORT-ict">ORT ICT</h2>
        <table class="table table-bordered">
            <thead>
                <tr>
                    <th scope="col">Control Item</th>
                    <th scope="col">Control Value</th>
                </tr>
            </thead>
			<tbody>
				<tr>
					<td>ItemName</td>
					<td>@(ViewBag.ItemName)</td>
				</tr>
				<tr>
					<td>IndicationNo</td>
					<td>@(ViewBag.IndicationNo)</td>
				</tr>
				<tr>
					<td>PcsID</td>
					<td id="pcs_id">@Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows[0]["ProductID"]</td>
				</tr>
				<tr>
					<td>DateTime</td>
					<td>@Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows[0]["DateTime"]</td>
				</tr>
				@*<tr>
			<td>Machine ID</td>
			<td>@Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows[0]["MachineID"]</td>
		</tr>*@
				<tr>
					<td>Operator ID</td>
					<td>@Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows[0]["OperatorID"]</td>
				</tr>
				<tr>
					<td>FacilityID</td>
					<td>@Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows[0]["FacilityID"]</td>
				</tr>
				<tr>
					<td>JigID</td>
					<td>@Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows[0]["JigID"]</td>
				</tr>
				<tr>
					<td>PCB</td>
					<td>@Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows[0]["PCB"]</td>
				</tr>
				@*<tr>
		<td>Location</td>
		<td>@Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows[0]["Location"]</td>
		</tr>*@
				<tr>
					<td>Retest Cycle</td>
					<td id="retestCycle">@Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows[0]["RetestCycle"]</td>
				</tr>
				<tr>
					<td>Test program</td>
					<td id="testprogram">@Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows[0]["TestProgram"]</td>
				</tr>
				<tr>
					<td>ERS Result</td>
					<td>@Model.DataFlexAssy.FlexAssy_22_ICT.Rows[0]["Result"]</td>
				</tr>
				<tr>
					<td>MPE Result</td>
					<td>@Model.DataFlexAssy.FlexAssy_22_ICT.Rows[0]["MPEResult"]</td>
				</tr>

				<tr>
					<td>DCR value max</td>
					<td>@Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows[0]["MaxValue"]</td>
				</tr>
				<tr>
					<td>DCR value min</td>
					<td>@Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows[0]["MinValue"]</td>
				</tr>
				<tr>
					<td>DCR value average</td>
					<td>@Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows[0]["AverageValue"]</td>
				</tr>
				@if (Model != null && Model.DataFlexAssy.FlexAssy_22_ICT_FirstPassRate_MPE?.Rows.Count > 0)
				{
					<tr>
						<td>First pass rate (%)</td>
						<td>@Model.DataFlexAssy.FlexAssy_22_ICT_FirstPassRate_MPE.Rows[0]["PassRateICT"]</td>
					</tr>
					<tr>
						<td>Yield rate (%)</td>
						<td>@Model.DataFlexAssy.FlexAssy_22_ICT_FirstPassRate_MPE.Rows[0]["PercentICT"]</td>
					</tr>
				}
				else
				{
					<tr>
						<td>First pass rate (%)</td>
						<td></td>
					</tr>
					<tr>
						<td>Yield rate (%)</td>
						<td></td>
					</tr>
				}
				<tr>
					<td>Golden sample checking record</td>
					@*<td><a onclick="ict_Sample('@Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows[0]["ProductID"]')" href="javascript:">@Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows[0]["GoldenIctSample"]</a></td>*@
					@{
						if (Model.DataFlexAssy.FlexAssy_22_ORT_ICT_LogFile_Sample?.Rows.Count > 0)
						{
							DateTime dateMax = Convert.ToDateTime(Model.DataFlexAssy.FlexAssy_22_ORT_ICT_LogFile_Sample.Rows[0]["DateTime"]);

							if (Model.DataFlexAssy.FlexAssy_22_ORT_ICT_LogFile_Sample.Rows.Count > 1)
							{
								<td>
									@for (int i = 0; i < Model.DataFlexAssy.FlexAssy_22_ORT_ICT_LogFile_Sample.Rows.Count; i++)
									{
										DateTime date = Convert.ToDateTime(Model.DataFlexAssy.FlexAssy_22_ORT_ICT_LogFile_Sample.Rows[i]["DateTime"]).AddHours(1);
										if (DateTime.Compare(date, dateMax) > 0)
										{
											<a target="_blank" href="@($"{ConfigurationManager.AppSettings["Log_Address"]}{Model.DataFlexAssy.FlexAssy_22_ORT_ICT_LogFile_Sample.Rows[i]["FileName"]}")">@($"{i + 1}. {ConfigurationManager.AppSettings["Log_Address"]}{Model.DataFlexAssy.FlexAssy_22_ORT_ICT_LogFile_Sample.Rows[i]["FileName"]}")</a><br />
										}
									}
								</td>
							}
							else
							{
								<td><a target="_blank" href="@($"{ConfigurationManager.AppSettings["Log_Address"]}{Model.DataFlexAssy.FlexAssy_22_ORT_ICT_LogFile_Sample.Rows[0]["FileName"]}")">@($"{ConfigurationManager.AppSettings["Log_Address"]}{Model.DataFlexAssy.FlexAssy_22_ORT_ICT_LogFile_Sample.Rows[0]["FileName"]}")</a></td>
							}
						}
						else
						{
							<td></td>
						}
					}
				</tr>
				<tr>
					<td>First piece buyoff control (link)</td>
					<td>
						@if (ViewBag.Factory == "F5")
						{
							<a onclick="viewDataFirstPieceBuyoffControlNew('@Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows[0]["MachineID"]','@DataConvert.ConvertToString(ViewBag.IndicationNo)')" href="javascript:">
								@(Singleton_04_Machine.IFPBCheckingService.FPBChecking_GetResultByMachineID_IndicationNumber(DataConvert.ConvertToString(Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows[0]["MachineID"]),DataConvert.ConvertToString(ViewBag.IndicationNo)))
							</a>
						}
						else
						{
							<a onclick="viewDataFirstPieceBuyoffControlByTime('@Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows[0]["MachineID"]','@DataConvert.ToDateTime(Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows[0]["DateTime"]).ToString("yyyy-MM-dd HH:mm:ss")', '@DataConvert.ConvertToString(ViewBag.ItemName)')" href="javascript:">
								@(Singleton_04_Machine.IFPBCheckingService.FPBChecking_GetResultByMachineID_Time(DataConvert.ConvertToString(Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows[0]["MachineID"]), DataConvert.ToDateTime(Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows[0]["DateTime"]), @DataConvert.ConvertToString(ViewBag.ItemName)))
							</a>
						}
					</td>
				</tr>
				<tr>
					<td>Production condition (link)</td>
					<td>
						<a onclick="viewDataProductionCondition(@Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows[0]["ProductionConditionID"])" href="javascript:">
							@Singleton_03_Common.ICommon_CommonService.GetProductionContidtionResult(DataConvert.ConvertToString(Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows[0]["ProductionConditionID"]),
								 DataConvert.ConvertToString(Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows[0]["ProductionConditionResult"]))
						</a>
					</td>
				</tr>
				<tr>
					<td>Machine ID</td>
					<td>@Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows[0]["MachineID"]</td>
				</tr>
				<tr>
					<td>Machine maintenance date (link)</td>
					@{
						DataTable dtMaintain = Singleton_04_Machine.ItMachineMtnService.tMachineMtn_GetDataByKey(
							DataConvert.ConvertToString(Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows[0]["MachineID"]),
							DataConvert.ToDateTime(Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows[0]["DateTime"]));
						if (dtMaintain?.Rows.Count > 0)
						{
							<td>
								<a onclick="viewDataMachineMaintenance(@dtMaintain.Rows[0]["ID"])" href="javascript:">@dtMaintain.Rows[0]["MaintenanceDate"]</a>
							</td>
						}
						else
						{
							<td></td>
						}
					}
				</tr>
				<tr>
					<td>View Log Detail</td>
					<td><a onclick="viewLogICT('@Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows[@Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows.Count - 1]["Line"]', '@Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows[@Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows.Count - 1]["FileName"]')" href="javascript:">@(Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows[@Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows.Count - 1]["FileName"] == DBNull.Value ? "" : "View Log Detail ICT")</a></td>
				</tr>
				@if (!string.IsNullOrEmpty(Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows[@Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows.Count - 1]["FileNameRoot"].ToString()))
				{
					<tr>
						<td>Log File On MachineID: @Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows[@Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows.Count - 1]["MachineID"]</td>
						<td><a target="_blank" href="@Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows[@Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows.Count - 1]["FileNameRoot"]">@Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows[@Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows.Count - 1]["FileNameRoot"]</a></td>
					</tr>
				}
				else
				{
					<tr>
						<td>Log File On MachineID: @Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows[@Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows.Count - 1]["MachineID"]</td>
						<td></td>
					</tr>
				}
				@if (!string.IsNullOrEmpty(Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows[@Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows.Count - 1]["FileName"].ToString()))
				{
					if (Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows[@Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows.Count - 1]["FileName"].ToString().Contains(".txt"))
					{
						<tr>
							<td>Log File On Trace</td>
							<td><a target="_blank" href="@($"{ConfigurationManager.AppSettings["Log_Address"]}{Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows[@Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows.Count - 1]["FileName"]}")">@($"{ConfigurationManager.AppSettings["Log_Address"]}{Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows[@Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows.Count - 1]["FileName"]}")</a></td>
						</tr>
					}
					else
					{
						<tr>
							<td>Log File On Trace</td>
							<td>
								@Html.ActionLink("Export_LogFilePcs_ICT", "LogFileProduct_ICT", "Home", new
						   {
							   productID = @Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows[0]["ProductID"],
							   fileName = $"{ConfigurationManager.AppSettings["Log_Address"]}{@Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows[0]["FileName"]}"
						   }, null)
							</td>
						</tr>
					}
				}
				else
				{
					<tr>
						<td>Log File On Trace</td>
						<td></td>
					</tr>
				}
				<tr>
					<td>ICT History</td>
					<td><a onclick="View_LogICT_History('@Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows[0]["ProductID"]', 'ORT','@Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows[0]["TestProgram"]')" href="javascript:">@(Model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows[0]["ID_LogFile"] == DBNull.Value ? "" : "View History")</a></td>
				</tr>
				<tr>
					<td>Temp/Humidity/Clearness Dashboard Link</td>
					<td>
						<a target="_blank" href="@Common.getCleanlink(ViewBag.Factory,1)">
							@Common.getCleanlink(ViewBag.Factory, 1)
						</a>
					</td>
				</tr>
			</tbody>
        </table>

        @if (Model.DataFlexAssy.FlexAssy_22_ICT_Component?.Rows.Count > 0)
        {
            <div style="overflow-x:auto;">
                <table class="table table-bordered image2D">
                    <thead>
                        <tr>
                            <th scope="col">No</th>
                            <th scope="col">ComponentID</th>
                            <th scope="col">CompName</th>
                            <th scope="col">CompType</th>
                            <th scope="col">SationID</th>
                            <th scope="col">Quantity</th>
                            <th scope="col">MakerName</th>
                            <th scope="col">MaxQuantity</th>
                            <th scope="col">PartLotMaker</th>
                            <th scope="col">Machine maintenance date (link)</th>
                        </tr>
                    </thead>
                    <tbody>
                        @for (int j = 0; j < Model.DataFlexAssy.FlexAssy_22_ICT_Component?.Rows.Count; j++)
                        {
                            counter++;
                            <tr>
                                <td>@counter</td>
                                <td>@Model.DataFlexAssy.FlexAssy_22_ICT_Component.Rows[j]["CompID"]</td>
                                <td>@Model.DataFlexAssy.FlexAssy_22_ICT_Component.Rows[j]["CompName"]</td>
                                <td>@Model.DataFlexAssy.FlexAssy_22_ICT_Component.Rows[j]["CompType"]</td>
                                <td>@Model.DataFlexAssy.FlexAssy_22_ICT_Component.Rows[j]["SationID"]</td>
                                <td>@Model.DataFlexAssy.FlexAssy_22_ICT_Component.Rows[j]["Quantity"]</td>
                                <td>@Model.DataFlexAssy.FlexAssy_22_ICT_Component.Rows[j]["MakerName"]</td>
                                <td>@Model.DataFlexAssy.FlexAssy_22_ICT_Component.Rows[j]["MaxQuantity"]</td>
                                <td>@Model.DataFlexAssy.FlexAssy_22_ICT_Component.Rows[j]["PartLotMaker"]</td>
                                <td>@Model.DataFlexAssy.FlexAssy_22_ICT_Component.Rows[j]["DateMaintain"]</td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
    </body>
</html>

}
