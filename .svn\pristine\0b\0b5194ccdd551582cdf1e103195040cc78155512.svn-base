﻿@using System.Configuration;
@using System.Data;
@using Trace_AbilitySystem.Libs
@model Trace_AbilitySystem.Libs.DTOClass.DataAllTrace

@if (Model != null && Model.DataPismoFlexAssy.FlexAssy_03_LaserSUS?.Rows.Count > 0)
{
    <h2 class="bd-title" id="3-LaserSUS">LaserSUS</h2>
    <table class="table table-bordered">
        <thead>
            <tr>
                <th scope="col">Control Item</th>
                <th scope="col">Control Value</th>
            </tr>
        </thead>
        <tbody>
            @*<tr>
            <td>ItemName</td>
            <td>@(ViewBag.ItemName)</td>
        </tr>
        <tr>
            <td>IndicationNo</td>
            <td>@(ViewBag.IndicationNo)</td>
        </tr>*@
            <tr>
                <td>BlockID</td>
                <td>@Model.DataPismoFlexAssy.FlexAssy_03_LaserSUS.Rows[0]["BlockID"]</td>
            </tr>
            <tr>
                <td>ProgramName</td>
                <td>@Model.DataPismoFlexAssy.FlexAssy_03_LaserSUS.Rows[0]["ProgramName"]</td>
            </tr>
            <tr>
                <td>MachineID</td>
                <td>@Model.DataPismoFlexAssy.FlexAssy_03_LaserSUS.Rows[0]["MachineID"]</td>
            </tr>
            <tr>
                <td>ItemName</td>
                <td>@Model.DataPismoFlexAssy.FlexAssy_03_LaserSUS.Rows[0]["ItemName"]</td>
            </tr>
            <tr>
                <td>DateTime</td>
                <td>@Model.DataPismoFlexAssy.FlexAssy_03_LaserSUS.Rows[0]["DateTime"]</td>
            </tr>
            <tr>
                <td>FileName</td>
                <td>@Model.DataPismoFlexAssy.FlexAssy_03_LaserSUS.Rows[0]["FileName"]</td>
            </tr>
            <tr>
                <td>ErrorMap</td>
                <td>@Model.DataPismoFlexAssy.FlexAssy_03_LaserSUS.Rows[0]["ErrorMap"]</td>
            </tr>
            <tr>
                <td>OperatorPkid</td>
                <td>@Model.DataPismoFlexAssy.FlexAssy_03_LaserSUS.Rows[0]["OperatorPkid"]</td>
            </tr>
            <tr>
                <td>SyncDataAVIcosmetic</td>
                <td>@Model.DataPismoFlexAssy.FlexAssy_03_LaserSUS.Rows[0]["SyncDataAVIcosmetic"]</td>
            </tr>
            <tr>
                <td>SyncDataAVI_SUS</td>
                <td>@Model.DataPismoFlexAssy.FlexAssy_03_LaserSUS.Rows[0]["SyncDataAVI_SUS"]</td>
            </tr>
            <tr>
                <td>EMapID_A</td>
                <td>@Model.DataPismoFlexAssy.FlexAssy_03_LaserSUS.Rows[0]["EMapID_A"]</td>
            </tr>
            @if (Model != null && Model.DataPismoFlexAssy.FlexAssy_03_LaserSUS_Detail?.Rows.Count > 0)
            {
                <tr>
                    <td>ProductID</td>
                    <td>@Model.DataPismoFlexAssy.FlexAssy_03_LaserSUS_Detail.Rows[0]["ProductID"]</td>
                </tr>
                <tr>
                    <td>Grade</td>
                    <td>@Model.DataPismoFlexAssy.FlexAssy_03_LaserSUS_Detail.Rows[0]["Grade"]</td>
                </tr>
            }
        </tbody>
    </table>
}