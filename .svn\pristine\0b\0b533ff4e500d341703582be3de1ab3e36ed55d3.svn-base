﻿using System;
using System.Data;
using System.Data.SqlClient;
using Trace_AbilitySystem.Libs.Dataconnect;

namespace Trace_AbilitySystem.Libs.ITrace_01_BareFlex_Services
{
    public class BareFlex_07_3_DryFilmForButtonPlating_DEVELOPService : IBareFlex_07_3_DryFilmForButtonPlating_DEVELOPService
    {
        private readonly DbExecute _db;
        private readonly string connectionStringOption = "F1";

        public BareFlex_07_3_DryFilmForButtonPlating_DEVELOPService()
        {
            _db = new SqlExecute();
        }

        public int BareFlex_07_3_DryFilmForButtonPlating_DEVELOP_Insert(string workOrder, DateTime dateTime, string machineID, string operatorID, DateTime? firstPieceBuyoffControlDate, int? firstPieceBuyoffControlID, DateTime? machineMaintenanceDate, int? machineMaintenanceID)
        {
            var paras = new SqlParameter[8];
            paras[0] = new SqlParameter("@WorkOrder", workOrder);
            paras[1] = new SqlParameter("@DateTime", dateTime);
            paras[2] = new SqlParameter("@MachineID", machineID ?? (object)DBNull.Value);
            paras[3] = new SqlParameter("@OperatorID", operatorID ?? (object)DBNull.Value);
            paras[4] = new SqlParameter("@FirstPieceBuyoffControlDate", firstPieceBuyoffControlDate ?? (object)DBNull.Value);
            paras[5] = new SqlParameter("@FirstPieceBuyoffControlID", firstPieceBuyoffControlID ?? (object)DBNull.Value);
            paras[6] = new SqlParameter("@MachineMaintenanceDate", machineMaintenanceDate ?? (object)DBNull.Value);
            paras[7] = new SqlParameter("@MachineMaintenanceID", machineMaintenanceID ?? (object)DBNull.Value);

            return _db.Execute_Modify("sp_sms_BareFlex_07_3_DryFilmForButtonPlating_DEVELOP_Insert", paras, CommandType.StoredProcedure, connectionStringOption);
        }

        public DataTable BareFlex_07_3_DryFilmForButtonPlating_DEVELOP_GetByWorkOrder(string workOrder)
        {
            var paras = new SqlParameter[1];
            paras[0] = new SqlParameter("@WorkOrder", workOrder ?? (object)DBNull.Value);

            return _db.Execute_Table("sp_sms_BareFlex_07_3_DryFilmForButtonPlating_DEVELOP_GetByWorkOrder", paras, CommandType.StoredProcedure, connectionStringOption);
        }

        public DataTable BareFlex_07_3_DryFilmForButtonPlating_DEVELOP_GetByListWorkOrder(DataTable listWorkOrder)
        {
            var paras = new SqlParameter[1];
            paras[0] = new SqlParameter("@ListWorkOrder", listWorkOrder ?? (object)DBNull.Value);

            return _db.Execute_Table("sp_sms_BareFlex_07_3_DryFilmForButtonPlating_DEVELOP_GetByListWorkOrder", paras, CommandType.StoredProcedure, connectionStringOption);

        }
    }
}