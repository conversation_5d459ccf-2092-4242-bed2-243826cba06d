﻿using System;
using System.Data;
using System.Data.SqlClient;
using Trace_AbilitySystem.Libs.Dataconnect;

namespace Trace_AbilitySystem.Libs.ITrace_01_BareFlex_Services
{
    public class BareFlex_07_4_5_DryFilmForCircuit_EXPOSINGService : IBareFlex_07_4_5_DryFilmForCircuit_EXPOSINGService
    {
        private readonly DbExecute _db;
        private readonly string connectionStringOption = "F1";

        public BareFlex_07_4_5_DryFilmForCircuit_EXPOSINGService()
        {
            _db = new SqlExecute();
        }

        public int BareFlex_07_4_5_DryFilmForCircuit_EXPOSING_Insert(string workOrder, DateTime dateTime, string machineID, string operatorID, DateTime? machineMaintenanceDate, int? machineMaintenanceID, string SequencyNumber)
        {
            var paras = new SqlParameter[7];
            paras[0] = new SqlParameter("@WorkOrder", workOrder);
            paras[1] = new SqlParameter("@DateTime", dateTime);
            paras[2] = new SqlParameter("@MachineID", machineID ?? (object)DBNull.Value);
            paras[3] = new SqlParameter("@OperatorID", operatorID ?? (object)DBNull.Value);
            paras[4] = new SqlParameter("@MachineMaintenanceDate", machineMaintenanceDate ?? (object)DBNull.Value);
            paras[5] = new SqlParameter("@MachineMaintenanceID", machineMaintenanceID ?? (object)DBNull.Value);
            paras[6] = new SqlParameter("@SequencyNumber", SequencyNumber);
            return _db.Execute_Modify("sp_sms_BareFlex_07_4_5_DryFilmForCircuit_EXPOSING_Insert_New", paras, CommandType.StoredProcedure, connectionStringOption);
        }

        public DataTable BareFlex_07_4_5_DryFilmForCircuit_EXPOSING_GetByWorkOrder(string workOrder)
        {
            var paras = new SqlParameter[1];
            paras[0] = new SqlParameter("@WorkOrder", workOrder ?? (object)DBNull.Value);

            return _db.Execute_Table("sp_sms_BareFlex_07_4_5_DryFilmForCircuit_EXPOSING_GetByWorkOrder", paras, CommandType.StoredProcedure, connectionStringOption);
        }

        public DataTable BareFlex_07_4_5_DryFilmForCircuit_EXPOSING_GetByListWorkOrder(DataTable listWorkOrder)
        {
            var paras = new SqlParameter[1];
            paras[0] = new SqlParameter("@ListWorkOrder", listWorkOrder ?? (object)DBNull.Value);

            return _db.Execute_Table("sp_sms_BareFlex_07_4_5_DryFilmForCircuit_EXPOSING_GetByListWorkOrder", paras, CommandType.StoredProcedure, connectionStringOption);
        }
    }
}