﻿using System;
namespace Trace_AbilitySystem.Libs.DTOClass.ShopFloor
{
    public class MachineAlarmContent
    {
        public int ID { get; set; }
        public string MachineID { get; set; }
        public string ProgramName { get; set; }
        public string ItemName { get; set; }
        public string Part { get; set; }
        public string AlarmContent { get; set; }
        public int isLock { get; set; }
        public string AlarmType { get; set; }
        public int MailAction { get; set; }
        public int ClientAction { get; set; }
        public int ProductionControlID { get; set; }
        public int SPC_ID { get; set; }
        public DateTime CreatedDate { get; set; }
        public string CreatedUser { get; set; }
        public string Comment { get; set; }
        public DateTime pdtCtrlDateTime { get; set; }
        public string LinkData { get; set; }
        //public string Act { get; set; }
        //public string MachineAreaID { get; set; }

    }

    public class MachineAlarmContentSetting
    {
        public string AlarmType { get; set; }
        public bool AllowUnLock { get; set; }        
    }

    public class TblParameterBaking_Blocks
    {
        public string BlockID { get; set; }
        public string Result { get; set; }
        public string Comment { get; set; }
    }

    public class MachineAreaAutoLock
    {
        public int PKID { get; set; }
        public string MachineID { get; set; }
        public string MachineName { get; set; }
        public string MachineAreaID { get; set; }
        public int IsSPC { get; set; }
        public int IsParamaster { get; set; }
        public int IsProcessChange { get; set; }
        public string OperatorID { get; set; }
        public string MachineType { get; set; }
        public string OrderType { get; set; }
        public DateTime CreatedDate { get; set; }
    }

}
