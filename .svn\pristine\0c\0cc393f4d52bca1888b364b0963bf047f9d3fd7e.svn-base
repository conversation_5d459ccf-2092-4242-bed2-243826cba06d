﻿using System;
using System.Data;
using System.Threading.Tasks;

namespace Trace_AbilitySystem.Libs.ITrace_02_FlexAssy_Services
{
    public interface IFlexAssy_10_Info_PastingService
    {
        Task<int> FlexAssy_10_Info_Pasting_ProcessData(string factory, DateTime createdDateSearch, int minutes, string connectionStringOption);

        Task<int> FlexAssy_10_Info_Pasting_Detail_DataPcs_ProcessData(string factory, DateTime createdDateSearch, int minutes, string connectionStringOption, string stage = "FlexAssy_10_Info_Pasting_Detail_DataPcs");
        int FlexAssy_10_Info_Pasting_Insert(string blockID, DateTime? dateTime, string machineID, string operatorID, string programName, string coverJigID, DateTime? coverJigManualCleaningTime, long? coverJigManualCleaningID,
            string carrierJigID, DateTime? carrierJigManualCleaningTime, long? carrierJigManualCleaningID, string locateID, DateTime? locateManualCleaningTime, long? locateManualCleaningID, string connectionStringOption);
        int FlexAssy_10_Info_Pasting_Detail_DataPcs_Insert(string productID, DateTime? dateTime, string blockID,  string machineID, string programName, string station, string result, string reelID, string feederID, string nozzleID,
           string compNameReel, string compNameFeeder, string compNameNozzle, string compTypeReel, string compTypeFeeder, string compTypeNozzle, DateTime? dateMaintainceReel, DateTime? dateMaintainceFeeder, DateTime? dateMaintainceNozzle,
           string makerNameReel, string makerNameFeeder, string makerNameNozzle, string partLotMakerReel, string partLotMakerFeeder, string partLotMakerNozzle,
           int? location, long? DateMaintainceFeederID, long? DateMaintainceNozzleID, string PositionReel, string PositionNozzleID, int? TotalPickUpFeederID, int? TotalErrorFeederID,
           DateTime? machineMaintenanceDate, int? machineMaintenanceID, string productionConditionResult, int? productionConditionID,
            DateTime? firstPieceBuyoffControlDate, int? firstPieceBuyoffControlID, string MaterialLot, string DiecutVendor, string DiecutLot, 
            string DiecutReelID, int? QuantityComp, int? TotalPickUpReelID, int? iTotalPickUpNozzleID, int? iTotalErrorNozzleID, string sPositionFeederID, string connectionStringOption);
        DataTable FlexAssy_10_Info_Pasting_GetByBlockID(string blockID, string connectionStringOption, out string connectionStringOk);
        DataTable FlexAssy_10_Info_Pasting_GetByListBlockID(DataTable dtBlockID, string connectionStringOption, out string connectionStringOk);
        DataTable FlexAssy_10_Info_Pasting_Detail_DataPcs_GetByProductID(string productID, string connectionStringOption, out string connectionStringOk);
        DataTable FlexAssy_10_Info_Pasting_Detail_DataPcs_GetByListProductID(DataTable listProductID, string connectionStringOption, out string connectionStringOk);
        DataTable FlexAssy_10_Info_Pasting_Detail_DataPcs_GetByBlockID(string blockID, string connectionStringOption, out string connectionStringOk);
        DataTable FlexAssy_10_Info_Pasting_Detail_DataPcs_GetByListBlockID(DataTable dtBlockID, string connectionStringOption, out string connectionStringOk);
        Task<int> OEE_10_Infor_Pasting_ProcessData(string factory, DateTime DateTime, int minutes, string connectionStringOption);
    }
}
