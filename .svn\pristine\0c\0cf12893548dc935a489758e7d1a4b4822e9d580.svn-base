﻿using System;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Timers;
using System.Windows.Forms;
using Trace_AbilitySystem.Libs;
using Timer = System.Timers.Timer;

namespace Trace_AbilitySystem_FlexAssy_F4_DataSyn_CopyLog_ICT_F2
{
	public partial class FrmProcess : Form
	{
		private readonly string _serviceName = "Trace-AbilitySystem_FlexAssy_F4_DataSyn_CopyLog_ICT_F2";
		private string _pathSaveLog = null;
		private string _pathSaveLog_Medium = null;
		private string _pathLinkTo_Medium = null;
		private Timer _timer;
		private int _interval = 30;
		private int _startService = 0;
		private readonly string _factory = "F4";

		public FrmProcess()
		{
			if (Common.IsProcessOpen(_serviceName))
			{
				DialogHelper.Info("Process " + _serviceName + " is running");
				Environment.Exit(0);
			}
			else
				InitializeComponent();
		}

		private void FrmProcess_Load(object sender, EventArgs e)
		{
			notifyIcon.BalloonTipIcon = ToolTipIcon.Info;
			notifyIcon.BalloonTipText = $"{_factory}: NotifyIcon FlexAssy DataSyn Copy Log ICT F2";
			notifyIcon.BalloonTipTitle = $"{_factory}: FlexAssy DataSyn Copy Log ICT F2";
			notifyIcon.Text = $"{_factory}: FlexAssy DataSyn Copy ICT Log F2";
			notifyIcon.ShowBalloonTip(1000);

			ManageLog.WriteProcess(false, "Start Service FlexAssy Read Log F2 " + _factory, 0, 0);

			DataTable dt = Singleton_03_Common.IServiceSettingService.ServiceSetting_GetByServiceName(_serviceName);
			if (dt?.Rows.Count > 0)
			{
				_interval = int.Parse(dt.Rows[0]["IntervalSecond"].ToString());
				_pathSaveLog = dt.Rows[0]["PathSynLog"].ToString();
				_pathSaveLog_Medium = dt.Rows[0]["PathSynLog_Medium"].ToString();
				_pathLinkTo_Medium = dt.Rows[0]["PathLinkTo_Medium"].ToString();
			}

			richTextBox.Invoke(new Action(() => richTextBox.SelectionColor = Color.Black));

			_startService = _interval;
			AppendRichTextBox(richTextBox, "Processing timeout loop: " + _startService);

			_timer = new Timer { Interval = 1000 };
			_timer.Elapsed += Timer_Tick;
			_timer.Enabled = true;
			_timer.Start();
		}

		/// <summary>
		///     Process Data From Database
		/// </summary>
		/// <param name="sender"></param>
		/// <param name="e"></param>
		private async void Timer_Tick(object sender, ElapsedEventArgs e)
		{
			_timer.Enabled = false;
			_timer.Stop();

			try
			{
				_startService -= 1;

				if (_startService == 0)
				{
					Singleton_03_Common.IServiceInfo_Interface.UpdateServiceRunning(_serviceName);

					int length = 0;
					richTextBox.Invoke(new Action(() => length = richTextBox.Lines.Length));
					richTextBox.Invoke(new Action(() => ChangeLineRichTextBox(richTextBox, richTextBox.Lines.Length - 1, "....................Processing DATA......................." + Environment.NewLine)));
					richTextBox.Invoke(new Action(() => richTextBox.SelectionColor = Color.Green));

					// Process Log
					await Singleton_02_FlexAssy.IFlexAssy_CommonService.ProcessService_CopyLogICT(richTextBox, _factory, _pathSaveLog, _pathSaveLog_Medium, _pathLinkTo_Medium);

					DataTable dt = Singleton_03_Common.IServiceSettingService.ServiceSetting_GetByServiceName(_serviceName);
					if (dt?.Rows.Count > 0)
					{
						_interval = int.Parse(dt.Rows[0]["IntervalSecond"].ToString());
						_pathSaveLog = dt.Rows[0]["PathSynLog"].ToString();
						_pathSaveLog_Medium = dt.Rows[0]["PathSynLog_Medium"].ToString();
						_pathLinkTo_Medium = dt.Rows[0]["PathLinkTo_Medium"].ToString();
					}

					_startService = _interval;
					richTextBox.Invoke(new Action(() => richTextBox.SelectionColor = Color.Black));
					AppendRichTextBox(richTextBox, "Processing timeout loop: " + _startService);
				}
				else
				{
					int length = 0;
					richTextBox.Invoke(new Action(() => length = richTextBox.Lines.Length));
					richTextBox.Invoke(new Action(() => ChangeLineRichTextBox(richTextBox, richTextBox.Lines.Length - 1, "Processing timeout loop: " + _startService)));
				}
			}
			catch (Exception ex)
			{
				richTextBox.Clear();
				ManageLog.WriteErrorApp("Timer_Tick: " + _serviceName + "\n" + ex.Message);
			}

			_timer.Interval = 1000;
			_timer.Enabled = true;
			_timer.Start();
		}

		private void RichTextBox_TextChanged(object sender, System.EventArgs e)
		{
			richTextBox.SelectionStart = richTextBox.Text.Length;
			richTextBox.ScrollToCaret();
		}

		public void InvokeMethod(RichTextBox RTB, string msg)
		{
			RTB.AppendText(msg);
			if (RTB.Lines.Count() >= 100)
			{
				RTB.Clear();
			}
		}

		public void AppendRichTextBox(RichTextBox RTB, string msg)
		{
			if (RTB.InvokeRequired)
			{
				RTB.BeginInvoke(new InvokeDelegate(InvokeMethod), RTB, msg);
			}
			else
			{
				InvokeMethod(RTB, msg);
			}
		}

		private delegate void InvokeDelegate(RichTextBox RTB, string msg);

		private void ChangeLineRichTextBox(RichTextBox RTB, int line, string text)
		{
			if (line < 0)
			{
				RTB.Clear();
				return;
			}

			int s1 = RTB.GetFirstCharIndexFromLine(line);
			int s2 = line < RTB.Lines.Count() - 1 ? RTB.GetFirstCharIndexFromLine(line + 1) - 1 : RTB.Text.Length;
			RTB.Select(s1, s2 - s1);
			RTB.SelectedText = text;
		}

		//Disable close button
		private const int CpDisableCloseButton = 0x200;
		protected override CreateParams CreateParams
		{
			get
			{
				CreateParams cp = base.CreateParams;
				cp.ClassStyle |= CpDisableCloseButton;
				return cp;
			}
		}

		private void NotifyIcon_DoubleClick(object sender, EventArgs e)
		{
			Show();
			WindowState = FormWindowState.Normal;
		}

		private void FrmProcess_Resize(object sender, EventArgs e)
		{
			if (FormWindowState.Minimized == WindowState)
			{
				notifyIcon.Visible = true;
				notifyIcon.ShowBalloonTip(500);
				Hide();
			}
			else if (FormWindowState.Normal == WindowState)
			{
				notifyIcon.Visible = false;
			}
		}

		private void FrmProcess_FormClosing(object sender, FormClosingEventArgs e)
		{
			DialogResult window = MessageBox.Show("Close the program: " + _serviceName + "?", "Are you sure?", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);

			e.Cancel = window == DialogResult.No;
		}
	}
}