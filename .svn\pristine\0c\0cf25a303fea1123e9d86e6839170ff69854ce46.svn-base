﻿using System;
using System.Data;

namespace Trace_AbilitySystem.Libs.ITrace_01_BareFlex_Services
{
    public interface IBareFlex_16_1_EMILamination_PASTEEMIService
    {
        int BareFlex_16_1_EMILamination_PASTEEMI_Insert(string workOrder, DateTime dateTime, string machineID, string operatorID, string fixtureID, DateTime? firstPieceBuyoffControlDate, int? firstPieceBuyoffControlID, DateTime? machineMaintenanceDate, int? machineMaintenanceID);
        DataTable BareFlex_16_1_EMILamination_PASTEEMI_GetByWorkOrder(string workOrder);
        DataTable BareFlex_16_1_EMILamination_PASTEEMI_GetByListWorkOrder(DataTable listWorkOrder);
    }
}