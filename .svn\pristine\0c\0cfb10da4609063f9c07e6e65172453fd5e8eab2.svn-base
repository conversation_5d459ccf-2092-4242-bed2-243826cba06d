﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using Trace_AbilitySystem.Libs.Dataconnect;
using Trace_AbilitySystem.Libs.DTOClass;
using Trace_AbilitySystem.Libs.ITrace_02_FlexAssy_Services;

namespace Trace_AbilitySystem.Libs.Trace_02_FlexAssy_Services
{
    public class FlexAssy_07_CoolingService : IFlexAssy_07_CoolingService
    {
        private readonly DbExecute _db;

        public FlexAssy_07_CoolingService()
        {
            _db = new SqlExecute();
        }

        public async Task<int> OEE_07_Cooling_ProcessData(string factory, DateTime DateTime, int minutes, string connectionStringOption)
        {
            string stage = "OEE_07_Cooling";
            string typeBarcode = "BlockID";
            DateTime dateNow = DateTime.Now;
            DateTime timeLast = DateTime;

            try
            {
                int recordNeedSyn = 0;
                int recordSyned = 0;

                DataTable dt = null;
                dt = Singleton_02_FlexAssy.ISource_FlexAssy_PMTT_TRACE_Service.reflow_info_GetOEEDataWithCoolingStartTime(timeLast, minutes, connectionStringOption);
                if (dt == null)
                    return -1;

                if (dt.Rows.Count > 0)
                {
                    string connectionStringOptionBoard = Singleton_03_Common.IDBInfoService.ConnectionStringOption(factory, "BoardRegistrationDB");
                    List<DTOClass.SPCBase.OEE_Item_Product> lstOEE_Item_Product = new List<DTOClass.SPCBase.OEE_Item_Product>();
                    for (int i = 0; i < dt.Rows.Count; i++)
                    {
                        string MachineID = DataConvert.ConvertToString(dt.Rows[i]["MachineID"]).Trim();
                        string BlockID = DataConvert.ConvertToString(dt.Rows[i]["BlockID"]);
                        string ItemName = SingletonLocal.BoardRegisService.GetItemNameByBlockID(BlockID, out string IndicationNumber, connectionStringOptionBoard);
                        DateTime? _DateTime = null;
                        if (dt.Rows[i]["DateTime"] != DBNull.Value)
                            _DateTime = Convert.ToDateTime(dt.Rows[i]["DateTime"]);

                        if (_DateTime.Value.Hour < 6)
                        {
                            _DateTime = _DateTime.Value.AddDays(-1);
                            _DateTime = _DateTime.Value.Date;
                        }
                        else
                        {
                            _DateTime = _DateTime.Value.Date;
                        }
                        string Line = "";
                        DataTable dtMachineID = Singleton_04_Machine.ItMachineMtnService.tMachineList_GetByID(MachineID);
                        if (dtMachineID?.Rows.Count > 0)
                        {
                            Line = dtMachineID.Rows[0]["LineID"] + "";
                        }
                        int index = lstOEE_Item_Product.FindIndex(item => item.LineID == Line && item.MachineID == MachineID && item.IndicationNumber == IndicationNumber && _DateTime == item.DateTime);
                        if (index >= 0)
                        {
                            lstOEE_Item_Product[index].MachineID = MachineID;
                            lstOEE_Item_Product[index].LineID = Line;
                            lstOEE_Item_Product[index].ItemName = ItemName;
                            lstOEE_Item_Product[index].IndicationNumber = IndicationNumber;
                            lstOEE_Item_Product[index].DateTime = _DateTime;
                            lstOEE_Item_Product[index].TotalOKDay += 1;
                            lstOEE_Item_Product[index].TotalNGDay = 0;
                        }
                        else
                        {
                            DTOClass.SPCBase.OEE_Item_Product New_OEE_Item_Product = new DTOClass.SPCBase.OEE_Item_Product();
                            New_OEE_Item_Product.MachineID = MachineID;
                            New_OEE_Item_Product.LineID = Line;
                            New_OEE_Item_Product.ItemName = ItemName;
                            New_OEE_Item_Product.IndicationNumber = IndicationNumber;
                            New_OEE_Item_Product.DateTime = _DateTime;
                            New_OEE_Item_Product.TotalOKDay = 1;
                            New_OEE_Item_Product.TotalNGDay = 0;
                            lstOEE_Item_Product.Add(New_OEE_Item_Product);
                        }

                        timeLast = Convert.ToDateTime(dt.Rows[i]["Datetime"]);
                    }
                    //number of records to sync
                    for (int i = 0; i < lstOEE_Item_Product.Count; i++)
                    {
                        int rs = Singleton_06_SPC.iSPC_CommonService.OEE_Product_Save(lstOEE_Item_Product[i].ItemName, lstOEE_Item_Product[i].IndicationNumber, lstOEE_Item_Product[i].LineID, "Cooling", lstOEE_Item_Product[i].MachineID,
                            lstOEE_Item_Product[i].TotalOKDay, lstOEE_Item_Product[i].TotalNGDay, lstOEE_Item_Product[i].DateTime, "NewSPC_" + factory);
                        if (rs == -9)
                            return -1;
                    }
                    Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, stage, timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff"));
                }
                else
                {
                    timeLast = timeLast.AddMinutes(minutes) > dateNow ? timeLast : timeLast.AddMinutes(minutes);

                    // Update ValueSearch
                    Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, stage, timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff"));
                }

                ManageLog.WriteProcess(true, null, recordNeedSyn, recordSyned);
            }
            catch (Exception ex)
            {
                Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, stage, timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff"));
                ManageLog.WriteErrorApp(stage + "\n" + ex.Message);

                return -1;
            }

            await Task.Delay(50);

            return 1;
        }
        public async Task<int> FlexAssy_07_Cooling_ProcessData(string factory, DateTime reflowTimeStartSearch, int minutes, string connectionStringOption)
        {
            string stage = "FlexAssy_07_Cooling";
            string typeBarcode = "BlockID";

            try
            {
                DateTime dateNow = DateTime.Now;
                DateTime timeLast = reflowTimeStartSearch;
                int recordNeedSyn = 0;
                int recordSyned = 0;
                // lấy chậm hơn thức tế 10 phút
                if (DateTime.Compare(timeLast.AddMinutes(10), dateNow) > 0)
                    return -1;

                DataTable dt = null;
                if (factory.Equals("F5"))
                {
                    dt = Singleton_02_FlexAssy.ISource_FlexAssy_PMTT_TRACE_Service.reflow_info_GetDataWithCoolingStartTime(timeLast, minutes, connectionStringOption);
                }
                else
                {
                    dt = Singleton_02_FlexAssy.ISource_FlexAssy_PMTT_TRACE_Service.reflow_info_GetDataWithCoolingStartTime(timeLast, minutes, connectionStringOption);
                }
                if (dt == null)
                    return -1;

                if (dt.Rows.Count > 0)
                {
                    List<MachineMaintenance> machineMaintenances = new List<MachineMaintenance>();
                    List<ProductionCondition> productionConditions = new List<ProductionCondition>();
                    List<FirstPieceBuyOffControl> firstPieceBuyOffControls = new List<FirstPieceBuyOffControl>();
                    List<MachineOfStage> machineOfStages = new List<MachineOfStage>();
                    List<OperatorOfStage> operatorOfStages = new List<OperatorOfStage>();

                    string connectionStringOptionMachine = Singleton_03_Common.IDBInfoService.ConnectionStringOption(factory, "MachineDB");

                    //number of records to sync
                    recordNeedSyn = dt.Rows.Count;
                    for (int i = 0; i < recordNeedSyn; i++)
                    {
                        string MachineMaintain = dt.Rows[i]["CoolingMachineID"].ToString().Trim();
                        string blockID = dt.Rows[i]["BlockID"].ToString().Trim();
                        DateTime dateTime = Convert.ToDateTime(dt.Rows[i]["CoolingStartTime"]);
                        string machineID = dt.Rows[i]["CoolingMachineID"].ToString().Trim();
                        string operatorID = dt.Rows[i]["CoolingOperatorID"].ToString().Trim();
                        string programName = null;
                        DateTime? timeTest = null;
                        int? laneID = null;
                        if (dt.Rows[i]["LaneID"] != DBNull.Value)
                        {
                            if (factory.Equals("F3"))
                            {
                                if (dt.Rows[i]["CoolingMachineID"].ToString().Trim().Equals("MORF017A"))
                                {
                                    laneID = 1;
                                }
                                else if (dt.Rows[i]["CoolingMachineID"].ToString().Trim().Equals("MORF017B"))
                                {
                                    laneID = 2;
                                }
                                else
                                {
                                    laneID = int.Parse(dt.Rows[i]["LaneID"].ToString());
                                }
                            }
                            else
                            {
                                laneID = int.Parse(dt.Rows[i]["LaneID"].ToString());
                            }
                        }
                        string profile = null;
                        string profileRoot = null;


                        if (dt.Rows[i]["ProfileSetupLogPKID"] != DBNull.Value)
                        {
                            DataTable dt1 = Singleton_02_FlexAssy.ISource_FlexAssy_PMTT_TRACE_Service.Reflow_Profile_Setup_log_GetDataWithPkid_FindProgramName(int.Parse(dt.Rows[i]["ProfileSetupLogPKID"].ToString()), connectionStringOption);
                            if (dt1 == null)
                                return -1;
                            if (dt1.Rows.Count > 0)
                                programName = dt1.Rows[0]["ProgramName"].ToString().Trim();
                        }
                        if (factory == "F5")
                        {
                            programName = dt.Rows[i]["ProgramName"].ToString().Trim();
                            laneID = int.Parse(dt.Rows[i]["PCBSide"].ToString());
                            if (laneID == 2)
                            {
                                continue;
                            }
                            if (machineID.Contains("-"))
                            {
                                MachineMaintain = machineID.Split('-')[0];
                            }
                        }

                        // Find info machine maintenance date
                        List<MachineMaintenance> machineMaintenancesOther = Singleton_03_Common.ICommon_CommonService.GetMachine_Maintenance(null, MachineMaintain, dateTime, out DateTime? machineMaintenanceDate, out int? machineMaintenanceID, machineMaintenances, connectionStringOptionMachine);
                        if (machineMaintenancesOther != null)
                            machineMaintenances.AddRange(machineMaintenancesOther);

                        // Find info production condition
                        ProductionCondition productionCondition = Singleton_03_Common.ICommon_CommonService.GetMachine_Condition(MachineMaintain, dateTime, programName, out string productionConditionResult, out int? productionConditionID, productionConditions, connectionStringOptionMachine);
                        if (productionCondition != null)
                            productionConditions.Add(productionCondition);

                        // find infor first piece buy off controll
                        FirstPieceBuyOffControl firstPieceBuyOffControl = Singleton_03_Common.ICommon_CommonService.GetMachine_FirstPieceBuyOff(factory, machineID, blockID, dateTime, out DateTime? firstPieceBuyoffControlDate, out int? firstPieceBuyoffControlID, firstPieceBuyOffControls, connectionStringOptionMachine);
                        if (firstPieceBuyOffControl != null)
                            firstPieceBuyOffControls.Add(firstPieceBuyOffControl);

                        //if (dt.Rows[i]["ProfileCheckPdfPKID"] != DBNull.Value)
                        //{
                        //    DataTable dt2 = Singleton_02_FlexAssy.ISource_FlexAssy_PMTT_TRACE_Service.Reflow_Profile_Setup_PDF_GetDataWithPkid_FindProfile(int.Parse(dt.Rows[i]["ProfileCheckPdfPKID"].ToString()), connectionStringOption);
                        //    if (dt2 == null)
                        //        return -1;
                        //    if (dt2.Rows.Count > 0)
                        //    {
                        //        timeTest = Convert.ToDateTime(dt2.Rows[0]["TimeTest"].ToString());
                        //        profileRoot = dt2.Rows[0]["LinkFile"].ToString().Trim(); // Need get log to F2
                        //    }
                        //}

                        // Insert data
                        int rs = FlexAssy_07_Cooling_Insert(blockID, dateTime, machineID, operatorID, programName, timeTest, laneID, profileRoot, profile, machineMaintenanceDate, machineMaintenanceID,
                            productionConditionResult, productionConditionID, firstPieceBuyoffControlDate, firstPieceBuyoffControlID, factory);
                        if (rs == -9)
                            return -1;

                        // machineID, OperatorID definition makes at the stage
                        MachineOfStage machineOfStage = Singleton_03_Common.ICommon_CommonService.GetMachineOfStage(machineID, null, stage, factory, typeBarcode, machineOfStages);
                        if (machineOfStage != null)
                            machineOfStages.Add(machineOfStage);

                        OperatorOfStage operatorOfStage = Singleton_03_Common.ICommon_CommonService.GetOperatorOfStage(operatorID, null, stage, factory, typeBarcode, operatorOfStages);
                        if (operatorOfStage != null)
                            operatorOfStages.Add(operatorOfStage);

                        // update the time that the record is synchronized
                        timeLast = Convert.ToDateTime(dt.Rows[i]["CoolingStartTime"]);

                        // count the number of synchronized records
                        recordSyned++;
                    }
                    // Update ValueSearch
                    if (recordSyned == 0)
                    {
                        timeLast = timeLast.AddMinutes(minutes) > dateNow ? timeLast : timeLast.AddMinutes(minutes);

                        // Update ValueSearch
                        Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, stage, timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff"));
                    }
                    else
                    {
                        Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, stage, timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff"));
                    }
                }
                else
                {
                    timeLast = timeLast.AddMinutes(minutes) > dateNow ? timeLast : timeLast.AddMinutes(minutes);

                    // Update ValueSearch
                    Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, stage, timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff"));
                }

                ManageLog.WriteProcess(true, null, recordNeedSyn, recordSyned);
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp(stage + "\n" + ex.Message);

                return -1;
            }

            await Task.Delay(500);

            return 1;
        }

        public int FlexAssy_07_Cooling_Insert(string blockID, DateTime? dateTime, string machineID, string operatorID, string programName, DateTime? timeTest, int? laneID, string profileRoot, string profile,
            DateTime? machineMaintenanceDate, int? machineMaintenanceID, string productionConditionResult, int? productionConditionID, DateTime? firstPieceBuyoffControlDate, int? firstPieceBuyoffControlID, string connectionStringOption)
        {
            var paras = new SqlParameter[15];
            paras[0] = new SqlParameter("@BlockID", blockID);
            paras[1] = new SqlParameter("@DateTime", dateTime);
            paras[2] = new SqlParameter("@MachineID", machineID ?? (object)DBNull.Value);
            paras[3] = new SqlParameter("@OperatorID", operatorID ?? (object)DBNull.Value);
            paras[4] = new SqlParameter("@ProgramName", programName ?? (object)DBNull.Value);
            paras[5] = new SqlParameter("@TimeTest", timeTest ?? (object)DBNull.Value);
            paras[6] = new SqlParameter("@LaneID", laneID ?? (object)DBNull.Value);
            paras[7] = new SqlParameter("@ProfileRoot", profileRoot ?? (object)DBNull.Value);
            paras[8] = new SqlParameter("@Profile", profile ?? (object)DBNull.Value);
            paras[9] = new SqlParameter("@MachineMaintenanceDate", machineMaintenanceDate ?? (object)DBNull.Value);
            paras[10] = new SqlParameter("@MachineMaintenanceID", machineMaintenanceID ?? (object)DBNull.Value);
            paras[11] = new SqlParameter("@ProductionConditionResult", productionConditionResult ?? (object)DBNull.Value);
            paras[12] = new SqlParameter("@ProductionConditionID", productionConditionID ?? (object)DBNull.Value);
            paras[13] = new SqlParameter("@FirstPieceBuyoffControlDate", firstPieceBuyoffControlDate ?? (object)DBNull.Value);
            paras[14] = new SqlParameter("@FirstPieceBuyoffControlID", firstPieceBuyoffControlID ?? (object)DBNull.Value);

            //return _db.Execute_Modify("sp_sms_FlexAssy_07_Cooling_Insert", paras, CommandType.StoredProcedure, connectionStringOption);
            return _db.Execute_Modify("sp_sms_FlexAssy_07_Cooling_Insert_FPB2022", paras, CommandType.StoredProcedure, connectionStringOption);
        }

        public DataTable FlexAssy_07_Cooling_GetByBlockID(string blockID, string connectionStringOption, out string connectionStringOk)
        {
            var paras = new SqlParameter[1];
            paras[0] = new SqlParameter("@BlockID", blockID ?? (object)DBNull.Value);

            string connectionStringDefault = null;
            DataTable dt;
            switch (connectionStringOption)
            {
                case "F3":
                    dt = _db.Execute_Table("sp_sms_FlexAssy_07_Cooling_GetByBlockID", paras, CommandType.StoredProcedure, connectionStringOption);
                    break;

                case "F4":
                    dt = _db.Execute_Table("sp_sms_FlexAssy_07_Cooling_GetByBlockID", paras, CommandType.StoredProcedure, connectionStringOption);
                    break;

                case "F5":
                    dt = _db.Execute_Table("sp_sms_FlexAssy_07_Cooling_GetByBlockID", paras, CommandType.StoredProcedure, connectionStringOption);
                    break;
                default:
                    connectionStringDefault = "F3";
                    dt = _db.Execute_Table("sp_sms_FlexAssy_07_Cooling_GetByBlockID", paras, CommandType.StoredProcedure, "F3");
                    if (dt == null || dt?.Rows.Count == 0)
                    {
                        connectionStringDefault = "F4";
                        paras = new SqlParameter[1];
                        paras[0] = new SqlParameter("@BlockID", blockID ?? (object)DBNull.Value);

                        dt = _db.Execute_Table("sp_sms_FlexAssy_07_Cooling_GetByBlockID", paras, CommandType.StoredProcedure, "F4");
                    }
                    if (dt == null || dt?.Rows.Count == 0)
                    {
                        connectionStringDefault = "F5";
                        paras = new SqlParameter[1];
                        paras[0] = new SqlParameter("@BlockID", blockID ?? (object)DBNull.Value);

                        dt = _db.Execute_Table("sp_sms_FlexAssy_07_Cooling_GetByBlockID", paras, CommandType.StoredProcedure, "F5");
                    }
                    break;
            }

            connectionStringOk = connectionStringOption ?? (dt?.Rows.Count > 0 ? connectionStringDefault : null);
            return dt;
        }

        public DataTable FlexAssy_07_Cooling_GetByListBlockID(DataTable listBlockID, string connectionStringOption, out string connectionStringOk)
        {
            var paras = new SqlParameter[1];
            paras[0] = new SqlParameter("@ListBlockID", listBlockID ?? (object)DBNull.Value);

            string connectionStringDefault = null;
            DataTable dt;
            switch (connectionStringOption)
            {
                case "F3":
                    dt = _db.Execute_Table("sp_sms_FlexAssy_07_Cooling_GetByListBlockID", paras, CommandType.StoredProcedure, connectionStringOption);
                    break;

                case "F4":
                    dt = _db.Execute_Table("sp_sms_FlexAssy_07_Cooling_GetByListBlockID", paras, CommandType.StoredProcedure, connectionStringOption);
                    break;

                case "F5":
                    dt = _db.Execute_Table("sp_sms_FlexAssy_07_Cooling_GetByListBlockID", paras, CommandType.StoredProcedure, connectionStringOption);
                    break;

                default:
                    connectionStringDefault = "F3";
                    dt = _db.Execute_Table("sp_sms_FlexAssy_07_Cooling_GetByListBlockID", paras, CommandType.StoredProcedure, "F3");
                    if (dt == null || dt?.Rows.Count == 0)
                    {
                        connectionStringDefault = "F4";
                        paras = new SqlParameter[1];
                        paras[0] = new SqlParameter("@ListBlockID", listBlockID ?? (object)DBNull.Value);

                        dt = _db.Execute_Table("sp_sms_FlexAssy_07_Cooling_GetByListBlockID", paras, CommandType.StoredProcedure, "F4");
                    }
                    if (dt == null || dt?.Rows.Count == 0)
                    {
                        connectionStringDefault = "F5";
                        paras = new SqlParameter[1];
                        paras[0] = new SqlParameter("@ListBlockID", listBlockID ?? (object)DBNull.Value);

                        dt = _db.Execute_Table("sp_sms_FlexAssy_07_Cooling_GetByListBlockID", paras, CommandType.StoredProcedure, "F5");
                    }
                    break;
            }

            connectionStringOk = connectionStringOption ?? (dt?.Rows.Count > 0 ? connectionStringDefault : null);
            return dt;
        }

    }
}