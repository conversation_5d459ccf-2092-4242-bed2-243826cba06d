﻿using System;
using System.Data;
using System.Threading.Tasks;

namespace Trace_AbilitySystem.Libs.ITrace_02_FlexAssy_Services
{
    public interface IFlexAssy_33_3_IPQC_B2BXRayService
    {
        int Insert(string ProductID, int ContentCode, DateTime inputTest,
                    string ItemName, string LineID, string connectionStringOption);

        int Update(string ProductID, int ContentCode, DateTime inputTest, DateTime finishTest,
                        string Result, string LinkResult, string connectionStringOption);
        int Delete(string ProductID, int ProcessCode, string connectionStringOption);
        DataTable GetByProductID(string productID, string connectionStringOption, out string connectionStringOk);
        DataTable GetTestFromIPQC(string ItemCode, string ProductionLine, DateTime ShiftStart, DateTime ShiftEnd,
          string connectionStringOption, out string connectionStringOk);
        DataTable GetByIndi(string IndicationNum, string connectionStringOption, out string connectionStringOk);
        DataTable GetByListIndi(DataTable DtWorkOrder, string connectionStringOption, out string connectionStringOk);
    }
}