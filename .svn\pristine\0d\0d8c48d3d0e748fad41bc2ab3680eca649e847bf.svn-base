﻿@model Trace_AbilitySystem.Libs.DTOClass.DataAllTrace
@using Trace_AbilitySystem.Libs
@using System.Data;
@using System.Configuration;
@if (Model != null && Model.DataFlexAssy.FlexAssy_27_Plasma?.Rows.Count > 0)
{
    string itemName = ViewBag.ItemName;
    if (DataConvert.ConvertToString(itemName).Contains("17AMSJ") == false && DataConvert.ConvertToString(itemName).Contains("18AMSJ") == false)
    {
        <h2 class="bd-title" id="27-plasma">Plasma</h2>
        <table class="table table-bordered">
            <thead>
                <tr>
                    <th scope="col">Control Item</th>
                    <th scope="col">Control Value</th>
                </tr>
            </thead>
			<tbody>
				<tr>
					<td>ItemName</td>
					<td>@(ViewBag.ItemName)</td>
				</tr>
				<tr>
					<td>IndicationNo</td>
					<td>@(ViewBag.IndicationNo)</td>
				</tr>
				<tr>
					<td>PcsID</td>
					<td>@Model.DataFlexAssy.FlexAssy_27_Plasma.Rows[0]["ProductID"]</td>
				</tr>
				<tr>
					<td>PlasmaID</td>
					<td>@Model.DataFlexAssy.FlexAssy_27_Plasma.Rows[0]["QrCode"]</td>
				</tr>
				<tr>
					<td>Input Time</td>
					<td>@Model.DataFlexAssy.FlexAssy_27_Plasma.Rows[0]["InputTime"]</td>
				</tr>
				<tr>
					<td>End Time</td>
					<td>@Model.DataFlexAssy.FlexAssy_27_Plasma.Rows[0]["PlasmaEndTime"]</td>
				</tr>
				@*<tr>
			<td>Machine ID</td>
			<td>@Model.DataFlexAssy.FlexAssy_27_Plasma.Rows[0]["MachineID"]</td>
		</tr>*@
				<tr>
					<td>Operator ID</td>
					<td>@Model.DataFlexAssy.FlexAssy_27_Plasma.Rows[0]["OperatorID"]</td>
				</tr>
				<tr>
					<td>Program name</td>
					<td>@Model.DataFlexAssy.FlexAssy_27_Plasma.Rows[0]["ProgramName"]</td>
				</tr>
				<tr>
					<td>TrolleyID</td>
					@*<td>@Model.DataFlexAssy.FlexAssy_27_Plasma.Rows[0]["TrolleyID"]</td>*@
					<td><a href="javascript:" onclick="viewDataByCompID('@Model.DataFlexAssy.FlexAssy_27_Plasma.Rows[0]["TrolleyID"]', '@(ViewBag.Factory)', '@(ViewBag.ItemName)', '')">@Model.DataFlexAssy.FlexAssy_27_Plasma.Rows[0]["TrolleyID"]</a></td>
				</tr>
				<tr>
					<td>TrolleyID maintaint time</td>
					<td><a onclick="viewDataToolCleaningDate(@Model.DataFlexAssy.FlexAssy_27_Plasma.Rows[0]["TrolleyIDManualCleaningID"], '@Model.DataFlexAssy.FlexAssy_27_Plasma.Rows[0]["TrolleyID"]')" href="javascript:">@Model.DataFlexAssy.FlexAssy_27_Plasma.Rows[0]["TrolleyIDManualCleaningTime"]</a></td>
				</tr>
				<tr>
					<td>Plasma Check</td>
					@if (Model.DataFlexAssy.FlexAssy_27_Plasma.Rows[0]["FinishTime"] != DBNull.Value)
					{
						if (DateTime.Compare(Convert.ToDateTime(Model.DataFlexAssy.FlexAssy_27_Plasma.Rows[0]["FinishTime"]), Convert.ToDateTime(Model.DataFlexAssy.FlexAssy_27_Plasma.Rows[0]["InputTime"])) > 0)
						{
							<td>@Model.DataFlexAssy.FlexAssy_27_Plasma.Rows[0]["FinishTime"]</td>
						}

					}
					else
					{
						<td></td>
					}
				</tr>
				<tr>
					<td>Plasma times</td>
					<td>@Model.DataFlexAssy.FlexAssy_27_Plasma.Rows[0]["Plasmatimes"]</td>
				</tr>

				<tr>
					<td>First piece buyoff control (link)</td>
					<td>
						@if (ViewBag.Factory == "F5")
						{
							<a onclick="viewDataFirstPieceBuyoffControlNew('@Model.DataFlexAssy.FlexAssy_27_Plasma.Rows[0]["MachineID"]','@DataConvert.ConvertToString(ViewBag.IndicationNo)')" href="javascript:">
								@(Singleton_04_Machine.IFPBCheckingService.FPBChecking_GetResultByMachineID_IndicationNumber(DataConvert.ConvertToString(Model.DataFlexAssy.FlexAssy_27_Plasma.Rows[0]["MachineID"]),DataConvert.ConvertToString(ViewBag.IndicationNo)))
							</a>
						}
						else
						{
							<a onclick="viewDataFirstPieceBuyoffControlByTime('@Model.DataFlexAssy.FlexAssy_27_Plasma.Rows[0]["MachineID"]','@DataConvert.ToDateTime(Model.DataFlexAssy.FlexAssy_27_Plasma.Rows[0]["InputTime"]).ToString("yyyy-MM-dd HH:mm:ss")', '@DataConvert.ConvertToString(ViewBag.ItemName)')" href="javascript:">
								@(Singleton_04_Machine.IFPBCheckingService.FPBChecking_GetResultByMachineID_Time(DataConvert.ConvertToString(Model.DataFlexAssy.FlexAssy_27_Plasma.Rows[0]["MachineID"]), DataConvert.ToDateTime(Model.DataFlexAssy.FlexAssy_27_Plasma.Rows[0]["InputTime"]), @DataConvert.ConvertToString(ViewBag.ItemName)))
							</a>
						}

					</td>
				</tr>

				@if (!(ViewBag.Factory == "F5"))
				{
					<tr>
						<td>
							Parameter (link)
						</td>
						<td>
							@{
								string date = DataConvert.ToDateTime(Model.DataFlexAssy.FlexAssy_27_Plasma.Rows[0]["DateTime"]).ToString("dd-MM-yyyy");
								string Hourdate = DataConvert.ToDateTime(Model.DataFlexAssy.FlexAssy_27_Plasma.Rows[0]["DateTime"]).ToString("HH:mm");
							}
							@if (ViewBag.Factory == "F4")
							{
								<a target="_blank" href="@($"{"http://************/AutoCollectParameter/Index?MachineID="}{Model.DataFlexAssy.FlexAssy_27_Plasma.Rows[0]["MachineID"]}" +
                                    $"{"&ProgramName="}{Model.DataFlexAssy.FlexAssy_27_Plasma.Rows[0]["ProgramName"]}{"&DateTime="}{date}{"%20"}{Hourdate}")">
									@($"{"http://************/AutoCollectParameter/Index?MachineID="}{Model.DataFlexAssy.FlexAssy_27_Plasma.Rows[0]["MachineID"]}" +
                                    $"{"&ProgramName="}{Model.DataFlexAssy.FlexAssy_27_Plasma.Rows[0]["ProgramName"]}{"&DateTime="}{date}{"%20"}{Hourdate}")
								</a>
							}
							else
							{
								<a target="_blank" href="@($"{"http://10.212.6.210/AutoCollectParameter/Index?MachineID="}{Model.DataFlexAssy.FlexAssy_27_Plasma.Rows[0]["MachineID"]}" +
                                    $"{"&ProgramName="}{Model.DataFlexAssy.FlexAssy_27_Plasma.Rows[0]["ProgramName"]}{"&DateTime="}{date}{"%20"}{Hourdate}")">
									@($"{"http://10.212.6.210/AutoCollectParameter/Index?MachineID="}{Model.DataFlexAssy.FlexAssy_27_Plasma.Rows[0]["MachineID"]}" +
                                    $"{"&ProgramName="}{Model.DataFlexAssy.FlexAssy_27_Plasma.Rows[0]["ProgramName"]}{"&DateTime="}{date}{"%20"}{Hourdate}")
								</a>
							}

						</td>
					</tr>
				}
				<tr>
					<td>Production condition (link)</td>
					<td>
						<a onclick="viewDataProductionCondition(@Model.DataFlexAssy.FlexAssy_27_Plasma.Rows[0]["ProductionConditionID"])" href="javascript:">
							@Singleton_03_Common.ICommon_CommonService.GetProductionContidtionResult(DataConvert.ConvertToString(Model.DataFlexAssy.FlexAssy_27_Plasma.Rows[0]["ProductionConditionID"]),
		 DataConvert.ConvertToString(Model.DataFlexAssy.FlexAssy_27_Plasma.Rows[0]["ProductionConditionResult"]))
						</a>
					</td>
				</tr>
				<tr>
					<td>Machine ID</td>
					<td>@Model.DataFlexAssy.FlexAssy_27_Plasma.Rows[0]["MachineID"]</td>
				</tr>
				<tr>
					<td>Machine maintenance date (link)</td>
					<td><a onclick="viewDataMachineMaintenance(@Model.DataFlexAssy.FlexAssy_27_Plasma.Rows[0]["MachineMaintenanceID"])" href="javascript:">@Model.DataFlexAssy.FlexAssy_27_Plasma.Rows[0]["MachineMaintenanceDate"]</a></td>
				</tr>
				<tr>
					<td>Temp/Humidity/Clearness Dashboard Link</td>
					<td>
						<a target="_blank" href="@Common.getCleanlink(ViewBag.Factory,2)">
							@Common.getCleanlink(ViewBag.Factory, 2)
						</a>
					</td>
				</tr>
			</tbody>
        </table>
    }
}