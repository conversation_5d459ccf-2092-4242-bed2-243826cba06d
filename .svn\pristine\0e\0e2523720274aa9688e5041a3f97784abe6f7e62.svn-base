﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using Trace_AbilitySystem.Libs.Dataconnect;
using Trace_AbilitySystem.Libs.DTOClass;
using Trace_AbilitySystem.Libs.ITrace_02_FlexAssy_Services;

namespace Trace_AbilitySystem.Libs.Trace_02_FlexAssy_Services
{
    public class FlexAssy_32_4_ORT_ThermalCyclingAndFlexBendingService : IFlexAssy_32_4_ORT_ThermalCyclingAndFlexBendingService
    {
        private readonly DbExecute _db;

        public FlexAssy_32_4_ORT_ThermalCyclingAndFlexBendingService()
        {
            _db = new SqlExecute();
        }

        public int Insert(string ProductID, int Content, DateTime inputTest, DateTime finishTest,
            string Result, string LinkResult, string connectionStringOption)
        {
            try
            {
                var paras = new SqlParameter[6];
                paras[0] = new SqlParameter("@p_ProductID", ProductID);
                paras[1] = new SqlParameter("@p_Content", Content);
                paras[2] = new SqlParameter("@p_InputTest", inputTest);
                paras[3] = new SqlParameter("@p_FinishTest", finishTest);
                paras[4] = new SqlParameter("@p_Result", Result);
                paras[5] = new SqlParameter("@p_LinkResult", LinkResult);
                _db.Execute_Modify("sp_sms_FlexAssy_32_ORT2_Insert", paras, CommandType.StoredProcedure, connectionStringOption);
                return 1;
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorWeb(ex.StackTrace);
                return -1;
            }
        }


        public int Delete(string ProductID, string connectionStringOption)
        {
            try
            {
                var paras = new SqlParameter[1];
                paras[0] = new SqlParameter("@w_ProductID", ProductID);
                _db.Execute_Modify("sp_sms_FlexAssy_32_ORT2_Delete", paras, CommandType.StoredProcedure, connectionStringOption);
                return 1;
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorWeb(ex.StackTrace);
                return -1;
            }
        }


        public int Update(string ProductID, int ContentCode, DateTime inputTest, DateTime finishTest,
                string Result, string LinkResult, string itemName, string lineID, string connectionStringOption)
        {
            try
            {
                var paras = new SqlParameter[8];
                paras[0] = new SqlParameter("@p_InputTest", inputTest);
                paras[1] = new SqlParameter("@p_FinishTest", finishTest);
                paras[2] = new SqlParameter("@p_Result", Result);
                paras[3] = new SqlParameter("@p_LinkResult", LinkResult);
                paras[4] = new SqlParameter("@p_ItemName", itemName);
                paras[5] = new SqlParameter("@p_LineID", lineID);
                paras[6] = new SqlParameter("@w_ProductID", ProductID);
                paras[7] = new SqlParameter("@w_ContentCode", ContentCode);
                _db.Execute_Modify("sp_sms_FlexAssy_32_ORT2_Update", paras, CommandType.StoredProcedure, connectionStringOption);
                return 1;
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorWeb(ex.StackTrace);
                return -1;
            }
        }

        public DataTable GetByProductID(string productID, string connectionStringOption, out string connectionStringOk)
        {
            var paras = new SqlParameter[1];
            paras[0] = new SqlParameter("@w_ProductID", productID ?? (object)DBNull.Value);
            string connectionStringDefault = null;
            DataTable dt;
            switch (connectionStringOption)
            {
                case "F3":
                    dt = _db.Execute_Table("sp_sms_FlexAssy_32_ORT2_GetByProductID", paras, CommandType.StoredProcedure, connectionStringOption);
                    break;

                case "F4":
                    dt = _db.Execute_Table("sp_sms_FlexAssy_32_ORT2_GetByProductID", paras, CommandType.StoredProcedure, connectionStringOption);
                    break;

                default:
                    connectionStringDefault = "F3";
                    dt = _db.Execute_Table("sp_sms_FlexAssy_32_ORT2_GetByProductID", paras, CommandType.StoredProcedure, "F3");
                    if (dt == null || dt?.Rows.Count == 0)
                    {
                        connectionStringDefault = "F4";
                        dt = _db.Execute_Table("sp_sms_FlexAssy_32_ORT2_GetByProductID", paras, CommandType.StoredProcedure, "F4");
                    }
                    break;
            }

            connectionStringOk = connectionStringOption ?? (dt?.Rows.Count > 0 ? connectionStringDefault : null);
            return dt;
        }
        public DataTable GetByBlockID(string productID, string connectionStringOption, out string connectionStringOk)
        {
            var paras = new SqlParameter[1];
            paras[0] = new SqlParameter("@w_ProductID", productID ?? (object)DBNull.Value);
            string connectionStringDefault = null;
            DataTable dt;
            switch (connectionStringOption)
            {
                case "F3":
                    dt = _db.Execute_Table("sp_sms_FlexAssy_32_ORT2_GetByProductID", paras, CommandType.StoredProcedure, connectionStringOption);
                    break;

                case "F4":
                    dt = _db.Execute_Table("sp_sms_FlexAssy_32_ORT2_GetByProductID", paras, CommandType.StoredProcedure, connectionStringOption);
                    break;

                default:
                    connectionStringDefault = "F3";
                    dt = _db.Execute_Table("sp_sms_FlexAssy_32_ORT2_GetByProductID", paras, CommandType.StoredProcedure, "F3");
                    if (dt == null || dt?.Rows.Count == 0)
                    {
                        connectionStringDefault = "F4";
                        dt = _db.Execute_Table("sp_sms_FlexAssy_32_ORT2_GetByProductID", paras, CommandType.StoredProcedure, "F4");
                    }
                    break;
            }

            connectionStringOk = connectionStringOption ?? (dt?.Rows.Count > 0 ? connectionStringDefault : null);
            return dt;
        }
        public DataTable GetTestFromIPQC(string ItemCode, string ProductionLine, DateTime ShiftStart, DateTime ShiftEnd,
          string connectionStringOption, out string connectionStringOk)
        {
            string spName = "sp_sms_FlexAssy_32_IPQC_OQC_ORT_GetTest";
            var paras = new SqlParameter[5];
            paras[0] = new SqlParameter("@w_ItemCode", ItemCode);
            paras[1] = new SqlParameter("@w_LineID", ProductionLine);
            paras[2] = new SqlParameter("@w_StartShift", ShiftStart);
            paras[3] = new SqlParameter("@w_endShift", ShiftEnd);
            paras[4] = new SqlParameter("@w_TestID", DataConvert.ConvertStringToInt(AccountRole.c_ort_HeatsoakAndFlexBending));
            string connectionStringDefault = null;
            DataTable dt;
            switch (connectionStringOption)
            {
                case "F3":
                    dt = _db.Execute_Table(spName, paras, CommandType.StoredProcedure, connectionStringOption);
                    break;

                case "F4":
                    dt = _db.Execute_Table(spName, paras, CommandType.StoredProcedure, connectionStringOption);
                    break;

                default:
                    connectionStringDefault = "F3";
                    dt = _db.Execute_Table(spName, paras, CommandType.StoredProcedure, connectionStringOption);
                    if (dt == null || dt?.Rows.Count == 0)
                    {
                        connectionStringDefault = "F4";
                        dt = _db.Execute_Table(spName, paras, CommandType.StoredProcedure, connectionStringOption);
                    }
                    //dt = _db.Execute_Table("sp_sms_FlexAssy_33_IPQC_GetByBlockID", paras, CommandType.StoredProcedure, connectionStringOption);
                    //if (dt == null || dt?.Rows.Count == 0)
                    //{
                    //    connectionStringDefault = "Test";
                    //    dt = _db.Execute_Table("sp_sms_FlexAssy_33_IPQC_GetByBlockID", paras, CommandType.StoredProcedure, connectionStringOption);
                    //}
                    break;
            }

            connectionStringOk = connectionStringOption ?? (dt?.Rows.Count > 0 ? connectionStringDefault : null);
            return dt;
        }
        //update 09/2020. Change from inputest to ProductionDateTest, change from itemcode to itemname.

        public DataTable GetTestFromIPQC_ByItemNameProductionDateTest(string ItemName, string ProductionLine, DateTime ShiftStart, DateTime ShiftEnd,
                  string connectionStringOption, out string connectionStringOk)
        {
            string spName = "sp_sms_FlexAssy_32_IPQC_OQC_ORT_GetTest_ByItemNameProductionDateTest";
            var paras = new SqlParameter[5];
            paras[0] = new SqlParameter("@w_ItemName", ItemName);
            paras[1] = new SqlParameter("@w_LineID", ProductionLine);
            paras[2] = new SqlParameter("@w_StartShift", ShiftStart);
            paras[3] = new SqlParameter("@w_endShift", ShiftEnd);
            //paras[4] = new SqlParameter("@w_TestID", DataConvert.ConvertStringToInt(AccountRole.c_ort_HeatsoakAndFlexBending));
            paras[4] = new SqlParameter("@w_TestID", DataConvert.ConvertStringToInt(AccountRole.c_ort_ThermalCyclingAndFlexBending));
            string connectionStringDefault = null;
            DataTable dt;
            switch (connectionStringOption)
            {
                case "F3":
                    dt = _db.Execute_Table(spName, paras, CommandType.StoredProcedure, connectionStringOption);
                    break;

                case "F4":
                    dt = _db.Execute_Table(spName, paras, CommandType.StoredProcedure, connectionStringOption);
                    break;

                default:
                    connectionStringDefault = "F3";
                    dt = _db.Execute_Table(spName, paras, CommandType.StoredProcedure, connectionStringOption);
                    if (dt == null || dt?.Rows.Count == 0)
                    {
                        paras = new SqlParameter[5];
                        paras[0] = new SqlParameter("@w_ItemName", ItemName);
                        paras[1] = new SqlParameter("@w_LineID", ProductionLine);
                        paras[2] = new SqlParameter("@w_StartShift", ShiftStart);
                        paras[3] = new SqlParameter("@w_endShift", ShiftEnd);
                        //paras[4] = new SqlParameter("@w_TestID", DataConvert.ConvertStringToInt(AccountRole.c_ort_HeatsoakAndFlexBending));
                        paras[4] = new SqlParameter("@w_TestID", DataConvert.ConvertStringToInt(AccountRole.c_ort_ThermalCyclingAndFlexBending));
                        connectionStringDefault = "F4";
                        dt = _db.Execute_Table(spName, paras, CommandType.StoredProcedure, connectionStringOption);
                    }
                    //dt = _db.Execute_Table("sp_sms_FlexAssy_33_IPQC_GetByBlockID", paras, CommandType.StoredProcedure, connectionStringOption);
                    //if (dt == null || dt?.Rows.Count == 0)
                    //{
                    //    connectionStringDefault = "Test";
                    //    dt = _db.Execute_Table("sp_sms_FlexAssy_33_IPQC_GetByBlockID", paras, CommandType.StoredProcedure, connectionStringOption);
                    //}
                    break;
            }

            connectionStringOk = connectionStringOption ?? (dt?.Rows.Count > 0 ? connectionStringDefault : null);
            return dt;
        }

        public DataTable GetByIndi(string IndicationNum, string connectionStringOption, out string connectionStringOk)
        {
            int TestID = 14;
            DataTable dt = new DataTable();
            string connectionStringDefault = null;
            DataTable dtProductInfo = new DataTable();
            string connectionStringOk1 = null;
            string connectionStringOk2 = null;
            try
            {
                dtProductInfo = Singleton_02_FlexAssy.FlexAssy_33_IPQC_OQC_Service.GetByIndiFromProductInfo(IndicationNum, connectionStringOption, out connectionStringOk1);
                string ItemCode = string.Empty;
                string ItemName = string.Empty;
                string ProductionLine = string.Empty;
                DateTime ShiftStartOut = new DateTime();
                DateTime ShiftEndOut = new DateTime();
                if (dtProductInfo?.Rows.Count > 0)
                {
                    ItemCode = dtProductInfo.Rows[0]["Itemcode"]?.ToString();
                    ItemName = dtProductInfo.Rows[0]["ItemName"]?.ToString();
                    ProductionLine = dtProductInfo.Rows[0]["ProductionLine"]?.ToString();
                    DateTime dtProduct = DataConvert.ConvertFieldToDateTime(dtProductInfo.Rows[0], "ProductionDate");
                    //DateTime dtProduct = Convert.ToDateTime(dtProductInfo.Rows[0]["ProductionDate"]);
                    switch (connectionStringOption)
                    {
                        case "F3":
                            if (Singleton_02_FlexAssy.FlexAssy_33_IPQC_OQC_Service.GetTimeInterval("F3", TestID, dtProduct, out ShiftStartOut, out ShiftEndOut, "Common"))
                            {
                                //dt = GetTestFromIPQC(ItemCode, ProductionLine, ShiftStartOut, ShiftEndOut, "F3", out connectionStringOk2);
                                dt = GetTestFromIPQC_ByItemNameProductionDateTest(ItemName, ProductionLine, ShiftStartOut, ShiftEndOut, "F3", out connectionStringOk2);
                            }
                            break;
                        case "F4":
                            if (Singleton_02_FlexAssy.FlexAssy_33_IPQC_OQC_Service.GetTimeInterval("F4", TestID, dtProduct, out ShiftStartOut, out ShiftEndOut, "Common"))
                            {
                                //dt = GetTestFromIPQC(ItemCode, ProductionLine, ShiftStartOut, ShiftEndOut, "F4", out connectionStringOk2);
                                dt = GetTestFromIPQC_ByItemNameProductionDateTest(ItemName, ProductionLine, ShiftStartOut, ShiftEndOut, "F4", out connectionStringOk2);
                            }
                            break;

                        default:
                            connectionStringDefault = "F3";
                            if (Singleton_02_FlexAssy.FlexAssy_33_IPQC_OQC_Service.GetTimeInterval("F3", TestID, dtProduct, out ShiftStartOut, out ShiftEndOut, "Common"))
                            {
                                //dt = GetTestFromIPQC(ItemCode, ProductionLine, ShiftStartOut, ShiftEndOut, connectionStringDefault, out connectionStringOk2);
                                dt = GetTestFromIPQC_ByItemNameProductionDateTest(ItemName, ProductionLine, ShiftStartOut, ShiftEndOut, connectionStringDefault, out connectionStringOk2);
                            }
                            if (dt == null || dt?.Rows.Count == 0)
                            {
                                connectionStringDefault = "F4";
                                if (Singleton_02_FlexAssy.FlexAssy_33_IPQC_OQC_Service.GetTimeInterval("F4", TestID, dtProduct, out ShiftStartOut, out ShiftEndOut, "Common"))
                                {
                                    //dt = GetTestFromIPQC(ItemCode, ProductionLine, ShiftStartOut, ShiftEndOut, connectionStringDefault, out connectionStringOk2);
                                    dt = GetTestFromIPQC_ByItemNameProductionDateTest(ItemName, ProductionLine, ShiftStartOut, ShiftEndOut, connectionStringDefault, out connectionStringOk2);
                                }
                                break;
                            }
                            break;
                    }
                    connectionStringOk = connectionStringOption ?? (dtProductInfo?.Rows.Count > 0 ? connectionStringOk2 : null);
                    return dt;
                }
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorWeb(ex.StackTrace);
            }
            connectionStringOk = connectionStringDefault;
            return dt;
        }
        public DataTable GetByListIndi(DataTable ListIndicationNum, string connectionStringOption, out string connectionStringOk)
        {
            int TestID = 14;
            connectionStringOk = connectionStringOption;
            DataTable dt = new DataTable();
            DataTable dtResult = new DataTable();
            string connectionStringDefault = null;
            DataTable dtProductInfo = new DataTable();
            string connectionStringOk1 = null;
            string connectionStringOk2 = null;
            try
            {
                dtProductInfo = Singleton_02_FlexAssy.FlexAssy_33_IPQC_OQC_Service.GetByListIndiFromProductInfo(ListIndicationNum, connectionStringOption, out connectionStringOk1);
                string ItemCode = string.Empty;
                string ItemName = string.Empty;
                string ProductionLine = string.Empty;
                DateTime ShiftStartOut = new DateTime();
                DateTime ShiftEndOut = new DateTime();
                if (dtProductInfo?.Rows.Count > 0)
                {
                    foreach (DataRow row in dtProductInfo.Rows)
                    {
                        ItemCode = row["Itemcode"]?.ToString();
                        ItemName = row["ItemName"]?.ToString();
                        ProductionLine = row["ProductionLine"]?.ToString();
                        DateTime dtProduct = DataConvert.ConvertFieldToDateTime(row, "ProductionDate");
                        switch (connectionStringOption)
                        {
                            case "F3":
                                if (Singleton_02_FlexAssy.FlexAssy_33_IPQC_OQC_Service.GetTimeInterval("F3", TestID, dtProduct, out ShiftStartOut, out ShiftEndOut, "Common"))
                                {
                                    dt = GetTestFromIPQC_ByItemNameProductionDateTest(ItemName, ProductionLine, ShiftStartOut, ShiftEndOut, "F3", out connectionStringOk2);
                                }
                                break;
                            case "F4":
                                if (Singleton_02_FlexAssy.FlexAssy_33_IPQC_OQC_Service.GetTimeInterval("F4", TestID, dtProduct, out ShiftStartOut, out ShiftEndOut, "Common"))
                                {
                                    dt = GetTestFromIPQC_ByItemNameProductionDateTest(ItemName, ProductionLine, ShiftStartOut, ShiftEndOut, "F4", out connectionStringOk2);
                                }
                                break;

                            default:
                                connectionStringDefault = "F3";
                                if (Singleton_02_FlexAssy.FlexAssy_33_IPQC_OQC_Service.GetTimeInterval("F3", TestID, dtProduct, out ShiftStartOut, out ShiftEndOut, "Common"))
                                {
                                    dt = GetTestFromIPQC_ByItemNameProductionDateTest(ItemName, ProductionLine, ShiftStartOut, ShiftEndOut, connectionStringDefault, out connectionStringOk2);
                                }
                                if (dt == null || dt?.Rows.Count == 0)
                                {
                                    connectionStringDefault = "F4";
                                    if (Singleton_02_FlexAssy.FlexAssy_33_IPQC_OQC_Service.GetTimeInterval("F4", TestID, dtProduct, out ShiftStartOut, out ShiftEndOut, "Common"))
                                    {
                                        dt = GetTestFromIPQC_ByItemNameProductionDateTest(ItemName, ProductionLine, ShiftStartOut, ShiftEndOut, connectionStringDefault, out connectionStringOk2);
                                    }
                                    break;
                                }
                                break;
                        }
                        connectionStringOk = connectionStringOption ?? (dtProductInfo?.Rows.Count > 0 ? connectionStringOk2 : null);
                        if (dt?.Rows.Count > 0)
                        {
                            for (int count = 0; count < dt?.Rows.Count; count++)
                            {
                                dt.Rows[count]["IndicationNumber"] = row["IndicationNumber"]?.ToString();
                            }
                        }
                        dtResult.Merge(dt);
                    }
                }
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorWeb(ex.ToString());
            }
            return dtResult;
        }
    }
}