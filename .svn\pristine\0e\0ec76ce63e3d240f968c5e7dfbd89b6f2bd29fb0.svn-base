﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
using Trace_AbilitySystem.Libs.DTOClass;

namespace Trace_AbilitySystem.Libs.ITrace_02_FlexAssy_Services
{
    public interface IFlexAssy_09_PostReflowAOIService
    {
        Task<int> FlexAssy_09_PostReflowAOI_ProcessData(string factory, DateTime dataRegistrationDateSearch, int minutes, string connectionStringOption);
        Task<int> FlexAssy_09_PostReflowAOI_ProcessData_SEI_MES(string factory, DateTime dataRegistrationDateSearch, int minutes, string connectionStringOption);
        Task<int> FlexAssy_09_PostReflowAOI_Sample_ProcessData_SEI_MES(string factory, DateTime dataRegistrationDateSearch, int minutes, string connectionStringOption);
        int FlexAssy_09_PostReflowAOI_Insert(string blockID, DateTime dateTime, string machineID, string operatorID, string programName, string result, int? panelQty, int? goodQty, int? machineCallQty, int? operatorCallQty,
            int? numberNGImage, string goldenSampleBlockID, DateTime? firstPieceBuyoffControlDate, int? firstPieceBuyoffControlID, DateTime? machineMaintenanceDate, int? machineMaintenanceID,
            string productionConditionResult, int? productionConditionID, string connectionStringOption);
        //Task<int> SPC_AOI_YeildRate_ProcessData(string factory, DateTime createdDateSearch, string stage);
        Task FlexAssy_09_PostReflowAOI_ProcessCopyLog(string factory, string pathSaveLog, string userName, string passWord, string pathSaveLog_Medium, string userName_Medium, string password_Medium);

        //Copy_Lognew
        Task FlexAssy_09_PostReflowAOI_Detail_ProcessCopyLog(string factory, string pathSaveLog, string userName, string passWord, string pathSaveLog_Medium, string userName_Medium, string password_Medium);
        Task ProcessLogShare_Security(int idLog, string networkPath, string factory, DateTime lastTime, string pathSaveLog, string machineID, string machineName, string logNGImageAddress, 
            string logBadMarkAOI_F4, string dbServerAOI_F4, string dbUserAOI_D4, string dbPaswordAOI_F4, string pathSaveLog_Medium, string GetType = "MultiFolder");
        int FlexAssy_09_PostReflowAOI_ReadFileAOI_F3(string factory, int iD_LogFile, string fileName, string machineID, string pathSaveLog, string pathSaveLogNew, string logNGImageAddress,
            List<MachineMaintenance> machineMaintenances, List<ProductionCondition> productionConditions, List<FirstPieceBuyOffControl> firstPieceBuyOffControls, string connectionStringOptionMachine);
        int FlexAssy_09_PostReflowAOI_ReadFileAOI_F4(string factory, string machineID, int iD_LogFile, string fileName, string pathSaveLog, string pathSaveLogNew, string logNGImageAddress, 
            string logBadMarkAOI_F4, string dbServerAOI_F4, string dbUserAOI_D4, string dbPaswordAOI_F4,
            List<MachineMaintenance> machineMaintenances, List<ProductionCondition> productionConditions, List<FirstPieceBuyOffControl> firstPieceBuyOffControls, string connectionStringOptionMachine);
        int FlexAssy_09_PostReflowAOI_ReadFileAOI_F5(string factory, string machineID, int iD_LogFile, string fileName, string pathSaveLog, string pathSaveLogNew, string logNGImageAddress,
            string logBadMarkAOI_F4, string dbServerAOI_F4, string dbUserAOI_D4, string dbPaswordAOI_F4,
            List<MachineMaintenance> machineMaintenances, List<ProductionCondition> productionConditions, List<FirstPieceBuyOffControl> firstPieceBuyOffControls, string connectionStringOptionMachine);
        int FlexAssy_09_PostReflowAOI_UpdateReadLog(string blockID, DateTime? dateTime, string machineID, string operatorID, string programName, string result, int panelQty, int goodQty, int machineCallQty, int operatorCallQty,
            int totalDefect, int numberNGImage, string goldenSampleBlockID, DateTime? firstPieceBuyoffControlDate, int? firstPieceBuyoffControlID, DateTime? machineMaintenanceDate, int? machineMaintenanceID, string productionConditionResult,
            int? productionConditionID, int iD_LogFile, string connectionStringOption);

        int FlexAssy_09_PostReflowAOI_LogFile_Insert(string machineID, string fileNameRoot, string fileNameMedium, int lineRead, string connectionStringOption);
        int FlexAssy_09_PostReflowAOI_LogFile_UpdateLineRead(int iD, int lineRead, bool isReadOK, string connectionStringOption);
        int FlexAssy_09_PostReflowAOI_LogFile_UpdateFileNameRoot(int iD, string fileNameRoot, string connectionStringOption);
        int FlexAssy_09_PostReflowAOI_LogFile_UpdateFileName(int iD, string fileName, string connectionStringOption);
        DataTable FlexAssy_09_PostReflowAOI_GetListBlock_ProductionDetail_IPQC (string lastDatetime, string connectionStringOption);
        DataTable FlexAssy_09_PostReflowAOI_LogFile_GetDataWithID(int iD, string connectionStringOption);
        DataTable FlexAssy_09_PostReflowAOI_GetByBlockID(string blockID, string connectionStringOption, out string connectionStringOk);
        DataTable FlexAssy_09_PostReflowAOI_GetByListBlockID(DataTable listBlockID, string connectionStringOption, out string connectionStringOk);
        DataTable SMES_FlexAssy_09_PostReflowAOI_GetByListBlockID(DataTable listBlockID, string connectionStringOption);


        //PostAOI_Detail
        DataTable FlexAssy_09_PostReflowAOI_Detail_GetByProductID(string productID, string connectionStringOption, out string connectionStringOk);
        DataTable FlexAssy_09_PostReflowAOI_Detail_GetByListProductID(DataTable listProductID, string connectionStringOption, out string connectionStringOk);
        string FlexAssy_09_PostReflowAOI_FindOperatorID(DateTime dateTime, string connectionStringOption);
        DataTable ShopFloor_GetListBlock_ProductionDetail_IPQC(string lastDatetime, string connectionStringOption);

        // Image2D
        int FlexAssy_09_PostReflowAOI_Image2D_Insert(string blockID, string machineID, int? no, int? blockNumber, string componentName, float? pos_X, float? pos_Y, string image2D, string faultName, string operatorConfirm, string connectionStringOption);
        int FlexAssy_09_PostReflowAOI_Image2D_Lane_Insert(string blockID, string machineID, int? no, int? blockNumber, string componentName, float? pos_X, float? pos_Y, string image2D, string faultName, string operatorConfirm,string Lane, string connectionStringOption);
        DataTable FlexAssy_09_PostReflowAOI_Image2D_GetByBlockID(string blockID, int limit, string connectionStringOption);
        DataTable FlexAssy_09_PostReflowAOI_Image2D_GetByListBlockID(DataTable listBlockID, string connectionStringOption);
        DataTable SMES_FlexAssy_09_PostReflowAOI_Image2D_GetByListBlockID(DataTable listBlockID, string connectionStringOption);

        // Sample
        int FlexAssy_09_PostReflowAOI_Sample_Insert(string blockID, DateTime dateTime, string machineID, string operatorID, string programName, string result, int? panelQty, int? goodQty, int? machineCallQty,
            int? operatorCallQty, int? numberNGImage, string connectionStringOption);
        DataTable FlexAssy_09_PostReflowAOI_Sample_GetByLastItemName(string itemName, string machineID, DateTime dateTime, string connectionStringOption);
        DataTable FlexAssy_09_PostReflowAOI_Sample_GetLogFile(string itemName, string machineID, DateTime dateTime, string connectionStringOption);
        int FlexAssy_09_PostReflowAOI_Sample_UpdateReadLog(string blockID, DateTime? dateTime, string machineID, string operatorID, string programName, string result, int panelQty, int goodQty, int machineCallQty, int operatorCallQty,
            int totalDefect, int numberNGImage, int iD_LogFile, string connectionStringOption);

        Task FlexAssy_09_PostReflowAOI_ProcessMediumToF2(string factory, int iD, string pathSaveLog, string pathSynMedium, string pathLinkTo_Medium);
        //Slip_LogFile
        Task FlexAssy_09_PostReflowAOI_Detail_ProcessReadLogFile(string factory, int iD, string pathSaveLog, string pathSynMedium, string pathLinkTo_Medium);
        //void CalcYeildRate(string indi, string Factory, string ItemName, DateTime DateTime = default);
        DataTable SMES_FlexAssy_09_PostReflowAOI_GetByBlockID(string BlockID, string connectionStringOption);
        Task<int> OEE_09_PostReflowAOI_ProcessData(string factory, DateTime DateTime, int minutes, string connectionStringOption);
        DataTable FlexAssy_09_PostReflowAOI_Image2D_GetNGByListBlockID(DataTable listBlockID, string connectionStringOption);
        Task<int> FlexAssy_09_PostReflowAOI_Detail_ProcessData_SEI_MES(string factory, DateTime lastTime, int minutes, string connectionStringOption);
    }
}