﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trace_AbilitySystem.Libs.ITrace_07_Pismo_Auto_Service
{
    public interface IFlexAssy_CoperService2025
    {
        Task<int> FlexAssy_Coper_ProcessData2025(string factory, DateTime createdDateSearch, int minutes, string connectionStringOption);
        DataTable Coper_GetDataWithCreatedDate2025(DateTime createdDate, int minutes, string connectionStringOption);
        Task<int> FlexAssy_Coper_Detail_ProcessData2025(string factory, DateTime createdDateSearch, int minutes, string connectionStringOption);
        int FlexAssy_Coper_Insert2025(int PKID, string BlockID, string LotNumber, string PanelNumber, string ItemName, string ProgramName, string MachineID, string MachineType, DateTime dateTime, string Side, string ErrorMap, string ErrorMapConvert, string FileName, string FullName, bool SyncPcsID, string ImageName, string Result, string ResultVerify, string OperatorID, string connectionStringOption);
        int FlexAssy_Coper_Detail_Insert2025(int PKID, int CoperPkid, int Location, string LocationPanel, string CavityNumber, int LocationInkJet, string ProductID, string DefectCode, string DefectDescription, string Result, string ResultVerify, DateTime? DateVerify, DateTime? SyncPcsDate, string connectionStringOption);
        DataTable Coper_Detail_GetDataWithCreatedDate2025(DateTime createdDate, int minutes, string connectionStringOption);
        DataTable FlexAssy_Coper_GetByProductID(string productID, string connectionStringOption);
        DataTable FlexAssy_Coper_GetByBlockID(string blockID, string connectionStringOption);
        DataTable FlexAssy_Coper_GetByListBlockID(DataTable listBlockID, string connectionStringOption, out string connectionStringOk);
        DataTable FlexAssy_Coper_GetByPkID(int PKID);
    }
}
