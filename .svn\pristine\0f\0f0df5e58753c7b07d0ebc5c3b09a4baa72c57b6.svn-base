﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Trace_AbilitySystem.Libs;

namespace Trace_AblilitySystem_Flexssy_Interface_OQC
{
    public partial class frmShiftDefinition : Form
    {
        private string _ConnectString;
        public frmShiftDefinition()
        {
            InitializeComponent();
            _ConnectString = Singleton.IniFile.GetDbConnectionString();
            InitGridDataTable(dgvMain);
            InitWorkItem();
            LoadAll(string.Empty);
            
        }
        private void InitGridDataTable(DataGridView dtGridView)
        {
            try
            {
                var columnHeaders = new List<string> { "WorkItem", "ShiftName", "StartTime", "EndTime" };
                dtGridView.ColumnCount = columnHeaders.Count;
                dtGridView.ColumnHeadersVisible = true;
                dtGridView.AllowUserToAddRows = false;
                dtGridView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
                dtGridView.EditMode = DataGridViewEditMode.EditProgrammatically;
                // Thiết lập Style cho Header
                DataGridViewCellStyle columnHeaderStyle = new DataGridViewCellStyle
                {
                    BackColor = Color.Azure,
                    Font = new Font("Tahoma", 12, FontStyle.Bold),
                    Alignment = DataGridViewContentAlignment.MiddleCenter
                };

                dtGridView.ColumnHeadersDefaultCellStyle = columnHeaderStyle;
                dtGridView.ColumnHeadersDefaultCellStyle.BackColor = Color.Azure;
                dtGridView.EnableHeadersVisualStyles = false;
                dtGridView.RowHeadersVisible = false;
                dtGridView.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
                dtGridView.ColumnHeadersHeight = 30;
                dtGridView.RowTemplate.Height = 40;
                for (int i = 0; i < columnHeaders.Count; i++)
                {
                    dtGridView.Columns[i].HeaderText = columnHeaders[i];
                    dtGridView.Columns[i].SortMode = DataGridViewColumnSortMode.NotSortable;
                }
                dtGridView.Columns[0].Width = 150;
                dtGridView.Columns[1].Width = 150;
                dtGridView.Columns[2].Width = 200;
                dtGridView.Columns[3].Width = 200;
                dtGridView.Columns[3].AutoSizeMode  = DataGridViewAutoSizeColumnMode.Fill;
                // Remove row default
                dtGridView.AllowUserToAddRows = false;
                dtGridView.AutoGenerateColumns = false;
                dtGridView.AllowUserToResizeColumns = false;
                // Auto resize height multi line cell
                dtGridView.DefaultCellStyle.WrapMode = DataGridViewTriState.True;
                dtGridView.RowsDefaultCellStyle.SelectionBackColor = Color.DeepSkyBlue;
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp(ex.Message);
            }
        }
        private void InitWorkItem()
        {
            try
            {
                DataTable dtWorkItem = Singleton_03_Common.ShiftDefinition.GetWorkItem(_ConnectString);
                cbWorkItem.Items.Clear();
                cbWorkItem.DataSource = dtWorkItem;
                cbWorkItem.DisplayMember = "WorkItem";
                cbWorkItem.ValueMember = "WorkItem";
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp(ex);
            }
        }
        private void LoadAll(string WorkItem)
        {
            try
            {
                DataTable dtShift = new DataTable();
                if(string.IsNullOrEmpty(WorkItem)) dtShift = Singleton_03_Common.ShiftDefinition.LoadAllShift(_ConnectString);
                else
                {

                }
                dgvMain.Rows.Clear();
                for (int i = 0; i < dtShift.Rows.Count; i++)
                {
                    dgvMain.Rows.Insert(0, new string[] {dtShift.Rows[i].ItemArray[0].ToString(),
                                    dtShift.Rows[i].ItemArray[1].ToString(),
                                    dtShift.Rows[i].ItemArray[2].ToString(),
                                    dtShift.Rows[i].ItemArray[3].ToString()});
                }
            }
            catch( Exception ex)
            {
                ManageLog.WriteErrorApp(ex);
            }
        }
        private void dgvMain_SelectionChanged(object sender, EventArgs e)
        {
            try
            {
                if (dgvMain.SelectedRows?.Count > 0)
                {
                    cbWorkItem.Text = dgvMain.SelectedRows[0].Cells[0].Value.ToString();
                    cbShiftName.Text = dgvMain.SelectedRows[0].Cells[1].Value.ToString();
                    TimePickerStart.Value = Convert.ToDateTime(dgvMain.SelectedRows[0].Cells[2].Value);
                    TimerPickerEnd.Value = Convert.ToDateTime(dgvMain.SelectedRows[0].Cells[3].Value);
                }
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp(ex);
            }
        }
        private void cbWorkItem_SelectedIndexChanged(object sender, EventArgs e)
        {
           
        }
        private void btAdd_Click(object sender, EventArgs e)
        {
            DataTable dt = Singleton_03_Common.ShiftDefinition.CheckIfExist(cbWorkItem.Text,cbShiftName.Text,_ConnectString);
            if(dt?.Rows.Count > 0)
            {
                DialogHelper.Warning("Định nghĩa Shift này đã tồn tại." + Environment.NewLine +  "This Shift definition is already exist.");
                return;
            }
            Singleton_03_Common.ShiftDefinition.AddShift(cbWorkItem.Text, cbShiftName.Text, TimePickerStart.Value,
                TimerPickerEnd.Value, _ConnectString);
            LoadAll(string.Empty);
        }
        private void btDelete_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show("Bạn có chắc chắn muốn xóa setting ca này"+Environment.NewLine
                +"Are you sure to delete this shift setting","Cảnh báo", MessageBoxButtons.OKCancel) == DialogResult.OK)
            {
                DataTable dt = Singleton_03_Common.ShiftDefinition.CheckIfExist(cbWorkItem.Text, cbShiftName.Text, _ConnectString);
                if (dt?.Rows.Count > 0)
                {
                    Singleton_03_Common.ShiftDefinition.DeleteShift(cbWorkItem.Text, cbShiftName.Text, TimePickerStart.Value,
                    TimerPickerEnd.Value, _ConnectString);
                }
                else
                {
                    DialogHelper.Warning("Định nghĩa Shift này không tồn tại." + Environment.NewLine + "This Shift definition is not exist.");
                }
            }
            LoadAll(string.Empty);
        }

        private void btEdit_Click(object sender, EventArgs e)
        {
            DataTable dt = Singleton_03_Common.ShiftDefinition.CheckIfExist(cbWorkItem.Text, cbShiftName.Text, _ConnectString);
            if (dt?.Rows.Count > 0)
            {
                Singleton_03_Common.ShiftDefinition.UpdateShift(cbWorkItem.Text, cbShiftName.Text, TimePickerStart.Value,
                TimerPickerEnd.Value, _ConnectString);
            }
            else
            {
                DialogHelper.Warning("Định nghĩa Shift này không tồn tại." + Environment.NewLine + "This Shift definition is not exist.");
            }
            LoadAll(string.Empty);
        }
    }
}
