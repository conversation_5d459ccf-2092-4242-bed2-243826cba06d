﻿using Microsoft.SqlServer.Server;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using Trace_AbilitySystem.Libs.Dataconnect;
using Trace_AbilitySystem.Libs.ITrace_01_BareFlex_Services;

namespace Trace_AbilitySystem.Libs.Trace_01_BareFlex_Services
{
    public class Source_BareFlex_ErpData_Service : ISource_BareFlex_ErpData_Service
    {
        private readonly DbExecute _db;

        public Source_BareFlex_ErpData_Service()
        {
            _db = new SqlExecute();
        }

        public DataTable QCDB_v_bat_no_trace_GetDataWith_xhpn_dtt(DateTime xhpn_dtt, string connectionStringOption)
        {
            string strWhere = "xhpn_dtt > '" + xhpn_dtt.ToString("yyyy-MM-dd HH:mm:ss.fff") + "'";

            string sql = "SELECT [xusr_itmnm],[xprc_block_nm_en_pre],[xpre_item_c],[xpre_bat_no],[xprc_block_nm_en_flw],[xflw_item_c],[xflw_bat_no],[xtrace_k],[xhpn_dtt] FROM QCDB_v_bat_no_trace WHERE " + strWhere + " ORDER BY xhpn_dtt ASC";

            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }

        public DataTable QCDB_v_mnfhist_plant_GetDataWith_xitem_cAndxbat_no(string itemCode, string batchNo, string connectionStringOption)
        {
            string sql = "SELECT A.[xitem_c]";
            sql += ",A.[xbat_no]";
            sql += ",A.[xrt]";
            sql += ",A.[xplant_c]";
            sql += ",A.[xplant_nm]";
            sql += ",A.[xoutput_inp_dtt]";
            sql += ",B.xpsn_c FROM (SELECT [xitem_c]";
            sql += ",[xbat_no]";
            sql += ",[xrt]";
            sql += ",[xplant_c]";
            sql += ",[xplant_nm]";
            sql += ",[xoutput_inp_dtt] FROM [ErpData].[dbo].[QCDB_v_mnfhist_plant] WHERE [xitem_c] = '" + itemCode + "' AND [xbat_no] = '" + batchNo + "') AS A LEFT OUTER JOIN (SELECT xitem_c, xqt_bat_no, xinp_dtt, xpsn_c FROM [ErpData].[dbo].[QCDB_v_prd_hist]) AS B ON A.xitem_c = B.xitem_c and A.xbat_no = B.xqt_bat_no AND A.xoutput_inp_dtt = B.xinp_dtt ORDER BY A.xoutput_inp_dtt ASC";
            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }

        public DataTable QCDB_v_mnfhist_plant_GetByDate(DateTime xinp_dtt, int minutes, string connectionStringOption)
        {
            string strWhere = "xoutput_inp_dtt > '" + xinp_dtt.ToString("yyyy-MM-dd HH:mm:ss.fff") + "' AND xoutput_inp_dtt <= '" + xinp_dtt.AddMinutes(minutes).ToString("yyyy-MM-dd HH:mm:ss.fff") + "'";

            string sql = "SELECT * FROM [ErpData].[dbo].[QCDB_v_mnfhist_plant] WHERE " + strWhere + " ORDER BY xoutput_inp_dtt ASC";

            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }
        public DataTable QCDB_v_prd_hist_GetDataWith_xinp_dtt(DateTime xinp_dtt, int minutes, string connectionStringOption)
        {
            string strWhere = "xinp_dtt > '" + xinp_dtt.ToString("yyyy-MM-dd HH:mm:ss.fff") + "' AND xinp_dtt <= '" + xinp_dtt.AddMinutes(minutes).ToString("yyyy-MM-dd HH:mm:ss.fff") + "'";

            string sql = "SELECT * FROM [ErpData].[dbo].[QCDB_v_prd_hist] WHERE " + strWhere + " ORDER BY xinp_dtt ASC";

            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }

        public DataTable QCDB_v_trace_rslt_GetDataWith_Process_Block(string itemCode, string batchNo, string connectionStringOption)
        {
            string sql = "SELECT IsNull(xprc_block_nm_en_pre, 'Mounting') Process_Block, vph.xrt Seq_No, vdp.xwork_nm_en Work_name, vph.xinp_dtt Input_date, vtr.MachineNo MachineNo, vph.xpsn_c Input_by, vph.xqc_data_k Data_type FROM QCDB_v_prd_hist vph";
            sql += " LEFT OUTER JOIN QCDB_v_dsgn_prc vdp ON(vph.xitem_c = vdp.xitem_c and vph.xrt = vdp.xrt)";
            sql += " LEFT OUTER JOIN(SELECT TOP 1 xprc_block_nm_en_pre, xpre_item_c, xpre_bat_no FROM QCDB_v_bat_no_trace WHERE xpre_item_c = '" + itemCode + "' AND xpre_bat_no = '" + batchNo + "') vnt ON (vph.xitem_c = vnt.xpre_item_c)";
            sql += " LEFT OUTER JOIN(SELECT DISTINCT seq, xitem_c, xqt_bat_no, xrt, xqcval MachineNo, xaddtnl_info FROM QCDB_v_trace_rslt where xitem_c = '" + itemCode + "' and xqt_bat_no = '" + batchNo + "' and xqcitem_c = 'Q0006' and xqcval <> '-' and xqcval <> '') vtr ON (vtr.xitem_c = vtr.xitem_c and vph.xqt_bat_no = vtr.xqt_bat_no and vph.xrt = vtr.xrt)";
            sql += " WHERE vph.xitem_c = '" + itemCode + "' and vph.xqt_bat_no = '" + batchNo + "'";
            sql += " ORDER BY vph.xitem_c, vph.xqt_bat_no, vph.xrt, vph.xinp_dtt";

            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }
        public DataTable QCDB_v_trace_rslt_GetData(string itemCode, string batchNo,string xrt, string connectionStringOption)
        {
            string sql = "QCDB_v_mnfhist_plant_GetData";
            List<SqlParameter> Para = new List<SqlParameter>();
            Para.Add(new SqlParameter("itemCode", itemCode));
            Para.Add(new SqlParameter("@Batno", batchNo));
            Para.Add(new SqlParameter("xrt", xrt));
            return _db.Execute_Table(sql, Para.ToArray(), CommandType.StoredProcedure, connectionStringOption);
        }
        public DataTable QCDB_v_prd_hist_plant_GetData(string itemCode, string batchNo, string xrt, string connectionStringOption)
        {
            string sql = "QCDB_v_prd_hist_GetData";
            List<SqlParameter> Para = new List<SqlParameter>();
            Para.Add(new SqlParameter("itemCode", itemCode));
            Para.Add(new SqlParameter("@Batno", batchNo));
            Para.Add(new SqlParameter("xrt", xrt));
            return _db.Execute_Table(sql, Para.ToArray(), CommandType.StoredProcedure, connectionStringOption);
        }
        public DataTable QCDB_v_trace_rslt_GetData_WorkOrder(string itemCode, string batchNo, string connectionStringOption)
        {
            string sql = "QCDB_v_mnfhist_plant_GetData_WorkOrder";
            List<SqlParameter> Para = new List<SqlParameter>();
            Para.Add(new SqlParameter("itemCode", itemCode));
            Para.Add(new SqlParameter("@Batno", batchNo));
            return _db.Execute_Table(sql, Para.ToArray(), CommandType.StoredProcedure, connectionStringOption);
        }

        public DataTable QCDB_v_WCS_URL_param_GetDataWith_xdate_time(DateTime xdate_time, int minutes, string connectionStringOption)
        {
            string strWhere = "xdate_time > '" + xdate_time.ToString("yyyy-MM-dd HH:mm:ss.fff") + "' AND xdate_time <= '" + xdate_time.AddMinutes(minutes).ToString("yyyy-MM-dd HH:mm:ss.fff") + "'";

            string sql = "SELECT [xitm_nm],[xpcard_bc],[xlnk_item_c],[xlnk_bat_no],[xrt],[xplant_c],[xlnk_plant_nm],[xrevid],[xdate_time] FROM [ErpData].[dbo].[QCDB_v_WCS_URL_param] WHERE " + strWhere + " ORDER BY xdate_time ASC";

            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }

        public DataTable QCDB_code_master_GetAll(string connectionStringOption)
        {
            string sql = "SELECT [code], [code_value] FROM [ErpData].[dbo].[QCDB_code_master]";
            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }

        public DataTable QCDB_v_bat_no_stk_new_GetByFinishedDate(DateTime createdDate, int minutes, string connectionStringOption, string factory)
        {
            string strWhere = "Finished_Date > '" + createdDate.ToString("yyyy-MM-dd HH:mm:ss.fff") + "' AND Finished_Date <= '" + createdDate.AddMinutes(minutes).ToString("yyyy-MM-dd HH:mm:ss.fff") + "'";

            string sql;
            if (factory.ToUpper().Equals("F3"))
            {
                sql = "SELECT [Finished_Date],[Item_Code],[Batch_No],[SO_Sub_No],[Stock_In_Qty],[Warehouse_Code],[Warehouse_Name],[PIC_Code],[PIC_Name],[Product_Name] FROM [QCDB_v_bat_no_stk_new] WHERE " + strWhere + " AND [Warehouse_Code] = 'Q310' ORDER BY Finished_Date ASC";
            }
            else if (factory.ToUpper().Equals("F4"))
            {
                sql = "SELECT [Finished_Date],[Item_Code],[Batch_No],[SO_Sub_No],[Stock_In_Qty],[Warehouse_Code],[Warehouse_Name],[PIC_Code],[PIC_Name],[Product_Name] FROM [QCDB_v_bat_no_stk_new] WHERE " + strWhere + "  AND [Warehouse_Code] = 'Q410' ORDER BY Finished_Date ASC";
            }
            else
            {
                sql = "SELECT [Finished_Date],[Item_Code],[Batch_No],[SO_Sub_No],[Stock_In_Qty],[Warehouse_Code],[Warehouse_Name],[PIC_Code],[PIC_Name],[Product_Name] FROM [QCDB_v_bat_no_stk_new] WHERE " + strWhere + " ORDER BY Finished_Date ASC";
            }
             
            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }
        public DataTable Tbl2DHeatMap_GetDataWith_Process_Block(string indication_id)
        {
            string sql = "SELECT TOP (1000) Tbl2DHeatMap.* FROM [ErpData].[dbo].[Tbl2DHeatMap] ";
            sql += " WHERE indication_id = '" + indication_id + "' ";
            return _db.Execute_Table(sql, null, CommandType.Text, "ErpData");
        }
        public DataTable Tbl2DHeatMap_GetDataWith_Process_Pcs(string block_id)
        {
            string sql = "SELECT TOP (1000) Tbl2DHeatMap_Detail.* FROM [dbo].[Tbl2DHeatMap_Detail] ";
            sql += " WHERE block_id = '" + block_id + "' ";
            return _db.Execute_Table(sql, null, CommandType.Text, "ErpData");
        }
        public DataTable AVI_GetDataWith_Process_Block(string indication_id)
        {
            string sql = "SELECT [BlockID]"
                + "  FROM [ErpData].[dbo].[FS_Avi_Utz_Detail_Tmp] WHERE IndicationID = '" + indication_id + "' " +
                " Group by BlockID";
            return _db.Execute_Table(sql, null, CommandType.Text, "ErpData");
        }
    }
}