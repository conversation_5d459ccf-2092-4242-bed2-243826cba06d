﻿using System;
using System.Data;

namespace Trace_AbilitySystem.Libs.ITrace_01_BareFlex_Services
{
    public interface IBareFlex_22_1_RELORL_HotOilTestService
    {
        int BareFlex_22_1_RELORL_HotOilTest_Insert(string workOrder, DateTime dateTime, string machineID, string operatorID, string toolingID, DateTime? machineMaintenanceDate, int? machineMaintenanceID, string testResult);
        DataTable BareFlex_22_1_RELORL_HotOilTest_GetByWorkOrder(string workOrder);
        DataTable BareFlex_22_1_RELORL_HotOilTest_GetByListWorkOrder(DataTable listWorkOrder);
    }
}