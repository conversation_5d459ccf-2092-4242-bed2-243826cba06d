﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Trace_AbilitySystem.Libs;

namespace Trace_AblilitySystem_Flexssy_Interface_OQC
{
    public partial class frmMain : Form
    {
        public frmMain()
        {
            InitializeComponent();
        }
        private void frmMain_Load(object sender, EventArgs e)
        {
            try
            {
                this.Text += " [" + OQC_Version.c_sVersion + " " + OQC_Version.c_sVersionDate + "]";
                var account = MySharedInfo.CurrentUser;
                if (account != null)
                {
                    string _role = account.Role;
                    rdORT_ThermalCycling.Enabled = rdORT_HeatSoakAndRecovery.Enabled = rdORT_ThermalShock.Enabled
                        = rdORT_ThermalCyclingFlexBending.Enabled = rdORT_FlexBending.Enabled = rdORT_HeatSoakFlexBending.Enabled
                        = rdIPQC_B2BXsection.Enabled = rdIPQC_B2BPellingTest.Enabled = rdIPQC_B2BXray.Enabled =
                        rdIPQC_OutlinePunching.Enabled = rdIPQC_PlasmaACF.Enabled = rd_OQC_FAISPCMeasurement.Enabled =
                        rdOQC_B2BPeeling.Enabled = rd_OQC_Pulling.Enabled = rd_OQC_Shearing.Enabled =
                        rd_OQC_ACFFlattness.Enabled = rd_OQC_Rounghness.Enabled = rd_OQC_Bonding.Enabled =
                        rd_OQC_HotOil.Enabled = rd_OQC_Heating4W.Enabled = rd_OQC_XSectionGND.Enabled = false;

                    var rs = _role.Split(',');
                    foreach (var item in rs)
                    {
                        if (!string.IsNullOrEmpty(item))
                        {
                            if (int.Parse(item) == int.Parse(AccountRole.c_sAdmin))
                            {
                                rdORT_ThermalCycling.Enabled = rdORT_HeatSoakAndRecovery.Enabled = rdORT_ThermalShock.Enabled
                                = rdORT_ThermalCyclingFlexBending.Enabled = rdORT_FlexBending.Enabled = rdORT_HeatSoakFlexBending.Enabled
                                = rdIPQC_B2BXsection.Enabled = rdIPQC_B2BPellingTest.Enabled = rdIPQC_B2BXray.Enabled =
                                rdIPQC_OutlinePunching.Enabled = rdIPQC_PlasmaACF.Enabled = rd_OQC_FAISPCMeasurement.Enabled =
                                rdOQC_B2BPeeling.Enabled = rd_OQC_Pulling.Enabled = rd_OQC_Shearing.Enabled =
                                rd_OQC_ACFFlattness.Enabled = rd_OQC_Rounghness.Enabled = rd_OQC_Bonding.Enabled =
                                rd_OQC_HotOil.Enabled = rd_OQC_Heating4W.Enabled = rd_OQC_XSectionGND.Enabled = true;
                                break;
                            }
                            if (int.Parse(item) == int.Parse(AccountRole.c_sEngineer))
                            {

                                rdORT_ThermalCycling.Enabled = rdORT_HeatSoakAndRecovery.Enabled = rdORT_ThermalShock.Enabled
                                = rdORT_ThermalCyclingFlexBending.Enabled = rdORT_FlexBending.Enabled = rdORT_HeatSoakFlexBending.Enabled
                                = rdIPQC_B2BXsection.Enabled = rdIPQC_B2BPellingTest.Enabled = rdIPQC_B2BXray.Enabled =
                                rdIPQC_OutlinePunching.Enabled = rdIPQC_PlasmaACF.Enabled = rd_OQC_FAISPCMeasurement.Enabled =
                                rdOQC_B2BPeeling.Enabled = rd_OQC_Pulling.Enabled = rd_OQC_Shearing.Enabled =
                                rd_OQC_ACFFlattness.Enabled = rd_OQC_Rounghness.Enabled = rd_OQC_Bonding.Enabled =
                                rd_OQC_HotOil.Enabled = rd_OQC_Heating4W.Enabled = rd_OQC_XSectionGND.Enabled = true;
                                break;
                            }
                            if (int.Parse(item) == int.Parse(AccountRole.c_ort_ThermalCycling))
                            {
                                rdORT_ThermalCycling.Enabled = true;
                            }
                            if (int.Parse(item) == int.Parse(AccountRole.c_ort_HeatsoakAndRecovery))
                            {
                                rdORT_HeatSoakAndRecovery.Enabled = true;
                            }
                            if (int.Parse(item) == int.Parse(AccountRole.c_ort_ThermalShock))
                            {
                                rdORT_ThermalShock.Enabled = true;
                            }
                            if (int.Parse(item) == int.Parse(AccountRole.c_ort_ThermalCyclingAndFlexBending))
                            {
                                rdORT_ThermalCyclingFlexBending.Enabled = true;
                            }
                            if (int.Parse(item) == int.Parse(AccountRole.c_ort_FlexBending))
                            {
                                rdORT_FlexBending.Enabled = true;
                            }
                            if (int.Parse(item) == int.Parse(AccountRole.c_ort_HeatsoakAndFlexBending))
                            {
                                rdORT_HeatSoakFlexBending.Enabled = true;
                            }
                            if (int.Parse(item) == int.Parse(AccountRole.c_ipqc_B2BXsection))
                            {
                                rdIPQC_B2BXsection.Enabled = true;
                            }
                            if (int.Parse(item) == int.Parse(AccountRole.c_ipqc_B2BPeelingTest))
                            {
                                rdIPQC_B2BPellingTest.Enabled = true;
                            }
                            if (int.Parse(item) == int.Parse(AccountRole.c_ipqc_B2BXray))
                            {
                                rdIPQC_B2BXray.Enabled = true;
                            }
                            if (int.Parse(item) == int.Parse(AccountRole.c_ipqc_OutlinePunchingDimension))
                            {
                                rdIPQC_OutlinePunching.Enabled = true;
                            }
                            //if (int.Parse(item) == int.Parse(AccountRole.c_ipqc_PlasmaACF))
                            //{
                            //    rdIPQC_PlasmaACF.Enabled = true;
                            //}
                            if (int.Parse(item) == int.Parse(AccountRole.c_oqc_measurement))
                            {
                                rd_OQC_FAISPCMeasurement.Enabled = true;
                            }
                            if (int.Parse(item) == int.Parse(AccountRole.c_oqc_B2BPeeling))
                            {
                                rdOQC_B2BPeeling.Enabled = true;
                            }
                            if (int.Parse(item) == int.Parse(AccountRole.c_oqc_B2BPulling))
                            {
                                rd_OQC_Pulling.Enabled = true;
                            }
                            if (int.Parse(item) == int.Parse(AccountRole.c_oqc_B2BShearing))
                            {
                                rd_OQC_Shearing.Enabled = true;
                            }
                            if (int.Parse(item) == int.Parse(AccountRole.c_oqc_ACFFlatness))
                            {
                                rd_OQC_ACFFlattness.Enabled = true;
                            }
                            if (int.Parse(item) == int.Parse(AccountRole.c_oqc_ACFRoughness))
                            {
                                rd_OQC_Rounghness.Enabled = true;
                            }
                            if (int.Parse(item) == int.Parse(AccountRole.c_oqc_ACFBonding))
                            {
                                rd_OQC_Bonding.Enabled = true;
                            }
                            if (int.Parse(item) == int.Parse(AccountRole.c_oqc_HotOil))
                            {
                                rd_OQC_HotOil.Enabled = true;
                            }
                            if (int.Parse(item) == int.Parse(AccountRole.c_oqc_Heating4WTest))
                            {
                                rd_OQC_Heating4W.Enabled = true;
                            }
                            if (int.Parse(item) == int.Parse(AccountRole.c_oqc_XSectionGNDB2Bpin))
                            {
                                rd_OQC_XSectionGND.Enabled = true;
                            }
                        }

                    }
                }
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp(ex);
            }
        }
        private void btConfirmOQC_Click(object sender, EventArgs e)
        {
            DialogResult res = DialogResult.OK;
            this.Hide();
            int nProcess = 1;
            string sTestType = string.Empty;
            string sTestID = string.Empty;
            if (rd_OQC_FAISPCMeasurement.Checked)
            {
                sTestType = AccountRole.c_oqc_measurement;
                FrmOQCDefinePunching fr = new FrmOQCDefinePunching("", sTestType, sTestID);
                res = fr.ShowDialog();
            }
            else if (rdOQC_B2BPeeling.Checked)
            {
                sTestType = AccountRole.c_oqc_B2BPeeling;
                FrmOQCDefineInvoice fr = new FrmOQCDefineInvoice("", sTestType, sTestID);
                res = fr.ShowDialog();
            }
            else if (rd_OQC_Pulling.Checked)
            {
                sTestType = AccountRole.c_oqc_B2BPulling;
                FrmOQCDefineInvoice fr = new FrmOQCDefineInvoice("", sTestType, sTestID);
                res = fr.ShowDialog();
            }
            else if (rd_OQC_Shearing.Checked)
            {
                sTestType = AccountRole.c_oqc_B2BShearing;
                FrmOQCDefineInvoice fr = new FrmOQCDefineInvoice("", sTestType, sTestID);
                res = fr.ShowDialog();
            }
            else if (rd_OQC_ACFFlattness.Checked)
            {
                sTestType = AccountRole.c_oqc_ACFFlatness;
                FrmOQCDefineIndiNoLine fr = new FrmOQCDefineIndiNoLine("", sTestType, sTestID);
                res = fr.ShowDialog();
            }
            else if (rd_OQC_Rounghness.Checked)
            {
                sTestType = AccountRole.c_oqc_ACFRoughness;
                FrmOQCDefineIndiNoLine fr = new FrmOQCDefineIndiNoLine("", sTestType, sTestID);
                res = fr.ShowDialog();
            }
            else if (rd_OQC_Bonding.Checked)
            {
                sTestType = AccountRole.c_oqc_ACFBonding;
                FrmOQCDefineInvoice fr = new FrmOQCDefineInvoice("", sTestType, sTestID);
                res = fr.ShowDialog();
            }
            else if (rd_OQC_HotOil.Checked)
            {
                sTestType = AccountRole.c_oqc_HotOil;
                FrmOQCDefineInvoice fr = new FrmOQCDefineInvoice("", sTestType, sTestID);
                res = fr.ShowDialog();
            }
            else if (rd_OQC_Heating4W.Checked)
            {
                sTestType = AccountRole.c_oqc_Heating4WTest;
                FrmOQCDefineInvoice fr = new FrmOQCDefineInvoice("", sTestType, sTestID);
                res = fr.ShowDialog();
            }
            else if (rd_OQC_XSectionGND.Checked)
            {
                sTestType = AccountRole.c_oqc_XSectionGNDB2Bpin;
                FrmOQCDefineIndi fr = new FrmOQCDefineIndi("", sTestType,sTestID);
                res = fr.ShowDialog();
            }
            else
            {
                this.Show();
                return;
            }
            if (res == DialogResult.OK || res == DialogResult.Cancel)
                this.Show();
        }

        private void btConfirmIPQC_Click(object sender, EventArgs e)
        {
            DialogResult res = DialogResult.OK;
            this.Hide();
            int nProcess = 0;
            string sTestType;
            string sTestID = string.Empty;
            if (rdIPQC_B2BXsection.Checked)
            {
                sTestType = AccountRole.c_ipqc_B2BXsection;
                FrmOQCDefineIndi fr = new FrmOQCDefineIndi("", sTestType, sTestID);
                res = fr.ShowDialog();
            }
            else if (rdIPQC_B2BPellingTest.Checked)
            {
                sTestType = AccountRole.c_ipqc_B2BPeelingTest;
                FrmOQCDefineIndi fr = new FrmOQCDefineIndi("", sTestType, sTestID);
                res = fr.ShowDialog();
            }
            else if (rdIPQC_B2BXray.Checked)
            {
                sTestType = AccountRole.c_ipqc_B2BXray;
                FrmOQCDefineIndi fr = new FrmOQCDefineIndi("", sTestType, sTestID);
                res = fr.ShowDialog();
            }
            else if (rdIPQC_OutlinePunching.Checked)
            {
                sTestType = AccountRole.c_ipqc_OutlinePunchingDimension;
                FrmOQCDefinePunching fr = new FrmOQCDefinePunching("", sTestType, sTestID);
                res = fr.ShowDialog();
            }
            //else if (rdIPQC_PlasmaACF.Checked)
            //{
            //    sTestType = AccountRole.c_ipqc_PlasmaACF;
            //    FrmOQCDefineIndi fr = new FrmOQCDefineIndi("", sTestType, sTestID);
            //    res = fr.ShowDialog();
            //}
            else
            {
                this.Show();
                return;
            }
            if (res == DialogResult.OK || res == DialogResult.Cancel)
                this.Show();
        }

        private void btConfirmORT_Click(object sender, EventArgs e)
        {
            DialogResult res = DialogResult.OK;
            this.Hide();
            int nProcess = 2;
            string sTestID = string.Empty;
            string sTestType;
            if (rdORT_ThermalCycling.Checked)
            {
                sTestType = AccountRole.c_ort_ThermalCycling;
                FrmOQCDefineIndi fr = new FrmOQCDefineIndi("", sTestType, sTestID);
                res = fr.ShowDialog();
            }
            else if (rdORT_HeatSoakAndRecovery.Checked)
            {
                sTestType = AccountRole.c_ort_HeatsoakAndRecovery;
                FrmOQCDefineIndi fr = new FrmOQCDefineIndi("", sTestType, sTestID);
                res = fr.ShowDialog();
            }
            else if (rdORT_ThermalShock.Checked)
            {
                sTestType = AccountRole.c_ort_ThermalShock;
                FrmOQCDefineIndi fr = new FrmOQCDefineIndi("", sTestType, sTestID);
                res = fr.ShowDialog();
            }
            else if (rdORT_ThermalCyclingFlexBending.Checked)
            {
                sTestType = AccountRole.c_ort_ThermalCyclingAndFlexBending;
                FrmOQCDefineIndi fr = new FrmOQCDefineIndi("", sTestType, sTestID);
                res = fr.ShowDialog();
            }
            else if (rdORT_FlexBending.Checked)
            {
                sTestType = AccountRole.c_ort_FlexBending;
                FrmOQCDefineIndi fr = new FrmOQCDefineIndi("", sTestType, sTestID);
                res = fr.ShowDialog();
            }
            else if (rdORT_HeatSoakFlexBending.Checked)
            {
                sTestType = AccountRole.c_ort_HeatsoakAndFlexBending;
                FrmOQCDefineIndi fr = new FrmOQCDefineIndi("", sTestType, sTestID);
                res = fr.ShowDialog();
            }
            else
            {
                this.Show();
                return;
            }
            if (res == DialogResult.OK || res == DialogResult.Cancel)
                this.Show();
        }

        private void rdORT_ThermalCycling_CheckedChanged(object sender, EventArgs e)
        {

        }

        private void rd_OQC_FAISPCMeasurement_CheckedChanged(object sender, EventArgs e)
        {

        }

        private void label4_Click(object sender, EventArgs e)
        {

        }

        private void rdOQC_B2BPeeling_CheckedChanged(object sender, EventArgs e)
        {

        }
    }
}
