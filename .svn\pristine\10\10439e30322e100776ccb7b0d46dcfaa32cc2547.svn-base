﻿using System;
using System.Data;
using Trace_AbilitySystem.Libs.Dataconnect;
using Trace_AbilitySystem.Libs.ITrace_02_FlexAssy_Services;

namespace Trace_AbilitySystem.Libs.Trace_02_FlexAssy_Services
{
    public class Source_FlexAssy_Plasma_Service : ISource_FlexAssy_Plasma_Service
    {
        private readonly DbExecute _db;

        public Source_FlexAssy_Plasma_Service()
        {
            _db = new SqlExecute();
        }

        public DataTable Plasma_GetDataWithEndTime(DateTime plasmaTime, string connectionStringOption)
        {
            string strWhere = "PlasmaEndTime > '" + plasmaTime.ToString("yyyy-MM-dd HH:mm:ss.fff") +"'";

            string sql = "SELECT ProductCount, QrCode, PlasmaTime, MachineName, PlasmaEndTime FROM Plasma WHERE " + strWhere + " ORDER BY PlasmaEndTime ASC";

            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }

        public DataTable Plasma_GetDataWithCheckTime(DateTime plasmaTime, string connectionStringOption)
        {
            string strWhere = "CheckTime > '" + plasmaTime.ToString("yyyy-MM-dd HH:mm:ss.fff") +"'";

            string sql = "SELECT ProductCount, QrCode, PlasmaTime, MachineName, CheckTime FROM Plasma WHERE " + strWhere + " ORDER BY CheckTime ASC";

            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }
        public DataTable Plasma_GetOEEDataWithCheckTime(DateTime plasmaTime, int minute, string connectionStringOption)
        {
            string strWhere = "CheckTime > '" + plasmaTime.ToString("yyyy-MM-dd HH:mm:ss.fff") + "' and CheckTime < '" + plasmaTime.AddMinutes(minute).ToString("yyyy-MM-dd HH:mm:ss.fff") + "'" +
                " and CheckOperatorId Is Not Null";

            string sql = "SELECT IndicationNumber,ProductCount, QrCode, PlasmaTime as DateTime, MachineName as MachineID, ItemName, CheckTime FROM Plasma WHERE " + strWhere + " ORDER BY CheckTime ASC";

            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }

        public DataTable Plasma_GetDataWithQrCode(string qrCode, string connectionStringOption)
        {
            string sql = "SELECT TOP(1) [QrCode], [IndicationNumber], [OperatorID], [PlasmaTime], [PlasmaCount], [ItemName], [MachineName], [CheckTime], [ProgramName], [TrolleyID], [PlasmaEndTime] FROM Plasma WHERE QrCode = '" + qrCode  + "'";

            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }

        public DataTable Plasma_GetDataWithAlbagID(string AlbagID, string connectionStringOption)
        {
            string sql = "SELECT TOP (1) [Plasma].*  FROM [dbo].[Plasma]  left join [Plasma_Albag] on [Plasma_Albag].[PlasmaCode] = [Plasma].QrCode  where Plasma_Albag.AlbagID = '" + AlbagID + "'  order by [Plasma].PlasmaEndTime desc";

            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }
        public DataTable Plasma_AlBag_GetByLastTime(DateTime LastTime, string connectionStringOption)
        {
            string sql = "SELECT TOP (20) PlasmaCode ,AlbagID ,PlasmaTime ,QtyProduct ,CheckTime ,TimeOk ,DifferenceTime ,Result ,ItemName ,ProgramName ,MachineID ,OperatorId ,CreatedDate ,IsReChecklTool"+
                            " FROM Plasma_DB.dbo.Plasma_Albag Where CreatedDate> '" + LastTime.ToString("yyyy-MM-dd HH:mm:ss.fff") + "' ORDER BY CreatedDate ASC";

            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }


        public DataTable PlasmaDetail_GetDataWithPlasmaID(string plasmaID, string connectionStringOption)
        {
            string sql = "SELECT FVICheckID FROM PlasmaDetail WHERE PlasmaID = '" + plasmaID + "' ORDER BY FVICheckID ASC";
            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }
    }
}