﻿using System;
using System.Data;

namespace Trace_AbilitySystem.Libs.ITrace_04_Machine_Services
{
    public interface ItProductionControlService
    {
        int tProductionControl_Insert(string indicationID, string machineID, DateTime? pdtCtrlDateTime, int? userID, string itemName, string itemCode, string batchNo, string seqNo, string result, string programName, string partAOI);

        DataTable tProductionControl_GetDataDetailWithID(int iD);
        DataTable tProductionControl_GetIDs(string machineID, string programName, DateTime dateTime);
        DataTable tProductionControl_GetDataDetailListID(DataTable iDs);
        DataTable tProductionControl_GetID2s(string machineID, string programName, DateTime dateTime);
        DataTable tProductionControl_GetDataByKey(string machineID, string programName, DateTime dateTime);
    }
}