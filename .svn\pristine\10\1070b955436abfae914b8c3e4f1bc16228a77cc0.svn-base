﻿@using System.Configuration;
@using Trace_AbilitySystem.Libs;
@model Trace_AbilitySystem.Libs.DTOClass.DataAllTrace
@if (Model != null && Model.DataBareFlex.BareFlex_09_3_DryFilmDES_ETCHING?.Rows.Count > 0
    && Model.DataBareFlex.BareFlex_09_3_DryFilmDES_ETCHING?.Rows.Count >= Model.DataBareFlex.BareFlex_09_3_DryFilmDES_ETCHING_Top_Display)
{
    //for (int i = 0; i < Model.DataBareFlex.BareFlex_09_3_DryFilmDES_ETCHING?.Rows.Count; i++)
    //{
    var i = Model.DataBareFlex.BareFlex_09_3_DryFilmDES_ETCHING_Top_Display - 1;

    string linkUrlParam = "";
    if (Model.DataBareFlex.BareFlex_09_3_DryFilmDES_ETCHING.Rows[i]["WCSID"] != DBNull.Value)
    {
        linkUrlParam = $"{Model.DataBareFlex.BareFlex_09_3_DryFilmDES_ETCHING.Rows[i]["WCSID"]}";
    }

    <h2 class="bd-title" id="9-3-dry-film-des">Dry film/DES (ETCHING) / <a title="Seq_No" href="#" id="@Model.DataBareFlex.BareFlex_09_3_DryFilmDES_ETCHING_Sequence.ToString()">@Model.DataBareFlex.BareFlex_09_3_DryFilmDES_ETCHING_Sequence.ToString()</a></h2>
    <table class="table table-bordered">
        <thead>
            <tr>
                <th scope="col">Control Item</th>
                <th scope="col">Control Value</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>WorkOrder</td>
                <td>@Model.DataBareFlex.BareFlex_09_3_DryFilmDES_ETCHING.Rows[i]["WorkOrder"]</td>
            </tr>
            <tr>
                <td>DateTime</td>
                <td>@Model.DataBareFlex.BareFlex_09_3_DryFilmDES_ETCHING.Rows[i]["DateTime"]</td>
            </tr>
            <tr>
                <td>Machine ID</td>
                <td>@Model.DataBareFlex.BareFlex_09_3_DryFilmDES_ETCHING.Rows[i]["MachineID"]</td>
            </tr>
            <tr>
                <td>Operator ID</td>
                <td>@Model.DataBareFlex.BareFlex_09_3_DryFilmDES_ETCHING.Rows[i]["OperatorID"]</td>
            </tr>
            <tr>
                <td>
                    WCS ID linkage
                </td>
                <td>@if (!string.IsNullOrEmpty(linkUrlParam)){string[] WCSIDArr = (linkUrlParam).Trim().Split(';');foreach (string Link in WCSIDArr){string _linkUrlParam = $"{ConfigurationManager.AppSettings["LinkUrlParam"]}{Link}";<a target="_blank" style="display:block" href="@_linkUrlParam">@_linkUrlParam</a>}}</td>
            </tr>

            @*<tr>
                <td>Machine maintenance & repair record</td>
                <td><a onclick="viewDataMachineMaintenance(@Model.DataBareFlex.BareFlex_09_3_DryFilmDES_ETCHING.Rows[i]["MachineMaintenanceID"])" href="javascript:">@Model.DataBareFlex.BareFlex_09_3_DryFilmDES_ETCHING.Rows[i]["MachineMaintenanceDate"]</a></td>
            </tr>
            <tr>
                <td>Chemistry analysis record</td>
                <td>@if (!string.IsNullOrEmpty(linkUrlParam)){string[] WCSIDArr = (linkUrlParam).Trim().Split(';');foreach (string Link in WCSIDArr){string _linkUrlParam = $"{ConfigurationManager.AppSettings["LinkUrlParam"]}{Link}";<a target="_blank" style="display:block" href="@_linkUrlParam">@_linkUrlParam</a>}}</td>
            </tr>
            <tr>
                <td>Chemistry SPC monitoring</td>
                <td>@if (!string.IsNullOrEmpty(linkUrlParam)){string[] WCSIDArr = (linkUrlParam).Trim().Split(';');foreach (string Link in WCSIDArr){string _linkUrlParam = $"{ConfigurationManager.AppSettings["LinkUrlParam"]}{Link}";<a target="_blank" style="display:block" href="@_linkUrlParam">@_linkUrlParam</a>}}</td>
            </tr>
            <tr>
                <td>Machine running record (pressure, flow rate, temperature, conveyor speed, etc )</td>
                <td>@if (!string.IsNullOrEmpty(linkUrlParam)){string[] WCSIDArr = (linkUrlParam).Trim().Split(';');foreach (string Link in WCSIDArr){string _linkUrlParam = $"{ConfigurationManager.AppSettings["LinkUrlParam"]}{Link}";<a target="_blank" style="display:block" href="@_linkUrlParam">@_linkUrlParam</a>}}</td>
            </tr>
            <tr>
                <td>Cosmetic check record</td>
                <td>@if (!string.IsNullOrEmpty(linkUrlParam)){string[] WCSIDArr = (linkUrlParam).Trim().Split(';');foreach (string Link in WCSIDArr){string _linkUrlParam = $"{ConfigurationManager.AppSettings["LinkUrlParam"]}{Link}";<a target="_blank" style="display:block" href="@_linkUrlParam">@_linkUrlParam</a>}}</td>
            </tr>
            <tr>
                <td>Impedance check record</td>
                <td>@if (!string.IsNullOrEmpty(linkUrlParam)){string[] WCSIDArr = (linkUrlParam).Trim().Split(';');foreach (string Link in WCSIDArr){string _linkUrlParam = $"{ConfigurationManager.AppSettings["LinkUrlParam"]}{Link}";<a target="_blank" style="display:block" href="@_linkUrlParam">@_linkUrlParam</a>}}</td>
            </tr>
            <tr>
                <td>Etching uniformity check record</td>
                <td>@if (!string.IsNullOrEmpty(linkUrlParam)){string[] WCSIDArr = (linkUrlParam).Trim().Split(';');foreach (string Link in WCSIDArr){string _linkUrlParam = $"{ConfigurationManager.AppSettings["LinkUrlParam"]}{Link}";<a target="_blank" style="display:block" href="@_linkUrlParam">@_linkUrlParam</a>}}</td>
            </tr>
            <tr>
                <td>Scan traveller bar code to get the information of P/N</td>
                <td>@if (!string.IsNullOrEmpty(linkUrlParam)){string[] WCSIDArr = (linkUrlParam).Trim().Split(';');foreach (string Link in WCSIDArr){string _linkUrlParam = $"{ConfigurationManager.AppSettings["LinkUrlParam"]}{Link}";<a target="_blank" style="display:block" href="@_linkUrlParam">@_linkUrlParam</a>}}</td>
            </tr>
            <tr>
                <td>Double confirm the Lot no. between traveller and panel lot code</td>
                <td>@if (!string.IsNullOrEmpty(linkUrlParam)){string[] WCSIDArr = (linkUrlParam).Trim().Split(';');foreach (string Link in WCSIDArr){string _linkUrlParam = $"{ConfigurationManager.AppSettings["LinkUrlParam"]}{Link}";<a target="_blank" style="display:block" href="@_linkUrlParam">@_linkUrlParam</a>}}</td>
            </tr>*@
            <tr>
                <td>Chemical process</td>
                <td>
                    @{ var dateTime = DataConvert.ToDateTime(@Model.DataBareFlex.BareFlex_09_3_DryFilmDES_ETCHING.Rows[i]["DateTime"]).ToString("yyyy-MM-dd");
                        var machineId = @Model.DataBareFlex.BareFlex_09_3_DryFilmDES_ETCHING.Rows[i]["MachineID"].ToString();
                        var link = $"http://spait-seev-2.is.sei.co.jp:8550/SEI-BOARD/index.html?bid=101&from={dateTime}&target_machineid={machineId}"; }
                    <a target="_blank" href="@link">@link</a>
                </td>
            </tr>
        </tbody>
    </table>
    //}
}