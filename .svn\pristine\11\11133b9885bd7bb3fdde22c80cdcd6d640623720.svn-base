﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trace_AbilitySystem.Libs.ITrace_05_Local_Services;
using Trace_AbilitySystem.Libs.Dataconnect;
using System.Data;
using System.Data.SqlClient;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;

namespace Trace_AbilitySystem.Libs.Trace_05_Local_Services
{
    public class BoardRegisService : IBoardRegisService
    {
        private readonly DbExecute _db;
        public BoardRegisService()
        {
            _db = new SqlExecute();
        }
        public string GetItemNameByProgramName(string programName, string connectionStringOption)
        {
            string ItemName = "";
            try
            {
                var paras = new SqlParameter[1];
                paras[0] = new SqlParameter("@ProgramName", programName ?? (object)DBNull.Value);

                DataTable rs = _db.Execute_Table("sp_ItemName_GetItemNameByProgramName", paras, CommandType.StoredProcedure, connectionStringOption);
                if (rs?.Rows.Count > 0)
                {
                    ItemName = rs.Rows[0]["ItemName"] + "";
                }
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp(ex.ToString());
            }
            return ItemName;
        }
        public bool CheckExist(string Indi, out string ItemName, string ConnectionString)
        {
            ItemName = string.Empty;
            bool bExist = false;
            try
            {

                var paras = new SqlParameter[1];
                paras[0] = new SqlParameter("@IndicationNumber", Indi);
                DataTable dt = _db.Execute_Table("sp_sms_YieldRate_GetByIndi", paras, CommandType.StoredProcedure, ConnectionString);
                if (dt?.Rows.Count > 0)
                {
                    bExist = true;
                    ItemName = dt.Rows[0]["ItemName"].ToString();
                }
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp(ex);
            }
            return bExist;
        }
        public bool IndiF1CheckExist(string Indi, out string ItemName, string ConnectionString)
        {
            ItemName = string.Empty;
            bool bExist = false;
            try
            {

                var paras = new SqlParameter[1];
                paras[0] = new SqlParameter("@IndicationNumber", Indi);
                DataTable dt = _db.Execute_Table("sp_sms_YieldRate_GetByIndiF1", paras, CommandType.StoredProcedure, ConnectionString);
                if (dt?.Rows.Count > 0)
                {
                    bExist = true;
                    ItemName = dt.Rows[0]["ItemName"].ToString();
                }
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp(ex);
            }
            return bExist;
        }
        public bool IndiSupplyCheckExist(string Indi, string ConnectionString)
        {
            bool bExist = false;
            try
            {
                var paras = new SqlParameter[1];
                paras[0] = new SqlParameter("@IndicationNumber", Indi);
                DataTable dt = _db.Execute_Table("sp_sms_TblIndiInvoiceDefine_GetByIndi", paras, CommandType.StoredProcedure, ConnectionString);
                if (dt?.Rows.Count > 0)
                {
                    bExist = true;
                }
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp(ex);
            }
            return bExist;
        }
        public int CheckTotalReelByLotNumber(string Indi, string ConnectionString)
        {
            int total = 0;
            try
            {
                var paras = new SqlParameter[1];
                paras[0] = new SqlParameter("@LotNumber", Indi);
                DataTable dt = _db.Execute_Table("sms_Component_countByLotNumber", paras, CommandType.StoredProcedure, ConnectionString);
                if (dt?.Rows.Count > 0)
                {
                    total = int.Parse(dt.Rows[0]["TotalReel"] + "");
                }
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp(ex);
                return total;
            }
            return total;
        }
        public bool checkIndiInvoiceExist(string Indi, string Invoice, string ConnectionString)
        {
            bool bExist = false;
            try
            {
                var paras = new SqlParameter[1];
                paras[0] = new SqlParameter("@IndicationNumber", Indi);
                paras[1] = new SqlParameter("@Invoice", Invoice);
                DataTable dt = _db.Execute_Table("sp_sms_TblIndiInvoiceDefine_GetByIndiInvoice", paras, CommandType.StoredProcedure, ConnectionString);
                if (dt?.Rows.Count > 0)
                {
                    bExist = true;
                }
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp(ex);
            }
            return bExist;
        }
        public bool checkOnlyInvoiceExist(string Invoice, string ConnectionString)
        {
            bool bExist = false;
            try
            {
                var paras = new SqlParameter[1];
                paras[0] = new SqlParameter("@Invoice", Invoice);
                DataTable dt = _db.Execute_Table("sp_sms_TblIndiInvoiceDefine_GetByInvoice", paras, CommandType.StoredProcedure, ConnectionString);
                if (dt?.Rows.Count > 0)
                {
                    bExist = true;
                }
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp(ex);
            }
            return bExist;
        }
        public string GetItemName(string Indi, string ConnectionString)
        {
            String ItemName = string.Empty;
            try
            {

                var paras = new SqlParameter[1];
                paras[0] = new SqlParameter("@IndicationNumber", Indi);
                DataTable dt = _db.Execute_Table("sp_sms_YieldRate_GetByIndi", paras, CommandType.StoredProcedure, ConnectionString);
                if (dt?.Rows.Count > 0)
                {
                    ItemName = dt.Rows[0]["ItemName"].ToString().Split('-')[0];
                }
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp(ex);
            }
            return ItemName;
        }
        public string GetItemNameByBlockID(string BlockID, out string IndicationNumber, string ConnectionString)
        {
            String ItemName = string.Empty;
            IndicationNumber = "";
            try
            {

                var paras = new SqlParameter[1];
                paras[0] = new SqlParameter("@BlockID", BlockID);
                DataTable dt = _db.Execute_Table("sp_sms_YieldRate_GetByBLockID", paras, CommandType.StoredProcedure, ConnectionString);
                if (dt?.Rows.Count > 0)
                {
                    ItemName = dt.Rows[0]["ItemName"].ToString().Split('-')[0];
                    IndicationNumber = dt.Rows[0]["FPCBALot"] + "";
                }
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp(ex);
            }
            return ItemName;
        }
        public bool CheckBlockExist(string BlockID, string ConnectionString)
        {
            bool bExist = false;
            try
            {

                var paras = new SqlParameter[1];
                paras[0] = new SqlParameter("@BlockID", BlockID);
                DataTable dt = _db.Execute_Table("sp_BlockID_FindByBlockID", paras, CommandType.StoredProcedure, ConnectionString);
                if (dt?.Rows.Count > 0)
                {
                    bExist = true;
                }
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp(ex);
            }
            return bExist;
        }
        public bool CheckProductIndicationExist(string ProductID, string Indication, string ConnectionString)
        {
            bool bExist = false;
            try
            {
                var paras = new SqlParameter[2];
                paras[0] = new SqlParameter("@ProductID", ProductID);
                paras[1] = new SqlParameter("@Indication", Indication);
                DataTable dt = _db.Execute_Table("sp_CheckProductIndicationExist", paras, CommandType.StoredProcedure, ConnectionString);

                //string paras = "SELECT BlockId.IndicationNumber FROM BlockId " +
                //    "LEFT JOIN [SEI-VCDTraceDB-NM4].dbo.IDLink ON [SEI-VCDTraceDB-NM4].dbo.IDLink.BlockID = BlockId.BlockIdSerial " +
                //    "where IndicationNumber = '" + Indication + "' and ProductID = '" + ProductID + "'";
                //DataTable dt = _db.Execute_Table(paras, null, CommandType.Text, ConnectionString);

                if (dt?.Rows.Count > 0)
                {
                    bExist = true;
                }
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp(ex);
            }
            return bExist;
        }

        public string YieldRateGetIndiFixByItemCode(string ItemCode, string ConnectionString)
        {
            string rs = "00000B";
            try
            {
                var paras = new SqlParameter[1];
                paras[0] = new SqlParameter("@ItemCode", ItemCode);
                DataTable dt = _db.Execute_Table("sms_YieldRateGetIndiFixByItemCode", paras, CommandType.StoredProcedure, ConnectionString);

                if (dt?.Rows.Count > 0)
                {
                    string indi = dt.Rows[0]["FPCBALot"] + "";
                    indi = indi.Replace(ItemCode, "");
                    rs = rs.Substring(0, 6);
                }
            }
            catch (Exception ex) {
                ManageLog.WriteErrorApp(ex);
                rs = "00000B";
            }
            return rs;
        }

        public string YieldRateGetIndiFixByItemNameItemCode(string ItemName, string ItemCode, string ConnectionString)
        {
            string rs = "00000B";
            try
            {
                var paras = new SqlParameter[2];
                paras[0] = new SqlParameter("@ItemName", ItemName);
                paras[1] = new SqlParameter("@ItemCode", ItemCode);
                DataTable dt = _db.Execute_Table("sms_YieldRateGetIndiFixByItemNameItemCode", paras, CommandType.StoredProcedure, ConnectionString);

                if (dt?.Rows.Count > 0)
                {
                    string indi = dt.Rows[0]["FPCBALot"] + "";
                    indi = indi.Replace(ItemCode, "");
                    rs = rs.Substring(0, 6);
                }
            }
            catch (Exception ex) {
                ManageLog.WriteErrorApp(ex);
                rs = "00000B";
            }
            return rs;
        }
    }
}
