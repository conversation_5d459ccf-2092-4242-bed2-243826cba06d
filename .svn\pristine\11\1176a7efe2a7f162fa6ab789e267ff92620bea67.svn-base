﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trace_AbilitySystem.Libs.Dataconnect;
using Trace_AbilitySystem.Libs.ITrace_07_Pismo_Auto_Service;

namespace Trace_AbilitySystem.Libs.Trace_07_Pismo_Auto_Services
{
    public class Pismo_ItemlotServices : IPismo_ItemlotServices
    {
        private readonly DbExecute _db;
        private readonly string _connectionStringOption = "PismoTest";

        public Pismo_ItemlotServices()
        {
            _db = new SqlExecute();
        }
        public async Task<int> Pismo_Itemlot_ProcessData(string factory, DateTime createdDateSearch, string connectionStringOption)
        {
            try
            {
                DateTime dateNow = DateTime.Now;
                DateTime timeLast = createdDateSearch;
                int recordNeedSyn = 0;
                int recordSyned = 0;
                int OperatorPkid = 0;
                DataTable dt = Singleton_07_Pismo.IPismo_ItemlotServices.Pismo_Itemlot_GetDataWithCreatedDate(timeLast, connectionStringOption);
                if (dt == null)
                    return -1;

                if (dt.Rows.Count > 0)
                {
                    recordNeedSyn = dt.Rows.Count;
                    for (int i = 0; i < recordNeedSyn; i++)
                    {
                        int PKID = int.Parse(dt.Rows[i]["PKID"].ToString());
                        int ItemNamePkid = int.Parse(dt.Rows[i]["ItemNamePkid"].ToString());
                        int ItemCodePkid = int.Parse(dt.Rows[i]["ItemCodePkid"].ToString());
                        string ItemLot = dt.Rows[i]["ItemLot"].ToString();
                        string ItemName = dt.Rows[i]["ItemName"].ToString();
                        string ItemCode = dt.Rows[i]["ItemCode"].ToString();
                        OperatorPkid = dt.Rows[i]["OperatorPkid"] == DBNull.Value ? 0 : int.Parse(dt.Rows[i]["OperatorPkid"].ToString());
                        DateTime CreatedDate = Convert.ToDateTime(dt.Rows[i]["CreateDate"]);
                        int rs = Pismo_Itemlot_Insert(PKID, ItemNamePkid, ItemCodePkid, ItemLot, ItemName, ItemCode, OperatorPkid, CreatedDate, "PismoTest");
                        if (rs == -9)
                            return -1;

                        timeLast = Convert.ToDateTime(dt.Rows[i]["CreateDate"]);
                        recordSyned++;
                    }
                    if (timeLast == createdDateSearch)
                    {
                        timeLast = timeLast.AddMinutes(5) > DateTime.Now ? timeLast : timeLast.AddMinutes(5);
                    }
                    // Update ValueSearch
                    Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, "Pismo_Itemlot", timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff"));
                }
                else
                {
                    timeLast = timeLast.AddMinutes(5) > dateNow ? timeLast : timeLast.AddMinutes(5);

                    Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, "Pismo_Itemlot", timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff"));
                }

                ManageLog.WriteProcess(true, null, recordNeedSyn, recordSyned);
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp("Pismo_Itemlot" + "\n" + ex.Message);
                return -1;
            }
            await Task.Delay(500);

            return 1;
        }
        public int Pismo_Itemlot_Insert(int PKID, int ItemNamePkid, int ItemCodePkid, string ItemLot, string ItemName, string ItemCode, int OperatorPkid, DateTime CreateDate, string connectionStringOption)
        {
            var paras = new SqlParameter[8];
            paras[0] = new SqlParameter("@ItemlotPkid", PKID);
            paras[1] = new SqlParameter("@ItemNamePkid", ItemNamePkid);
            paras[2] = new SqlParameter("@ItemCodePkid", ItemCodePkid);
            paras[3] = new SqlParameter("@ItemLot", ItemLot);
            paras[4] = new SqlParameter("@ItemName", ItemName);
            paras[5] = new SqlParameter("@ItemCode", ItemCode);
            paras[6] = new SqlParameter("@OperatorPkid", OperatorPkid);
            paras[7] = new SqlParameter("@CreateDate", CreateDate);

            return _db.Execute_Modify("_Pismo_Itemlot_Insert", paras, CommandType.StoredProcedure, _connectionStringOption);
        }
        public DataTable Pismo_Itemlot_GetDataWithCreatedDate(DateTime createdDate, string connectionStringOption)
        {
            var paras = new SqlParameter[1];
            paras[0] = new SqlParameter("@CreatedDate", createdDate);
            return _db.Execute_Table("sms_Pismo_Itemlot_GetDataWithCreatedDate", paras, CommandType.StoredProcedure, connectionStringOption);
        }
    }
}
