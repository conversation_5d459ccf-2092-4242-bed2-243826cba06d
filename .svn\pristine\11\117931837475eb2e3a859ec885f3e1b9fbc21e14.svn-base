﻿using OfficeOpenXml.FormulaParsing.Excel.Functions.DateTime;
using OfficeOpenXml.FormulaParsing.Excel.Functions.RefAndLookup;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Xml.Linq;
using Trace_AbilitySystem.Libs.Dataconnect;
using Trace_AbilitySystem.Libs.ITrace_05_Local_Services;
using Trace_AbilitySystem.Libs.Trace_AbilitySystemDB_Common_Tables;
namespace Trace_AbilitySystem.Libs.Trace_05_Local_Services
{
    public class OQC_IPQC_Local_Service : IOQC_IPQC_Local_Service
    {
        private readonly DbExecute _db;
        private readonly string connectionStringOption = "Local";

        public OQC_IPQC_Local_Service()
        {
            _db = new SqlExecute();
        }

        public DataTable IPQC_OQC_GetDateTimeAndMachineID_ByBlockID(string blockID, string connectionStringOption)
        {
            string sql = "select top(1) DateTime, MachineID from FlexAssy_19_Punching where BlockID = '" + blockID + "' order by PunchDieID desc";
            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }
        public DataTable IPQC_OQC_GetDateTimeAndMachineID_ByBlockIDForFai(string blockID, string connectionStringOption)
        {
            string sql = "select top(1) DateTime, MachineID from FlexAssy_19_Punching where BlockID = '" + blockID + "' order by PunchDieID asc";
            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }


        public DataTable IPQC_OQC_Detail_GetByProductID(string productID, string connectionStringOption)
        {
            string sql = "SELECT * from FlexAssy_32_IPQC_OQC_ORT as t1 inner join FlexAssy_32_IPQC_OQC_ORT_Detail as t2 on t1.DetailID = t2.DetailID where ProductID ='" + productID + "'" ;
            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }

        public DataTable IPQC_OQC_GetByItemCode(string itemcode, string connectionStringOption)
        {
            string sql = "SELECT * from FlexAssy_32_IPQC_OQC_ORT_Detail  as t1 left join FlexAssy_32_IPQC_OQC_ORT"
                + " as t2 on t2.DetailID = t1.DetailID where t2.ItemCode ='" + itemcode + "'";
            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }

        public bool CheckProductExist(int nTestID,string pcsID, string connectionString)
        {
            bool bExist = false;
            var paras = new SqlParameter[1];
            paras[0] = new SqlParameter("@ProductID", pcsID);
            DataTable dt =  _db.Execute_Table("sp_sms_FlexAssy_32_IPQC_OQC_ORT_Detail_GetByProductID_Test", paras, CommandType.StoredProcedure, connectionString);
            if (dt?.Rows.Count > 0)
            {
                if (!((string)dt?.Rows[0]["TestName"].ToString()).Contains("ORT"))
                {
                    return bExist;
                } 
                bExist = true;
            }
            return bExist;
        }
        public int InsertTest(string ItemCode,string  ItemName,string LineID,string OperatorID,string TestID,string TestName,decimal DetailID, string connectionString)
        {
            int nRes = -1;
            //@ItemCode, @ItemName,@LineID, @OperatorID , @TestID,  @TestName,@DetailID
            //
            try
            {
                var paras = new SqlParameter[7];
                paras[0] = new SqlParameter("@ItemCode", ItemCode);
                paras[1] = new SqlParameter("@ItemName", ItemName);
                paras[2] = new SqlParameter("@LineID", LineID);
                paras[3] = new SqlParameter("@InputOperator", OperatorID);
                paras[4] = new SqlParameter("@TestID", TestID);
                paras[5] = new SqlParameter("@TestName", TestName);
                paras[6] = new SqlParameter("@DetailID", DetailID);
                nRes = _db.Execute_Modify("sp_sms_FlexAssy_32_IPQC_OQC_ORT_InsertTest", paras, CommandType.StoredProcedure, connectionString);
                
            }
            catch(Exception ex)
            {
                ManageLog.WriteErrorApp(ex.Message);
            }
            return nRes;
        }


        // Không sử dụng đến
        public int InsertTest_v134(string ItemCode, string ItemName, string LineID, string OperatorID, string TestID, string TestName, decimal DetailID, DateTime productionDateTest, string connectionString)
        {
            int nRes = -1;
            //@ItemCode, @ItemName,@LineID, @OperatorID , @TestID,  @TestName,@DetailID
            //
            try
            {
                var paras = new SqlParameter[8];
                paras[0] = new SqlParameter("@ItemCode", ItemCode);
                paras[1] = new SqlParameter("@ItemName", ItemName);
                paras[2] = new SqlParameter("@LineID", LineID);
                paras[3] = new SqlParameter("@InputOperator", OperatorID);
                paras[4] = new SqlParameter("@TestID", TestID);
                paras[5] = new SqlParameter("@TestName", TestName);
                paras[6] = new SqlParameter("@DetailID", DetailID);
                paras[7] = new SqlParameter("@ProductionDateTest", productionDateTest);
                nRes = _db.Execute_Modify("sp_sms_FlexAssy_32_IPQC_OQC_ORT_InsertTest_v134", paras, CommandType.StoredProcedure, connectionString);

            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp(ex.Message);
            }
            return nRes;
        }



        // Không sử dụng đến
        public bool InsertTestIndi(string Indi,string ItemCode, string ItemName, string LineID, string OperatorID, string TestID, string TestName, decimal DetailID, string connectionString)
        {
            int nRes = -1;
            //@ItemCode, @ItemName,@LineID, @OperatorID , @TestID,  @TestName,@DetailID
            //
            try
            {
                var paras = new SqlParameter[8];
                paras[0] = new SqlParameter("@IndicationNumber", Indi);
                paras[1] = new SqlParameter("@ItemCode", ItemCode);
                paras[2] = new SqlParameter("@ItemName", ItemName);
                paras[3] = new SqlParameter("@LineID", LineID);
                paras[4] = new SqlParameter("@InputOperator", OperatorID);
                paras[5] = new SqlParameter("@TestID", TestID);
                paras[6] = new SqlParameter("@TestName", TestName);
                paras[7] = new SqlParameter("@DetailID", DetailID);
                nRes = _db.Execute_Modify("sp_sms_FlexAssy_32_IPQC_OQC_ORT_InsertTestIndi", paras, CommandType.StoredProcedure, connectionString);

            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp(ex.Message);
            }
            return nRes > 0;
        }


        public bool InsertTestIndi_v134(string Indi, string ItemCode, string ItemName, string LineID, string OperatorID, string TestID, string TestName, decimal DetailID,DateTime productionDateTest ,string connectionString)
        {
            int nRes = -1;
            //@ItemCode, @ItemName,@LineID, @OperatorID , @TestID,  @TestName,@DetailID
            //
            try
            {
                var paras = new SqlParameter[9];
                paras[0] = new SqlParameter("@IndicationNumber", Indi);
                paras[1] = new SqlParameter("@ItemCode", ItemCode);
                paras[2] = new SqlParameter("@ItemName", ItemName);
                paras[3] = new SqlParameter("@LineID", LineID);
                paras[4] = new SqlParameter("@InputOperator", OperatorID);
                paras[5] = new SqlParameter("@TestID", TestID);
                paras[6] = new SqlParameter("@TestName", TestName);
                paras[7] = new SqlParameter("@DetailID", DetailID);
                paras[8] = new SqlParameter("@ProductionDateTest", productionDateTest);
                nRes = _db.Execute_Modify("sp_sms_FlexAssy_32_IPQC_OQC_ORT_InsertTestIndi_v134", paras, CommandType.StoredProcedure, connectionString);
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp(ex.Message);
            }
            return nRes > 0;
        }


        public bool InsertTestInvoice(string Invoice, string Indi, string ItemCode, string ItemName, string LineID, string OperatorID, string TestID,
            string TestName, decimal DetailID, string connectionString)
        {
            int nRes = -1;
            //@ItemCode, @ItemName,@LineID, @OperatorID , @TestID,  @TestName,@DetailID
            //
            try
            {
                var paras = new SqlParameter[10];
                paras[0] = new SqlParameter("@InvoiceID", Invoice);
                paras[1] = new SqlParameter("@IndicataionNumber", Indi);
                paras[2] = new SqlParameter("@ItemName", ItemName);
                paras[3] = new SqlParameter("@LineID", LineID);
                paras[4] = new SqlParameter("@InputTest", DateTime.Now.ToString("yyyy-MM-dd hh:mm:ss.fff"));
                paras[5] = new SqlParameter("@InputOperator", OperatorID);
                paras[6] = new SqlParameter("@TestID", TestID);
                paras[7] = new SqlParameter("@TestName", TestName);
                paras[8] = new SqlParameter("@DetailID", DetailID);
                paras[9] = new SqlParameter("@ItemCode", ItemCode);
                nRes = _db.Execute_Modify("sp_sms_FlexAssy_32_IPQC_OQC_ORT_InsertInvoice", paras, CommandType.StoredProcedure, connectionString);

            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp(ex.Message);
            }
            return nRes > 0;
        }



        // Không sử dụng đến
        public bool InsertTestInvoice_v134(string Invoice, string Indi, string ItemCode, string ItemName, string LineID, string OperatorID, string TestID,
            string TestName, decimal DetailID, DateTime productionDateTest, string connectionString)
        {
            int nRes = -1;
            //@ItemCode, @ItemName,@LineID, @OperatorID , @TestID,  @TestName,@DetailID
            //
            try
            {
                var paras = new SqlParameter[11];
                paras[0] = new SqlParameter("@InvoiceID", Invoice);
                paras[1] = new SqlParameter("@IndicataionNumber", Indi);
                paras[2] = new SqlParameter("@ItemName", ItemName);
                paras[3] = new SqlParameter("@LineID", LineID);
                paras[4] = new SqlParameter("@InputTest", DateTime.Now.ToString("yyyy-MM-dd hh:mm:ss.fff"));
                paras[5] = new SqlParameter("@InputOperator", OperatorID);
                paras[6] = new SqlParameter("@TestID", TestID);
                paras[7] = new SqlParameter("@TestName", TestName);
                paras[8] = new SqlParameter("@DetailID", DetailID);
                paras[9] = new SqlParameter("@ItemCode", ItemCode);
                paras[10] = new SqlParameter("@ProductionDateTest", productionDateTest);
                nRes = _db.Execute_Modify("sp_sms_FlexAssy_32_IPQC_OQC_ORT_InsertInvoice_v134", paras, CommandType.StoredProcedure, connectionString);

            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp(ex.Message);
            }
            return nRes > 0;
        }
        public decimal GetDetailID(string connectionString)
        {//sp_sms_FlexAssy_32_IPQC_OQC_ORT_Detail_GetNextDetailID
            decimal nID = 0;
            DataTable dt = _db.Execute_Table("sp_sms_FlexAssy_32_IPQC_OQC_ORT_Detail_GetNextDetailID", null, CommandType.StoredProcedure, connectionString);
            if (dt?.Rows.Count > 0)
            {
                nID = decimal.Parse(dt.Rows[0].ItemArray[0].ToString());
            }
            return nID;
        }
        public int InsertProductDetail(string ProductID, decimal DetailID, string connectionString)
        {
            
            int nRes = -1;
            var paras = new SqlParameter[2];
            try
            {
                paras[0] = new SqlParameter("@ProductID", ProductID);
                paras[1] = new SqlParameter("@IDDetail", DetailID);
                nRes = _db.Execute_Modify("sp_sms_FlexAssy_32_IPQC_OQC_ORT_Detail_InsertProduct", paras, CommandType.StoredProcedure, connectionString);
            }
            catch(Exception ex)
            {
                ManageLog.WriteErrorApp(ex.Message);
            }
            return nRes;
        }
      
        public DataTable GetUnsyncTest(string lastDate)
        {
            var paras = new SqlParameter[1];
            paras[0] = new SqlParameter("@CreatedDate", lastDate);
            return _db.Execute_Table("sp_sms_FlexAssy_32_IPQC_OQC_ORT_GetUnsyncTest", paras, CommandType.StoredProcedure, connectionStringOption);
        }
        public DataTable GetUnsyncDetail(string lastDate)
        {
            var paras = new SqlParameter[1];
            paras[0] = new SqlParameter("@CreatedDate", lastDate);
            return _db.Execute_Table("sp_sms_FlexAssy_32_IPQC_OQC_ORT_Detail_GetUnsyncDetail", paras, CommandType.StoredProcedure, connectionStringOption);
        }
        public DataTable GetProductTestDetail(decimal DetailID)
        {
            string connectionStringOption = "Local";
            DataTable dt = new DataTable();
            var paras = new SqlParameter[1];
            paras[0] = new SqlParameter("@w_DetailID", DetailID);
            dt = _db.Execute_Table("sp_sms_FlexAssy_32_IPQC_OQC_ORT_Detail_GetProductTest", paras, CommandType.StoredProcedure, connectionStringOption);
            return dt;
        }
        public string CheckBlock(string ProductID, string connectionStringOption)
        {
            string BlockID = string.Empty;
            var paras = new SqlParameter[1];
            paras[0] = new SqlParameter("@ProductID", ProductID);
            DataTable dt = _db.Execute_Table("sp_IDLink_FindProductID", paras, CommandType.StoredProcedure, connectionStringOption);
            if(dt?.Rows?.Count >0)
            {
                if(dt.Rows.Count ==1)
                {
                    BlockID = dt.Rows[0]["BlockID"].ToString();
                }
            }    
            return BlockID;

        }
        public bool CheckBlockPunchingData(string BlockID,string MachineIDIn, out string MachineID, string connectionString)
        {
            bool bExist = false;
            var paras = new SqlParameter[3];
            paras[0] = new SqlParameter("@BlockID", BlockID);
            paras[1] = new SqlParameter("@ProgramType", "Punching");
            paras[2] = new SqlParameter("@MachineID", MachineIDIn);
            DataTable dt = _db.Execute_Table("sp_ShopFloorMain_FindByBlockIDAndProgramTypeMachineID", paras, CommandType.StoredProcedure, connectionString);
            if (dt?.Rows?.Count > 0)
            {
                bExist = true;
                MachineID = dt.Rows[0]["MachineID"].ToString();
                return bExist;
            }
            else
            {
                bExist = false;
                var paras2 = new SqlParameter[2];
                paras2[0] = new SqlParameter("@BlockID", BlockID);
                paras2[1] = new SqlParameter("@ProgramType", "Punching");
                DataTable dt2 = _db.Execute_Table("sp_ShopFloorMain_FindByBlockIDAndProgramType", paras2, CommandType.StoredProcedure, connectionString);
                MachineID = string.Empty;
                if (dt2?.Rows?.Count > 0)
                {
                    foreach (DataRow row1 in dt2.Rows)
                    { MachineID += row1["MachineID"].ToString() +","; }
                    return bExist;
                }
            }
            MachineID = string.Empty;
            return bExist;
        }
        public bool CheckBlockPastingData(string BlockID, string MachineIDIn, out string MachineID, string connectionString)
        {
            bool bExist = false;
            var paras = new SqlParameter[3];
            paras[0] = new SqlParameter("@BlockID", BlockID);
            paras[1] = new SqlParameter("@ProgramType", "Pasting");
            paras[2] = new SqlParameter("@MachineID", MachineIDIn);
            DataTable dt = _db.Execute_Table("sp_ShopFloorMain_FindByBlockIDAndProgramTypeMachineID", paras, CommandType.StoredProcedure, connectionString);
            if (dt?.Rows?.Count > 0)
            {
                bExist = true;
                MachineID = dt.Rows[0]["MachineID"].ToString();
                return bExist;
            }
            else
            {
                bExist = false;
                var paras2 = new SqlParameter[2];
                paras2[0] = new SqlParameter("@BlockID", BlockID);
                paras2[1] = new SqlParameter("@ProgramType", "Punching");
                DataTable dt2 = _db.Execute_Table("sp_ShopFloorMain_FindByBlockIDAndProgramType", paras2, CommandType.StoredProcedure, connectionString);
                MachineID = string.Empty;
                if (dt2?.Rows?.Count > 0)
                {
                    foreach (DataRow row1 in dt2.Rows)
                    { MachineID += row1["MachineID"].ToString() + ","; }
                    return bExist;
                }
            }
            MachineID = string.Empty;
            return bExist;
        }
        public DataTable GetMachineByBlockID_CompName(string BlockID, string PunchingName, string connectionString)
        {
            DataTable dtMachine_ID = new DataTable();
            try
            {
                var paras = new SqlParameter[3];
                paras[0] = new SqlParameter("@BlockID", BlockID);
                paras[1] = new SqlParameter("@ProgramType", "Punching");//Lamination
                paras[2] = new SqlParameter("@PunchingName", PunchingName);
                // lấy theo máy mới nhất
                dtMachine_ID = _db.Execute_Table("sp_ShopFloorMain_GetMachineByBlockID_CompName", paras, CommandType.StoredProcedure, connectionString);
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp("sp_ShopFloorMain_GetMachineByBlockID_CompName" + ex);
            }
            return dtMachine_ID;
        }
        public DataTable GetMachineByBlockID_CompName_20211019(string BlockID, string connectionString)
        {
            DataTable dtMachine_ID = new DataTable();
            try
            {
                var paras = new SqlParameter[2];
                paras[0] = new SqlParameter("@BlockID", BlockID);
                paras[1] = new SqlParameter("@ProgramType", "Pasting");//Lamination
                // lấy theo máy mới nhất
                dtMachine_ID = _db.Execute_Table("sp_ShopFloorMain_GetMachineByBlockID_CompName_20211019", paras, CommandType.StoredProcedure, connectionString);
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp("sp_ShopFloorMain_GetMachineByBlockID_CompName_20211019" + ex);
            }
            return dtMachine_ID;
        }
        public bool CheckProductIndi(string ProductID, string Indi, string connectionStringOption)
        {
            bool bMatch = false;
            try
            {
                var paras = new SqlParameter[1];
                paras[0] = new SqlParameter("@ProductID", ProductID);
                DataTable dt = _db.Execute_Table("sp_IDLink_FindIndiFromProductID", paras, CommandType.StoredProcedure, connectionStringOption);
                if (dt?.Rows.Count > 0)
                {
                    string s = dt.Rows[0]["IndicationNumber"].ToString();
                    if(Indi.ToLower().Equals(s.ToLower()))
                    {
                        bMatch = true;
                    }    
                }
            }
            catch(Exception ex)
            {
                ManageLog.WriteErrorApp(ex);
            }
            return bMatch;
        }

        public string GetProductionDateTest(string indiNumber, string connectionStringOption)
        {
            string rs = string.Empty;
            string sql = "SELECT [ProductionDate] from [dbo].[FlexAssy_32_ProductionInfor] WHERE IndicationNumber = '" + indiNumber + "'";
            DataTable dt = _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
            if (dt?.Rows?.Count > 0)
            {
                if (dt.Rows.Count == 1)
                {
                    rs = dt.Rows[0]["ProductionDate"] == null ? DateTime.MinValue.ToString() : dt.Rows[0]["ProductionDate"].ToString();
                }
            }
            return rs;
        }

        public DataTable GetIctDateTest(string indiNumber, string connectionStringOption)
        {
            string sql = " with vtv as ";
            sql += " ( ";
            sql += " select [IctDateTime], [IctMachineID], [Shift], ROW_NUMBER() over (PARTITION by[IctMachineID] order by[IctDateTime] asc) as raw ";
            sql += " from[FlexAssy_32_IctInfor] where IndicationNumber = '" + indiNumber + "' ";
            sql += " ) ";
            sql += " select[IctDateTime], [IctMachineID], [Shift] from vtv where raw = 1 ";
            DataTable dt = _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
            return dt;
        }

        public string GetProductionLine(string indiNumber, string connectionStringOption)
        {
            string rs = string.Empty;
            string sql = "SELECT [ProductionLine] from [dbo].[FlexAssy_32_ProductionInfor] WHERE IndicationNumber = '" + indiNumber + "'";
            DataTable dt = _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
            if (dt?.Rows?.Count > 0)
            {
                if (dt.Rows.Count == 1)
                {
                    rs = dt.Rows[0]["ProductionLine"].ToString();
                }
            }
            return rs;
        }

        public string GetProductionLineNew(string sqlWhereLine, string indiNumber, string connectionStringOption)
        {
            string rs = string.Empty;
            string sql = "SELECT [ProductionLine] from [dbo].[FlexAssy_32_SPAInfor] WHERE IndicationNumber = '" + indiNumber + "' ORDER BY [ProductionLine] ASC";
            DataTable dt = _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
            if (dt?.Rows?.Count > 0)
            {
                dt = dt.Select(sqlWhereLine).CopyToDataTable();

                if (dt?.Rows.Count > 0)
                {
                    foreach (DataRow row in dt.Rows)
                    {
                        if (!rs.Contains(row["ProductionLine"] + "-"))
                        {
                            rs += row["ProductionLine"] + "-";
                        }
                    }
                    rs = rs.Substring(0, rs.Length - 1);
                }
            }
            return rs;
        }

        public DataTable GetCheckTypeLineDefineByItemNameCheckType(string ItemName, string CheckType, string connectionStringOption)
        {
            var paras = new SqlParameter[2];
            paras[0] = new SqlParameter("@ItemName", ItemName);
            paras[1] = new SqlParameter("@CheckType", CheckType);
            DataTable dt = _db.Execute_Table("sms_CheckTypeLineDefine_ByItemNameCheckType", paras, CommandType.StoredProcedure, connectionStringOption);
            return dt;
        }

        public List<string> GetProductionLineAndDateTest(string indiNumber, string connectionStringOption, out DateTime dateTest, string connectionStringOptionShopFloor, string BlockID)
        {
            List<string> rs = new List<string>();
            dateTest = DateTime.MinValue;
            string sql = "SELECT [ProductionLine] FROM [dbo].[FlexAssy_32_SPAInfor]  where IndicationNumber = '" + indiNumber + "' group by [ProductionLine]";
            DataTable dt = _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
            if (dt?.Rows?.Count > 0)
            {
                foreach (DataRow row in dt.Rows)
                {
                    rs.Add(row["ProductionLine"].ToString());
                }
            }

            var paras = new SqlParameter[2];
            paras[0] = new SqlParameter("@BlockID", BlockID);
            paras[1] = new SqlParameter("@ProgramType", "SPA");
            var tb = _db.Execute_Table("sp_ShopFloorMain_GetDataByBlockID_Type", paras, CommandType.StoredProcedure, connectionStringOptionShopFloor);
            if (tb != null && tb.Rows.Count > 0)
            {
                if (tb.Rows[tb.Rows.Count - 1]["CreatedDate"] != null)
                {
                    dateTest = Convert.ToDateTime(tb.Rows[tb.Rows.Count - 1]["CreatedDate"] + "");
                }
            }
            return rs;
        }

        public List<string> GetProductionLineAndDateTestNew(string indiNumber, string connectionStringOption, out List<DateTime> lstProductionDateTest, string connectionStringOptionShopFloor, string BlockID)
        {
            lstProductionDateTest = new List<DateTime>();
            List<string> rs = new List<string>();
            string sql = "SELECT [ProductionLine] FROM [dbo].[FlexAssy_32_SPAInfor]  where IndicationNumber = '" + indiNumber + "' group by [ProductionLine]";
            DataTable dt = _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
            if (dt?.Rows?.Count > 0)
            {
                foreach (DataRow row in dt.Rows)
                {
                    rs.Add(row["ProductionLine"].ToString());
                }
            }

            var paras = new SqlParameter[2];
            paras[0] = new SqlParameter("@BlockID", BlockID);
            paras[1] = new SqlParameter("@ProgramType", "SPA");
            var tb = _db.Execute_Table("sp_ShopFloorMain_GetDataByBlockID_Type", paras, CommandType.StoredProcedure, connectionStringOptionShopFloor);
            if (tb != null && tb.Rows.Count > 0)
            {
                foreach(DataRow row in tb.Rows)
                {
                    lstProductionDateTest.Add(Convert.ToDateTime(tb.Rows[tb.Rows.Count - 1]["CreatedDate"] + ""));
                }
            }
            return rs;
        }

        public bool CheckExistIndiNumber(string indiNumber, string testID, string connectionStringOption)
        {
            bool bMatch = false;
            try
            {
                string _testID = string.Empty;
                string testName = string.Empty;
                string sql = "SELECT [IndicationNumber],[TestID],[TestName],[DetailID] FROM [dbo].[tbl_IPQC_OQC_ORT_OBA] WHERE IndicationNumber = '" + indiNumber + "'";
                DataTable dt = _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
                if (dt != null)
                {
                    if (dt.Rows.Count > 0)
                    {
                        for (int i = 0; i < dt.Rows.Count; i++)
                        {
                            _testID = dt.Rows[i]["TestID"] + "";
                            testName = dt.Rows[i]["TestName"] + "";
                            //Nếu testType là ORT chỉ được đăng ký 1 lần cho mỗi mục trong ORT và ko được đăng ký với test khác.
                            if (testName.Contains("ORT"))
                            {
                                if (_testID == testID)
                                {
                                    if (testID == AccountRole.c_ort_ThermalCycling || testID == AccountRole.c_ort_HeatsoakAndRecovery || testID == AccountRole.c_ort_ThermalShock || testID == AccountRole.c_ort_ThermalCyclingAndFlexBending || testID == AccountRole.c_ort_FlexBending || testID == AccountRole.c_ort_HeatsoakAndFlexBending)
                                    {
                                        bMatch = true;
                                        break;
                                    }
                                }

                            }

                            //Nếu FAI/SPC chỉ được đăng ký 1 lần vói chính nó.
                            // eidt by tungud 2020/10/23 sửa Fai/SPC giống outline punching
                            //if (testName.Contains("FAI/SPC"))
                            //{
                            //    if (testID == AccountRole.c_oqc_measurement)
                            //    {
                            //        bMatch = true;
                            //        break;
                            //    }
                            //}
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp(ex);
            }
            return bMatch;
        }
        //IPQC
        public List<tbl_ORT_IPQC_OQC_ItemName_Setting> GetItemNameByItemName(string Factory, string ItemName ,string connectionStringOption,string Role)
        {
            List<tbl_ORT_IPQC_OQC_ItemName_Setting> testNames = new List<tbl_ORT_IPQC_OQC_ItemName_Setting>();
            var paras = new SqlParameter[3];
            paras[0] = new SqlParameter("@Factory", Factory);
            paras[1] = new SqlParameter("@ItemName", ItemName);
            paras[2] = new SqlParameter("@Role", Role);
            var tb = _db.Execute_Table("sp_tbl_ORT_IPQC_OQC_ItemName_Setting_GetItemNameByFactoryAndItemName", paras, CommandType.StoredProcedure,connectionStringOption);
            if (tb != null)
            {
                int count = tb.Rows.Count;
                for (int i = 0; i < count; i++)
                {
                    var testName = new tbl_ORT_IPQC_OQC_ItemName_Setting
                    {
                        TestName = tb.Rows[i]["TestName"] + "",
                        TestType = tb.Rows[i]["TestType"] + "",
                    };
                    testNames.Add(testName);
                }
                return testNames;
            }

            return null;
        }

        public List<tbl_ORT_IPQC_OQC_ItemName_Setting> GetTestNameByItemName_Check(string Factory, string ItemName, string connectionStringOption)
        {
            List<tbl_ORT_IPQC_OQC_ItemName_Setting> testNames = new List<tbl_ORT_IPQC_OQC_ItemName_Setting>();
            var paras = new SqlParameter[2];
            paras[0] = new SqlParameter("@Factory", Factory);
            paras[1] = new SqlParameter("@ItemName", ItemName);
            var tb = _db.Execute_Table("sp_tbl_ORT_IPQC_OQC_ItemName_Setting_GetTestNameByFactoryAndItemName", paras, CommandType.StoredProcedure, connectionStringOption);
            if (tb != null)
            {
                int count = tb.Rows.Count;
                for (int i = 0; i < count; i++)
                {
                    var testName = new tbl_ORT_IPQC_OQC_ItemName_Setting
                    {
                        TestName = tb.Rows[i]["TestName"] + "",
                        TestType = tb.Rows[i]["TestType"] + "",
                    };
                    testNames.Add(testName);
                }
                return testNames;
            }

            return null;
        }
        public tbl_ORT_IPQC_OQC_ItemName_Setting GetFormulaByItemNameAndTestName(string Factory, string ItemName,string TestName, string connectionStringOption)
        {
            string Formula = string.Empty;
            var paras = new SqlParameter[3];
            paras[0] = new SqlParameter("@Factory", Factory);
            paras[1] = new SqlParameter("@ItemName", ItemName);
            paras[2] = new SqlParameter("@TestName", TestName);
            var tb = _db.Execute_Table("sp_tbl_ORT_IPQC_OQC_ItemName_Setting_GetItemNameByItemNameAndTestName", paras, CommandType.StoredProcedure, connectionStringOption);
            if (tb.Rows.Count > 0)
            {
                var settings = new tbl_ORT_IPQC_OQC_ItemName_Setting
                {
                    Formula = tb.Rows[0]["Formula"] + "",
                    TestType = tb.Rows[0]["TestType"] + "",
                    TestID = tb.Rows[0]["TestID"] + ""
                };
                return settings;
            }

            return null;
        }
        public List<tbl_ORT_IPQC_OQC_ItemName_Setting> GetListItemNameSettingByItemNameAndFormula(string Factory, string Formula, string ItemName, string andWhere, string connectionStringOption)
        {
            var paras = new SqlParameter[4];
            paras[0] = new SqlParameter("@Factory", Factory);
            paras[1] = new SqlParameter("@ItemName", ItemName);
            paras[2] = new SqlParameter("@Formula", Formula);
            paras[3] = new SqlParameter("@AndWhere", andWhere);
            List<tbl_ORT_IPQC_OQC_ItemName_Setting> lstResult = new List<tbl_ORT_IPQC_OQC_ItemName_Setting>();
            var tb = _db.Execute_Table("sms_tbl_ORT_IPQC_OQC_ItemName_Setting_GetItemNameByItemNameAndFormula", paras, CommandType.StoredProcedure, connectionStringOption);
            if (tb.Rows.Count > 0)
            {
                int count = tb.Rows.Count;
                for (int i = 0; i < count; i++)
                {
                    var settings = new tbl_ORT_IPQC_OQC_ItemName_Setting
                    {
                        TestName = tb.Rows[i]["TestName"] + "",
                        TestID = tb.Rows[i]["TestID"] + ""
                    };
                    lstResult.Add(settings);
                }                
            }

            return lstResult;
        }
        public List<IPQC_TestName> GetTestNameByTestType(string TestType,string ItemName, string connectionStringOption, string Role)
        {
            List<IPQC_TestName> testNames = new List<IPQC_TestName>();
            var paras = new SqlParameter[3];
            paras[0] = new SqlParameter("@TestType", TestType);
            paras[1] = new SqlParameter("@ItemName", ItemName);
            paras[2] = new SqlParameter("@Role", Role);
            var tb = _db.Execute_Table("sp_IPQC_TestName_GetTestNameByTestType", paras, CommandType.StoredProcedure, connectionStringOption);
            if (tb != null)
            {
                int count = tb.Rows.Count;
                for (int i = 0; i < count; i++)
                {
                    var testName = new IPQC_TestName
                    {
                        TestName = tb.Rows[i]["TestName"] + "",
                        TestType = tb.Rows[i]["TestType"] + ""
                    };
                    testNames.Add(testName);
                }
                return testNames;
            }

            return null;
        }
        public List<IPQC_TestName> GetAllTestNameByTestType(string TestType, string connectionStringOption)
        {
            List<IPQC_TestName> testNames = new List<IPQC_TestName>();
            var paras = new SqlParameter[1];
            paras[0] = new SqlParameter("@TestType", TestType);
            var tb = _db.Execute_Table("sms_IPQC_TestName_GetTestNameByTestType", paras, CommandType.StoredProcedure, connectionStringOption);
            if (tb != null)
            {
                int count = tb.Rows.Count;
                for (int i = 0; i < count; i++)
                {
                    var testName = new IPQC_TestName
                    {
                        ID = int.Parse(tb.Rows[i]["ID"] + ""),
                        TestName = tb.Rows[i]["TestName"] + "",
                        TestType = tb.Rows[i]["TestType"] + ""
                    };
                    testNames.Add(testName);
                }
                return testNames;
            }

            return null;
        }
        public List<IPQC_TestName> GetAllTestType(string ItemName , string connectionStringOption)
        {
            List<IPQC_TestName> testTypes = new List<IPQC_TestName>();
            string sql = "SELECT [TestType] FROM [dbo].[tbl_ORT_IPQC_OQC_ItemName_Setting] where ItemName = " + ItemName + "";
            var tb = _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
            if (tb.Rows.Count > 0)
            {
                int count = tb.Rows.Count;
                for (int i = 0; i < count; i++)
                {
                    var testType = new IPQC_TestName
                    {
                        TestType = tb.Rows[i]["TestType"] + "",
                    };
                    testTypes.Add(testType);
                }
                return testTypes;
            }
            return null;
        }
        public string CheckExistIndiNumber_IPQC(string indiNumber, string connectionStringOption)
        {
            string rs = string.Empty;
            string sql = "SELECT * FROM [dbo].[tbl_IPQC_OQC_ORT_OBA] WHERE IndicationNumber = '" + indiNumber + "'";
            DataTable dt = _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
            if (dt != null)
            {
                rs = dt.Rows[0]["ItemName"].ToString();
                return rs;
            }
            return null;
        }
        public List<tbl_IPQC_OQC_ORT_OBA> CheckExistIndiNumber_IPQC_Detail(string indiNumber, string connectionStringOption)
        {
            List<tbl_IPQC_OQC_ORT_OBA> countTests = new List<tbl_IPQC_OQC_ORT_OBA>();
            string sql = "SELECT * FROM [dbo].[Tbl_IPQC_DeTail] WHERE IndiNumber = '" + indiNumber + "' order by CreatedDate Desc" ;
            var tb = _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
            if (tb.Rows.Count > 0)
            {
                int count = tb.Rows.Count;
                for (int i = 0; i < count; i++)
                {
                    var countTest = new tbl_IPQC_OQC_ORT_OBA
                    {
                        ItemName = tb.Rows[i]["ItemName"] + "",
                    };
                    countTests.Add(countTest);
                }
                return countTests;
            }
            return null;
        }
        public List<tbl_ORT_IPQC_OQC_ItemName_Setting> GetIPQC_SettingsByItemName(string ItemName, string connectionStringOption)
        {
            List<tbl_ORT_IPQC_OQC_ItemName_Setting> testTypes = new List<tbl_ORT_IPQC_OQC_ItemName_Setting>();
            try
            {
                string sql = "SELECT * FROM [dbo].[tbl_ORT_IPQC_OQC_ItemName_Setting] WHERE ItemName = '" + ItemName + "'";
                var tb = _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
                if (tb.Rows.Count > 0)
                {
                    int count = tb.Rows.Count;
                    for (int i = 0; i < count; i++)
                    {
                        var testType = new tbl_ORT_IPQC_OQC_ItemName_Setting
                        {
                            ItemName = tb.Rows[i]["ItemName"] + "",
                            TestID = tb.Rows[i]["TestID"] + "",
                            TestName = tb.Rows[i]["TestName"] + "",
                            CreatedDate = Convert.ToDateTime(tb.Rows[i]["CreatedDate"].ToString()) 
                        };
                        testTypes.Add(testType);
                    }
                    return testTypes;
                }
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp(ex);
            }
            return null;
        }

        public List<tbl_IPQC_OQC_ORT_OBA> CheckExistIndiNumber_IPQC1(string indiNumber, string connectionStringOption)
        {
            List<tbl_IPQC_OQC_ORT_OBA> testTypes = new List<tbl_IPQC_OQC_ORT_OBA>();
            try
            {
                string sql = "SELECT * FROM [dbo].[tbl_IPQC_OQC_ORT_OBA] WHERE IndicationNumber = '" + indiNumber + "'";
                var tb = _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
                if (tb.Rows.Count > 0)
                {
                    int count = tb.Rows.Count;
                    for (int i = 0; i < count; i++)
                    {
                        var testType = new tbl_IPQC_OQC_ORT_OBA
                        {
                            IndicationNumber = tb.Rows[i]["IndicationNumber"] + "",
                            ItemName = tb.Rows[i]["ItemName"] + "",
                            TestID = int.Parse(tb.Rows[i]["TestID"].ToString()),
                            TestName = tb.Rows[i]["TestName"] + "",
                            Result = tb.Rows[i]["Result"] + "",
                            InvoiceID = tb.Rows[i]["InvoiceID"] + ""
                        };
                        testTypes.Add(testType);
                    }
                    return testTypes;
                }
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp(ex);
            }
            return null;
        }
        public List<IPQC_TestName> GetTestNameByItemName(string ItemName,string connectionStringOption)
        {
            List<IPQC_TestName> testNames = new List<IPQC_TestName>();
            string sql = "SELECT * FROM [dbo].[tbl_ORT_IPQC_OQC_ItemName_Setting] WHERE ItemName = '" + ItemName + "'";

            var tb = _db.Execute_Table( sql , null, CommandType.Text, connectionStringOption);
            if (tb?.Rows.Count > 0)
            {
                int count = tb.Rows.Count;
                for (int i = 0; i < count; i++)
                {
                    var testName = new IPQC_TestName
                    {
                        TestName = tb.Rows[i]["TestName"].ToString()
                    };
                    testNames.Add(testName);
                }
                return testNames;
            }
            return null;
        }
        public int Insert_IPQC_CheckResult(string ItemName,string IndiNumber , string InvoiceID,string Result,string CreatedUser, string connectionString)
        {
            int nRes = -1;
            //@ItemCode, @ItemName,@LineID, @OperatorID , @TestID,  @TestName,@DetailID
            //
            try
            {
                var paras = new SqlParameter[6];
                paras[0] = new SqlParameter("@ItemName", ItemName);
                paras[1] = new SqlParameter("@IndiNumber", IndiNumber);
                paras[2] = new SqlParameter("@InvoiceID", InvoiceID);
                paras[3] = new SqlParameter("@Result", Result);
                paras[4] = new SqlParameter("@CreatedUser", CreatedUser ?? (object)DBNull.Value);
                paras[5] = new SqlParameter("@Rs", SqlDbType.Int) { Direction = ParameterDirection.Output };
                nRes = _db.Execute_Modify("sp_Tbl_IPQC_CheckResult_Insert_12112021", paras, CommandType.StoredProcedure, connectionString);
                if(nRes > 0)
                {
                    return (int)paras[5].Value;
                }
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp(ex.Message);
            }
            return nRes;
        }

        public int Insert_IPQC_Detail(string ItemName, string IndiNumber,int TestID, string InvoiceID, string Result, string CreatedUser, string connectionString)
        {
            int nRes = -1;
            //@ItemCode, @ItemName,@LineID, @OperatorID , @TestID,  @TestName,@DetailID
            //
            try
            {
                var paras = new SqlParameter[7];
                paras[0] = new SqlParameter("@ItemName", ItemName);
                paras[1] = new SqlParameter("@IndiNumber", IndiNumber);
                paras[2] = new SqlParameter("@TestID", TestID);
                paras[3] = new SqlParameter("@InvoiceID", InvoiceID);
                paras[4] = new SqlParameter("@Result", Result);
                paras[5] = new SqlParameter("@CreatedUser", CreatedUser);
                paras[6] = new SqlParameter("@Rs", SqlDbType.Int) { Direction = ParameterDirection.Output };
                nRes = _db.Execute_Modify("sp_Tbl_IPQC_DeTail_Insert", paras, CommandType.StoredProcedure, connectionString);
                if (nRes > 0)
                {
                    return (int)paras[6].Value;
                }
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp(ex.Message);
            }
            return nRes;
        }
        public List<Tbl_IPQC_CheckResult_History> SearchByIndi(string indiNumber, string connectionStringOption)
        {
            List<Tbl_IPQC_CheckResult_History> checkResultHistories = new List<Tbl_IPQC_CheckResult_History>();
            try
            {
                string sql = "SELECT * FROM [dbo].[Tbl_IPQC_CheckResult_History] WHERE IndiNumber = '" + indiNumber + "' order by CheckDate desc";
                var tb = _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
                if (tb.Rows.Count > 0)
                {
                    int count = tb.Rows.Count;
                    for (int i = 0; i < count; i++)
                    {
                        var checkResultHistory = new Tbl_IPQC_CheckResult_History
                        {
                            IndiNumber = tb.Rows[i]["IndiNumber"] + "",
                            ItemName = tb.Rows[i]["ItemName"] + "",
                            CreatedDate = Convert.ToDateTime(tb.Rows[i]["CreatedDate"].ToString()),
                            CreatedUser = tb.Rows[i]["CreatedUser"] + "",
                            Result = tb.Rows[i]["Result"] + "",
                            InvoiceID = tb.Rows[i]["InvoiceID"] + ""
                        };
                        checkResultHistories.Add(checkResultHistory);
                    }
                    return checkResultHistories;
                }
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp(ex);
            }
            return null;
        }

        public List<IPQC_TestName> GetIdAndTestName( string connectionStringOption)
        {
            List<IPQC_TestName> iPQC_TestNames = new List<IPQC_TestName>();
            try
            {
                string sql = "SELECT * FROM [dbo].[IPQC_TestName]";
                var tb = _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
                if (tb.Rows.Count > 0)
                {
                    int count = tb.Rows.Count;
                    for (int i = 0; i < count; i++)
                    {
                        var iPQC_TestName = new IPQC_TestName
                        {
                            ID = int.Parse(tb.Rows[i]["IndiNumber"].ToString()),
                            TestName = tb.Rows[i]["TestName"] + "",
                            TestType = tb.Rows[i]["TestType"] + ""
                        };
                        iPQC_TestNames.Add(iPQC_TestName);
                    }
                    return iPQC_TestNames;
                }
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp(ex);
            }
            return null;
        }
    }
}
