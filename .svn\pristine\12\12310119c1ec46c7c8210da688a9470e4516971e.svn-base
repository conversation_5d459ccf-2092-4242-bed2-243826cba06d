﻿using System;
using System.Data;
using System.Threading.Tasks;

namespace Trace_AbilitySystem.Libs.ITrace_02_FlexAssy_Services
{
    public interface IFlexAssy_33_OQC_Service
    {
        Task FlexAssy_33_OQC_ProcessData(string factory, DateTime createdDateSearch, string connectionStringOption);

        int FlexAssy_33_OQCDefine_Insert(string productID, DateTime dateTime, string itemName, string indicationNumber, string operatorID, string connectionStringOption);
        Task SPC_03_OQCFai_ProcessReadLog(string factory, string pathSaveLog, string pathSynMedium);

    }
}