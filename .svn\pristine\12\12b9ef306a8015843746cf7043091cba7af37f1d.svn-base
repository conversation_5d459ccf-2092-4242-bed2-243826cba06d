﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using Trace_AbilitySystem.Libs.Dataconnect;
using Trace_AbilitySystem.Libs.DTOClass;
using Trace_AbilitySystem.Libs.ITrace_01_BareFlex_Services;

namespace Trace_AbilitySystem.Libs.Trace_01_BareFlex_Services
{
    public class BareFlex_BlockIDService : IBareFlex_BlockIDService
    {
        private readonly DbExecute _db;
        private readonly string connectionStringOption = "F1";

        public BareFlex_BlockIDService()
        {
            _db = new SqlExecute();
        }

        public async Task<int> BareFlex_BlockID_ProcessData(DateTime createdDateSearch, int minutes, string connectionStringOption)
        {
            string stage = "BareFlex_BlockID";
            string typeBarcode = "BlockID";
            string factory = "F1";

            try
            {
                DateTime dateNow = DateTime.Now;
                DateTime timeLast = createdDateSearch;
                int recordNeedSyn = 0;
                int recordSyned = 0;

                DataTable dt = Singleton_01_BareFlex.ISource_BareFlex_BoardRegistration_Service.BlockID_GetDataCreatedDate(timeLast, minutes, connectionStringOption);
                if (dt == null)
                    return -1;

                if (dt.Rows.Count > 0)
                {
                    List<OperatorOfStage> operatorOfStages = new List<OperatorOfStage>();

                    //number of records to sync
                    recordNeedSyn = dt.Rows.Count;
                    for (int i = 0; i < recordNeedSyn; i++)
                    {
                        string blockID = dt.Rows[i]["BlockIdSerial"].ToString().Trim();
                        DateTime dateTime = Convert.ToDateTime(dt.Rows[i]["CreatedDate"]);
                        string workOrderFront = dt.Rows[i]["IndicationNumber"].ToString().Trim();
                        string workOrderBack = dt.Rows[i]["IndicationNumberChange"].ToString().Trim();
                        string operatorID = dt.Rows[i]["CreatedByOperatorId"].ToString().Trim();

                        // insert data
                        int rs = BareFlex_BlockID_Insert(blockID, dateTime, workOrderFront, workOrderBack, operatorID);
                        if (rs == -9)
                            return -1;

                        OperatorOfStage operatorOfStage = Singleton_03_Common.ICommon_CommonService.GetOperatorOfStage(operatorID, null, stage, factory, typeBarcode, operatorOfStages);
                        if (operatorOfStage != null)
                            operatorOfStages.Add(operatorOfStage);

                        // update the time that the record is synchronized
                        timeLast = Convert.ToDateTime(dt.Rows[i]["CreatedDate"]);

                        // count the number of synchronized records
                        recordSyned++;
                    }

                    Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, stage, timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff"));
                }
                else
                {
                    timeLast = timeLast.AddMinutes(minutes) > dateNow ? timeLast : timeLast.AddMinutes(minutes);

                    // Update ValueSearch
                    Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, stage, timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff"));
                }

                ManageLog.WriteProcess(true, null, recordNeedSyn, recordSyned);
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp(stage + "\n" + ex.Message);
                return -1;
            }

            await Task.Delay(500);

            return 1;
        }

        public int BareFlex_BlockID_Insert(string blockID, DateTime dateTime, string workOrderFront, string workOrderBack, string operatorID)
        {
            var paras = new SqlParameter[5];
            paras[0] = new SqlParameter("@BlockID", blockID);
            paras[1] = new SqlParameter("@DateTime", dateTime);
            paras[2] = new SqlParameter("@WorkOrderFront", workOrderFront ?? (object)DBNull.Value);
            paras[3] = new SqlParameter("@WorkOrderBack", workOrderBack ?? (object)DBNull.Value);
            paras[4] = new SqlParameter("@OperatorID", operatorID ?? (object)DBNull.Value);

            return _db.Execute_Modify("sp_sms_BareFlex_BlockID_Insert", paras, CommandType.StoredProcedure, connectionStringOption);
        }

        public DataTable BareFlex_BlockID_GetByBlockID(string blockID)
        {
            var paras = new SqlParameter[1];
            paras[0] = new SqlParameter("@BlockID", blockID ?? (object)DBNull.Value);

            return _db.Execute_Table("sp_sms_BareFlex_BlockID_GetByBlockID", paras, CommandType.StoredProcedure, connectionStringOption);
        }

        public DataTable BareFlex_BlockID_GetByWorkOrder(string workOrder)
        {
            var paras = new SqlParameter[1];
            paras[0] = new SqlParameter("@WorkOrder", workOrder ?? (object)DBNull.Value);

            return _db.Execute_Table("sp_sms_BareFlex_BlockID_GetByWorkOrder", paras, CommandType.StoredProcedure, connectionStringOption);
        }
    }
}