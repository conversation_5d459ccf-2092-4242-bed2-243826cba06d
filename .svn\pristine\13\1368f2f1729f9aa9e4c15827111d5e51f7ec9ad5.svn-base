﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text.RegularExpressions;
using System.Windows.Forms;
using Trace_AbilitySystem.Libs;
using Trace_AbilitySystem.Libs.ITrace_03_Common_Services;
using Trace_AbilitySystem.Libs.Trace_03_Common_Services;
using Trace_AbilitySystem.Libs.Trace_05_Local_Services;
using Trace_AbilitySystem.Libs.ITrace_05_Local_Services;
using Trace_AblilitySystem_Flexssy_Interface_IPQC_Define;

namespace Trace_AblilitySystem_Flexssy_Interface_OQC
{
    public partial class frmAccount : Form
    {
        private int _id;
        private string _fullName;
        private string _operatorID;
        private string _passWord;
        private string _roleAdmin;
        private string _role;
        private string _connectionStringMain;
        private string _connectionStringTraceCommon;
        public object Trace_05_Local_Services { get; private set; }

        /// <summary>
        ///     FrmAccount
        /// </summary>
        public frmAccount()
        {
            InitializeComponent();
            InitGridDataTable();
            _connectionStringMain = Singleton.IniFile.GetDbConnectionString();
            _connectionStringTraceCommon = Singleton.IniFile.GetDbConnectionStringTrace_Common();

            if (MySharedInfo.CurrentUser != null)
            {
                lbAdminID.Text = MySharedInfo.CurrentUser.OperatorID.ToString();
                lbAdminName.Text = MySharedInfo.CurrentUser.FullName;
                _roleAdmin = MySharedInfo.CurrentUser.Role;
            }
        }

        /// <summary>
        ///     InitGridDataTable
        /// </summary>
        private void InitGridDataTable()
        {
            var columnHeaders = new List<string> { "Order", "Id", "FullName", "OperatorID", "CreatedDate", "Role" };
            dtGridView.ColumnCount = columnHeaders.Count;
            dtGridView.ColumnHeadersVisible = true;

            // Thiết lập Style cho Header
            DataGridViewCellStyle columnHeaderStyle = new DataGridViewCellStyle
            {
                BackColor = Color.PaleGreen,
                Font = new Font("Tahoma", 9, FontStyle.Bold),
                Alignment = DataGridViewContentAlignment.MiddleCenter
            };

            dtGridView.ColumnHeadersDefaultCellStyle = columnHeaderStyle;
            dtGridView.ColumnHeadersDefaultCellStyle.BackColor = Color.PaleGreen;
            dtGridView.EnableHeadersVisualStyles = false;
            dtGridView.RowHeadersVisible = false;
            dtGridView.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            dtGridView.ColumnHeadersHeight = 35;

            for (int i = 0; i < columnHeaders.Count; i++)
            {
                dtGridView.Columns[i].HeaderText = columnHeaders[i];
                dtGridView.Columns[i].SortMode = DataGridViewColumnSortMode.NotSortable;
            }
            dtGridView.Columns[0].Width = 50;
            dtGridView.Columns[2].Width = 180;
            dtGridView.Columns[3].Width = 180;
            dtGridView.Columns[4].Width = 180;
            dtGridView.Columns[5].Width = 750;
            //dtGridView.Columns[6].Width = 230;
            dtGridView.Columns[0].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dtGridView.Columns[1].Visible = false;
            //  dtGridView.EditMode = DataGridViewEditMode.EditOnEnter;
            // Remove row default
            dtGridView.AllowUserToAddRows = false;
            dtGridView.AutoGenerateColumns = false;

            // Auto resize height multi line cell
            dtGridView.DefaultCellStyle.WrapMode = DataGridViewTriState.True;
            //  dtGridView.AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.AllCells;
            //   dtGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.AllCells;

            dtGridView.RowsDefaultCellStyle.SelectionBackColor = Color.DeepSkyBlue;
        }

        /// <summary>
        ///     BindingData
        /// </summary>
        private void BindingData()
        {
            ResetTxt();
            var data = SingletonLocal.AccountService.GetAll("", _connectionStringMain);
            if (data?.Count > 0)
            {
                lbOK.Text = data.Count + @" thành viên/" + data.Count + @" user";
                dtGridView.ClearSelection();
                dtGridView.Rows.Clear();
                dtGridView.Refresh();
                int addItem = 0;
                foreach (var item in data)
                {
                    addItem++;
                    var row = new DataGridViewRow
                    {
                        Height = 50,
                        DefaultCellStyle =
                    {
                        Font = new Font("Tahoma", 9, FontStyle.Regular),
                        BackColor =  Color.Azure
                    },
                        ReadOnly = true
                    };

                    row.Cells.Add(new DataGridViewTextBoxCell { Value = addItem });
                    row.Cells.Add(new DataGridViewTextBoxCell { Value = item.Id });
                    row.Cells.Add(new DataGridViewTextBoxCell { Value = item.FullName });
                    row.Cells.Add(new DataGridViewTextBoxCell { Value = item.OperatorID });
                    row.Cells.Add(new DataGridViewTextBoxCell { Value = $"{item.CreatedDate:HH:mm dd/MM/yyyy}" });
                    row.Cells.Add(new DataGridViewTextBoxCell { Value = ShowStr(item.Role) });
                    dtGridView.Rows.Add(row);
                }
            }
        }
        private string AddString(string input, string Role)
        {
            string res;
            if (!string.IsNullOrEmpty(input)) 
                res = input+ ", " + Role;
            else res = Role;
            return res;
        }

        //Control GetControlByName(string Name)
        //{
        //    foreach (Control c in this.Controls)
        //        if (c.Name == Name)
        //            return c;

        //    return null;
        //}
        private string ShowStr(string role)
        {
            string str = string.Empty;
            if (string.IsNullOrEmpty(role)) return str;

            string[] roles_arr = role.Split(',');
            foreach(var itemIn in roles_arr)
            {
                string item = itemIn.Trim();
                if (item == AccountRole.c_sAdmin)
                {
                    str += AccountRole.c_sAdminName;
                }
                if (item == AccountRole.c_sEngineer)
                {
                    str = AddString(str, AccountRole.c_sEngineerName);
                }
                if (item == AccountRole.c_ort_ThermalCycling)
                {
                    str = AddString(str, AccountRole.c_ort_ThermalCyclingName);
                }
                if (item == AccountRole.c_ort_HeatsoakAndRecovery)
                {
                    str = AddString(str, AccountRole.c_ort_HeatsoakAndRecoveryName);
                }
                if (item == AccountRole.c_ort_ThermalShock)
                {
                    str = AddString(str, AccountRole.c_ort_ThermalShockName);
                }
                if (item == AccountRole.c_ort_ThermalCyclingAndFlexBending)
                {
                    str = AddString(str, AccountRole.c_ort_ThermalCyclingAndFlexBendingName);
                }
                if (item == AccountRole.c_ort_FlexBending)
                {
                    str = AddString(str, AccountRole.c_ort_FlexBendingName);
                }
                if (item == AccountRole.c_ort_HeatsoakAndFlexBending)
                {
                    str = AddString(str, AccountRole.c_ort_HeatsoakAndFlexBendingName);
                }
                if (item == AccountRole.c_ipqc_B2BXsection)
                {
                    str = AddString(str, AccountRole.c_ipqc_B2BXsectionName);
                }
                if (item == AccountRole.c_ipqc_B2BPeelingTest)
                {
                    str = AddString(str, AccountRole.c_ipqc_B2BPeelingTestName);
                }
                if (item == AccountRole.c_ipqc_B2BXray)
                {
                    str = AddString(str, AccountRole.c_ipqc_B2BXrayName);
                }
                if (item == AccountRole.c_ipqc_OutlinePunchingDimension)
                {
                    str = AddString(str, AccountRole.c_ipqc_OutlinePunchingDimensionName);
                }
                if (item == AccountRole.c_oqc_measurement)
                {
                    str = AddString(str, AccountRole.c_oqc_measurementName);
                }
                if (item == AccountRole.c_oqc_B2BPeeling)
                {
                    str = AddString(str, AccountRole.c_oqc_B2BPeelingName);
                }
                if (item == AccountRole.c_oqc_B2BPulling)
                {
                    str = AddString(str, AccountRole.c_oqc_B2BPullingName);
                }
                if (item == AccountRole.c_oqc_B2BShearing)
                {
                    str = AddString(str, AccountRole.c_oqc_B2BShearingName);
                }
                if (item == AccountRole.c_oqc_ACFFlatness)
                {
                    str = AddString(str, AccountRole.c_oqc_ACFFlatnessName);
                }
                if (item == AccountRole.c_oqc_ACFRoughness)
                {
                    str = AddString(str, AccountRole.c_oqc_ACFRoughnessName);
                }
                if (item == AccountRole.c_oqc_ACFBonding)
                {
                    str = AddString(str, AccountRole.c_oqc_ACFBondingName);
                }
                if (item == AccountRole.c_oqc_HotOil)
                {
                    str = AddString(str, AccountRole.c_oqc_HotOilName);
                }
                if (item == AccountRole.c_oqc_Heating4WTest)
                {
                    str = AddString(str, AccountRole.c_oqc_Heating4WTestName);
                }
                if (item == AccountRole.c_oqc_XSectionGNDB2Bpin)
                {
                    str = AddString(str, AccountRole.c_oqc_XSectionGNDB2BpinName);
                }
                if (item == AccountRole.ID_OQCVisualInspection)
                {
                    str = AddString(str, AccountRole.OQCVisualInspection);
                }
                if (item == AccountRole.ID_OQCICTtest)
                {
                    str = AddString(str, AccountRole.OQCICTtest);
                }
                if (item == AccountRole.ID_OQCFunctiontest)
                {
                    str = AddString(str, AccountRole.OQCFunctiontest);
                }
                if (item == AccountRole.ID_OQCPSA_TSAlinerpeelofftest)
                {
                    str = AddString(str, AccountRole.OQCPSA_TSAlinerpeelofftest);
                }
                if (item == AccountRole.ID_OQCPSA_TSApeelingforce)
                {
                    str = AddString(str, AccountRole.OQCPSA_TSApeelingforce);
                }
                if (item == AccountRole.ID_OQCLinerpeelingforce)
                {
                    str = AddString(str, AccountRole.OQCLinerpeelingforce);
                }
                if (item == AccountRole.ID_OQC2Dbarcodegrade)
                {
                    str = AddString(str, AccountRole.OQC2Dbarcodegrade);
                }
                if (item == AccountRole.ID_OQCPlasmaACF_GND)
                {
                    str = AddString(str, AccountRole.OQCPlasmaACF_GND);
                }
                if (item == AccountRole.ID_OQCHotbarPre_solderheight)
                {
                    str = AddString(str, AccountRole.OQCHotbarPre_solderheight);
                }
                if (item == AccountRole.ID_ORTI_Oconnectmating_unmatingtest)
                {
                    str = AddString(str, AccountRole.ORTI_Oconnectmating_unmatingtest);
                }
                if (item == AccountRole.ID_ORTThermalstress)
                {
                    str = AddString(str, AccountRole.ORTThermalstress);
                }
                if (item == AccountRole.ID_ORTHotbarlooptest)
                {
                    str = AddString(str, AccountRole.ORTHotbarlooptest);
                }
                if (item == AccountRole.ID_OBAVisualInspection)
                {
                    str = AddString(str, AccountRole.OBAVisualInspection);
                }
                if (item == AccountRole.ID_OBAICTtest)
                {
                    str = AddString(str, AccountRole.OBAICTtest);
                }
                if (item == AccountRole.ID_OBAFunctiontest)
                {
                    str = AddString(str, AccountRole.OBAFunctiontest);
                }
                if (item == AccountRole.ID_OBA2Dbarcodegrade)
                {
                    str = AddString(str, AccountRole.OBA2Dbarcodegrade);
                }
                if (item == AccountRole.ID_IPQCB2BPullingTest)
                {
                    str = AddString(str, AccountRole.IPQCB2BPullingTest);
                }
                if (item == AccountRole.ID_IPQC_Pasting_Dimension)
                {
                    str = AddString(str, AccountRole.IPQC_Pasting_Dimension);
                }
                if (item == AccountRole.ID_OQC_B2B_Matting_Un_Matting_Test)
                {
                    str = AddString(str, AccountRole.OQC_B2B_Matting_Un_Matting_Test);
                }
                if (item == AccountRole.ID_IPQC_PSA_TSA_peeling_force)
                {
                    str = AddString(str, AccountRole.IPQC_PSA_TSA_peeling_force);
                }
                if (item == AccountRole.ID_IPQC_Liner_peeling_force)
                {
                    str = AddString(str, AccountRole.IPQC_Liner_peeling_force);
                }
                if (item == AccountRole.ID_IPQC_SEM)
                {
                    str = AddString(str, AccountRole.IPQC_SEM);
                }
                if (item == AccountRole.ID_IPQC_PSA_TSA_Liner_peel_off_test)
                {
                    str = AddString(str, AccountRole.IPQC_PSA_TSA_Liner_peel_off_test);
                }
                if (item == AccountRole.ID_Ipqc_Plasma_Wca_On_Acf)
                {
                    str = AddString(str, AccountRole.Ipqc_Plasma_Wca_On_Acf);
                }
                if (item == AccountRole.ID_Ipqc_Plasma_Wca_On_Gnd)
                {
                    str = AddString(str, AccountRole.Ipqc_Plasma_Wca_On_Gnd);
                }
                if (item == AccountRole.ID_Coc_Report)
                {
                    str = AddString(str, AccountRole.Coc_Report);
                }
                if (item == AccountRole.ID_Iqc_B2b_Visual_Inspection)
                {
                    str = AddString(str, AccountRole.Iqc_B2b_Visual_Inspection);
                }
                if (item == AccountRole.ID_Iqc_B2b_Dimension)
                {
                    str = AddString(str, AccountRole.Iqc_B2b_Dimension);
                }
                if (item == AccountRole.ID_Iqc_B2b_Coplanarity)
                {
                    str = AddString(str, AccountRole.Iqc_B2b_Coplanarity);
                }
                if (item == AccountRole.ID_Iqc_B2b_Plating_thickness)
                {
                    str = AddString(str, AccountRole.Iqc_B2b_Plating_thickness);
                }
                if (item == AccountRole.ID_Iqc_B2b_Solderability)
                {
                    str = AddString(str, AccountRole.Iqc_B2b_Solderability);
                }
                if (item == AccountRole.ID_IQC_Bare_FPC_Visual_inspection)
                {
                    str = AddString(str, AccountRole.IQC_Bare_FPC_Visual_inspection);
                }
                if (item == AccountRole.ID_IQC_Bare_FPC_Dimension_Included_BDvsT)
                {
                    str = AddString(str, AccountRole.IQC_Bare_FPC_Dimension_Included_BDvsT);
                }
                if (item == AccountRole.ID_IQC_Bare_FPC_Shrinkage_Panel_TDMD)
                {
                    str = AddString(str, AccountRole.IQC_Bare_FPC_Shrinkage_Panel_TDMD);
                }
                if (item == AccountRole.ID_IQC_Bare_FPC_Solderability)
                {
                    str = AddString(str, AccountRole.IQC_Bare_FPC_Solderability);
                }
                if (item == AccountRole.ID_IQC_Bare_FPC_Ni_Au_thickness)
                {
                    str = AddString(str, AccountRole.IQC_Bare_FPC_Ni_Au_thickness);
                }
                if (item == AccountRole.ID_IQC_Bare_FPC_ACF_Pad_Roughness)
                {
                    str = AddString(str, AccountRole.IQC_Bare_FPC_ACF_Pad_Roughness);
                }
                if (item == AccountRole.ID_IQC_Bare_FPC_WCA_PSAandSolderingP)
                {
                    str = AddString(str, AccountRole.IQC_Bare_FPC_WCA_PSAandSolderingP);
                }
                if (item == AccountRole.ID_IQC_Barcode_on_FPC_Pad_or_SUS_Grade_verification)
                {
                    str = AddString(str, AccountRole.IQC_Barcode_on_FPC_Pad_or_SUS_Grade_verification);
                }
                if (item == AccountRole.ID_IQC_PSA_Liner_Visual_inspection)
                {
                    str = AddString(str, AccountRole.IQC_PSA_Liner_Visual_inspection);
                }
                if (item == AccountRole.ID_IQC_PSA_Liner_Dimension_Thickness)
                {
                    str = AddString(str, AccountRole.IQC_PSA_Liner_Dimension_Thickness);
                }
                if (item == AccountRole.ID_IQC_PSA_Liner_Pre_Peel_test)
                {
                    str = AddString(str, AccountRole.IQC_PSA_Liner_Pre_Peel_test);
                }
                if (item == AccountRole.ID_IQC_PSA_Coupon_peel_force_test)
                {
                    str = AddString(str, AccountRole.IQC_PSA_Coupon_peel_force_test);
                }
                if (item == AccountRole.ID_IQC_Liner_Coupon_peel_force_test)
                {
                    str = AddString(str, AccountRole.IQC_Liner_Coupon_peel_force_test);
                }
                if (item == AccountRole.ID_IQC_PSA_Die_cut_Peel_force_test)
                {
                    str = AddString(str, AccountRole.IQC_PSA_Die_cut_Peel_force_test);
                }
                if (item == AccountRole.ID_IQC_Liner_Die_cut_peel_force_test)
                {
                    str = AddString(str, AccountRole.IQC_Liner_Die_cut_peel_force_test);
                }
                if (item == AccountRole.ID_IQC_Packing_Tray_Cosmetic)
                {
                    str = AddString(str, AccountRole.IQC_Packing_Tray_Cosmetic);
                }
                if (item == AccountRole.ID_IQC_Packing_Tray_ESD)
                {
                    str = AddString(str, AccountRole.IQC_Packing_Tray_ESD);
                }
                if (item == AccountRole.ID_IQC_Packing_Tray_Dimension)
                {
                    str = AddString(str, AccountRole.IQC_Packing_Tray_Dimension);
                }
                if (item == AccountRole.ID_IQC_Solder_paste_Cosmetic)
                {
                    str = AddString(str, AccountRole.IQC_Solder_paste_Cosmetic);
                }
                if (item == AccountRole.ID_IQC_Solder_paste_Particle_Dimension)
                {
                    str = AddString(str, AccountRole.IQC_Solder_paste_Particle_Dimension);
                }
                if (item == AccountRole.ID_IQC_Solder_paste_Viscosity)
                {
                    str = AddString(str, AccountRole.IQC_Solder_paste_Viscosity);
                }
                if (item == AccountRole.ID_ORT_Liner_peeling_force)
                {
                    str = AddString(str, AccountRole.ORT_Liner_peeling_force);
                }
                if (item == AccountRole.ID_ORT_PSA_TSA_peeling_force)
                {
                    str = AddString(str, AccountRole.ORT_PSA_TSA_peeling_force);
                }
                if (item == AccountRole.ID_OQC_Liner_peeling_force)
                {
                    str = AddString(str, AccountRole.OQC_Liner_peeling_force);
                }
                if (item == AccountRole.ID_OQC_PSA_TSA_peeling_force)
                {
                    str = AddString(str, AccountRole.OQC_PSA_TSA_peeling_force);
                }
                if (item == AccountRole.ID_Iqc_B2b_Un_matting_force)
                {
                    str = AddString(str, AccountRole.Iqc_B2b_Un_matting_force);
                }
                if (item == AccountRole.ID_Iqc_Chip_Visual_Inspection)
                {
                    str = AddString(str, AccountRole.Iqc_Chip_Visual_Inspection);
                }
                if (item == AccountRole.ID_Iqc_Chip_Dimension)
                {
                    str = AddString(str, AccountRole.Iqc_Chip_Dimension);
                }
                if (item == AccountRole.ID_Iqc_Chip_Solderability)
                {
                    str = AddString(str, AccountRole.Iqc_Chip_Solderability);
                }
                if (item == AccountRole.ID_Iqc_Chip_Function_LCR_value)
                {
                    str = AddString(str, AccountRole.Iqc_Chip_Function_LCR_value);
                }
                if (item == AccountRole.ID_Iqc_Shieldcan_Visual_Inspection)
                {
                    str = AddString(str, AccountRole.Iqc_Shieldcan_Visual_Inspection);
                }
                if (item == AccountRole.ID_Iqc_Shieldcan_Dimension)
                {
                    str = AddString(str, AccountRole.Iqc_Shieldcan_Dimension);
                }
                if (item == AccountRole.ID_Iqc_Shieldcan_Solderability)
                {
                    str = AddString(str, AccountRole.Iqc_Shieldcan_Solderability);
                }
                if (item == AccountRole.ID_Iqc_Shieldcan_Plating_Thickness)
                {
                    str = AddString(str, AccountRole.Iqc_Shieldcan_Plating_Thickness);
                }
                if (item == AccountRole.ID_Iqc_Shieldcan_Coplanarity_warpage)
                {
                    str = AddString(str, AccountRole.Iqc_Shieldcan_Coplanarity_warpage);
                }
                if (item == AccountRole.ID_Iqc_PSA_Liner_FTIR)
                {
                    str = AddString(str, AccountRole.Iqc_PSA_Liner_FTIR);
                }
                if (item == AccountRole.ID_Iqc_PSA_Liner_Surface_resistance)
                {
                    str = AddString(str, AccountRole.Iqc_PSA_Liner_Surface_resistance);
                }
                if (item == AccountRole.ID_Iqc_PSA_Liner_Surface_static_electricity)
                {
                    str = AddString(str, AccountRole.Iqc_PSA_Liner_Surface_static_electricity);
                }
                if (item == AccountRole.ID_Iqc_PSA_Liner_Environment)
                {
                    str = AddString(str, AccountRole.Iqc_PSA_Liner_Environment);
                }
                if (item == AccountRole.ID_Iqc_Si_coating_weight)
                {
                    str = AddString(str, AccountRole.Iqc_Si_coating_weight);
                }
                if (item == AccountRole.ID_ORT_Check_sample)
                {
                    str = AddString(str, AccountRole.ORT_Check_sample);
                }
            }
            return str;
        }

        /// <summary>
        ///     FrmAccount_Load
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void FrmAccount_Load(object sender, EventArgs e)
        {
            txtOperatorID.Focus();
            lbVersion.Text = OQC_Version.c_sVersion;
            lbDate.Text = OQC_Version.c_sVersionDate;
            lblLoginTime.Text = @"Đăng nhập/Login: " + $"{DateTime.Now:HH:mm dd/MM/yyyy}";
            WindowState = FormWindowState.Maximized;
            BindingData();
        }

        /// <summary>
        ///     ResetTxt
        /// </summary>
        private void ResetTxt()
        {
            txtFullName.Text = txtPassword.Text = txtOperatorID.Text = "";
            btnDelete.Visible = false;
            btnSave.Text = @"Thêm/Add";
            _id = 0;
            _fullName = "";
            _operatorID = "";
            _passWord = "";
            ckAdmin.Checked = ckAccount.Checked = ck_ORT_ThermalCycling.Checked = ck_shipment_check.Checked = ck_ORT_FlexBending.Checked = ck_ORT_HeatsoakAndFlexBending.Checked
                    = ck_ORT_HeatsoakAndRecovery.Checked = ck_ORT_ThermalCyclingAndFlexBending.Checked = ck_ORT_ThermalShock.Checked = ck_IPQC_B2BXsection.Checked
                    = ck_IPQC_B2BPeelingTest.Checked = ck_IPQC_Pasting_Dimension.Checked = ck_OQC_B2B_Matting_Un_Matting_Test.Checked =
                    ck_IPQC_OutlinePunchingDimension.Checked = ck_IPQC_B2BXray.Checked = ck_OQC_ACFRoughness.Checked = ck_IPQC_Liner_Peeling.Checked = ck_IPQC_PSA_TSA_peeling.Checked =
                    ck_OQC_XSectionGNDB2Bpin.Checked = ck_OQC_B2BPeelingTest.Checked = ck_OQC_FAImeasurement.Checked = ck_IPQC_SEM.Checked = ck_IPQC_PSA_TSA_Liner_peel_off_test.Checked = 
                    ck_OQC_ACFFlatness.Checked = ck_OQC_B2BShearingTest.Checked = ck_OQC_B2BPullingTest.Checked = ck_OQC_Heat4W.Checked = ck_OQC_ACFBonding.Checked =
                    ck_OQC_HotOil.Checked = ck_OQC_Visual_Inspection.Checked = ck_OQC_ICT_test.Checked = ck_OQC_Function_test.Checked = ck_OQC_PSA_TSA_liner.Checked =
                    ck_OQC_PSA_TSA_peeling.Checked = ck_OQC_Liner_Peeling.Checked = ck_OQC_2D_barcode.Checked = ck_OQC_Plasma.Checked = ck_OQC_Hot_bar_pre_solder.Checked =
                    ck_ORT_IO_Connect.Checked = ck_ORT_Thermal_stress.Checked = ck_ORT_Hotbar_loop_test.Checked = ck_OBA_Visual_inspection.Checked = ck_OBA_ICT_test.Checked =
                    ck_Ipqc_Plasma_Wca_On_Acf.Checked = ck_Ipqc_Plasma_Wca_On_Gnd.Checked = ck_Coc_Report.Checked = ck_ORT_PSA_TSA_peeling_force.Checked =
                    ck_Iqc_B2b_Visual_Inspection.Checked = ck_Iqc_B2b_Dimension.Checked = ck_Iqc_B2b_Coplanarity.Checked = ck_Iqc_B2b_Plating_thickness.Checked =
                    ck_Iqc_B2b_Solderability.Checked = ck_IQC_Bare_FPC_Visual_inspection.Checked = ck_IQC_Bare_FPC_Dimension_Included_BDvsT.Checked = 
                    ck_IQC_Bare_FPC_Shrinkage_Panel_TDMD.Checked = ck_IQC_Bare_FPC_Solderability.Checked = ck_IQC_Bare_FPC_Ni_Au_thickness.Checked = ck_IQC_Bare_FPC_ACF_Pad_Roughness.Checked =
                    ck_IQC_Bare_FPC_WCA_PSAandSolderingP.Checked =ck_IQC_Barcode_on_FPC_Pad_or_SUS_Grade_verification.Checked = ck_IQC_PSA_Liner_Visual_inspection.Checked = 
                    ck_IQC_PSA_Liner_Dimension_Thickness.Checked = ck_IQC_PSA_Liner_Pre_Peel_test.Checked = ck_IQC_PSA_Coupon_peel_force_test.Checked = 
                    ck_IQC_Liner_Coupon_peel_force_test.Checked = ck_IQC_PSA_Die_cut_Peel_force_test.Checked = ck_IQC_Liner_Die_cut_peel_force_test.Checked =
                    ck_IQC_Packing_Tray_Cosmetic.Checked = ck_IQC_Packing_Tray_ESD.Checked = ck_IQC_Packing_Tray_Dimension.Checked = ck_IQC_Solder_paste_Cosmetic.Checked =
                    ck_IQC_Solder_paste_Particle_Dimension.Checked = ck_IQC_Solder_paste_Viscosity.Checked = ck_ORT_Liner_peeling_force.Checked = 
                    ck_OBA_Function_Test.Checked = ck_IPQC_All.Checked = ck_OBA_All.Checked = ck_OQC_All.Checked = ck_ORT_All.Checked = ck_OBA_2DBarcode_Grade.Checked =
                    ck_OQC_Liner_peeling_force.Checked = ck_OQC_PSA_TSA_peeling_force.Checked = ckb_IPQCB2BPullingTest.Checked =
                    ck_Iqc_B2b_Un_matting_force.Checked = ck_Iqc_Chip_Visual_Inspection.Checked = ck_Iqc_Chip_Dimension.Checked =
                    ck_Iqc_Chip_Solderability.Checked = ck_Iqc_Chip_Function_LCR_value.Checked = ck_Iqc_Shieldcan_Visual_Inspection.Checked =
                    ck_Iqc_Shieldcan_Dimension.Checked = ck_Iqc_Shieldcan_Solderability.Checked = ck_Iqc_Shieldcan_Plating_Thickness.Checked =
                    ck_Iqc_Shieldcan_Coplanarity_warpage.Checked = ck_Iqc_PSA_Liner_FTIR.Checked = ck_Iqc_PSA_Liner_Surface_resistance.Checked =
                    ck_Iqc_PSA_Liner_Surface_static_electricity.Checked = ck_Iqc_PSA_Liner_Environment.Checked = ck_Iqc_Si_coating_weight.Checked = 
                    ck_ORT_Check_sample.Checked = false;
            //ck1.Checked  = ck3.Checked =  false;
        }
        /// <summary>
        ///     btnCancel_Click
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnCancel_Click(object sender, EventArgs e)
        {
            ResetTxt();
        }

        /// <summary>
        ///     btnDelete_Click
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (_id != 0)
            {
                var result = MessageBox.Show(@"Bạn muốn xóa Account này?.", @"Xóa Account", MessageBoxButtons.YesNo);
                if (result == DialogResult.Yes)
                {
                    if (MySharedInfo.CurrentUser.Id != _id)
                    {
                        SingletonLocal.AccountService.Delete(_id, _connectionStringMain);
                        BindingData();
                    }
                    else
                    {
                        MessageBox.Show(@"Bạn không thể xóa tài khoản của mình/You can not delete your account");
                    }
                }
            }
        }

        /// <summary>
        ///     dtGridOK_CellClick
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void dtGridOK_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            GetRowDetail(dtGridView.CurrentRow);
        }

        /// <summary>
        ///     GetRowDetail
        /// </summary>
        /// <param name="row"></param>
        private void GetRowDetail(DataGridViewRow row)
        {
            try
            {
                //Get info of row
                string getId = row.Cells[1].Value.ToString().Trim();
                int id = Convert.ToInt32(getId);
                var account = SingletonLocal.AccountService.GetById(id, _connectionStringMain);
                if (account != null)
                {
                    _id = account.Id;
                    _fullName = account.FullName;
                    _operatorID = account.OperatorID;
                    _passWord = account.PassWord;
                    _role = account.Role;

                    txtFullName.Text = _fullName;
                    txtOperatorID.Text = _operatorID;
                    txtPassword.Text = _passWord;
                    ckAdmin.Checked = ckAccount.Checked = ck_ORT_ThermalCycling.Checked = ck_shipment_check.Checked = ck_ORT_FlexBending.Checked = ck_ORT_HeatsoakAndFlexBending.Checked
                        = ck_ORT_HeatsoakAndRecovery.Checked = ck_ORT_ThermalCyclingAndFlexBending.Checked = ck_ORT_ThermalShock.Checked = ck_IPQC_B2BXsection.Checked
                        = ck_IPQC_B2BPeelingTest.Checked = ck_IPQC_Pasting_Dimension.Checked = ck_OQC_B2B_Matting_Un_Matting_Test.Checked =
                        ck_IPQC_OutlinePunchingDimension.Checked = ck_IPQC_B2BXray.Checked = ck_OQC_ACFRoughness.Checked = ck_IPQC_SEM.Checked = ck_IPQC_Liner_Peeling.Checked =
                        ck_OQC_XSectionGNDB2Bpin.Checked = ck_OQC_B2BPeelingTest.Checked = ck_OQC_FAImeasurement.Checked = ck_IPQC_PSA_TSA_peeling.Checked = ck_IPQC_PSA_TSA_Liner_peel_off_test.Checked = 
                        ck_OQC_ACFFlatness.Checked = ck_OQC_B2BShearingTest.Checked = ck_OQC_B2BPullingTest.Checked = ck_OQC_Heat4W.Checked = ck_OQC_ACFBonding.Checked =
                        ck_OQC_HotOil.Checked = ck_OQC_Visual_Inspection.Checked = ck_OQC_ICT_test.Checked = ck_OQC_Function_test.Checked = ck_OQC_PSA_TSA_liner.Checked =
                        ck_OQC_PSA_TSA_peeling.Checked = ck_OQC_Liner_Peeling.Checked = ck_OQC_2D_barcode.Checked = ck_OQC_Plasma.Checked = ck_OQC_Hot_bar_pre_solder.Checked =
                        ck_ORT_IO_Connect.Checked = ck_ORT_Thermal_stress.Checked = ck_ORT_Hotbar_loop_test.Checked = ck_OBA_Visual_inspection.Checked = ck_OBA_ICT_test.Checked =
                        ck_OBA_Function_Test.Checked = ck_IPQC_All.Checked = ck_OBA_All.Checked = ck_OQC_All.Checked = ck_ORT_All.Checked = ckb_IPQCB2BPullingTest.Checked =
                        ck_Ipqc_Plasma_Wca_On_Acf.Checked = ck_Ipqc_Plasma_Wca_On_Gnd.Checked = ck_Coc_Report.Checked = ck_ORT_PSA_TSA_peeling_force.Checked =
                        ck_Iqc_B2b_Visual_Inspection.Checked = ck_Iqc_B2b_Dimension.Checked = ck_Iqc_B2b_Coplanarity.Checked = ck_Iqc_B2b_Plating_thickness.Checked =
                        ck_Iqc_B2b_Solderability.Checked = ck_IQC_Bare_FPC_Visual_inspection.Checked = ck_IQC_Bare_FPC_Dimension_Included_BDvsT.Checked =
                        ck_IQC_Bare_FPC_Shrinkage_Panel_TDMD.Checked = ck_IQC_Bare_FPC_Solderability.Checked = ck_IQC_Bare_FPC_Ni_Au_thickness.Checked = ck_IQC_Bare_FPC_ACF_Pad_Roughness.Checked =
                        ck_IQC_Bare_FPC_WCA_PSAandSolderingP.Checked = ck_IQC_Barcode_on_FPC_Pad_or_SUS_Grade_verification.Checked = ck_IQC_PSA_Liner_Visual_inspection.Checked =
                        ck_IQC_PSA_Liner_Dimension_Thickness.Checked = ck_IQC_PSA_Liner_Pre_Peel_test.Checked = ck_IQC_PSA_Coupon_peel_force_test.Checked =
                        ck_IQC_Liner_Coupon_peel_force_test.Checked = ck_IQC_PSA_Die_cut_Peel_force_test.Checked = ck_IQC_Liner_Die_cut_peel_force_test.Checked =
                        ck_IQC_Packing_Tray_Cosmetic.Checked = ck_IQC_Packing_Tray_ESD.Checked = ck_IQC_Packing_Tray_Dimension.Checked = ck_IQC_Solder_paste_Cosmetic.Checked =
                        ck_IQC_Solder_paste_Particle_Dimension.Checked = ck_IQC_Solder_paste_Viscosity.Checked = ck_ORT_Liner_peeling_force.Checked =
                        ck_OBA_2DBarcode_Grade.Checked = ck_OQC_Liner_peeling_force.Checked = ck_OQC_PSA_TSA_peeling_force.Checked =
                        ck_Iqc_B2b_Un_matting_force.Checked = ck_Iqc_Chip_Visual_Inspection.Checked = ck_Iqc_Chip_Dimension.Checked =
                        ck_Iqc_Chip_Solderability.Checked = ck_Iqc_Chip_Function_LCR_value.Checked = ck_Iqc_Shieldcan_Visual_Inspection.Checked =
                        ck_Iqc_Shieldcan_Dimension.Checked = ck_Iqc_Shieldcan_Solderability.Checked = ck_Iqc_Shieldcan_Plating_Thickness.Checked =
                        ck_Iqc_Shieldcan_Coplanarity_warpage.Checked = ck_Iqc_PSA_Liner_FTIR.Checked = ck_Iqc_PSA_Liner_Surface_resistance.Checked =
                        ck_Iqc_PSA_Liner_Surface_static_electricity.Checked = ck_Iqc_PSA_Liner_Environment.Checked = ck_Iqc_Si_coating_weight.Checked =
                        ck_ORT_Check_sample.Checked = false;
                    var rs = _role.Split(',');

                    //foreach (var item in rs)
                    //{
                    //    if (!string.IsNullOrEmpty(item))
                    //    {
                    //        if( int.Parse(item) ==int.Parse(AccountRole.c_sAdmin))
                    //        {
                    //            ckAdmin.Checked = true; 
                    //        }
                    //        if (int.Parse(item) == int.Parse(AccountRole.c_sEngineer))
                    //        {
                    //            ckAccount.Checked = true;
                    //        }
                    //        if (int.Parse(item) == int.Parse(AccountRole.c_ort_ThermalCycling))
                    //        {
                    //            ck_ORT_ThermalCycling.Checked = true;
                    //        }
                    //        if (int.Parse(item) == int.Parse(AccountRole.c_ort_HeatsoakAndRecovery))
                    //        {
                    //            ck_ORT_HeatsoakAndRecovery.Checked = true;
                    //        }
                    //        if (int.Parse(item) == int.Parse(AccountRole.c_ort_ThermalShock))
                    //        {
                    //            ck_ORT_ThermalShock.Checked = true;
                    //        }
                    //        if (int.Parse(item) == int.Parse(AccountRole.c_ort_ThermalCyclingAndFlexBending))
                    //        {
                    //            ck_ORT_ThermalCyclingAndFlexBending.Checked = true;
                    //        }
                    //        if (int.Parse(item) == int.Parse(AccountRole.c_ort_FlexBending))
                    //        {
                    //           ck_ORT_FlexBending.Checked = true;
                    //        }
                    //        if (int.Parse(item) == int.Parse(AccountRole.c_ort_HeatsoakAndFlexBending))
                    //        {
                    //            ck_ORT_HeatsoakAndFlexBending.Checked = true;
                    //        }
                    //        if (int.Parse(item) == int.Parse(AccountRole.c_ipqc_B2BXsection))
                    //        {
                    //            ck_IPQC_B2BXsection.Checked = true;
                    //        }
                    //        if (int.Parse(item) == int.Parse(AccountRole.c_ipqc_B2BPeelingTest))
                    //        {
                    //            ck_IPQC_B2BPeelingTest.Checked = true;
                    //        }
                    //        if (int.Parse(item) == int.Parse(AccountRole.c_ipqc_B2BXray))
                    //        {
                    //            ck_IPQC_B2BXray.Checked = true;
                    //        }
                    //        if (int.Parse(item) == int.Parse(AccountRole.c_ipqc_OutlinePunchingDimension))
                    //        {
                    //            ck_IPQC_OutlinePunchingDimension.Checked = true;
                    //        }
                    //        if (int.Parse(item) == int.Parse(AccountRole.c_ipqc_PlasmaACF))
                    //        {
                    //            ck_IPQC_PlasmaACFWettingAngle.Checked = true;
                    //        }
                    //        if (int.Parse(item) == int.Parse(AccountRole.c_oqc_measurement))
                    //        {
                    //            ck_OQC_FAImeasurement.Checked = true;
                    //        }
                    //        if (int.Parse(item) == int.Parse(AccountRole.c_oqc_B2BPeeling))
                    //        {
                    //            ck_OQC_B2BPeelingTest.Checked = true;
                    //        }
                    //        if (int.Parse(item) == int.Parse(AccountRole.c_oqc_B2BPulling))
                    //        {
                    //            ck_OQC_B2BPullingTest.Checked = true;
                    //        }
                    //        if (int.Parse(item) == int.Parse(AccountRole.c_oqc_B2BShearing))
                    //        {
                    //            ck_OQC_B2BShearingTest.Checked = true;
                    //        }
                    //        if (int.Parse(item) == int.Parse(AccountRole.c_oqc_ACFFlatness))
                    //        {
                    //            ck_OQC_ACFFlatness.Checked = true;
                    //        }
                    //        if (int.Parse(item) == int.Parse(AccountRole.c_oqc_ACFRoughness))
                    //        {
                    //            ck_OQC_ACFRoughness.Checked = true;
                    //        }
                    //        if (int.Parse(item) == int.Parse(AccountRole.c_oqc_ACFBonding))
                    //        {
                    //            ck_OQC_ACFBonding.Checked = true;
                    //        }
                    //        if (int.Parse(item) == int.Parse(AccountRole.c_oqc_HotOil))
                    //        {
                    //            ck_OQC_HotOil.Checked = true;
                    //        }
                    //        if (int.Parse(item) == int.Parse(AccountRole.c_oqc_Heating4WTest))
                    //        {
                    //            ck_OQC_Heat4W.Checked = true;
                    //        }
                    //        if (int.Parse(item) == int.Parse(AccountRole.c_oqc_XSectionGNDB2Bpin))
                    //        {
                    //            ck_OQC_XSectionGNDB2Bpin.Checked = true; 
                    //        }
                    //    }

                    //}
                    foreach (var item in rs)
                    {
                        if (!string.IsNullOrEmpty(item))
                        {
                            if (int.Parse(item) == 99)
                            {
                                ckAdmin.Checked = true;
                            }
                            if (int.Parse(item) == 98)
                            {
                                ckAccount.Checked = true;
                            }
                            if (int.Parse(item) == 97)
                            {
                                ck_shipment_check.Checked = true;
                            }
                            if (int.Parse(item) == 1)
                            {
                                ck_IPQC_B2BXsection.Checked = true;
                            }
                            if (int.Parse(item) == 2)
                            {
                                ck_IPQC_B2BPeelingTest.Checked = true;
                            }
                            if (int.Parse(item) == 3)
                            {
                                ck_IPQC_B2BXray.Checked = true;
                            }
                            if (int.Parse(item) == 4)
                            {
                                ck_IPQC_OutlinePunchingDimension.Checked = true;
                            }
                            if (int.Parse(item) == 6)
                            {
                                ck_OQC_FAImeasurement.Checked = true;
                            }
                            if (int.Parse(item) == 7)
                            {
                                ck_OQC_B2BPeelingTest.Checked = true;
                            }
                            if (int.Parse(item) == 8)
                            {
                                ck_OQC_ACFFlatness.Checked = true;
                            }
                            if (int.Parse(item) == 9)
                            {
                                ck_OQC_ACFRoughness.Checked = true;
                            }
                            if (int.Parse(item) == 10)
                            {
                                ck_OQC_XSectionGNDB2Bpin.Checked = true;
                            }
                            if (int.Parse(item) == 11)
                            {
                                ck_ORT_ThermalCycling.Checked = true;
                            }
                            if (int.Parse(item) == 12)
                            {
                                ck_ORT_HeatsoakAndRecovery.Checked = true;
                            }
                            if (int.Parse(item) == 13)
                            {
                                ck_ORT_ThermalShock.Checked = true;
                            }
                            if (int.Parse(item) == 14)
                            {
                                ck_ORT_ThermalCyclingAndFlexBending.Checked = true;
                            }
                            if (int.Parse(item) == 15)
                            {
                                ck_ORT_FlexBending.Checked = true;
                            }
                            if (int.Parse(item) == 16)
                            {
                                ck_ORT_HeatsoakAndFlexBending.Checked = true;
                            }
                            if (int.Parse(item) == 17)
                            {
                                ck_OQC_B2BPullingTest.Checked = true;
                            }
                            if (int.Parse(item) == 18)
                            {
                                ck_OQC_B2BShearingTest.Checked = true;
                            }
                            if (int.Parse(item) == 19)
                            {
                                ck_OQC_ACFBonding.Checked = true;
                            }
                            if (int.Parse(item) == 20)
                            {
                                ck_OQC_HotOil.Checked = true;
                            }
                            if (int.Parse(item) == 21)
                            {
                                ck_OQC_Heat4W.Checked = true;
                            }
                            if (int.Parse(item) == 22)
                            {
                                ck_OQC_Visual_Inspection.Checked = true;
                            }
                            if (int.Parse(item) == 23)
                            {
                                ck_OQC_ICT_test.Checked = true;
                            }
                            if (int.Parse(item) == 24)
                            {
                                ck_OQC_Function_test.Checked = true;
                            }
                            if (int.Parse(item) == 25)
                            {
                                ck_OQC_PSA_TSA_liner.Checked = true;
                            }
                            if (int.Parse(item) == 26)
                            {
                                ck_OQC_PSA_TSA_peeling.Checked = true;
                            }
                            if (int.Parse(item) == 27)
                            {
                                ck_OQC_Liner_Peeling.Checked = true;
                            }
                            if (int.Parse(item) == 28)
                            {
                                ck_OQC_2D_barcode.Checked = true;
                            }
                            if (int.Parse(item) == 29)
                            {
                                ck_OQC_Plasma.Checked = true;
                            }
                            if (int.Parse(item) == 30)
                            {
                                ck_OQC_Hot_bar_pre_solder.Checked = true;
                            }
                            if (int.Parse(item) == 31)
                            {
                                ck_ORT_IO_Connect.Checked = true;
                            }
                            if (int.Parse(item) == 32)
                            {
                                ck_ORT_Thermal_stress.Checked = true;
                            }
                            if (int.Parse(item) == 33)
                            {
                                ck_ORT_Hotbar_loop_test.Checked = true;
                            }
                            if (int.Parse(item) == 34)
                            {
                                ck_OBA_Visual_inspection.Checked = true;
                            }
                            if (int.Parse(item) == 35)
                            {
                                ck_OBA_ICT_test.Checked = true;
                            }
                            if (int.Parse(item) == 36)
                            {
                                ck_OBA_Function_Test.Checked = true;
                            }
                            if (int.Parse(item) == 37)
                            {
                                ck_OBA_2DBarcode_Grade.Checked = true;
                            }
                            if (int.Parse(item) == 38)
                            {
                                ckb_IPQCB2BPullingTest.Checked = true;
                            }
                            if (int.Parse(item) == 39)
                            {
                                ck_IPQC_Pasting_Dimension.Checked = true;
                            }
                            if (int.Parse(item) == 40)
                            {
                                ck_OQC_B2B_Matting_Un_Matting_Test.Checked = true;
                            }
                            if (int.Parse(item) == 41)
                            {
                                ck_IPQC_PSA_TSA_peeling.Checked = true;
                            }
                            if (int.Parse(item) == 42)
                            {
                                ck_IPQC_Liner_Peeling.Checked = true;
                            }
                            if (int.Parse(item) == 43)
                            {
                                ck_IPQC_SEM.Checked = true;
                            }
                            if (int.Parse(item) == 44)
                            {
                                ck_IPQC_PSA_TSA_Liner_peel_off_test.Checked = true;
                            }
                            if (int.Parse(item) == 47)
                            {
                                ck_Ipqc_Plasma_Wca_On_Acf.Checked = true;
                            }
                            if (int.Parse(item) == 48)
                            {
                                ck_Ipqc_Plasma_Wca_On_Gnd.Checked = true;
                            }
                            if (int.Parse(item) == 49)
                            {
                                ck_Coc_Report.Checked = true;
                            }
                            if (int.Parse(item) == 50)
                            {
                                ck_Iqc_B2b_Visual_Inspection.Checked = true;
                            }
                            if (int.Parse(item) == 51)
                            {
                                ck_Iqc_B2b_Dimension.Checked = true;
                            }
                            if (int.Parse(item) == 52)
                            {
                                ck_Iqc_B2b_Coplanarity.Checked = true;
                            }
                            if (int.Parse(item) == 53)
                            {
                                ck_Iqc_B2b_Plating_thickness.Checked = true;
                            }
                            if (int.Parse(item) == 54)
                            {
                                ck_Iqc_B2b_Solderability.Checked = true;
                            }
                            if (int.Parse(item) == 55)
                            {
                                ck_IQC_Bare_FPC_Visual_inspection.Checked = true;
                            }
                            if (int.Parse(item) == 56)
                            {
                                ck_IQC_Bare_FPC_Dimension_Included_BDvsT.Checked = true;
                            }
                            if (int.Parse(item) == 57)
                            {
                                ck_IQC_Bare_FPC_Shrinkage_Panel_TDMD.Checked = true;
                            }
                            if (int.Parse(item) == 58)
                            {
                                ck_IQC_Bare_FPC_Solderability.Checked = true;
                            }
                            if (int.Parse(item) == 59)
                            {
                                ck_IQC_Bare_FPC_Ni_Au_thickness.Checked = true;
                            }
                            if (int.Parse(item) == 60)
                            {
                                ck_IQC_Bare_FPC_ACF_Pad_Roughness.Checked = true;
                            }
                            if (int.Parse(item) == 61)
                            {
                                ck_IQC_Bare_FPC_WCA_PSAandSolderingP.Checked = true;
                            }
                            if (int.Parse(item) == 62)
                            {
                                ck_IQC_Barcode_on_FPC_Pad_or_SUS_Grade_verification.Checked = true;
                            }
                            if (int.Parse(item) == 63)
                            {
                                ck_IQC_PSA_Liner_Visual_inspection.Checked = true;
                            }
                            if (int.Parse(item) == 64)
                            {
                                ck_IQC_PSA_Liner_Dimension_Thickness.Checked = true;
                            }
                            if (int.Parse(item) == 65)
                            {
                                ck_IQC_PSA_Liner_Pre_Peel_test.Checked = true;
                            }
                            if (int.Parse(item) == 66)
                            {
                                ck_IQC_PSA_Coupon_peel_force_test.Checked = true;
                            }
                            if (int.Parse(item) == 67)
                            {
                                ck_IQC_Liner_Coupon_peel_force_test.Checked = true;
                            }
                            if (int.Parse(item) == 68)
                            {
                                ck_IQC_PSA_Die_cut_Peel_force_test.Checked = true;
                            }
                            if (int.Parse(item) == 69)
                            {
                                ck_IQC_Liner_Die_cut_peel_force_test.Checked = true;
                            }
                            if (int.Parse(item) == 70)
                            {
                                ck_IQC_Packing_Tray_Cosmetic.Checked = true;
                            }
                            if (int.Parse(item) == 71)
                            {
                                ck_IQC_Packing_Tray_ESD.Checked = true;
                            }
                            if (int.Parse(item) == 72)
                            {
                                ck_IQC_Packing_Tray_Dimension.Checked = true;
                            }
                            if (int.Parse(item) == 73)
                            {
                                ck_IQC_Solder_paste_Cosmetic.Checked = true;
                            }
                            if (int.Parse(item) == 74)
                            {
                                ck_IQC_Solder_paste_Particle_Dimension.Checked = true;
                            }
                            if (int.Parse(item) == 75)
                            {
                                ck_IQC_Solder_paste_Viscosity.Checked = true;
                            }
                            if (int.Parse(item) == 76)
                            {
                                ck_ORT_Liner_peeling_force.Checked = true;
                            }
                            if (int.Parse(item) == 77)
                            {
                                ck_ORT_PSA_TSA_peeling_force.Checked = true;
                            }
                            if (int.Parse(item) == 78)
                            {
                                ck_OQC_Liner_peeling_force.Checked = true;
                            }
                            if (int.Parse(item) == 79)
                            {
                                ck_OQC_PSA_TSA_peeling_force.Checked = true;
                            }
                            if (int.Parse(item) == 79)
                            {
                                ck_OQC_PSA_TSA_peeling_force.Checked = true;
                            }
                            if (int.Parse(item) == 87) { ck_Iqc_B2b_Un_matting_force.Checked = true; }
                            if (int.Parse(item) == 88) { ck_Iqc_Chip_Visual_Inspection.Checked = true; }
                            if (int.Parse(item) == 89) { ck_Iqc_Chip_Dimension.Checked = true; }
                            if (int.Parse(item) == 90) { ck_Iqc_Chip_Solderability.Checked = true; }
                            if (int.Parse(item) == 91) { ck_Iqc_Chip_Function_LCR_value.Checked = true; }
                            if (int.Parse(item) == 92) { ck_Iqc_Shieldcan_Visual_Inspection.Checked = true; }
                            if (int.Parse(item) == 93) { ck_Iqc_Shieldcan_Dimension.Checked = true; }
                            if (int.Parse(item) == 94) { ck_Iqc_Shieldcan_Solderability.Checked = true; }
                            if (int.Parse(item) == 95) { ck_Iqc_Shieldcan_Plating_Thickness.Checked = true; }
                            if (int.Parse(item) == 96) { ck_Iqc_Shieldcan_Coplanarity_warpage.Checked = true; }
                            if (int.Parse(item) == 97) { ck_Iqc_PSA_Liner_FTIR.Checked = true; }
                            if (int.Parse(item) == 98) { ck_Iqc_PSA_Liner_Surface_resistance.Checked = true; }
                            if (int.Parse(item) == 99) { ck_Iqc_PSA_Liner_Surface_static_electricity.Checked = true; }
                            if (int.Parse(item) == 100) { ck_Iqc_PSA_Liner_Environment.Checked = true; }
                            if (int.Parse(item) == 101) { ck_Iqc_Si_coating_weight.Checked = true; }
                            if (int.Parse(item) == 102) { ck_ORT_Check_sample.Checked = true; }
                        }

                    }
                    btnDelete.Visible = true;
                    btnSave.Text = @"Cập nhật";
                }
            }
            catch (Exception)
            {
            }
        }

        /// <summary>
        ///     txtOperatorID_TextChanged
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void txtOperatorID_TextChanged(object sender, EventArgs e)
        {
            var cursorPosition = txtOperatorID.SelectionStart;
            txtOperatorID.Text = Regex.Replace(txtOperatorID.Text, "[^0-9]", "");
            txtOperatorID.SelectionStart = cursorPosition;
        }

        /// <summary>
        ///     
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void FrmAccount_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (e.CloseReason == CloseReason.UserClosing)
            {
                DialogResult result = MessageBox.Show(@"Trở về chương trình chính?.", @"Trở về", MessageBoxButtons.YesNo);
                if (result == DialogResult.Yes)
                {
                    //Environment.Exit(0);
                    //Application.Exit();
                    Hide();
                }
                else
                {
                    e.Cancel = true;
                }
            }
            else
            {
                e.Cancel = true;
            }
        }

        /// <summary>
        ///     btnLogout_Click
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnLogout_Click(object sender, EventArgs e)
        {
            var result = MessageBox.Show(@"Bạn muốn đăng xuất? / You want to logout?.", @"Đăng xuất / LogOut", MessageBoxButtons.YesNo);
            if (result == DialogResult.Yes)
            {
                Hide();
                //var frmAccountLogin = new frmAccountLogin();
                //frmAccountLogin.Closed += (s, args) => Close();
                //frmAccountLogin.Show();
            }
        }

        /// <summary>
        ///     Admin mở form packing
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void lblChooseProgram_Click(object sender, EventArgs e)
        {
            Hide();
            frmNewMain frm = new frmNewMain();
            DialogResult res = frm.ShowDialog();
            if (res == DialogResult.OK || res == DialogResult.Cancel)
            {
                this.DialogResult = DialogResult.OK;
                this.Close();
            }    
        }

        private void btnSearchOperatorID_Click(object sender, EventArgs e)
        {
            string operatorID = txtOperatorIDSearch.Text.Trim();

            var data = SingletonLocal.AccountService.GetAll(operatorID, _connectionStringMain);
            if (data == null) return;
            lbOK.Text = data.Count + @" thành viên/" + data.Count + @" user";
            dtGridView.ClearSelection();
            dtGridView.Rows.Clear();
            dtGridView.Refresh();
            int addItem = 0;
            foreach (var item in data)
            {
                addItem++;
                var row = new DataGridViewRow
                {
                    Height = 30,
                    DefaultCellStyle =
                    {
                        Font = new Font("Tahoma", 9, FontStyle.Regular),
                        BackColor =  Color.Azure
                    },
                    ReadOnly = true
                };

                row.Cells.Add(new DataGridViewTextBoxCell { Value = addItem });
                row.Cells.Add(new DataGridViewTextBoxCell { Value = item.Id });
                row.Cells.Add(new DataGridViewTextBoxCell { Value = item.FullName });
                row.Cells.Add(new DataGridViewTextBoxCell { Value = item.OperatorID });
                row.Cells.Add(new DataGridViewTextBoxCell { Value = $"{item.CreatedDate:HH:mm dd/MM/yyyy}" });
                row.Cells.Add(new DataGridViewTextBoxCell { Value = ShowStr(item.Role) });

                dtGridView.Rows.Add(row);
                GetRowDetail(dtGridView.CurrentRow);
            }
        }

        private void txtOperatorIDSearch_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                btnSearchOperatorID.PerformClick();
            }
        }

        void AddAccount(string operatorID, string passWord, string fullName, string rolesOQC)
        {
            try
            {
                // Kiểm tra đã có operatorID chưa
                Account account = SingletonLocal.AccountService.GetByOperatorID(operatorID, _connectionStringMain);
                if (account != null)
                {
                    DialogHelper.Info("Tài khoản này đã tồn tại", "Cảnh báo");
                    Cursor.Current = Cursors.Default;
                    return;
                }

                var result = SingletonLocal.AccountService.Insert(operatorID, Common.Md5Endcoding(passWord),
                    fullName, rolesOQC, "12345", "Saomai", "Saomai", _connectionStringMain);

                //var result = SingletonLocal.AccountService.Insert(operatorID, Common.Md5Endcoding(passWord),
                //    fullName, roles, rolesAOI, Convert.ToString(MySharedInfo.CurrentUser.Id),
                //    MySharedInfo.CurrentUser.OperatorID, MySharedInfo.CurrentUser.FullName);
                if (result == 1)
                {
                    DialogHelper.Info("Thêm mới thành công", "Thành công");
                    BindingData();
                }
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp(ex);
            }
        }
        void UpdateAccount(string operatorID, string passWord, string fullName, string roles)
        {
            try
            {
                if (passWord == _passWord || Common.Md5Endcoding(passWord) == _passWord)
                {
                    passWord = _passWord;
                }
                else
                {
                    passWord = Common.Md5Endcoding(passWord);
                }
                var result = SingletonLocal.AccountService.Update(_id, operatorID, passWord, fullName, roles, _connectionStringMain);
                if (result == 1)
                {
                    DialogHelper.Info("Cập nhật thành công", "Thành công");
                    BindingData();
                }
            }
            catch(Exception ex)
            {
                ManageLog.WriteErrorApp(ex);
            }
        }

        /// <summary>
        ///     btnSave_Click
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnSave_Click(object sender, EventArgs e)
        {
            Cursor.Current = Cursors.WaitCursor;
            try
            {
                //string roles = "99";
                //if (_role != null)
                //{
                //    if (!string.IsNullOrEmpty(_role)) roles = _role;
                //}
                string rolesOQC = string.Empty;
                string fullName = txtFullName.Text.Trim();
                string operatorID = txtOperatorID.Text.Trim();
                string passWord = txtPassword.Text.Trim();

                if (fullName.Length == 0 || operatorID.Length == 0 || passWord.Length == 0)
                {
                    DialogHelper.Info("Tên, ID, mật khẩu công nhân không được bỏ trống", "Cảnh báo");
                    Cursor.Current = Cursors.Default;
                    return;
                }
                var lstControlName = SingletonLocal.IPQC_ORT_OBA_Local_Service.GetControlName(_connectionStringTraceCommon);
                //if (ckAdmin.Checked)
                //{
                //    rolesOQC += "99";
                //}
                //if (ckAccount.Checked)
                //{
                //    rolesOQC = AddString(rolesOQC,"98");
                //}
                //if (ck_ORT_ThermalCycling.Checked)
                //{
                //    rolesOQC = AddString(rolesOQC, AccountRole.c_ort_ThermalCycling);
                //}
                //if (ck_ORT_HeatsoakAndRecovery.Checked)
                //{
                //    rolesOQC = AddString(rolesOQC, AccountRole.c_ort_HeatsoakAndRecovery);
                //}
                //if (ck_ORT_ThermalShock.Checked)
                //{
                //    rolesOQC = AddString(rolesOQC, AccountRole.c_ort_ThermalShock);
                //}
                //if (ck_ORT_ThermalCyclingAndFlexBending.Checked)
                //{
                //    rolesOQC = AddString(rolesOQC, AccountRole.c_ort_ThermalCyclingAndFlexBending);
                //}
                //if (ck_ORT_FlexBending.Checked)
                //{
                //    rolesOQC = AddString(rolesOQC, AccountRole.c_ort_FlexBending);
                //}
                //if (ck_ORT_HeatsoakAndFlexBending.Checked)
                //{
                //    rolesOQC = AddString(rolesOQC, AccountRole.c_ort_HeatsoakAndFlexBending);
                //}
                //if (ck_IPQC_B2BXsection.Checked)
                //{
                //    rolesOQC = AddString(rolesOQC, AccountRole.c_ipqc_B2BXsection);
                //}
                //if (ck_IPQC_B2BPeelingTest.Checked)
                //{
                //    rolesOQC = AddString(rolesOQC, AccountRole.c_ipqc_B2BPeelingTest);
                //}
                //if (ck_IPQC_B2BXray.Checked)
                //{
                //    rolesOQC = AddString(rolesOQC, AccountRole.c_ipqc_B2BXray);
                //}
                //if (ck_IPQC_OutlinePunchingDimension.Checked)
                //{
                //    rolesOQC = AddString(rolesOQC, AccountRole.c_ipqc_OutlinePunchingDimension);
                //}
                //if (ck_IPQC_PlasmaACFWettingAngle.Checked)
                //{
                //    rolesOQC = AddString(rolesOQC, AccountRole.c_ipqc_PlasmaACF);
                //}
                //if (ck_OQC_FAImeasurement.Checked)
                //{
                //    rolesOQC = AddString(rolesOQC, AccountRole.c_oqc_measurement);
                //}
                //if (ck_OQC_B2BPeelingTest.Checked)
                //{
                //    rolesOQC = AddString(rolesOQC, AccountRole.c_oqc_B2BPeeling);
                //}
                //if (ck_OQC_B2BPullingTest.Checked)
                //{
                //    rolesOQC = AddString(rolesOQC, AccountRole.c_oqc_B2BPulling);
                //}
                //if (ck_OQC_B2BShearingTest.Checked)
                //{
                //    rolesOQC = AddString(rolesOQC, AccountRole.c_oqc_B2BShearing);
                //}
                //if (ck_OQC_ACFFlatness.Checked)
                //{
                //    rolesOQC = AddString(rolesOQC, AccountRole.c_oqc_ACFFlatness);
                //}
                //if (ck_OQC_ACFRoughness.Checked)
                //{
                //    rolesOQC = AddString(rolesOQC, AccountRole.c_oqc_ACFRoughness);
                //}
                //if (ck_OQC_ACFBonding.Checked)
                //{
                //    rolesOQC = AddString(rolesOQC, AccountRole.c_oqc_ACFBonding);
                //}
                //if (ck_OQC_HotOil.Checked)
                //{
                //    rolesOQC = AddString(rolesOQC, AccountRole.c_oqc_HotOil);
                //}
                //if (ck_OQC_Heat4W.Checked)
                //{
                //    rolesOQC = AddString(rolesOQC, AccountRole.c_oqc_Heating4WTest);
                //}
                //if (ck_OQC_XSectionGNDB2Bpin.Checked)
                //{
                //    rolesOQC = AddString(rolesOQC, AccountRole.c_oqc_XSectionGNDB2Bpin);
                //}
                if (ckAdmin.Checked)
                {
                    rolesOQC += "99";
                }
                if (ckAccount.Checked)
                {
                    rolesOQC = AddString(rolesOQC, "98");
                }
                if (ck_shipment_check.Checked)
                {
                    rolesOQC = AddString(rolesOQC, "97");
                }
                if (ck_IPQC_B2BXsection.Checked)
                {
                    rolesOQC = AddString(rolesOQC, "1");
                }
                if (ck_IPQC_B2BPeelingTest.Checked)
                {
                    rolesOQC = AddString(rolesOQC, "2");
                }
                if (ck_IPQC_B2BXray.Checked)
                {
                    rolesOQC = AddString(rolesOQC, "3");
                }
                if (ck_IPQC_OutlinePunchingDimension.Checked)
                {
                    rolesOQC = AddString(rolesOQC, "4");
                }
                if (ck_OQC_FAImeasurement.Checked)
                {
                    rolesOQC = AddString(rolesOQC, "6");
                }
                if (ck_OQC_B2BPeelingTest.Checked)
                {
                    rolesOQC = AddString(rolesOQC, "7");
                }
                if (ck_OQC_ACFFlatness.Checked)
                {
                    rolesOQC = AddString(rolesOQC, "8");
                }
                if (ck_OQC_ACFRoughness.Checked)
                {
                    rolesOQC = AddString(rolesOQC, "9");
                }
                if (ck_OQC_XSectionGNDB2Bpin.Checked)
                {
                    rolesOQC = AddString(rolesOQC, "10");
                }
                if (ck_ORT_ThermalCycling.Checked)
                {
                    rolesOQC = AddString(rolesOQC, "11");
                }
                if (ck_ORT_HeatsoakAndRecovery.Checked)
                {
                    rolesOQC = AddString(rolesOQC, "12");
                }
                if (ck_ORT_ThermalShock.Checked)
                {
                    rolesOQC = AddString(rolesOQC, "13");
                }
                if (ck_ORT_ThermalCyclingAndFlexBending.Checked)
                {
                    rolesOQC = AddString(rolesOQC, "14");
                }
                if (ck_ORT_FlexBending.Checked)
                {
                    rolesOQC = AddString(rolesOQC, "15");
                }
                if (ck_ORT_HeatsoakAndFlexBending.Checked)
                {
                    rolesOQC = AddString(rolesOQC, "16");
                }
                if (ck_OQC_B2BPullingTest.Checked)
                {
                    rolesOQC = AddString(rolesOQC, "17");
                }
                if (ck_OQC_B2BShearingTest.Checked)
                {
                    rolesOQC = AddString(rolesOQC, "18");
                }
                if (ck_OQC_ACFBonding.Checked)
                {
                    rolesOQC = AddString(rolesOQC, "19");
                }
                if (ck_OQC_HotOil.Checked)
                {
                    rolesOQC = AddString(rolesOQC, "20");
                }
                if (ck_OQC_Heat4W.Checked)
                {
                    rolesOQC = AddString(rolesOQC, "21");
                }
                if (ck_OQC_Visual_Inspection.Checked)
                {
                    rolesOQC = AddString(rolesOQC, "22");
                }
                if (ck_OQC_ICT_test.Checked)
                {
                    rolesOQC = AddString(rolesOQC,"23");
                }
                if (ck_OQC_Function_test.Checked)
                {
                    rolesOQC = AddString(rolesOQC, "24");
                }
                if (ck_OQC_PSA_TSA_liner.Checked)
                {
                    rolesOQC = AddString(rolesOQC, "25");
                }
                if (ck_OQC_PSA_TSA_peeling.Checked)
                {
                    rolesOQC = AddString(rolesOQC, "26");
                }
                if (ck_OQC_Liner_Peeling.Checked)
                {
                    rolesOQC = AddString(rolesOQC,"27");
                }
                if (ck_OQC_2D_barcode.Checked)
                {
                    rolesOQC = AddString(rolesOQC,"28");
                }
                if (ck_OQC_Plasma.Checked)
                {
                    rolesOQC = AddString(rolesOQC, "29");
                }
                if (ck_OQC_Hot_bar_pre_solder.Checked)
                {
                    rolesOQC = AddString(rolesOQC, "30");
                }
                if (ck_ORT_IO_Connect.Checked)
                {
                    rolesOQC = AddString(rolesOQC, "31");
                }
                if (ck_ORT_Thermal_stress.Checked)
                {
                    rolesOQC = AddString(rolesOQC, "32");
                }
                if (ck_ORT_Hotbar_loop_test.Checked)
                {
                    rolesOQC = AddString(rolesOQC, "33");
                }
                if (ck_OBA_Visual_inspection.Checked)
                {
                    rolesOQC = AddString(rolesOQC, "34");
                }
                if (ck_OBA_ICT_test.Checked)
                {
                    rolesOQC = AddString(rolesOQC, "35");
                }
                if (ck_OBA_Function_Test.Checked)
                {
                    rolesOQC = AddString(rolesOQC, "36");
                }
                if (ck_OBA_2DBarcode_Grade.Checked)
                {
                    rolesOQC = AddString(rolesOQC, "37");
                }
                if (ckb_IPQCB2BPullingTest.Checked)
                {
                    rolesOQC = AddString(rolesOQC, "38");
                }
                if (ck_IPQC_Pasting_Dimension.Checked)
                {
                    rolesOQC = AddString(rolesOQC, "39");
                }
                if (ck_OQC_B2B_Matting_Un_Matting_Test.Checked)
                {
                    rolesOQC = AddString(rolesOQC, "40");
                }
                if (ck_IPQC_PSA_TSA_peeling.Checked)
                {
                    rolesOQC = AddString(rolesOQC, "41");
                }
                if (ck_IPQC_Liner_Peeling.Checked)
                {
                    rolesOQC = AddString(rolesOQC, "42");
                }
                if (ck_IPQC_SEM.Checked)
                {
                    rolesOQC = AddString(rolesOQC, "43");
                }
                if (ck_IPQC_PSA_TSA_Liner_peel_off_test.Checked)
                {
                    rolesOQC = AddString(rolesOQC, "44");
                }
                if (ck_Ipqc_Plasma_Wca_On_Acf.Checked) { rolesOQC = AddString(rolesOQC, "47"); }
                if (ck_Ipqc_Plasma_Wca_On_Gnd.Checked) { rolesOQC = AddString(rolesOQC, "48"); }
                if (ck_Coc_Report.Checked) { rolesOQC = AddString(rolesOQC, "49"); }
                if (ck_Iqc_B2b_Visual_Inspection.Checked) { rolesOQC = AddString(rolesOQC, "50"); }
                if (ck_Iqc_B2b_Dimension.Checked) { rolesOQC = AddString(rolesOQC, "51"); }
                if (ck_Iqc_B2b_Coplanarity.Checked) { rolesOQC = AddString(rolesOQC, "52"); }
                if (ck_Iqc_B2b_Plating_thickness.Checked) { rolesOQC = AddString(rolesOQC, "53"); }
                if (ck_Iqc_B2b_Solderability.Checked) { rolesOQC = AddString(rolesOQC, "54"); }
                if (ck_IQC_Bare_FPC_Visual_inspection.Checked) { rolesOQC = AddString(rolesOQC, "55"); }
                if (ck_IQC_Bare_FPC_Dimension_Included_BDvsT.Checked) { rolesOQC = AddString(rolesOQC, "56"); }
                if (ck_IQC_Bare_FPC_Shrinkage_Panel_TDMD.Checked) { rolesOQC = AddString(rolesOQC, "57"); }
                if (ck_IQC_Bare_FPC_Solderability.Checked) { rolesOQC = AddString(rolesOQC, "58"); }
                if (ck_IQC_Bare_FPC_Ni_Au_thickness.Checked) { rolesOQC = AddString(rolesOQC, "59"); }
                if (ck_IQC_Bare_FPC_ACF_Pad_Roughness.Checked) { rolesOQC = AddString(rolesOQC, "60"); }
                if (ck_IQC_Bare_FPC_WCA_PSAandSolderingP.Checked) { rolesOQC = AddString(rolesOQC, "61"); }
                if (ck_IQC_Barcode_on_FPC_Pad_or_SUS_Grade_verification.Checked) { rolesOQC = AddString(rolesOQC, "62"); }
                if (ck_IQC_PSA_Liner_Visual_inspection.Checked) { rolesOQC = AddString(rolesOQC, "63"); }
                if (ck_IQC_PSA_Liner_Dimension_Thickness.Checked) { rolesOQC = AddString(rolesOQC, "64"); }
                if (ck_IQC_PSA_Liner_Pre_Peel_test.Checked) { rolesOQC = AddString(rolesOQC, "65"); }
                if (ck_IQC_PSA_Coupon_peel_force_test.Checked) { rolesOQC = AddString(rolesOQC, "66"); }
                if (ck_IQC_Liner_Coupon_peel_force_test.Checked) { rolesOQC = AddString(rolesOQC, "67"); }
                if (ck_IQC_PSA_Die_cut_Peel_force_test.Checked) { rolesOQC = AddString(rolesOQC, "68"); }
                if (ck_IQC_Liner_Die_cut_peel_force_test.Checked) { rolesOQC = AddString(rolesOQC, "69"); }
                if (ck_IQC_Packing_Tray_Cosmetic.Checked) { rolesOQC = AddString(rolesOQC, "70"); }
                if (ck_IQC_Packing_Tray_ESD.Checked) { rolesOQC = AddString(rolesOQC, "71"); }
                if (ck_IQC_Packing_Tray_Dimension.Checked) { rolesOQC = AddString(rolesOQC, "72"); }
                if (ck_IQC_Solder_paste_Cosmetic.Checked) { rolesOQC = AddString(rolesOQC, "73"); }
                if (ck_IQC_Solder_paste_Particle_Dimension.Checked) { rolesOQC = AddString(rolesOQC, "74"); }
                if (ck_IQC_Solder_paste_Viscosity.Checked) { rolesOQC = AddString(rolesOQC, "75"); }
                if (ck_ORT_Liner_peeling_force.Checked) { rolesOQC = AddString(rolesOQC, "76"); }
                if (ck_ORT_PSA_TSA_peeling_force.Checked) { rolesOQC = AddString(rolesOQC, "77"); }
                if (ck_OQC_Liner_peeling_force.Checked) { rolesOQC = AddString(rolesOQC, "78"); }
                if (ck_OQC_PSA_TSA_peeling_force.Checked) { rolesOQC = AddString(rolesOQC, "79"); }
                if (ck_Iqc_B2b_Un_matting_force.Checked) { rolesOQC = AddString(rolesOQC, "87"); }
                if (ck_Iqc_Chip_Visual_Inspection.Checked) { rolesOQC = AddString(rolesOQC, "88"); }
                if (ck_Iqc_Chip_Dimension.Checked) { rolesOQC = AddString(rolesOQC, "89"); }
                if (ck_Iqc_Chip_Solderability.Checked) { rolesOQC = AddString(rolesOQC, "90"); }
                if (ck_Iqc_Chip_Function_LCR_value.Checked) { rolesOQC = AddString(rolesOQC, "91"); }
                if (ck_Iqc_Shieldcan_Visual_Inspection.Checked) { rolesOQC = AddString(rolesOQC, "92"); }
                if (ck_Iqc_Shieldcan_Dimension.Checked) { rolesOQC = AddString(rolesOQC, "93"); }
                if (ck_Iqc_Shieldcan_Solderability.Checked) { rolesOQC = AddString(rolesOQC, "94"); }
                if (ck_Iqc_Shieldcan_Plating_Thickness.Checked) { rolesOQC = AddString(rolesOQC, "95"); }
                if (ck_Iqc_Shieldcan_Coplanarity_warpage.Checked) { rolesOQC = AddString(rolesOQC, "96"); }
                if (ck_Iqc_PSA_Liner_FTIR.Checked) { rolesOQC = AddString(rolesOQC, "97"); }
                if (ck_Iqc_PSA_Liner_Surface_resistance.Checked) { rolesOQC = AddString(rolesOQC, "98"); }
                if (ck_Iqc_PSA_Liner_Surface_static_electricity.Checked) { rolesOQC = AddString(rolesOQC, "99"); }
                if (ck_Iqc_PSA_Liner_Environment.Checked) { rolesOQC = AddString(rolesOQC, "100"); }
                if (ck_Iqc_Si_coating_weight.Checked) { rolesOQC = AddString(rolesOQC, "101"); }
                if (ck_ORT_Check_sample.Checked) { rolesOQC = AddString(rolesOQC, "102"); }

                if (string.IsNullOrEmpty(rolesOQC))
                {
                    DialogHelper.Info("Bạn chưa chọn quyền cho tài khoản này", "Cảnh báo");
                    Cursor.Current = Cursors.Default;
                    return;
                }
                if (!_operatorID.Equals(operatorID))
                {
                    AddAccount(operatorID, passWord, fullName, rolesOQC);
                }
                // Update
                else
                {
                    UpdateAccount(operatorID, passWord, fullName, rolesOQC);
                }
            }
            catch (Exception ex)
            {
                // Ghi lỗi ra file Log
                ManageLog.WriteErrorApp(ex.Message);
            }

            Cursor.Current = Cursors.Default;
        }
        private void ck_OQC_All_CheckedChanged(object sender, EventArgs e)
        {
           if(ck_OQC_All.Checked)
            {
                ck_OQC_FAImeasurement.Checked = true;
                ck_OQC_B2BPeelingTest.Checked = true;
                ck_OQC_B2BPullingTest.Checked = true;
                ck_OQC_B2BShearingTest.Checked = true;
                ck_OQC_ACFFlatness.Checked = true;
                ck_OQC_ACFRoughness.Checked = true;
                ck_OQC_ACFBonding.Checked = true;
                ck_OQC_Heat4W.Checked = true;
                ck_OQC_HotOil.Checked = true;
                ck_OQC_XSectionGNDB2Bpin.Checked = true;
                ck_OQC_Visual_Inspection.Checked = true;
                ck_OQC_ICT_test.Checked = true;
                ck_OQC_Function_test.Checked = true;
                ck_OQC_PSA_TSA_liner.Checked = true;
                ck_OQC_PSA_TSA_peeling.Checked = true;
                ck_OQC_Liner_Peeling.Checked = true;
                ck_OQC_2D_barcode.Checked = true;
                ck_OQC_Plasma.Checked = true;
                ck_OQC_Hot_bar_pre_solder.Checked = true;
                ck_OQC_B2B_Matting_Un_Matting_Test.Checked = true;
                ck_OQC_Liner_peeling_force.Checked = true;
                ck_OQC_PSA_TSA_peeling_force.Checked = true;
            }    
        }

        private void ck_ORT_All_CheckedChanged(object sender, EventArgs e)
        {
            if(ck_ORT_All.Checked)
            {
                ck_ORT_FlexBending.Checked = true;
                ck_ORT_HeatsoakAndFlexBending.Checked = true;
                ck_ORT_HeatsoakAndRecovery.Checked = true;
                ck_ORT_ThermalCycling.Checked = true;
                ck_ORT_ThermalShock.Checked = true;
                ck_ORT_ThermalCyclingAndFlexBending.Checked = true;
                ck_ORT_Thermal_stress.Checked = true;
                ck_ORT_Hotbar_loop_test.Checked = true;
                ck_ORT_IO_Connect.Checked = true;
                ck_ORT_Liner_peeling_force.Checked = true;
                ck_ORT_PSA_TSA_peeling_force.Checked = true;
                ck_ORT_Check_sample.Checked = true;                
            }    
        }

        private void ck_IPQC_All_CheckedChanged(object sender, EventArgs e)
        {
            if(ck_IPQC_All.Checked)
            {
                ck_IPQC_B2BPeelingTest.Checked = true;
                ck_IPQC_B2BXray.Checked = true;
                ck_IPQC_B2BXsection.Checked = true;
                ck_IPQC_OutlinePunchingDimension.Checked = true;
                ckb_IPQCB2BPullingTest.Checked = true;
                ck_IPQC_Pasting_Dimension.Checked = true;
                ck_IPQC_Liner_Peeling.Checked = true;
                ck_IPQC_PSA_TSA_peeling.Checked = true;
                ck_IPQC_PSA_TSA_Liner_peel_off_test.Checked = true;
                ck_IPQC_SEM.Checked = true;
                ck_Ipqc_Plasma_Wca_On_Acf.Checked = true; 
                ck_Ipqc_Plasma_Wca_On_Gnd.Checked = true;
            }    
        }

        private void label2_Click(object sender, EventArgs e)
        {

        }

        private void checkBox4_CheckedChanged(object sender, EventArgs e)
        {
            if(ck_OBA_All.Checked)
            {
                ck_OBA_Visual_inspection.Checked = true;
                ck_OBA_ICT_test.Checked = true;
                ck_OBA_Function_Test.Checked = true;
                ck_OBA_2DBarcode_Grade.Checked = true;
            }
        }

        private void ck_COC_All_CheckedChanged(object sender, EventArgs e)
        {
            if (ck_COC_All.Checked)
            {
                ck_Coc_Report.Checked = true;
            }
        }

        private void ck_IQC_All_CheckedChanged(object sender, EventArgs e)
        {
            if (ck_IQC_All.Checked)
            {
                ck_Iqc_B2b_Visual_Inspection.Checked = true; 
                ck_Iqc_B2b_Dimension.Checked = true; 
                ck_Iqc_B2b_Coplanarity.Checked = true; 
                ck_Iqc_B2b_Plating_thickness.Checked = true;
                ck_Iqc_B2b_Solderability.Checked = true; 
                ck_IQC_Bare_FPC_Visual_inspection.Checked = true; 
                ck_IQC_Bare_FPC_Dimension_Included_BDvsT.Checked = true;
                ck_IQC_Bare_FPC_Shrinkage_Panel_TDMD.Checked = true; 
                ck_IQC_Bare_FPC_Solderability.Checked = true; 
                ck_IQC_Bare_FPC_Ni_Au_thickness.Checked = true; 
                ck_IQC_Bare_FPC_ACF_Pad_Roughness.Checked = true;
                ck_IQC_Bare_FPC_WCA_PSAandSolderingP.Checked = true; 
                ck_IQC_Barcode_on_FPC_Pad_or_SUS_Grade_verification.Checked = true; 
                ck_IQC_PSA_Liner_Visual_inspection.Checked = true;
                ck_IQC_PSA_Liner_Dimension_Thickness.Checked = true; 
                ck_IQC_PSA_Liner_Pre_Peel_test.Checked = true; 
                ck_IQC_PSA_Coupon_peel_force_test.Checked = true;
                ck_IQC_Liner_Coupon_peel_force_test.Checked = true; 
                ck_IQC_PSA_Die_cut_Peel_force_test.Checked = true; 
                ck_IQC_Liner_Die_cut_peel_force_test.Checked = true;
                ck_IQC_Packing_Tray_Cosmetic.Checked = true; 
                ck_IQC_Packing_Tray_ESD.Checked = true; 
                ck_IQC_Packing_Tray_Dimension.Checked = true; 
                ck_IQC_Solder_paste_Cosmetic.Checked = true;
                ck_IQC_Solder_paste_Particle_Dimension.Checked = true; 
                ck_IQC_Solder_paste_Viscosity.Checked = true;
                ck_Iqc_B2b_Un_matting_force.Checked = true;
                ck_Iqc_Chip_Visual_Inspection.Checked = true;
                ck_Iqc_Chip_Dimension.Checked = true;
                ck_Iqc_Chip_Solderability.Checked = true;
                ck_Iqc_Chip_Function_LCR_value.Checked = true;
                ck_Iqc_Shieldcan_Visual_Inspection.Checked = true;
                ck_Iqc_Shieldcan_Dimension.Checked = true;
                ck_Iqc_Shieldcan_Solderability.Checked = true;
                ck_Iqc_Shieldcan_Plating_Thickness.Checked = true;
                ck_Iqc_Shieldcan_Coplanarity_warpage.Checked = true;
                ck_Iqc_PSA_Liner_FTIR.Checked = true;
                ck_Iqc_PSA_Liner_Surface_resistance.Checked = true;
                ck_Iqc_PSA_Liner_Surface_static_electricity.Checked = true;
                ck_Iqc_PSA_Liner_Environment.Checked = true;
                ck_Iqc_Si_coating_weight.Checked = true;
            }
        }

        private void ck_OQC_Liner_peeling_force_CheckedChanged(object sender, EventArgs e)
        {

        }

        private void ck_OQC_B2B_Matting_Un_Matting_Test_CheckedChanged(object sender, EventArgs e)
        {

        }
    }
}