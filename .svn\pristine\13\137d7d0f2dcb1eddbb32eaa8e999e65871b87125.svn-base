﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using Trace_AbilitySystem.Libs;
using Trace_AbilitySystem.Libs.DTOClass;
using System.Web.Script.Serialization;
using Trace_AbilitySystem.Libs.Trace_02_FlexAssy_Services;
using System.Text;
using System.IO;
using System.Runtime.InteropServices;
using Trace_AbilitySystem.Libs.DTOClass.FlexAssyModel;

namespace Trace_AbilitySystem.Controllers
{
	[AuthorizationRequired]
	public class HomeController : Controller
	{
		public ActionResult Index()
		{
			//    string k = "5DVCD";
			//    if (k.IndexOf('-') != -1)
			//        k = k.Substring(0, k.IndexOf('-'));

			//List<int> sd = new List<int> { 1, 10, 2, 11, 20 };
			//sd = sd.OrderBy(x => x).ToList();

			Stopwatch stop = new Stopwatch();
			stop.Start();

			// Check Brower
			HttpRequest req = System.Web.HttpContext.Current.Request;
			string browserName = req.Browser.Browser;
			if (!browserName.Contains("Chrome") && !browserName.Contains("Firefox"))
			{
				return Redirect("/browser");
			}
			// Lay cac cong doan duoc Active
			DataTable dtStageActive = Singleton_03_Common.IStageSettingActiveViewTraceService.StageSettingActiveViewTrace_GetAll("#");
			ViewBag.StageActive = dtStageActive;
			// Lay cac cong doan duoc Active Bare Flex
			DataTable dtStageActiveBareFlex = Singleton_03_Common.IStageSettingActiveViewTraceService.StageSettingActiveViewTrace_GetAll("F1");
			ViewBag.StageActiveBareFlex = dtStageActiveBareFlex;

			// Lay cac cong doan duoc Active IPQC
			DataTable dtStageActiveIPQC = Singleton_03_Common.IStageSettingActiveViewTraceService.StageSettingActiveViewTrace_GetAll("IPQC");
			ViewBag.StageActiveIPQC = dtStageActiveIPQC;

			//DataTable dtStageActivePismo = Singleton_03_Common.IStageSettingActiveViewTraceService.StageSettingActiveViewTrace_GetAllPismo();
			DataTable dtStageActivePismo = Singleton_03_Common.IStageSettingActiveViewTraceService.StageSettingActiveViewTrace_GetAll("Pismo");
			ViewBag.StageActivePismo = dtStageActivePismo;

			string itemName = null;
			string laneID = "1";
			string pcsID = Request.Params["PcsID"] ?? "";
			string blockID = Request.Params["BlockID"] ?? "";
			string workOrder = Request.Params["WorkOrder"] ?? "";


			string upper_p = ""; // Upper-p: Indi Front F1
			string lower_p = ""; // Lower-p: Indi Back F1
			string mounting = ""; // Mounting: Indi F3,F4
			string invoiceID = ""; // invoiceID
			string IndicationIDAVI = ""; // IndicationIDAVI

			string DatetimeInvoice = ""; // invoiceID
			string connectionStringOption = null;
			string connectionStringOk = null;
			string plasmaID = "";
			string PunchMachineID = string.Empty;
			string VendorCode = string.Empty;
			string ANPCode = string.Empty;
			string IPQC_Mounting_Search = string.Empty;
			DataAllTrace model = null;
			DataTable ListComp = new DataTable();
			ListComp.Columns.Add("CompID", typeof(string));
			if (pcsID.Length != 0 || blockID.Length != 0 || workOrder.Length != 0)
			{
				model = new DataAllTrace();
				if (pcsID.Length != 0)
				{
					model.DataFlexAssy.FlexAssy_08_LaserMarking = Singleton_02_FlexAssy.IFlexAssy_08_LaserMarkingService.FlexAssy_08_LaserMarking_GetByProductID(pcsID, connectionStringOption, out connectionStringOk);
					if (connectionStringOk == "F3")
					{
						DataTable dt = Singleton_02_FlexAssy.IFlexAssy_08_LaserMarkingService.FlexAssy_08_LaserMarking_GetByProductID(pcsID, connectionStringOption = "F4", out connectionStringOk);
						if (dt?.Rows.Count > 0)
						{
							connectionStringOk = null;
						}
						else
						{
							connectionStringOk = "F3";
						}
					}
					model.DataFlexAssy.FlexAssy_18_PanelAndSingleFPCBarcodeLink = Singleton_02_FlexAssy.IFlexAssy_18_PanelAndSingleFPCBarcodeLinkService.FlexAssy_18_PanelAndSingleFPCBarcodeLink_GetByProductID(pcsID, connectionStringOption = connectionStringOk, out connectionStringOk);
					if (connectionStringOk == "F3")
					{
						DataTable dt = Singleton_02_FlexAssy.IFlexAssy_18_PanelAndSingleFPCBarcodeLinkService.FlexAssy_18_PanelAndSingleFPCBarcodeLink_GetByProductID(pcsID, connectionStringOption = "F4", out connectionStringOk);
						if (dt?.Rows.Count > 0)
						{
							connectionStringOk = null;
						}
						else
						{
							connectionStringOk = "F3";
						}
					}
					connectionStringOk = null;

					// Lấy giá trị BlockID
					if (model.DataFlexAssy.FlexAssy_08_LaserMarking?.Rows.Count > 0)
					{
						itemName = model.DataFlexAssy.FlexAssy_08_LaserMarking.Rows[0]["ItemName"].ToString();
						blockID = model.DataFlexAssy.FlexAssy_08_LaserMarking.Rows[0]["BlockID"].ToString();

					}

					model.DataFlexAssy.FlexAssy_20_Relocation = Singleton_02_FlexAssy.IFlexAssy_20_RelocationService.FlexAssy_20_Relocation_GetByProductID(pcsID, connectionStringOption = connectionStringOk, out connectionStringOk);
					model.DataFlexAssy.FlexAssy_21_2DCodeScan = Singleton_02_FlexAssy.IFlexAssy_21_2DCodeScanService.FlexAssy_21_2DCodeScan_GetByProductID(pcsID, connectionStringOption = connectionStringOk, out connectionStringOk);
					model.DataFlexAssy.FlexAssy_22_ICT = Singleton_02_FlexAssy.IFlexAssy_22_ICTService.FlexAssy_22_ICT_GetByProductID(pcsID, connectionStringOption = connectionStringOk, out connectionStringOk);
					model.DataFlexAssy.FlexAssy_22_OQC_ICT = Singleton_02_FlexAssy.IFlexAssy_22_OQC_ICTService.FlexAssy_22_OQC_ICT_GetByProductID(pcsID, connectionStringOption = connectionStringOk, out connectionStringOk);
					model.DataFlexAssy.FlexAssy_30_OBA_ICT_New = Singleton_02_FlexAssy.IFlexAssy_30_OBAService.FlexAssy_30_OBA_ICT_New_GetByProductID(pcsID, connectionStringOption = connectionStringOk, out connectionStringOk);
					model.DataFlexAssy.FlexAssy_22_H4W_ICT = Singleton_02_FlexAssy.IFlexAssy_22_H4W_ICTService.FlexAssy_22_H4W_ICT_GetByProductID(pcsID, connectionStringOption = connectionStringOk, out connectionStringOk);
					model.DataFlexAssy.FlexAssy_22_ORT_ICT = Singleton_02_FlexAssy.IFlexAssy_22_ORT_ICTService.FlexAssy_22_ORT_ICT_GetByProductID(pcsID, connectionStringOption = connectionStringOk, out connectionStringOk);
					model.DataFlexAssy.FlexAssy_22_RC_ICT = Singleton_02_FlexAssy.IFlexAssy_22_ICTService.FlexAssy_22_RC_ICT_GetByProductID(pcsID, connectionStringOption = connectionStringOk, out connectionStringOk);
					model.DataFlexAssy.FlexAssy_22_ICT_Component = Singleton_02_FlexAssy.IFlexAssy_22_ICTService.FlexAssy_22_ICT_Component_GetByProductID(pcsID, connectionStringOption = connectionStringOk, out connectionStringOk);

					model.DataFlexAssy.FlexAssy_23_FCT_ICT_DIOT = Singleton_02_FlexAssy.IFlexAssy_23_FCTService.FlexAssy_23_FCT_ICT_DIOT_GetByProductID(pcsID, connectionStringOption = connectionStringOk, out connectionStringOk);
					model.DataFlexAssy.FlexAssy_24_AutoPickup = Singleton_02_FlexAssy.IFlexAssy_24_AutoPickupService.FlexAssy_24_AutoPickup_GetByProductID(pcsID, connectionStringOption = connectionStringOk, out connectionStringOk);
					model.DataFlexAssy.FlexAssy_24_Sorting = Singleton_02_FlexAssy.IFlexAssy_24_SortingService.FlexAssy_24_Sorting_GetByProductID(pcsID, connectionStringOption = connectionStringOk, out connectionStringOk);
					model.DataFlexAssy.FlexAssy_24_AutoPickup_Component = Singleton_02_FlexAssy.IFlexAssy_24_AutoPickupService.FlexAssy_24_AutoPickup_Component_GetByProductID(pcsID, connectionStringOption = connectionStringOk, out connectionStringOk);
					model.DataFlexAssy.FlexAssy_23_Cognex = Singleton_02_FlexAssy.IFlexAssy_23_CognexService.FlexAssy_23_Cognex_GetByProductID(pcsID, connectionStringOption = connectionStringOk, out connectionStringOk);

					model.DataFlexAssy.FlexAssy_04_SolderPasteInspection_Detail = Singleton_02_FlexAssy.IFlexAssy_04_SolderPasteInspectionService.FlexAssy_04_SolderPasteInspection_Detail_GetByProductID(pcsID, connectionStringOption = connectionStringOk, out connectionStringOk);
					model.DataFlexAssy.FlexAssy_06_PreReflowAOI_Detail = Singleton_02_FlexAssy.IFlexAssy_06_PreReflowAOIService.FlexAssy_06_PreReflowAOI_Detail_GetByProductID(pcsID, connectionStringOption = connectionStringOk, out connectionStringOk);
					model.DataFlexAssy.FlexAssy_09_PostReflowAOI_Detail = Singleton_02_FlexAssy.IFlexAssy_09_PostReflowAOIService.FlexAssy_09_PostReflowAOI_Detail_GetByProductID(pcsID, connectionStringOption = connectionStringOk, out connectionStringOk);
					model.DataFlexAssy.FlexAssy_10_Info_Pasting_Detail_DataPcs = Singleton_02_FlexAssy.IFlexAssy_10_Info_PastingService.FlexAssy_10_Info_Pasting_Detail_DataPcs_GetByProductID(pcsID, connectionStringOption = connectionStringOk, out connectionStringOk);
					if (model.DataFlexAssy.FlexAssy_10_Info_Pasting_Detail_DataPcs?.Rows.Count > 0)
					{
						for (int j = 0; j < model.DataFlexAssy.FlexAssy_10_Info_Pasting_Detail_DataPcs?.Rows.Count; j++)
						{
							ListComp.Rows.Add(model.DataFlexAssy.FlexAssy_10_Info_Pasting_Detail_DataPcs.Rows[0]["ReelID"]);
						}
					}
					model.DataFlexAssy.FlexAssy_15_Bending_Detail = Singleton_02_FlexAssy.IFlexAssy_15_Bending_DetailService.FlexAssy_15_Bending_Detail_GetByProductID(pcsID, connectionStringOption = connectionStringOk, out connectionStringOk);
					model.DataFlexAssy.FlexAssy_19_Punching_Detail = Singleton_02_FlexAssy.IFlexAssy_19_Punching_DetailService.FlexAssy_19_Punching_Detail_GetByProductID(pcsID, connectionStringOption = connectionStringOk, out connectionStringOk);
					model.DataFlexAssy.FlexAssy_20_Sorting_Detail = Singleton_02_FlexAssy.IFlexAssy_20_Sorting_DetailService.FlexAssy_20_Sorting_Detail_GetByProductID(pcsID, connectionStringOption = connectionStringOk, out connectionStringOk);
					model.DataFlexAssy.FlexAssy_21_IDScan = Singleton_02_FlexAssy.IFlexAssy_21_IDScanService.FlexAssy_21_IDScan_GetByProductID(pcsID, connectionStringOption = connectionStringOk, out connectionStringOk);

					model.DataFlexAssy.FlexAssy_08_LaserMarking_SecondSide = Singleton_02_FlexAssy.IFlexAssy_08_LaserMarkingService.FlexAssy_08_LaserMarking_SecondSide_getByProductID(pcsID, connectionStringOption = connectionStringOk, out connectionStringOk);
					if (pcsID.Length == 17 && (model.DataFlexAssy.FlexAssy_22_ICT?.Rows.Count > 0 && model.DataFlexAssy.FlexAssy_22_ICT.Rows[0]["ID_LogFile"] == DBNull.Value))
					{
						DataTable dt_ICT = Singleton_02_FlexAssy.IFlexAssy_22_ICTService.FlexAssy_22_ICT_GetByProductID(pcsID.Substring(3, 12), connectionStringOption = connectionStringOk, out connectionStringOk);

						if (dt_ICT?.Rows.Count > 0)
						{
							dt_ICT.Rows[0]["ProductID"] = model.DataFlexAssy.FlexAssy_22_ICT.Rows[0]["ProductID"];
							model.DataFlexAssy.FlexAssy_22_ICT = dt_ICT;
						}
					}
					if (pcsID.Length == 17 && (model.DataFlexAssy.FlexAssy_22_OQC_ICT?.Rows.Count > 0 && model.DataFlexAssy.FlexAssy_22_OQC_ICT.Rows[0]["ID_LogFile"] == DBNull.Value))
					{
						DataTable dt_ICT = Singleton_02_FlexAssy.IFlexAssy_22_OQC_ICTService.FlexAssy_22_OQC_ICT_GetByProductID(pcsID.Substring(3, 12), connectionStringOption = connectionStringOk, out connectionStringOk);

						if (dt_ICT?.Rows.Count > 0)
						{
							dt_ICT.Rows[0]["ProductID"] = model.DataFlexAssy.FlexAssy_22_OQC_ICT.Rows[0]["ProductID"];
							model.DataFlexAssy.FlexAssy_22_OQC_ICT = dt_ICT;
						}
					}
					if (pcsID.Length == 17 && (model.DataFlexAssy.FlexAssy_30_OBA_ICT_New?.Rows.Count > 0 && model.DataFlexAssy.FlexAssy_30_OBA_ICT_New.Rows[0]["ID_LogFile"] == DBNull.Value))
					{
						DataTable dt_ICT = Singleton_02_FlexAssy.IFlexAssy_30_OBAService.FlexAssy_30_OBA_ICT_New_GetByProductID(pcsID.Substring(3, 12), connectionStringOption = connectionStringOk, out connectionStringOk);

						if (dt_ICT?.Rows.Count > 0)
						{
							dt_ICT.Rows[0]["ProductID"] = model.DataFlexAssy.FlexAssy_30_OBA_ICT_New.Rows[0]["ProductID"];
							model.DataFlexAssy.FlexAssy_30_OBA_ICT_New = dt_ICT;
						}
					}
					if (pcsID.Length == 17 && (model.DataFlexAssy.FlexAssy_22_H4W_ICT?.Rows.Count > 0 && model.DataFlexAssy.FlexAssy_22_H4W_ICT.Rows[0]["ID_LogFile"] == DBNull.Value))
					{
						DataTable dt_ICT = Singleton_02_FlexAssy.IFlexAssy_22_H4W_ICTService.FlexAssy_22_H4W_ICT_GetByProductID(pcsID.Substring(3, 12), connectionStringOption = connectionStringOk, out connectionStringOk);

						if (dt_ICT?.Rows.Count > 0)
						{
							dt_ICT.Rows[0]["ProductID"] = model.DataFlexAssy.FlexAssy_22_H4W_ICT.Rows[0]["ProductID"];
							model.DataFlexAssy.FlexAssy_22_H4W_ICT = dt_ICT;
						}
					}
					if (pcsID.Length == 17 && (model.DataFlexAssy.FlexAssy_22_ORT_ICT?.Rows.Count > 0 && model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows[0]["ID_LogFile"] == DBNull.Value))
					{
						DataTable dt_ICT = Singleton_02_FlexAssy.IFlexAssy_22_ORT_ICTService.FlexAssy_22_ORT_ICT_GetByProductID(pcsID.Substring(3, 12), connectionStringOption = connectionStringOk, out connectionStringOk);

						if (dt_ICT?.Rows.Count > 0)
						{
							dt_ICT.Rows[0]["ProductID"] = model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows[0]["ProductID"];
							model.DataFlexAssy.FlexAssy_22_ORT_ICT = dt_ICT;
						}
					}
					if (pcsID.Length == 17 && (model.DataFlexAssy.FlexAssy_22_RC_ICT?.Rows.Count > 0 && model.DataFlexAssy.FlexAssy_22_RC_ICT.Rows[0]["ID_LogFile"] == DBNull.Value))
					{
						DataTable dt_ICT = Singleton_02_FlexAssy.IFlexAssy_22_ICTService.FlexAssy_22_RC_ICT_GetByProductID(pcsID.Substring(3, 12), connectionStringOption = connectionStringOk, out connectionStringOk);

						if (dt_ICT?.Rows.Count > 0)
						{
							dt_ICT.Rows[0]["ProductID"] = model.DataFlexAssy.FlexAssy_22_RC_ICT.Rows[0]["ProductID"];
							model.DataFlexAssy.FlexAssy_22_RC_ICT = dt_ICT;
						}
					}

					if (pcsID.Length == 17 && (model.DataFlexAssy.FlexAssy_24_AutoPickup?.Rows.Count > 0 && model.DataFlexAssy.FlexAssy_24_AutoPickup.Rows[0]["FileName"] == DBNull.Value))
					{
						DataTable dt_AutoPickup = Singleton_02_FlexAssy.IFlexAssy_24_AutoPickupService.FlexAssy_24_AutoPickup_GetByProductID(pcsID.Substring(3, 12), connectionStringOption = connectionStringOk, out connectionStringOk);

						if (dt_AutoPickup?.Rows.Count > 0)
						{
							model.DataFlexAssy.FlexAssy_24_AutoPickup.Rows[0]["FileName"] = dt_AutoPickup.Rows[0]["FileName"];
						}
					}
					model.DataFlexAssy.FlexAssy_25_AVI = Singleton_02_FlexAssy.IFlexAssy_25_AVIService.FlexAssy_25_AVI_GetByProductID(pcsID, connectionStringOption = connectionStringOk, out connectionStringOk);
					model.DataFlexAssy.FlexAssy_25_FVI = Singleton_02_FlexAssy.IFlexAssy_25_FVIService.FlexAssy_25_FVI_GetByProductID(pcsID, connectionStringOption = connectionStringOk, out connectionStringOk);
					model.DataFlexAssy.FlexAssy_25_FVI_History = Singleton_02_FlexAssy.IFlexAssy_25_FVIService.FlexAssy_25_FVI_History_GetTop1ByProductID(pcsID, connectionStringOption = connectionStringOk);
					model.DataFlexAssy.FlexAssy_26_QcGate = Singleton_02_FlexAssy.IFlexAssy_26_QcGateService.FlexAssy_26_QcGate_GetByProductID(pcsID, connectionStringOption = connectionStringOk, out connectionStringOk);
					model.DataFlexAssy.FlexAssy_27_Plasma = Singleton_02_FlexAssy.IFlexAssy_27_PlasmaService.FlexAssy_27_Plasma_GetByProductID(pcsID, connectionStringOption = connectionStringOk, out connectionStringOk);
					model.DataFlexAssy.FlexAssy_29_HeatSeal = Singleton_02_FlexAssy.IFlexAssy_29_HeatSealService.FlexAssy_29_HeatSeal_GetByProductID(pcsID, connectionStringOption = connectionStringOk, out connectionStringOk);
					model.DataFlexAssy.FlexAssy_29_PackingBarcodeScanning = Singleton_02_FlexAssy.IFlexAssy_29_PackingBarcodeScanningService.FlexAssy_29_PackingBarcodeScanning_GetByProductID(pcsID, connectionStringOption = connectionStringOk, out connectionStringOk);
					if (model.DataFlexAssy.FlexAssy_29_PackingBarcodeScanning?.Rows.Count > 0)
					{
						invoiceID = model.DataFlexAssy.FlexAssy_29_PackingBarcodeScanning.Rows[0]["InvoiceID"].ToString();
						DatetimeInvoice = model.DataFlexAssy.FlexAssy_29_PackingBarcodeScanning.Rows[0]["DateTime"].ToString();
					}
					model.DataFlexAssy.FlexAssy_30_OBADefine = Singleton_02_FlexAssy.IFlexAssy_30_OBAService.FlexAssy_30_OBADefine_GetByProductID(pcsID, connectionStringOption = connectionStringOk, out connectionStringOk);
					//model.DataFlexAssy.FlexAssy_30_OBA_ICT = Singleton_02_FlexAssy.IFlexAssy_30_OBAService.FlexAssy_30_OBA_ICT_GetByProductID(pcsID, connectionStringOption = connectionStringOk, out connectionStringOk);
					model.DataFlexAssy.FlexAssy_30_OBA_FVI = Singleton_02_FlexAssy.IFlexAssy_30_OBAService.FlexAssy_30_OBA_FVI_GetByProductID(pcsID, connectionStringOption = connectionStringOk, out connectionStringOk);
					model.DataFlexAssy.FlexAssy_30_OBA_QcGate = Singleton_02_FlexAssy.IFlexAssy_30_OBAService.FlexAssy_30_OBA_QcGate_GetByProductID(pcsID, connectionStringOption = connectionStringOk, out connectionStringOk);
					model.DataFlexAssy.FlexAssy_31_RELORT = Singleton_02_FlexAssy.IFlexAssy_31_RELORTService.FlexAssy_31_RELORT_GetByProductID(pcsID, connectionStringOption = connectionStringOk, out connectionStringOk);
					model.DataFlexAssy.FlexAssy_10_Lamination_QYC_Pcs = Singleton_02_FlexAssy.IFlexAssy_10_LaminationService.FlexAssy_10_Lamination_QYC_Detail_GetByPcs(pcsID, connectionStringOption = connectionStringOk, out connectionStringOk);

					#region Trace PisMo
					model.DataPismoFlexAssy.FlexAssy_01_AVI_Detail = Singleton_07_Pismo.IPismo_01_AVIServices.FlexAssy_01_AVI_Detail_GetByProductID(pcsID);
					// Lấy giá trị BlockID
					if (model.DataPismoFlexAssy.FlexAssy_01_AVI_Detail?.Rows.Count > 0)
					{
						int pkid = int.Parse(model.DataPismoFlexAssy.FlexAssy_01_AVI_Detail.Rows[0]["AVIPkid"].ToString());
						var AVI = Singleton_07_Pismo.IPismo_01_AVIServices.FlexAssy_01_AVI_GetByPkID(pkid);
						if (AVI?.Rows.Count > 0)
						{
							blockID = AVI.Rows[0]["BlockID"].ToString();
						}
					}
					model.DataPismoFlexAssy.FlexAssy_01_TraceAOI = Singleton_07_Pismo.IFlexAssy_01_TraceAOIService.FlexAssy_01_TraceAOI_GetByProductID(pcsID, "Pismo");
					model.DataPismoFlexAssy.FlexAssy_02_AVI_SUS_Detail = Singleton_07_Pismo.IPismo_02_AVI_SUSServices.FlexAssy_02_AVI_SUS_Detail_GetByProductID(pcsID);
					// Lấy giá trị BlockID
					if (model.DataPismoFlexAssy.FlexAssy_02_AVI_SUS_Detail?.Rows.Count > 0)
					{
						int pkid = int.Parse(model.DataPismoFlexAssy.FlexAssy_02_AVI_SUS_Detail.Rows[0]["AVISUSPkid"].ToString());
						var AVISUS = Singleton_07_Pismo.IPismo_02_AVI_SUSServices.FlexAssy_02_AVI_SUS_GetByPkID(pkid);
						if (AVISUS?.Rows.Count > 0)
						{
							blockID = AVISUS.Rows[0]["BlockID"].ToString();
						}
					}
					model.DataPismoFlexAssy.FlexAssy_03_LaserSUS_Detail = Singleton_07_Pismo.IPismo_03_LaserSUSServices.FlexAssy_03_LaserSUS_Detail_GetByProductID(pcsID);

					// Lấy giá trị BlockID
					if (model.DataPismoFlexAssy.FlexAssy_03_LaserSUS_Detail?.Rows.Count > 0)
					{
						int pkid = int.Parse(model.DataPismoFlexAssy.FlexAssy_03_LaserSUS_Detail.Rows[0]["LaserSUSPkid"].ToString());
						var LaserSus = Singleton_07_Pismo.IPismo_03_LaserSUSServices.FlexAssy_03_LaserSUS_GetByPkID(pkid);
						if (LaserSus?.Rows.Count > 0)
						{
							blockID = LaserSus.Rows[0]["BlockID"].ToString();
						}
					}
					model.DataPismoFlexAssy.FlexAssy_04_QcGate = Singleton_07_Pismo.IFlexAssy_04_QcgateService.FlexAssy_04_QcGate_GetByProductID(pcsID, "Pismo");
					model.DataPismoFlexAssy.FlexAssy_05_ViaAOI_Detail = Singleton_07_Pismo.IFlexAssy_05_ViaAOIService.FlexAssy_05_ViaAOI_GetByProductID(pcsID, "Pismo");

					// Lấy giá trị BlockID
					if (model.DataPismoFlexAssy.FlexAssy_05_ViaAOI_Detail?.Rows.Count > 0)
					{
						int pkid = int.Parse(model.DataPismoFlexAssy.FlexAssy_05_ViaAOI_Detail.Rows[0]["ViaAOIId"].ToString());
						var ViaAOI = Singleton_07_Pismo.IFlexAssy_05_ViaAOIService.FlexAssy_05_ViaAOI_GetByPkID(pkid);
						if (ViaAOI?.Rows.Count > 0)
						{
							blockID = ViaAOI.Rows[0]["BlockID"].ToString();
						}
					}
					model.DataPismoFlexAssy.FlexAssy_06_Cognex_Detail = Singleton_07_Pismo.IFlexAssy_06_CognexService.FlexAssy_06_Cognex_GetByProductID(pcsID, "Pismo");
					// Lấy giá trị BlockID
					if (model.DataPismoFlexAssy.FlexAssy_06_Cognex_Detail?.Rows.Count > 0)
					{
						int pkid = int.Parse(model.DataPismoFlexAssy.FlexAssy_06_Cognex_Detail.Rows[0]["CognexPkid"].ToString());
						var CognexPk = Singleton_07_Pismo.IFlexAssy_06_CognexService.FlexAssy_06_Cognex_GetByPkID(pkid);
						if (CognexPk?.Rows.Count > 0)
						{
							blockID = CognexPk.Rows[0]["BlockID"].ToString();
						}
					}
					model.DataPismoFlexAssy.FlexAssy_07_ECheck_Detail = Singleton_07_Pismo.IFlexAssy_07_ECheckService.FlexAssy_07_ECheck_GetByProductID(pcsID, "Pismo");
					// Lấy giá trị BlockID
					if (model.DataPismoFlexAssy.FlexAssy_07_ECheck_Detail?.Rows.Count > 0)
					{
						int pkid = int.Parse(model.DataPismoFlexAssy.FlexAssy_07_ECheck_Detail.Rows[0]["ECheckPkid"].ToString());
						var ECheckPk = Singleton_07_Pismo.IFlexAssy_07_ECheckService.FlexAssy_07_ECheck_GetByPkID(pkid);
						if (ECheckPk?.Rows.Count > 0)
						{
							blockID = ECheckPk.Rows[0]["BlockID"].ToString();
						}
					}
					model.DataPismoFlexAssy.FlexAssy_Coper_Detail = Singleton_07_Pismo.IFlexAssy_CoperService2025.FlexAssy_Coper_GetByProductID(pcsID, "Pismo");
					// Lấy giá trị BlockID
					if (model.DataPismoFlexAssy.FlexAssy_Coper_Detail?.Rows.Count > 0)
					{
						int pkid = int.Parse(model.DataPismoFlexAssy.FlexAssy_Coper_Detail.Rows[0]["CoperPkid"].ToString());
						var Coper = Singleton_07_Pismo.IFlexAssy_CoperService2025.FlexAssy_Coper_GetByPkID(pkid);
						if (Coper?.Rows.Count > 0)
						{
							blockID = Coper.Rows[0]["BlockID"].ToString();
						}
					}
					model.DataPismoFlexAssy.FlexAssy_Laser_Detail = Singleton_07_Pismo.IFlexAssy_LaserService2025.FlexAssy_Laser_GetByProductID(pcsID, "Pismo");
					// Lấy giá trị BlockID
					if (model.DataPismoFlexAssy.FlexAssy_Laser_Detail?.Rows.Count > 0)
					{
						int pkid = int.Parse(model.DataPismoFlexAssy.FlexAssy_Laser_Detail.Rows[0]["LaserPkid"].ToString());
						var Laser = Singleton_07_Pismo.IFlexAssy_LaserService2025.FlexAssy_Laser_GetByPkID(pkid);
						if (Laser?.Rows.Count > 0)
						{
							blockID = Laser.Rows[0]["BlockID"].ToString();
						}
					}
					model.DataPismoFlexAssy.FlexAssy_TraceAOI_Detail = Singleton_07_Pismo.IFlexAssy_TraceAOIServices.FlexAssy_TraceAOI_GetByProductID(pcsID, "Pismo");
					// Lấy giá trị BlockID
					if (model.DataPismoFlexAssy.FlexAssy_TraceAOI_Detail?.Rows.Count > 0)
					{
						int pkid = int.Parse(model.DataPismoFlexAssy.FlexAssy_TraceAOI_Detail.Rows[0]["TraceAOIPkid"].ToString());
						var TraceAOI = Singleton_07_Pismo.IFlexAssy_TraceAOIServices.FlexAssy_TraceAOI_GetByPkID(pkid);
						if (TraceAOI?.Rows.Count > 0)
						{
							blockID = TraceAOI.Rows[0]["BlockID"].ToString();
						}
					}
					#endregion
				}

				if (blockID.Length != 0)
				{
					//AVI
					model.DataBareFlex.BareFlex_21_FinalQC_Block = Singleton_01_BareFlex.IBareFlex_21_FinalQCService.BareFlex_21_FinalQC_GetByBlock(blockID);
					if (model.DataBareFlex.BareFlex_21_FinalQC_Block?.Rows.Count > 0)
					{
						IndicationIDAVI = model.DataBareFlex.BareFlex_21_FinalQC_Block.Rows[0]["IndicationID"].ToString();
					}

					model.DataFlexAssy.FlexAssy_10_Info_Pasting = Singleton_02_FlexAssy.IFlexAssy_10_Info_PastingService.FlexAssy_10_Info_Pasting_GetByBlockID(blockID, connectionStringOption = connectionStringOk, out connectionStringOk);
					model.DataFlexAssy.FlexAssy_10_Info_Pasting_Detail_DataPcs_BlockID = Singleton_02_FlexAssy.IFlexAssy_10_Info_PastingService.FlexAssy_10_Info_Pasting_Detail_DataPcs_GetByBlockID(blockID, connectionStringOption = connectionStringOk, out connectionStringOk);

					#region data punchinglinking
					model.DataFlexAssy.FlexAssy_19_PunchingLinking = Singleton_02_FlexAssy.IFlexAssy_19_PunchingService.FlexAssy_19_PunchingLinking_GetByBlockID(blockID, connectionStringOption = connectionStringOk);
					if (model.DataFlexAssy.FlexAssy_19_PunchingLinking?.Rows.Count > 0)
					{
						var blockIDMap = model.DataFlexAssy.FlexAssy_19_PunchingLinking.Rows[0]["BlockIDMap"].ToString();
						blockID = model.DataFlexAssy.FlexAssy_19_PunchingLinking.Rows[0]["BlockID"].ToString();
						model.DataFlexAssy.FlexAssy_19_Punching = Singleton_02_FlexAssy.IFlexAssy_19_PunchingService.FlexAssy_19_Punching_GetByBlockIDOrBlockIDMap(blockID, blockIDMap, connectionStringOption = connectionStringOk, out connectionStringOk);
						if (pcsID.Length != 0)
						{
							//tim pcs nay thuoc block nao
							model.DataFlexAssy.FlexAssy_19_PunchingLinking_PunDataManual = Singleton_02_FlexAssy.IFlexAssy_19_PunchingService.FlexAssy_19_PunchingLinking_PunDataManual_GetByPcs(pcsID, connectionStringOption = connectionStringOk);
						}
					}
					else
					{
						model.DataFlexAssy.FlexAssy_19_Punching = Singleton_02_FlexAssy.IFlexAssy_19_PunchingService.FlexAssy_19_Punching_GetByBlockID(blockID, connectionStringOption = connectionStringOk, out connectionStringOk);
					}
					if (model.DataFlexAssy.FlexAssy_19_Punching?.Rows.Count > 0)
					{
						PunchMachineID = model.DataFlexAssy.FlexAssy_19_Punching.Rows[0]["MachineID"].ToString();
					}
					#endregion
					model.DataFlexAssy.FlexAssy_01_FPCBaking = Singleton_02_FlexAssy.IFlexAssy_01_FPCBakingService.FlexAssy_01_FPCBaking_GetByBlockID(blockID, connectionStringOption = connectionStringOk, out connectionStringOk);
					model.DataFlexAssy.FlexAssy_01_FPCBaking_Cleaning = Singleton_02_FlexAssy.IFlexAssy_01_FPCBaking_CleaningService.FlexAssy_01_FPCBaking_Cleaning_GetByBlockID(blockID, connectionStringOption = connectionStringOk, out connectionStringOk);
					model.DataFlexAssy.FlexAssy_01_Surface_Cleaning = Singleton_02_FlexAssy.IFlexAssy_01_Surface_CleaningService.FlexAssy_01_Surface_Cleaning_GetByBlockID(blockID, connectionStringOption = connectionStringOk, out connectionStringOk);
					model.DataFlexAssy.FlexAssy_02_SMTFPCPanelLoading = Singleton_02_FlexAssy.IFlexAssy_02_SMTFPCPanelLoadingService.FlexAssy_02_SMTFPCPanelLoading_GetByBlockID(blockID, connectionStringOption = connectionStringOk, out connectionStringOk);

					if (model.DataFlexAssy.FlexAssy_02_SMTFPCPanelLoading?.Rows.Count > 0)
					{
						if ((model.DataFlexAssy.FlexAssy_02_SMTFPCPanelLoading.Rows[0]["MachineID"] + "").ToUpper().Trim().Equals("MOLD002"))
						{
							laneID = "2";
						}
					}
					
					model.DataFlexAssy.FlexAssy_02_FPCPasting = Singleton_02_FlexAssy.IFlexAssy_02_SMTFPCPanelLoadingService.FlexAssy_02_FPCPasting_GetByBlockID(blockID, connectionStringOption = connectionStringOk, out connectionStringOk);
					model.DataFlexAssy.FlexAssy_03_SolderPastePrinting = Singleton_02_FlexAssy.IFlexAssy_03_SolderPastePrintingService.FlexAssy_03_SolderPastePrinting_GetByBlockID(blockID, connectionStringOption = connectionStringOk, out connectionStringOk);
					model.DataFlexAssy.FlexAssy_03_SolderPastePrinting_List = Singleton_02_FlexAssy.IFlexAssy_03_SolderPastePrintingService.FlexAssy_03_SolderPastePrinting_List_GetByBlockID(blockID, connectionStringOption = connectionStringOk);
					if (model.DataFlexAssy.FlexAssy_03_SolderPastePrinting_List?.Rows.Count > 0)
					{
						for (int count = 0; count < model.DataFlexAssy.FlexAssy_03_SolderPastePrinting_List?.Rows.Count; count++)
						{
							ListComp.Rows.Add(model.DataFlexAssy.FlexAssy_03_SolderPastePrinting_List?.Rows[count]["SolderPasteID"]);
						}
					}
					if (model.DataFlexAssy.FlexAssy_03_SolderPastePrinting?.Rows.Count > 0)
					{
						for (int count = 0; count < model.DataFlexAssy.FlexAssy_03_SolderPastePrinting?.Rows.Count; count++)
						{
							ListComp.Rows.Add(model.DataFlexAssy.FlexAssy_03_SolderPastePrinting.Rows[count]["StencilID"]);
							ListComp.Rows.Add(model.DataFlexAssy.FlexAssy_03_SolderPastePrinting.Rows[count]["FSqueegeeID"]);
						}
					}
					model.DataFlexAssy.FlexAssy_04_SolderPasteInspection = Singleton_02_FlexAssy.IFlexAssy_04_SolderPasteInspectionService.FlexAssy_04_SolderPasteInspection_GetByBlockID(blockID, connectionStringOption = connectionStringOk, out connectionStringOk);
					model.DataFlexAssy.FlexAssy_04_SolderPasteInspection_Image2D = Common.GetImageNG2D("FlexAssy_04_SolderPasteInspection", blockID, model.DataFlexAssy.FlexAssy_04_SolderPasteInspection, connectionStringOption = connectionStringOk);
					model.DataFlexAssy.FlexAssy_05_PickAndPlace = Singleton_02_FlexAssy.IFlexAssy_05_PickAndPlaceService.FlexAssy_05_PickAndPlace_GetByBlockID(blockID, connectionStringOption = connectionStringOk, out connectionStringOk);
					//model.DataFlexAssy.FlexAssy_05_PickAndPlace_Group = Singleton_02_FlexAssy.IFlexAssy_05_PickAndPlaceService.FlexAssy_05_PickAndPlace_Group_GetByBlockID(blockID, connectionStringOption = connectionStringOk);
					model.DataFlexAssy.FlexAssy_05_PickAndPlace_Group = Singleton_02_FlexAssy.IFlexAssy_05_PickAndPlaceService.FlexAssy_05_PickAndPlace_MounterDataPcs_Group_GetByBlockID(blockID, connectionStringOption = connectionStringOk);
					if (model.DataFlexAssy.FlexAssy_05_PickAndPlace_Group == null || model.DataFlexAssy.FlexAssy_05_PickAndPlace_Group?.Rows.Count == 0)
					{
						model.DataFlexAssy.FlexAssy_05_PickAndPlace_Group = Singleton_02_FlexAssy.IFlexAssy_05_PickAndPlaceService.FlexAssy_05_PickAndPlace_Group_GetByBlockID(blockID, connectionStringOption = connectionStringOk);
					}
					//
					#region FlexAssy_05_PickAndPlace_Group detail FeederID by pcsID
					try
					{
						if (pcsID.Length != 0)
						{
							//tim pcs nay thuoc block nao
							//model.DataFlexAssy.FlexAssy_05_PickAndPlace_MounterDataPcs = Singleton_02_FlexAssy.IFlexAssy_05_PickAndPlaceService.FlexAssy_05_PickAndPlace_MounterDataPcs_GetByBlockID(blockID, connectionStringOption = connectionStringOk);
							// lay list product trong laserMarking tu blockid
							var listpcsbyBlockIDinFlexAssy_08_LaserMarking = Singleton_02_FlexAssy.IFlexAssy_08_LaserMarkingService.FlexAssy_08_LaserMarking_GetAllByBlockID(blockID, connectionStringOption = connectionStringOk);
							if (listpcsbyBlockIDinFlexAssy_08_LaserMarking.Rows.Count > 0)
							{
								// lay product trong laserMarking
								listpcsbyBlockIDinFlexAssy_08_LaserMarking = listpcsbyBlockIDinFlexAssy_08_LaserMarking.Select("ProductID ='" + pcsID + "'").CopyToDataTable();
								#region process data
								if (listpcsbyBlockIDinFlexAssy_08_LaserMarking.Rows.Count > 0)
								{
									//listpcsbyBlockIDinFlexAssy_08_LaserMarking
									var _FlexAssy_05_PickAndPlace_MounterDataPcs = Singleton_02_FlexAssy.IFlexAssy_05_PickAndPlaceService.FlexAssy_05_PickAndPlace_MounterDataPcs_GetByBlockID(blockID, connectionStringOption = connectionStringOk);
									model.DataFlexAssy.FlexAssy_05_PickAndPlace_MounterDataPcs = _FlexAssy_05_PickAndPlace_MounterDataPcs;
									var FeederPosition = "";
									int Side = 1;
									if (_FlexAssy_05_PickAndPlace_MounterDataPcs != null && _FlexAssy_05_PickAndPlace_MounterDataPcs.Rows.Count > 0)
									{
										int locationlaser = 0;
										try
										{
											var strLocationLaser = listpcsbyBlockIDinFlexAssy_08_LaserMarking.Rows[0]["Location"].ToString();
											locationlaser = int.Parse(strLocationLaser.Split(',')[0] + "");
										}
										catch { locationlaser = 0; }

										var lst_FlexAssy_05_PickAndPlace_Group_Model = new List<Trace_AbilitySystem.Libs.DTOClass.FlexAssyModel.FlexAssy_05_PickAndPlace_Group_Model>();

										if (locationlaser > 0)
										{
											var strWhere = "BlockID= '" + blockID + "' and LocationLaser =" + locationlaser;
											var dtmouterPCS = _FlexAssy_05_PickAndPlace_MounterDataPcs.Select(strWhere);
											if (dtmouterPCS.Length > 0)
											{
												_FlexAssy_05_PickAndPlace_MounterDataPcs = dtmouterPCS.CopyToDataTable();
											}
											else
											{
												_FlexAssy_05_PickAndPlace_MounterDataPcs.Clear();
											}
											if (_FlexAssy_05_PickAndPlace_MounterDataPcs.Rows.Count > 0)
											{
												var _PartLocationStation = string.Empty;
												var _PartLocationSLot = string.Empty;
												for (int i = 0; i < _FlexAssy_05_PickAndPlace_MounterDataPcs.Rows.Count; i++)
												{
													FeederPosition = _FlexAssy_05_PickAndPlace_MounterDataPcs.Rows[i]["FeederPosition"].ToString();
													if (!string.IsNullOrEmpty(FeederPosition))
													{
														strWhere = "FeederPosition = '" + FeederPosition + "'";
													}
													Side = int.Parse(_FlexAssy_05_PickAndPlace_MounterDataPcs.Rows[i]["Side"].ToString());
													strWhere += " and Side=" + Side;
													var machineid = _FlexAssy_05_PickAndPlace_MounterDataPcs.Rows[i]["MachineID"].ToString();
													strWhere += " and MachineID = '" + machineid + "'";
													//var dr = model.DataFlexAssy.FlexAssy_05_PickAndPlace_Group.Select(strWhere);
													var dr = _FlexAssy_05_PickAndPlace_MounterDataPcs.Select(strWhere);
													var _FlexAssy_05_PickAndPlace_Group_meta = new DataTable();
													if (dr != null && dr.Length > 0)
													{
														_FlexAssy_05_PickAndPlace_Group_meta = dr.CopyToDataTable();
													}
													var item = new Trace_AbilitySystem.Libs.DTOClass.FlexAssyModel.FlexAssy_05_PickAndPlace_Group_Model();
													if (_FlexAssy_05_PickAndPlace_Group_meta != null && _FlexAssy_05_PickAndPlace_Group_meta.Rows.Count > 0)
													{
														item.BlockID = _FlexAssy_05_PickAndPlace_Group_meta.Rows[0]["BlockID"] + "";
														item.MachineID = machineid;
														item.ReelID = _FlexAssy_05_PickAndPlace_Group_meta.Rows[0]["ReelID"] + "";
														var PartLocationStationBase = FeederPosition.Split('0');
														if (!string.IsNullOrEmpty(PartLocationStationBase[0])
															&& !string.IsNullOrEmpty(PartLocationStationBase[PartLocationStationBase.Length - 1]))
														{
															if (FeederPosition.Length > 5)
															{
																item.PartLocationStation = FeederPosition.Substring(0, 2);
															}
															else
															{
																item.PartLocationStation = PartLocationStationBase[0];
															}
															item.PartLocationSLot = PartLocationStationBase[PartLocationStationBase.Length - 1];
														}
														var subslot = Side + "";
														if (subslot == "0")
														{
															subslot = "1";
														}
														item.PartLocationSubSlot = subslot;
														item.PartName = _FlexAssy_05_PickAndPlace_Group_meta.Rows[0]["PartName"] + "";
														item.MakerName = _FlexAssy_05_PickAndPlace_Group_meta.Rows[0]["MakerName"] + "";
														item.PartLotMaker = _FlexAssy_05_PickAndPlace_Group_meta.Rows[0]["PartLotMaker"] + "";
														item.FeederID = _FlexAssy_05_PickAndPlace_Group_meta.Rows[0]["FeederID"] + "";
														item.FeederNozzleRateTotalPickup = _FlexAssy_05_PickAndPlace_Group_meta.Rows[0]["FeederNozzleRateTotalPickup"] + "";
														item.FeederNozzleRateTotalError = _FlexAssy_05_PickAndPlace_Group_meta.Rows[0]["FeederNozzleRateTotalError"] + "";
														item.FeederMaintainDate = _FlexAssy_05_PickAndPlace_Group_meta.Rows[0]["FeederMaintainDate"] + "";
														item.OperatorID = _FlexAssy_05_PickAndPlace_Group_meta.Rows[0]["OperatorID"] + "";
														item.NozzleType = _FlexAssy_05_PickAndPlace_MounterDataPcs.Rows[i]["NozzleType"] + "";
														item.NozzlePosition = _FlexAssy_05_PickAndPlace_MounterDataPcs.Rows[i]["NozzlePosition"] + "";
														item.CReferenceIDs = _FlexAssy_05_PickAndPlace_MounterDataPcs.Rows[i]["CReferenceIDs"] + "";
														item.LaneID = _FlexAssy_05_PickAndPlace_MounterDataPcs.Rows[i]["LaneID"] + "";
														item.DateBlock = (_FlexAssy_05_PickAndPlace_MounterDataPcs.Rows[i]["DateBlock"] != DBNull.Value) ? DateTime.Parse(_FlexAssy_05_PickAndPlace_MounterDataPcs.Rows[i]["DateBlock"] + "") : DateTime.MinValue;
														item.LocationMounter = _FlexAssy_05_PickAndPlace_MounterDataPcs.Rows[i]["LocationMounter"].ToString();
														item.NozzleID = _FlexAssy_05_PickAndPlace_MounterDataPcs.Rows[i]["NozzleID"].ToString();
														item.NozzleMaintainDate = _FlexAssy_05_PickAndPlace_MounterDataPcs.Rows[i]["NozzleMaintainDate"].ToString();
														item.ReelTotalPickup = DataConvert.ConvertToInt(_FlexAssy_05_PickAndPlace_MounterDataPcs.Rows[i]["ReelTotalPickup"]);
														item.ReelTotalError = DataConvert.ConvertToInt(_FlexAssy_05_PickAndPlace_MounterDataPcs.Rows[i]["ReelTotalError"]);
														item.NozzleTotalPickup = DataConvert.ConvertToInt(_FlexAssy_05_PickAndPlace_MounterDataPcs.Rows[i]["NozzleTotalPickup"]);
														item.NozzleTotalError = DataConvert.ConvertToInt(_FlexAssy_05_PickAndPlace_MounterDataPcs.Rows[i]["NozzleTotalError"]);
														item.MakerSupplierID = _FlexAssy_05_PickAndPlace_MounterDataPcs.Rows[i]["MakerSupplierID"] + "";
														item.DateCode = (_FlexAssy_05_PickAndPlace_MounterDataPcs.Rows[i]["DateCode"] != DBNull.Value) ? DateTime.Parse(_FlexAssy_05_PickAndPlace_MounterDataPcs.Rows[i]["DateCode"] + "") : DateTime.MinValue;
														item.Vendor = _FlexAssy_05_PickAndPlace_MounterDataPcs.Rows[i]["Vendor"] + "";
														item.LotCode = _FlexAssy_05_PickAndPlace_MounterDataPcs.Rows[i]["LotCode"] + "";
														item.MSD_FloorLife = (_FlexAssy_05_PickAndPlace_MounterDataPcs.Rows[i]["MSD_FloorLife"] != DBNull.Value) ? DateTime.Parse(_FlexAssy_05_PickAndPlace_MounterDataPcs.Rows[i]["MSD_FloorLife"] + "") : DateTime.MinValue;
														item.CompQuantity = DataConvert.ConvertToInt(_FlexAssy_05_PickAndPlace_MounterDataPcs.Rows[i]["CompQuantity"]);
														lst_FlexAssy_05_PickAndPlace_Group_Model.Add(item);
														ListComp.Rows.Add(item.ReelID);
													}
												}
												model.DataFlexAssy.FlexAssy_05_PickAndPlace_Group_Models = lst_FlexAssy_05_PickAndPlace_Group_Model;
											}

										}
									}
								}
								#endregion
							}
						}
						else
						{
							#region input list

							if (model.DataFlexAssy.FlexAssy_05_PickAndPlace_Group?.Rows.Count > 0)
							{
								var lst_FlexAssy_05_PickAndPlace_Group_Model = new List<Trace_AbilitySystem.Libs.DTOClass.FlexAssyModel.FlexAssy_05_PickAndPlace_Group_Model>();
								for (int i = 0; i < model.DataFlexAssy.FlexAssy_05_PickAndPlace_Group.Rows.Count; i++)
								{
									var item = new Trace_AbilitySystem.Libs.DTOClass.FlexAssyModel.FlexAssy_05_PickAndPlace_Group_Model();
									item.BlockID = model.DataFlexAssy.FlexAssy_05_PickAndPlace_Group.Rows[i]["BlockID"] + "";
									item.MachineID = model.DataFlexAssy.FlexAssy_05_PickAndPlace_Group.Rows[i]["MachineID"] + "";
									item.ReelID = model.DataFlexAssy.FlexAssy_05_PickAndPlace_Group.Rows[i]["ReelID"] + "";
									var FeederPosition = "";
									try
									{
										FeederPosition = model.DataFlexAssy.FlexAssy_05_PickAndPlace_Group.Rows[i]["FeederPosition"] + "";
									}
									catch (Exception)
									{
									}
									if (!string.IsNullOrEmpty(FeederPosition))
									{
										var PartLocationStationBase = FeederPosition.Split('0');
										if (!string.IsNullOrEmpty(PartLocationStationBase[0])
											&& !string.IsNullOrEmpty(PartLocationStationBase[PartLocationStationBase.Length - 1]))
										{
											if (FeederPosition.Length > 5)
											{
												item.PartLocationStation = FeederPosition.Substring(0, 2);
											}
											else
											{
												item.PartLocationStation = PartLocationStationBase[0];
											}
											item.PartLocationSLot = PartLocationStationBase[PartLocationStationBase.Length - 1];
										}
										var subslot = model.DataFlexAssy.FlexAssy_05_PickAndPlace_Group.Rows[i]["Side"].ToString();
										if (subslot == "0")
										{
											subslot = "1";
										}
										item.PartLocationSubSlot = subslot;
									}
									else
									{
										item.PartLocationStation = model.DataFlexAssy.FlexAssy_05_PickAndPlace_Group.Rows[i]["PartLocationStation"] + "";
										item.PartLocationSLot = model.DataFlexAssy.FlexAssy_05_PickAndPlace_Group.Rows[i]["PartLocationSLot"] + "";
										item.PartLocationSubSlot = (model.DataFlexAssy.FlexAssy_05_PickAndPlace_Group.Rows[i]["PartLocationSubSlot"] + "") == "" ? "1" : (model.DataFlexAssy.FlexAssy_05_PickAndPlace_Group.Rows[i]["PartLocationSubSlot"] + "");
									}
									//var FeederPosition = model.DataFlexAssy.FlexAssy_05_PickAndPlace_Group.Rows[i]["FeederPosition"].ToString();
									//var PartLocationStationBase = FeederPosition.Split('0');
									//if (!string.IsNullOrEmpty(PartLocationStationBase[0])
									//    && !string.IsNullOrEmpty(PartLocationStationBase[PartLocationStationBase.Length - 1]))
									//{
									//    item.PartLocationStation = PartLocationStationBase[0];
									//    item.PartLocationSLot = PartLocationStationBase[PartLocationStationBase.Length - 1];
									//}


									item.PartName = model.DataFlexAssy.FlexAssy_05_PickAndPlace_Group.Rows[i]["PartName"] + "";
									item.MakerName = model.DataFlexAssy.FlexAssy_05_PickAndPlace_Group.Rows[i]["MakerName"] + "";
									item.PartLotMaker = model.DataFlexAssy.FlexAssy_05_PickAndPlace_Group.Rows[i]["PartLotMaker"] + "";
									item.FeederID = model.DataFlexAssy.FlexAssy_05_PickAndPlace_Group.Rows[i]["FeederID"] + "";
									item.FeederNozzleRateTotalPickup = model.DataFlexAssy.FlexAssy_05_PickAndPlace_Group.Rows[i]["FeederNozzleRateTotalPickup"] + "";
									item.FeederNozzleRateTotalError = model.DataFlexAssy.FlexAssy_05_PickAndPlace_Group.Rows[i]["FeederNozzleRateTotalError"] + "";
									item.FeederMaintainDate = model.DataFlexAssy.FlexAssy_05_PickAndPlace_Group.Rows[i]["FeederMaintainDate"] + "";
									item.OperatorID = model.DataFlexAssy.FlexAssy_05_PickAndPlace_Group.Rows[i]["OperatorID"] + "";
									item.DateCode = !string.IsNullOrEmpty(model.DataFlexAssy.FlexAssy_05_PickAndPlace_Group.Rows[i]["DateCode"] + "") ? DateTime.Parse(model.DataFlexAssy.FlexAssy_05_PickAndPlace_Group.Rows[i]["DateCode"] + "") : DateTime.MinValue;
									item.Vendor = model.DataFlexAssy.FlexAssy_05_PickAndPlace_Group.Rows[i]["Vendor"] + "";
									item.LotCode = model.DataFlexAssy.FlexAssy_05_PickAndPlace_Group.Rows[i]["LotCode"] + "";
									item.MSD_FloorLife = !string.IsNullOrEmpty(model.DataFlexAssy.FlexAssy_05_PickAndPlace_Group.Rows[i]["MSD_FloorLife"] + "") ? DateTime.Parse(model.DataFlexAssy.FlexAssy_05_PickAndPlace_Group.Rows[i]["MSD_FloorLife"] + "") : DateTime.MinValue;
									item.CompQuantity = !string.IsNullOrEmpty(model.DataFlexAssy.FlexAssy_05_PickAndPlace_Group.Rows[i]["CompQuantity"] + "") ? DataConvert.ConvertToInt(model.DataFlexAssy.FlexAssy_05_PickAndPlace_Group.Rows[i]["CompQuantity"]) : 0;
									try
									{
										item.LaneID = model.DataFlexAssy.FlexAssy_05_PickAndPlace_Group.Rows[i]["LaneID"] + "";
									}
									catch (Exception)
									{
									}
									item.ReelTotalPickup = DataConvert.ConvertToInt(model.DataFlexAssy.FlexAssy_05_PickAndPlace_Group.Rows[i]["ReelTotalPickup"]);
									item.ReelTotalError = DataConvert.ConvertToInt(model.DataFlexAssy.FlexAssy_05_PickAndPlace_Group.Rows[i]["ReelTotalError"]);
									item.MakerSupplierID = model.DataFlexAssy.FlexAssy_05_PickAndPlace_Group.Rows[i]["MakerSupplierID"] + "";
									//item.NozzleID = model.DataFlexAssy.FlexAssy_05_PickAndPlace_Group.Rows[i]["NozzleID"] + "";
									lst_FlexAssy_05_PickAndPlace_Group_Model.Add(item);

								}
								model.DataFlexAssy.FlexAssy_05_PickAndPlace_Group_Models = lst_FlexAssy_05_PickAndPlace_Group_Model;
							}
							#endregion
						}
					}
					catch (Exception ex)
					{
						ManageLog.WriteErrorWeb("Detail FeederID by pcsID: " + pcsID + "\n BlockID: " + blockID + "\n" + ex.ToString());
					}
					#endregion
					model.DataFlexAssy.FlexAssy_06_PreReflowAOI = Singleton_02_FlexAssy.IFlexAssy_06_PreReflowAOIService.FlexAssy_06_PreReflowAOI_GetByBlockID(blockID, connectionStringOption = connectionStringOk, out connectionStringOk);
					model.DataFlexAssy.FlexAssy_06_PreReflowAOI_Image2D = Common.GetImageNG2D("FlexAssy_06_PreReflowAOI", blockID, model.DataFlexAssy.FlexAssy_06_PreReflowAOI, connectionStringOption = connectionStringOk);
					model.DataFlexAssy.FlexAssy_07_Reflow = Singleton_02_FlexAssy.IFlexAssy_07_ReflowService.FlexAssy_07_Reflow_GetByBlockID(blockID, connectionStringOption = connectionStringOk, out connectionStringOk);
					model.DataFlexAssy.FlexAssy_07_Cooling = Singleton_02_FlexAssy.IFlexAssy_07_CoolingService.FlexAssy_07_Cooling_GetByBlockID(blockID, connectionStringOption = connectionStringOk, out connectionStringOk);
					if (connectionStringOption == "F5")
					{
						model.DataFlexAssy.FlexAssy_07_Reflow_HeatCure = Singleton_02_FlexAssy.IFlexAssy_07_Reflow_HeatCureService.FlexAssy_07_Reflow_HeatCure_GetByBlockID(blockID, connectionStringOption = connectionStringOk, out connectionStringOk);
					}
					model.DataFlexAssy.FlexAssy_09_PostReflowAOI = Singleton_02_FlexAssy.IFlexAssy_09_PostReflowAOIService.FlexAssy_09_PostReflowAOI_GetByBlockID(blockID, connectionStringOption = connectionStringOk, out connectionStringOk);
					model.DataFlexAssy.FlexAssy_09_PostReflowAOI_Image2D = Common.GetImageNG2D("FlexAssy_09_PostReflowAOI", blockID, model.DataFlexAssy.FlexAssy_09_PostReflowAOI, connectionStringOption = connectionStringOk);

					//if (model.DataFlexAssy.FlexAssy_10_Info_Pasting_Detail_DataPcs?.Rows.Count > 0)
					//{
					//    string blockIDPasting = model.DataFlexAssy.FlexAssy_10_Info_Pasting_Detail_DataPcs.Rows[0]["BlockID"].ToString();
					//    model.DataFlexAssy.FlexAssy_10_Info_Pasting = Singleton_02_FlexAssy.IFlexAssy_10_Info_PastingService.FlexAssy_10_Info_Pasting_GetByBlockID(blockIDPasting, connectionStringOption = connectionStringOk, out connectionStringOk);
					//    model.DataFlexAssy.FlexAssy_10_Info_Pasting_Detail_DataPcs_BlockID = Singleton_02_FlexAssy.IFlexAssy_10_Info_PastingService.FlexAssy_10_Info_Pasting_Detail_DataPcs_GetByBlockID(blockIDPasting, connectionStringOption = connectionStringOk, out connectionStringOk);
					//}
					//else
					//{
					//    model.DataFlexAssy.FlexAssy_10_Info_Pasting = Singleton_02_FlexAssy.IFlexAssy_10_Info_PastingService.FlexAssy_10_Info_Pasting_GetByBlockID(blockID, connectionStringOption = connectionStringOk, out connectionStringOk);
					//    model.DataFlexAssy.FlexAssy_10_Info_Pasting_Detail_DataPcs_BlockID = Singleton_02_FlexAssy.IFlexAssy_10_Info_PastingService.FlexAssy_10_Info_Pasting_Detail_DataPcs_GetByBlockID(blockID, connectionStringOption = connectionStringOk, out connectionStringOk);
					//}        
					model.DataFlexAssy.FlexAssy_10_Lamination = Singleton_02_FlexAssy.IFlexAssy_10_LaminationService.FlexAssy_10_Lamination_GetByBlockID(blockID, connectionStringOption = connectionStringOk, out connectionStringOk);

					if (pcsID.Length == 0)
					{
						model.DataFlexAssy.FlexAssy_10_Lamination_QYC = Singleton_02_FlexAssy.IFlexAssy_10_LaminationService.FlexAssy_10_Lamination_QYC_GetByBlockID(blockID, connectionStringOption = connectionStringOk, out connectionStringOk);
					}

					if (model.DataFlexAssy.FlexAssy_10_Lamination.Rows.Count > 0)
					{
						string LaminationToolType = model.DataFlexAssy.FlexAssy_10_Lamination.Rows[0]["ToolType"].ToString();
						if (LaminationToolType != "")
						{
							LaminationToolType = LaminationToolType.Substring(0, LaminationToolType.Length - 1);
						}
						model.DataFlexAssy.FlexAssy_10_Lamination_ToolType = Singleton_02_FlexAssy.IFlexAssy_10_LaminationService.FlexAssy_10_Lamination_ToolType_GetByBlockID(LaminationToolType, connectionStringOption = connectionStringOk, out connectionStringOk);
					}

					model.DataFlexAssy.FlexAssy_10_XrayInspection = Singleton_02_FlexAssy.IFlexAssy_10_XrayInspectionService.FlexAssy_10_XrayInspection_GetByBlockID(blockID, connectionStringOption = connectionStringOk, out connectionStringOk);

					model.DataFlexAssy.FlexAssy_10_XrayInspection_Componet = Singleton_02_FlexAssy.IFlexAssy_10_XrayInspectionService.FlexAssy_10_XrayInspection_Component_GetByBlockID_v2(blockID, connectionStringOption = connectionStringOk, out connectionStringOk);
					model.DataFlexAssy.FlexAssy_10_XrayInspection_NG_Image = Singleton_02_FlexAssy.IFlexAssy_10_XrayInspectionService.FlexAssy_10_XrayInspection_NG_Image_GetByBlockID_v2(blockID, connectionStringOption = connectionStringOk, out connectionStringOk);

					if (model.DataFlexAssy.FlexAssy_10_XrayInspection.Rows?.Count > 0)
					{
						for (int count = 0; count < model.DataFlexAssy.FlexAssy_10_XrayInspection?.Rows.Count; count++)
						{
							string machineID = model.DataFlexAssy.FlexAssy_10_XrayInspection.Rows[0]["MachineID"].ToString();
							string programName = model.DataFlexAssy.FlexAssy_10_XrayInspection.Rows[0]["ProgramName"].ToString();
							DataTable Sample = Singleton_02_FlexAssy.IFlexAssy_10_XrayInspectionService.FlexAssy_10_XrayInspection_Sample_GetDataWithMachineIDAndProgramName(machineID, programName, DataConvert.ConvertFieldToDateTime(model.DataFlexAssy.FlexAssy_10_XrayInspection.Rows[0], "DateTime"), connectionStringOk);
							if (Sample != null)
							{
								if (model.DataFlexAssy.FlexAssy_10_XrayInspection_Sample != null)
								{
									model.DataFlexAssy.FlexAssy_10_XrayInspection_Sample.Merge(Sample);
								}
								else
								{
									model.DataFlexAssy.FlexAssy_10_XrayInspection_Sample = Sample;
								}
							}
						}
						model.DataFlexAssy.FlexAssy_10_XrayInspection_Image2D = Common.GetImageNG2D("FlexAssy_10_XrayInspection", blockID, model.DataFlexAssy.FlexAssy_10_XrayInspection, connectionStringOption = connectionStringOk);
					}
					//model.DataFlexAssy.FlexAssy_10_XrayInspection_Image2D = Singleton_02_FlexAssy.IFlexAssy_10_XrayInspectionService.FlexAssy_10_XrayInspection_NG_Image_GetByBlockID(blockID, connectionStringOption);
					model.DataFlexAssy.FlexAssy_11_GlueDispensing = Singleton_02_FlexAssy.IFlexAssy_11_GlueDispensingService.FlexAssy_11_GlueDispensing_GetByBlockID(blockID, connectionStringOption = connectionStringOk, out connectionStringOk);
					model.DataFlexAssy.FlexAssy_11_GlueDispensing_Material = Singleton_02_FlexAssy.IFlexAssy_11_GlueDispensingService.FlexAssy_11_GlueDispensing_Material_GetByBlockID(blockID, connectionStringOption = connectionStringOk);
					model.DataFlexAssy.FlexAssy_11_6_Lamination_ByInfoPasting = Singleton_02_FlexAssy.IFlexAssy_10_LaminationService.FlexAssy_11_6_Lamination_ByInfoPasting_GetByBlockID(blockID, connectionStringOption = connectionStringOk, out connectionStringOk);
					model.DataFlexAssy.FlexAssy_12_GlueCuringHeatCure = Singleton_02_FlexAssy.IFlexAssy_12_GlueCuringHeatCureService.FlexAssy_12_GlueCuringHeatCure_GetByBlockID(blockID, connectionStringOption = connectionStringOk, out connectionStringOk);
					model.DataFlexAssy.FlexAssy_12_GlueCuringHeatCure_Material = Singleton_02_FlexAssy.IFlexAssy_12_GlueCuringHeatCureService.FlexAssy_12_GlueCuringHeatCure_Material_GetByBlockID(blockID, connectionStringOption = connectionStringOk);
					model.DataFlexAssy.FlexAssy_13_GlueCuringUVCure = Singleton_02_FlexAssy.IFlexAssy_13_GlueCuringUVCureService.FlexAssy_13_GlueCuringUVCure_GetByBlockID(blockID, connectionStringOption = connectionStringOk, out connectionStringOk);
					model.DataFlexAssy.FlexAssy_13_GlueCuringUVCure_Material = Singleton_02_FlexAssy.IFlexAssy_13_GlueCuringUVCureService.FlexAssy_13_GlueCuringUVCure_Material_GetByBlockID(blockID, connectionStringOption = connectionStringOk);
					model.DataFlexAssy.FlexAssy_14_BarcodePSATSAStiffenerPasting = Singleton_02_FlexAssy.IFlexAssy_14_BarcodePSATSAStiffenerPastingService.FlexAssy_14_BarcodePSATSAStiffenerPasting_GetByBlockID(blockID, connectionStringOption = connectionStringOk, out connectionStringOk);
					model.DataFlexAssy.FlexAssy_14_BarcodePSATSAStiffenerPasting_Material = Singleton_02_FlexAssy.IFlexAssy_14_BarcodePSATSAStiffenerPastingService.FlexAssy_14_BarcodePSATSAStiffenerPasting_Material_GetByBlockID(blockID, connectionStringOption = connectionStringOk);
					model.DataFlexAssy.FlexAssy_15_Bending = Singleton_02_FlexAssy.IFlexAssy_15_BendingService.FlexAssy_15_Bending_GetByBlockID(blockID, connectionStringOption = connectionStringOk, out connectionStringOk);
					//model.DataFlexAssy.FlexAssy_15_Bending_Material = Singleton_02_FlexAssy.IFlexAssy_15_BendingService.FlexAssy_15_Bending_Material_GetByBlockID(blockID, connectionStringOption = connectionStringOk);
					model.DataFlexAssy.FlexAssy_16_BarcodePSATSAStiffenerPasting = Singleton_02_FlexAssy.IFlexAssy_16_BarcodePSATSAStiffenerPastingService.FlexAssy_16_BarcodePSATSAStiffenerPasting_GetByBlockID(blockID, connectionStringOption = connectionStringOk, out connectionStringOk);
					model.DataFlexAssy.FlexAssy_18_CCD_Load = Singleton_02_FlexAssy.IFlexAssy_18_PanelAndSingleFPCBarcodeLinkService.FlexAssy_18_CCD_Load_GetByBlockID(blockID, connectionStringOption = connectionStringOk, out connectionStringOk);
					//model.DataFlexAssy.FlexAssy_19_Punching = Singleton_02_FlexAssy.IFlexAssy_19_PunchingService.FlexAssy_19_Punching_GetByBlockID(blockID, connectionStringOption = connectionStringOk, out connectionStringOk);

					#region Trace Pismo

					if (model.DataPismoFlexAssy.FlexAssy_01_TraceAOI == null)
					{
						model.DataPismoFlexAssy.FlexAssy_01_TraceAOI = Singleton_07_Pismo.IFlexAssy_01_TraceAOIService.FlexAssy_01_TraceAOI_GetByBlockID(blockID, "Pismo");
					}
					model.DataPismoFlexAssy.FlexAssy_01_AVI = Singleton_07_Pismo.IPismo_01_AVIServices.FlexAssy_01_AVI_GetByBlockID(blockID);
					model.DataPismoFlexAssy.FlexAssy_02_AVI_SUS = Singleton_07_Pismo.IPismo_02_AVI_SUSServices.FlexAssy_02_AVI_SUS_GetByBlockID(blockID);
					model.DataPismoFlexAssy.FlexAssy_03_LaserSUS = Singleton_07_Pismo.IPismo_03_LaserSUSServices.FlexAssy_03_LaserSUS_GetByBlockID(blockID);
					model.DataPismoFlexAssy.FlexAssy_03_LaserEMap = Singleton_07_Pismo.IFlexAssy_03_LaserEMapService.FlexAssy_03_LaserEMap_GetByBlockID(blockID, "Pismo");
					model.DataPismoFlexAssy.FlexAssy_05_ViaAOI = Singleton_07_Pismo.IFlexAssy_05_ViaAOIService.FlexAssy_05_ViaAOI_GetByBlockID(blockID, "Pismo");
					model.DataPismoFlexAssy.FlexAssy_06_Cognex = Singleton_07_Pismo.IFlexAssy_06_CognexService.FlexAssy_06_Cognex_GetByBlockID(blockID, "Pismo");
					model.DataPismoFlexAssy.FlexAssy_07_ECheck = Singleton_07_Pismo.IFlexAssy_07_ECheckService.FlexAssy_07_ECheck_GetByBlockID(blockID, "Pismo");
					model.DataPismoFlexAssy.FlexAssy_Coper = Singleton_07_Pismo.IFlexAssy_CoperService2025.FlexAssy_Coper_GetByBlockID(blockID, "Pismo");
					model.DataPismoFlexAssy.FlexAssy_Laser = Singleton_07_Pismo.IFlexAssy_LaserService2025.FlexAssy_Laser_GetByBlockID(blockID, "Pismo");
					model.DataPismoFlexAssy.FlexAssy_TraceAOI = Singleton_07_Pismo.IFlexAssy_TraceAOIServices.FlexAssy_TraceAOI_GetByBlockID(blockID, "Pismo");
					// Cắt theo logic Block Pismo Theo a Luân bảo
					if (string.IsNullOrEmpty(IndicationIDAVI))
					{
						if (blockID.Contains("-"))
						{
							var dt = Singleton_07_Pismo.IFlexAssy_01_TraceAOIService.FlexAssy_01_TraceAOI_GetIndicationByBlockID(blockID.Substring(0, 16), "PismoCommon");
							if (dt?.Rows.Count > 0)
							{
								IndicationIDAVI = dt.Rows[0]["LotName"].ToString();
							}
						}
					}
					#endregion

					// Lấy giá trị WorkOrder Mounting
					DataTable FlexAssy_BlockID = Singleton_02_FlexAssy.IFlexAssy_BlockIDService.FlexAssy_BlockID_GetByBlockID(blockID, connectionStringOption = connectionStringOk, out connectionStringOk);
					if (FlexAssy_BlockID?.Rows.Count > 0)
					{
						// neu la pismo 
						if (connectionStringOk == "Pismo")
						{
							string itemCode = FlexAssy_BlockID.Rows[0]["ItemCode"].ToString();
							// neu itemcode co ky tu thu 3 la chu cai' thi` 
							// mounting = itemcode+ itemlot
							if (itemCode.Length >= 3)
							{
								char thirdChar = itemCode[2];
								if (char.IsLetter(thirdChar))
								{
									mounting = FlexAssy_BlockID.Rows[0]["ItemCode"].ToString() + FlexAssy_BlockID.Rows[0]["ItemLot"].ToString();
								}
								// neu la chu so thi`
								// mounting = itemcode+00000B+itemlot
								else
								{
									string fixIndi = Singleton_02_FlexAssy.IFlexAssy_WorkOrderService.FlexAssy_WorkOrderGetIndiFixByItemNameItemCode(itemName, itemCode, connectionStringOk);

									mounting = FlexAssy_BlockID.Rows[0]["ItemCode"].ToString() + fixIndi + FlexAssy_BlockID.Rows[0]["ItemLot"].ToString();

								}
							}
						}
						else
						{
							mounting = FlexAssy_BlockID.Rows[0]["IndicationNumber"].ToString();
						}
					}
					// Lấy giá trị ItemName trong WorkOrder
					DataTable FlexAssy_WorkOrder = Singleton_02_FlexAssy.IFlexAssy_WorkOrderService.FlexAssy_WorkOrder_GetByWorkOrder(mounting, connectionStringOk);
					if (FlexAssy_WorkOrder?.Rows.Count > 0)
					{
						itemName = FlexAssy_WorkOrder.Rows[0]["ItemName"].ToString();
					}

					if (Request.Params["BlockID"] != null && Request.Params["BlockID"] != "")
					{
						if (Session["BlockID_Value"] == null)
						{
							connectionStringOk = PcsID_FromBlockID(blockID, connectionStringOk);
						}
						else if ((string)Session["BlockID_Value"] != Request.Params["BlockID"])
						{
							connectionStringOk = PcsID_FromBlockID(blockID, connectionStringOk);
						}

						else if (Session["PcsIDs"] == null)
						{
							//connectionStringOk = PcsID_FromBlockID(Request.Params["BlockID"], connectionStringOk);
							connectionStringOk = PcsID_FromBlockID(blockID, connectionStringOk);
						}
					}
				}

				// Upper-p: Indi front
				// Lower-p: Indi Back
				// Mounting: Indi F3, F4
				IPQC_Mounting_Search = mounting;
				if (workOrder.Length != 0 || mounting.Length != 0 || IndicationIDAVI.Length != 0)
				//if (workOrder.Length != 0 || mounting.Length != 0)
				{
					if (workOrder.Length == 0 && mounting.Length == 0)
					{
						model.DataBareFlex.BareFlex_WorkOrder = Singleton_01_BareFlex.IBareFlex_WorkOrderService.BareFlex_WorkOrder_GetByWorkOrder_IDLink(IndicationIDAVI, mounting.Length != 0 ? "Mounting" : null, connectionStringOk);
					}
					else
					{
						model.DataBareFlex.BareFlex_WorkOrder = Singleton_01_BareFlex.IBareFlex_WorkOrderService.BareFlex_WorkOrder_GetByWorkOrder_IDLink(mounting.Length != 0 ? mounting : workOrder, mounting.Length != 0 ? "Mounting" : null, connectionStringOk);
					}
					mounting = mounting.Length != 0 ? mounting : workOrder;
					if (model.DataBareFlex.BareFlex_WorkOrder?.Rows.Count > 0)
					{
						model.DataBareFlex.BareFlex_WorkOrder.Columns.Add("BlockID", typeof(string));
						model.DataBareFlex.BareFlex_WorkOrder.Columns.Add("ProductID", typeof(string));
						model.DataBareFlex.BareFlex_WorkOrder.Columns.Add("Factory", typeof(string));
						model.DataBareFlex.BareFlex_WorkOrder.Rows[0]["BlockID"] = blockID;
						model.DataBareFlex.BareFlex_WorkOrder.Rows[0]["ProductID"] = pcsID;
						model.DataBareFlex.BareFlex_WorkOrder.Rows[0]["Factory"] = connectionStringOk;

						upper_p = model.DataBareFlex.BareFlex_WorkOrder.Rows[0]["Upper_p"].ToString();
						lower_p = model.DataBareFlex.BareFlex_WorkOrder.Rows[0]["Lower_p"].ToString();
						if (model.DataBareFlex.BareFlex_WorkOrder.Rows[0]["Lower_p"] != DBNull.Value)
						{
							string[] strs = model.DataBareFlex.BareFlex_WorkOrder.Rows[0]["Lower_p"].ToString().Split(',');
							for (int i = 0; i < strs.Length; i++)
							{
								if (workOrder == strs[i])
								{
									lower_p = workOrder;
									break;
								}
								else
								{
									lower_p = strs[strs.Length - 1];
								}
							}
						}
						IPQC_Mounting_Search = mounting;
						mounting = model.DataBareFlex.BareFlex_WorkOrder.Rows[0]["Mounting"].ToString();
						// Lấy giá trị ItemName trong WorkOrder
						DataTable FlexAssy_WorkOrder = Singleton_02_FlexAssy.IFlexAssy_WorkOrderService.FlexAssy_WorkOrder_GetByWorkOrder(workOrder, connectionStringOk);
						if (FlexAssy_WorkOrder?.Rows.Count > 0)
						{
							itemName = FlexAssy_WorkOrder.Rows[0]["ItemName"].ToString();
						}
					}

					//ICT_FirstPassRate Get by Indi
					model.DataFlexAssy.FlexAssy_22_ICT_FirstPassRate_MPE = Singleton_02_FlexAssy.IFlexAssy_22_ICTService.FlexAssy_22_ICT_FirstPassRate_MPE_GetByIndi(mounting, connectionStringOk);
					DataTable dtWorkOrder = new DataTable();
					List<string> WorkOrderArr = new List<string>();
					WorkOrderArr.AddRange(model.DataBareFlex.BareFlex_WorkOrder.Rows[0]["Upper_p"].ToString().Split(','));
					WorkOrderArr.AddRange(model.DataBareFlex.BareFlex_WorkOrder.Rows[0]["Lower_p"].ToString().Split(','));
					WorkOrderArr.AddRange(model.DataBareFlex.BareFlex_WorkOrder.Rows[0]["Mounting"].ToString().Split(','));

					DataTable _dtWorkOrder = new DataTable();
					_dtWorkOrder.Columns.Add("WorkOrder", typeof(string));

					// Đổ dữ liệu từ list vào DataTable
					foreach (string _workOrder in WorkOrderArr)
					{
						_dtWorkOrder.Rows.Add(_workOrder);
					}

					model.DataBareFlex.BareFlex_Process_Block = Singleton_01_BareFlex.IBareFlex_Process_BlockService.BareFlex_Process_Block_GetByListWorkOrder(_dtWorkOrder);

					if (lower_p.Length != 0)
					{
						string[] LowerArr = model.DataBareFlex.BareFlex_WorkOrder.Rows[0]["Lower_p"].ToString().Split(',');
						foreach (string LowerItem in LowerArr)
						{
							GetLowerData(model, LowerItem);
						}
						if (model.DataBareFlex.BareFlex_20_ET?.Rows.Count > 0)
						{
							if (!string.IsNullOrEmpty(blockID))
							{
								model.DataBareFlex.BareFlex_20_ET_Block = Singleton_01_BareFlex.IBareFlex_20_ETService.BareFlex_20_ET_GetByBlock(blockID);
							}
							if (!string.IsNullOrEmpty(pcsID))
							{
								model.DataBareFlex.BareFlex_20_ET_Pcs = Singleton_01_BareFlex.IBareFlex_20_ETService.BareFlex_20_ET_GetByPcs(pcsID);
							}
						}
					}
					if (upper_p.Length != 0)
					{
						string[] UpperARR = model.DataBareFlex.BareFlex_WorkOrder.Rows[0]["Upper_p"].ToString().Split(',');
						foreach (string UpperItem in UpperARR)
						{
							GetUpperData(model, UpperItem);
						}
					}


					if (Request.Params["WorkOrder"] != null && Request.Params["WorkOrder"] != "")
					{
						if (Session["WorkOrder_Value"] == null)
						{
							connectionStringOk = BlockID_FromWorkOrder(Request.Params["WorkOrder"], connectionStringOk);
						}
						else if ((string)Session["WorkOrder_Value"] != Request.Params["WorkOrder"])
						{
							connectionStringOk = BlockID_FromWorkOrder(Request.Params["WorkOrder"], connectionStringOk);
						}
						else if (Session["BlockIDs"] == null)
						{
							connectionStringOk = BlockID_FromWorkOrder(Request.Params["WorkOrder"], connectionStringOk);
						}
						//connectionStringOk = BlockID_FromWorkOrder(Request.Params["WorkOrder"], connectionStringOk);

					}
				}
				else
				{
					model.DataBareFlex.BareFlex_WorkOrder = new DataTable();

					model.DataBareFlex.BareFlex_WorkOrder.Columns.Add("ItemName", typeof(string));
					model.DataBareFlex.BareFlex_WorkOrder.Columns.Add("Upper_p", typeof(string));
					model.DataBareFlex.BareFlex_WorkOrder.Columns.Add("Lower_p", typeof(string));
					model.DataBareFlex.BareFlex_WorkOrder.Columns.Add("Mounting", typeof(string));
					model.DataBareFlex.BareFlex_WorkOrder.Columns.Add("BlockID", typeof(string));
					model.DataBareFlex.BareFlex_WorkOrder.Columns.Add("ProductID", typeof(string));
					model.DataBareFlex.BareFlex_WorkOrder.Columns.Add("Vendor", typeof(string));
					model.DataBareFlex.BareFlex_WorkOrder.Columns.Add("ANP", typeof(string));
					model.DataBareFlex.BareFlex_WorkOrder.Columns.Add("Factory", typeof(string));

					var dtItemNameVendor1 = Singleton_03_Common.IItemName_VendorService.ItemName_Vendor_GetByitemName(itemName);
					if (dtItemNameVendor1 != null && dtItemNameVendor1.Rows.Count > 0)
					{
						VendorCode = dtItemNameVendor1.Rows[0]["VendorCode"] + "";
						ANPCode = dtItemNameVendor1.Rows[0]["ANPCode"] + "";
					}

					model.DataBareFlex.BareFlex_WorkOrder.Rows.Add(new Object[]
					{
						itemName,
						null,
						null,
						null,
						blockID,
						pcsID,
						VendorCode,
						ANPCode,
						connectionStringOk
					});
				}

				if (model.DataFlexAssy.FlexAssy_21_2DCodeScan?.Rows.Count > 0)
				{
					string machineID = model.DataFlexAssy.FlexAssy_21_2DCodeScan.Rows[0]["MachineID"].ToString();
					string programName = model.DataFlexAssy.FlexAssy_21_2DCodeScan.Rows[0]["ProductType"].ToString();
					model.DataFlexAssy.FlexAssy_21_2DCodeScan_LogFile_Sample = Singleton_02_FlexAssy.IFlexAssy_21_2DCodeScanService.FlexAssy_21_2DCodeScan_LogFile_Sample_GetDataWithMachineIDAndItemName(machineID, programName, Convert.ToDateTime(model.DataFlexAssy.FlexAssy_21_2DCodeScan.Rows[0]["DateTime"]), connectionStringOk);
				}

				if (model.DataFlexAssy.FlexAssy_22_ICT?.Rows.Count > 0)
				{
					//Regex rg = new Regex("-C" + "");

					//var isOBA = "0";
					//if (model.DataFlexAssy.FlexAssy_30_OBA_ICT?.Rows.Count > 0) isOBA = "1";
					//System.Data.DataColumn newColumn_OBA = new System.Data.DataColumn("IsOBA", typeof(System.String));
					//newColumn_OBA.DefaultValue = isOBA;
					//model.DataFlexAssy.FlexAssy_22_ICT.Columns.Add(newColumn_OBA);

					string machineID = model.DataFlexAssy.FlexAssy_22_ICT.Rows[0]["MachineID"].ToString();
					string programName = model.DataFlexAssy.FlexAssy_22_ICT.Rows[0]["TestProgram"].ToString();
					model.DataFlexAssy.FlexAssy_22_ICT_LogFile_Sample = Singleton_02_FlexAssy.IFlexAssy_22_ICTService.FlexAssy_22_ICT_LogFile_Sample_GetDataWithMachineIDAndItemName(machineID, programName, Convert.ToDateTime(model.DataFlexAssy.FlexAssy_22_ICT.Rows[0]["DateTime"]), connectionStringOk);


					//Xác định Product là Master Sample or ORT
					DataTable dt_ICT_Sample = Singleton_02_FlexAssy.IFlexAssy_22_ICTService.FlexAssy_22_ICT_Sample_GetByProductID(pcsID, connectionStringOk);
					DataTable dt_ICT_OQC = Singleton_02_FlexAssy.IFlexAssy_22_ICTService.FlexAssy_22_ICT_OQC_GetByProductID(pcsID, connectionStringOk);

					//var isOQC = "0";
					//if (dt_ICT_OQC?.Rows.Count > 0) isOQC = "1";
					//System.Data.DataColumn newColumn_OQC = new System.Data.DataColumn("IsOQC", typeof(System.String));
					//newColumn_OQC.DefaultValue = isOQC;
					//model.DataFlexAssy.FlexAssy_22_ICT.Columns.Add(newColumn_OQC);

					var isSample = "0";
					if (model.DataFlexAssy.FlexAssy_31_RELORT?.Rows.Count > 0 || dt_ICT_Sample?.Rows.Count > 0)
					{
						isSample = "1";
					}
					System.Data.DataColumn newColumn = new System.Data.DataColumn("IsSample", typeof(System.String));
					newColumn.DefaultValue = isSample;
					model.DataFlexAssy.FlexAssy_22_ICT.Columns.Add(newColumn);
				}
				if (model.DataFlexAssy.FlexAssy_22_OQC_ICT?.Rows.Count > 0)
				{
					string machineID = model.DataFlexAssy.FlexAssy_22_OQC_ICT.Rows[0]["MachineID"].ToString();
					string programName = model.DataFlexAssy.FlexAssy_22_OQC_ICT.Rows[0]["TestProgram"].ToString();
					model.DataFlexAssy.FlexAssy_22_OQC_ICT_LogFile_Sample = Singleton_02_FlexAssy.IFlexAssy_22_ICTService.FlexAssy_22_ICT_LogFile_Sample_GetDataWithMachineIDAndItemName(machineID, programName, Convert.ToDateTime(model.DataFlexAssy.FlexAssy_22_OQC_ICT.Rows[0]["DateTime"]), connectionStringOk);
				}
				if (model.DataFlexAssy.FlexAssy_30_OBA_ICT_New?.Rows.Count > 0)
				{
					string machineID = model.DataFlexAssy.FlexAssy_30_OBA_ICT_New.Rows[0]["MachineID"].ToString();
					string programName = model.DataFlexAssy.FlexAssy_30_OBA_ICT_New.Rows[0]["TestProgram"].ToString();
					model.DataFlexAssy.FlexAssy_30_OBA_ICT_New_LogFile_Sample = Singleton_02_FlexAssy.IFlexAssy_22_ICTService.FlexAssy_22_ICT_LogFile_Sample_GetDataWithMachineIDAndItemName(machineID, programName, Convert.ToDateTime(model.DataFlexAssy.FlexAssy_30_OBA_ICT_New.Rows[0]["DateTime"]), connectionStringOk);
				}
				if (model.DataFlexAssy.FlexAssy_22_H4W_ICT?.Rows.Count > 0)
				{
					string machineID = model.DataFlexAssy.FlexAssy_22_H4W_ICT.Rows[0]["MachineID"].ToString();
					string programName = model.DataFlexAssy.FlexAssy_22_H4W_ICT.Rows[0]["TestProgram"].ToString();
					model.DataFlexAssy.FlexAssy_22_H4W_ICT_LogFile_Sample = Singleton_02_FlexAssy.IFlexAssy_22_ICTService.FlexAssy_22_ICT_LogFile_Sample_GetDataWithMachineIDAndItemName(machineID, programName, Convert.ToDateTime(model.DataFlexAssy.FlexAssy_22_H4W_ICT.Rows[0]["DateTime"]), connectionStringOk);
				}
				if (model.DataFlexAssy.FlexAssy_22_ORT_ICT?.Rows.Count > 0)
				{
					string machineID = model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows[0]["MachineID"].ToString();
					string programName = model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows[0]["TestProgram"].ToString();
					model.DataFlexAssy.FlexAssy_22_ORT_ICT_LogFile_Sample = Singleton_02_FlexAssy.IFlexAssy_22_ICTService.FlexAssy_22_ICT_LogFile_Sample_GetDataWithMachineIDAndItemName(machineID, programName, Convert.ToDateTime(model.DataFlexAssy.FlexAssy_22_ORT_ICT.Rows[0]["DateTime"]), connectionStringOk);
				}
				if (model.DataFlexAssy.FlexAssy_22_RC_ICT?.Rows.Count > 0)
				{
					string machineID = model.DataFlexAssy.FlexAssy_22_RC_ICT.Rows[0]["MachineID"].ToString();
					string programName = model.DataFlexAssy.FlexAssy_22_RC_ICT.Rows[0]["TestProgram"].ToString();
					model.DataFlexAssy.FlexAssy_22_RC_ICT_LogFile_Sample = Singleton_02_FlexAssy.IFlexAssy_22_ICTService.FlexAssy_22_ICT_LogFile_Sample_GetDataWithMachineIDAndItemName(machineID, programName, Convert.ToDateTime(model.DataFlexAssy.FlexAssy_22_RC_ICT.Rows[0]["DateTime"]), connectionStringOk);
				}
				if (model.DataFlexAssy.FlexAssy_24_AutoPickup?.Rows.Count > 0)
				{
					string machineID = model.DataFlexAssy.FlexAssy_24_AutoPickup.Rows[0]["MachineID"].ToString();
					string itemNameAutopickup = model.DataFlexAssy.FlexAssy_24_AutoPickup.Rows[0]["ProgramName"].ToString();
					model.DataFlexAssy.FlexAssy_24_AutoPickup_LogFile_Sample = Singleton_02_FlexAssy.IFlexAssy_24_AutoPickupService.FlexAssy_24_AutoPickup_LogFile_Sample_GetDataWithMachineIDAndItemName(machineID, itemNameAutopickup, Convert.ToDateTime(model.DataFlexAssy.FlexAssy_24_AutoPickup.Rows[0]["DateTime"]), connectionStringOk);
				}
				if (model.DataFlexAssy.FlexAssy_24_Sorting?.Rows.Count > 0)
				{
					string machineID = model.DataFlexAssy.FlexAssy_24_Sorting.Rows[0]["MachineID"].ToString();
					string itemNameAutopickup = model.DataFlexAssy.FlexAssy_24_Sorting.Rows[0]["ProgramName"].ToString();
					model.DataFlexAssy.FlexAssy_24_Sorting_Sample = Singleton_02_FlexAssy.IFlexAssy_24_SortingService.FlexAssy_24_Sorting_LogFile_Sample_GetDataWithMachineIDAndItemName(machineID, itemNameAutopickup, Convert.ToDateTime(model.DataFlexAssy.FlexAssy_24_Sorting.Rows[0]["DateTime"]), connectionStringOk);
				}
				if (model.DataFlexAssy.FlexAssy_26_QcGate?.Rows.Count > 0 || model.DataFlexAssy.FlexAssy_27_Plasma?.Rows.Count > 0)
				{
					//string cutDataItemName = itemName;
					//if (itemName.IndexOf('-') != -1)
					//    cutDataItemName = cutDataItemName.Substring(0, cutDataItemName.IndexOf('-'));// !string.IsNullOrEmpty(itemName) ? itemName.Substring(0, 3) : "";

					if (model.DataFlexAssy.FlexAssy_26_QcGate?.Rows.Count > 0)
					{
						string machineID = model.DataFlexAssy.FlexAssy_26_QcGate.Rows[0]["MachineID"].ToString();
						string programName = model.DataFlexAssy.FlexAssy_26_QcGate.Rows[0]["ProgramName"].ToString();
						model.DataFlexAssy.FlexAssy_26_QcGate_LogFile_Sample = Singleton_02_FlexAssy.IFlexAssy_26_QcGateService.FlexAssy_26_QcGate_LogFile_Sample_GetDataWithMachineIDAndItemName(machineID, programName, Convert.ToDateTime(model.DataFlexAssy.FlexAssy_26_QcGate.Rows[0]["DateTime"]), connectionStringOk);

						//plasmaID = cutDataItemName != "" ? $"{cutDataItemName}-{model.DataFlexAssy.FlexAssy_26_QcGate.Rows[0]["QrCodePlasma"].ToString().Trim()}" : model.DataFlexAssy.FlexAssy_26_QcGate.Rows[0]["QrCodePlasma"].ToString().Trim();
						plasmaID = model.DataFlexAssy.FlexAssy_26_QcGate.Rows[0]["QrCodePlasma"].ToString().Trim();
						model.DataFlexAssy.FlexAssy_28_ContactAngle = Singleton_02_FlexAssy.IFlexAssy_28_ContactAngleService.FlexAssy_28_ContactAngle_GetByPlasmaID(plasmaID, connectionStringOption = connectionStringOk, out connectionStringOk);
						if (model.DataFlexAssy.FlexAssy_28_ContactAngle?.Rows.Count > 0)
						{
							model.DataFlexAssy.FlexAssy_28_ContactAngle_Pattern = Singleton_02_FlexAssy.IFlexAssy_28_ContactAngleService.FlexAssy_28_ContactAngle_Pattern_GetByPlasmaID(plasmaID, connectionStringOk);
						}
					}
					else if (model.DataFlexAssy.FlexAssy_27_Plasma?.Rows.Count > 0)
					{
						//plasmaID = cutDataItemName != "" ? $"{cutDataItemName}-{model.DataFlexAssy.FlexAssy_27_Plasma.Rows[0]["QrCode"].ToString().Trim()}" : model.DataFlexAssy.FlexAssy_27_Plasma.Rows[0]["QrCode"].ToString().Trim();
						plasmaID = model.DataFlexAssy.FlexAssy_27_Plasma.Rows[0]["QrCode"].ToString().Trim();
						model.DataFlexAssy.FlexAssy_28_ContactAngle = Singleton_02_FlexAssy.IFlexAssy_28_ContactAngleService.FlexAssy_28_ContactAngle_GetByPlasmaID(plasmaID, connectionStringOption = connectionStringOk, out connectionStringOk);
						if (model.DataFlexAssy.FlexAssy_28_ContactAngle?.Rows.Count > 0)
						{
							model.DataFlexAssy.FlexAssy_28_ContactAngle_Pattern = Singleton_02_FlexAssy.IFlexAssy_28_ContactAngleService.FlexAssy_28_ContactAngle_Pattern_GetByPlasmaID(plasmaID, connectionStringOk);
						}
					}
				}

				#region IPQC_ORT_OQC
				//mounting -- indi F3, F4
				//if (!string.IsNullOrEmpty(mounting))
				//{
				//    //get dữ liệu từ DB
				//    model.DataFlexAssy.FlexAssy_33_IPQC_OQC = Singleton_02_FlexAssy.FlexAssy_33_IPQC_OQC_Service.GetByIndi(mounting, out string ItemCode, out string ProductionLine,
				//        out DateTime ShiftStart, out DateTime ShiftEnd, connectionStringOption = connectionStringOk, out connectionStringOk);
				//    model.DataFlexAssy.FlexAssy_33_IPQC_OQC_ORT_OBA = Singleton_02_FlexAssy.FlexAssy_33_IPQC_OQC_ORT_OBAService.GetByIndi(mounting, out string ItemCode2, out string ProductionLine2,
				//        out DateTime ShiftStart2, out DateTime ShiftEnd2, connectionStringOption = connectionStringOk, out connectionStringOk);

				//    if (model.DataFlexAssy.FlexAssy_33_IPQC_OQC?.Rows.Count > 0)
				//    {
				//        #region IPQC Old
				//        //get dữ liệu từ DB -old
				//        //model.DataFlexAssy.FlexAssy_32_1_ORT_ThermalCycling = Singleton_02_FlexAssy.FlexAssy_32_1_ORTThermalCyclingService.GetTestFromIPQC
				//        //    (ItemCode, ProductionLine, ShiftStart, ShiftEnd, connectionStringOption = connectionStringOk, out connectionStringOk);
				//        //model.DataFlexAssy.FlexAssy_32_2_ORT_HeatSoakAndRecovery = Singleton_02_FlexAssy.FlexAssy_32_2_ORT_HeatsoakAndRecoveryService.GetTestFromIPQC
				//        //    (ItemCode, ProductionLine, ShiftStart, ShiftEnd, connectionStringOption = connectionStringOk, out connectionStringOk);
				//        //model.DataFlexAssy.FlexAssy_32_3_ORT_ThermalShock = Singleton_02_FlexAssy.FlexAssy_32_3_ORT_ThermalShockService.GetTestFromIPQC
				//        //    (ItemCode, ProductionLine, ShiftStart, ShiftEnd, connectionStringOption = connectionStringOk, out connectionStringOk);
				//        //model.DataFlexAssy.FlexAssy_32_4_ORT_ThermalCyclingAndFlexBending =
				//        //    Singleton_02_FlexAssy.FlexAssy_32_4_ORT_ThermalCyclingAndFlexBendingService.GetTestFromIPQC
				//        //    (ItemCode, ProductionLine, ShiftStart, ShiftEnd, connectionStringOption = connectionStringOk, out connectionStringOk);
				//        //model.DataFlexAssy.FlexAssy_32_5_ORT_FlexBending = Singleton_02_FlexAssy.FlexAssy_32_5_ORT_FlexBendingService.GetTestFromIPQC
				//        //    (ItemCode, ProductionLine, ShiftStart, ShiftEnd, connectionStringOption = connectionStringOk, out connectionStringOk);
				//        //model.DataFlexAssy.FlexAssy_32_6_ORT_HeatSoakAndFlexBending = Singleton_02_FlexAssy.FlexAssy_32_6_ORT_HeatSoakAndFlexBendingService.GetTestFromIPQC
				//        //    (ItemCode, ProductionLine, ShiftStart, ShiftEnd, connectionStringOption = connectionStringOk, out connectionStringOk);


				//        //model.DataFlexAssy.FlexAssy_33_1_IPQC_B2BXSection = Singleton_02_FlexAssy.FlexAssy_33_1_IPQC_B2BXsectionService.GetTestFromIPQC
				//        //    (ItemCode, ProductionLine, ShiftStart, ShiftEnd, connectionStringOption = connectionStringOk, out connectionStringOk);
				//        //model.DataFlexAssy.FlexAssy_33_2_IPQC_B2BPeelingTest = Singleton_02_FlexAssy.FlexAssy_33_2_IPQC_B2BPeelingTestService.GetTestFromIPQC
				//        //    (ItemCode, ProductionLine, ShiftStart, ShiftEnd, connectionStringOption = connectionStringOk, out connectionStringOk);
				//        //model.DataFlexAssy.FlexAssy_33_3_IPQC_B2BXRay = Singleton_02_FlexAssy.FlexAssy_33_3_IPQC_B2BXRayService.GetTestFromIPQC
				//        //    (ItemCode, ProductionLine, ShiftStart, ShiftEnd, connectionStringOption = connectionStringOk, out connectionStringOk);
				//        //model.DataFlexAssy.FlexAssy_33_4_IPQC_OutlinePunchingDimension = Singleton_02_FlexAssy.FlexAssy_33_4_IPQC_OutlinePunchingService.GetTestFromIPQC
				//        //    (ItemCode, ProductionLine, ShiftStart, ShiftEnd, connectionStringOption = connectionStringOk, out connectionStringOk);
				//        //model.DataFlexAssy.FlexAssy_33_5_IPQC_PlasmaACFWettingAngle = Singleton_02_FlexAssy.FlexAssy_33_5_IPQC_PlasmaACFWettingAngleService.GetTestFromIPQC
				//        //    (ItemCode, ProductionLine, ShiftStart, ShiftEnd, connectionStringOption = connectionStringOk, out connectionStringOk);


				//        //model.DataFlexAssy.FlexAssy_34_1_OQC_FAISPCMeasurement = Singleton_02_FlexAssy.FlexAssy_34_1_OQC_FAISPCMesurementService.GetTestFromIPQC
				//        //    (ItemCode, ProductionLine, ShiftStart, ShiftEnd, connectionStringOption = connectionStringOk, out connectionStringOk);
				//        //model.DataFlexAssy.FlexAssy_34_2_OQC_B2BPeelingPullingShearing = Singleton_02_FlexAssy.FlexAssy_34_2_OQC_B2BPeelingPullShearService.GetTestFromIPQC
				//        //    (ItemCode, ProductionLine, ShiftStart, ShiftEnd, connectionStringOption = connectionStringOk, out connectionStringOk);
				//        //model.DataFlexAssy.FlexAssy_34_3_OQC_ACFFlatness = Singleton_02_FlexAssy.FlexAssy_34_3_OQC_ACFFLatness.GetTestFromIPQC
				//        //    (ItemCode, ProductionLine, ShiftStart, ShiftEnd, connectionStringOption = connectionStringOk, out connectionStringOk);
				//        //model.DataFlexAssy.FlexAssy_34_4_OQC_ACFRoughness = Singleton_02_FlexAssy.FlexAssy_34_4_OQC_ACFRoughnessService.GetTestFromIPQC
				//        //    (ItemCode, ProductionLine, ShiftStart, ShiftEnd, connectionStringOption = connectionStringOk, out connectionStringOk);
				//        //model.DataFlexAssy.FlexAssy_34_5_OQC_XSectionGNDB2B = Singleton_02_FlexAssy.FlexAssy_34_5_OQC_XsectionGNDB2BService.GetTestFromIPQC
				//        //    (ItemCode, ProductionLine, ShiftStart, ShiftEnd, connectionStringOption = connectionStringOk, out connectionStringOk);
				//        #endregion
				//        #region IPQC
				//        //get dữ liệu từ DB
				//        model.DataFlexAssy.FlexAssy_32_1_ORT_ThermalCycling = Singleton_02_FlexAssy.FlexAssy_32_1_ORTThermalCyclingService.GetByIndi
				//            (mounting, connectionStringOption = connectionStringOk, out connectionStringOk);
				//        model.DataFlexAssy.FlexAssy_32_2_ORT_HeatSoakAndRecovery = Singleton_02_FlexAssy.FlexAssy_32_2_ORT_HeatsoakAndRecoveryService.GetByIndi
				//            (mounting, connectionStringOption = connectionStringOk, out connectionStringOk);
				//        model.DataFlexAssy.FlexAssy_32_3_ORT_ThermalShock = Singleton_02_FlexAssy.FlexAssy_32_3_ORT_ThermalShockService.GetByIndi
				//            (mounting, connectionStringOption = connectionStringOk, out connectionStringOk);
				//        model.DataFlexAssy.FlexAssy_32_4_ORT_ThermalCyclingAndFlexBending = Singleton_02_FlexAssy.FlexAssy_32_4_ORT_ThermalCyclingAndFlexBendingService.GetByIndi
				//            (mounting, connectionStringOption = connectionStringOk, out connectionStringOk);
				//        model.DataFlexAssy.FlexAssy_32_5_ORT_FlexBending = Singleton_02_FlexAssy.FlexAssy_32_5_ORT_FlexBendingService.GetByIndi
				//            (mounting, connectionStringOption = connectionStringOk, out connectionStringOk);
				//        model.DataFlexAssy.FlexAssy_32_6_ORT_HeatSoakAndFlexBending = Singleton_02_FlexAssy.FlexAssy_32_6_ORT_HeatSoakAndFlexBendingService.GetByIndi
				//            (mounting, connectionStringOption = connectionStringOk, out connectionStringOk);
				//        model.DataFlexAssy.FlexAssy_33_1_IPQC_B2BXSection = Singleton_02_FlexAssy.FlexAssy_33_1_IPQC_B2BXsectionService.GetByIndi
				//            (mounting, connectionStringOption = connectionStringOk, out connectionStringOk);
				//        model.DataFlexAssy.FlexAssy_33_2_IPQC_B2BPeelingTest = Singleton_02_FlexAssy.FlexAssy_33_2_IPQC_B2BPeelingTestService.GetByIndi
				//            (mounting, connectionStringOption = connectionStringOk, out connectionStringOk);
				//        model.DataFlexAssy.FlexAssy_33_3_IPQC_B2BXRay = Singleton_02_FlexAssy.FlexAssy_33_3_IPQC_B2BXRayService.GetByIndi
				//            (mounting, connectionStringOption = connectionStringOk, out connectionStringOk);
				//        model.DataFlexAssy.FlexAssy_33_5_IPQC_PlasmaACFWettingAngle = Singleton_02_FlexAssy.FlexAssy_33_5_IPQC_PlasmaACFWettingAngleService.GetByIndi
				//            (mounting, connectionStringOption = connectionStringOk, out connectionStringOk);
				//        model.DataFlexAssy.FlexAssy_34_3_OQC_ACFFlatness = Singleton_02_FlexAssy.FlexAssy_34_3_OQC_ACFFLatness.GetByIndi
				//            (mounting, connectionStringOption = connectionStringOk, out connectionStringOk);
				//        model.DataFlexAssy.FlexAssy_34_4_OQC_ACFRoughness = Singleton_02_FlexAssy.FlexAssy_34_6_OQC_ACFRoughnessService.GetByIndi
				//            (mounting, connectionStringOption = connectionStringOk, out connectionStringOk);
				//        model.DataFlexAssy.FlexAssy_34_5_OQC_XSectionGNDB2B = Singleton_02_FlexAssy.FlexAssy_34_99_OQC_XsectionGNDB2BService.GetByIndi
				//            (mounting, connectionStringOption = connectionStringOk, out connectionStringOk);
				//        #endregion
				//    }
				//}
				//if (!string.IsNullOrEmpty(invoiceID))
				//{
				//    //load các testinput theo Invoice
				//    model.DataFlexAssy.FlexAssy_34_2_OQC_B2BPeeling = Singleton_02_FlexAssy.FlexAssy_34_2_OQC_B2BPeelingService.GetByInvoiceID
				//           (invoiceID, DatetimeInvoice, connectionStringOption = connectionStringOk, out connectionStringOk);
				//    model.DataFlexAssy.FlexAssy_34_2_OQC_B2BPulling = Singleton_02_FlexAssy.FlexAssy_34_3_OQC_B2BPullingService.GetByInvoiceID
				//           (invoiceID, DatetimeInvoice, connectionStringOption = connectionStringOk, out connectionStringOk);
				//    model.DataFlexAssy.FlexAssy_34_2_OQC_B2BShearing = Singleton_02_FlexAssy.FlexAssy_34_4_OQC_B2BShearingService.GetByInvoiceID
				//           (invoiceID, DatetimeInvoice, connectionStringOption = connectionStringOk, out connectionStringOk);
				//    model.DataFlexAssy.FlexAssy_34_4_OQC_ACFBonding = Singleton_02_FlexAssy.FlexAssy_34_7_OQC_ACFBondingService.GetByInvoiceID
				//           (invoiceID, DatetimeInvoice, connectionStringOption = connectionStringOk, out connectionStringOk);
				//    model.DataFlexAssy.FlexAssy_34_4_OQC_HotOil = Singleton_02_FlexAssy.FlexAssy_34_8_OQC_HotOilService.GetByInvoiceID
				//           (invoiceID, DatetimeInvoice, connectionStringOption = connectionStringOk, out connectionStringOk);
				//    model.DataFlexAssy.FlexAssy_34_4_OQC_Heating4W = Singleton_02_FlexAssy.FlexAssy_34_9_OQC_Heating4WService.GetByInvoiceID
				//           (invoiceID, DatetimeInvoice, connectionStringOption = connectionStringOk, out connectionStringOk);
				//}
				//if (blockID.Length != 0)
				//{
				//    model.DataFlexAssy.FlexAssy_33_4_IPQC_OutlinePunchingDimension = Singleton_02_FlexAssy.FlexAssy_33_4_IPQC_OutlinePunchingService.GetByBLockID
				//            (blockID, "", itemName, connectionStringOk);
				//    model.DataFlexAssy.FlexAssy_34_1_OQC_FAISPCMeasurement = Singleton_02_FlexAssy.FlexAssy_34_1_OQC_FAISPCMesurementService.GetByBLockID
				//                (blockID, "", itemName, connectionStringOk);

				//}
				#endregion
				#region IPQC_ORT_OQC_OBA
				//get dữ liệu từ DB
				//model.DataFlexAssy.FlexAssy_33_IPQC_OQC = Singleton_02_FlexAssy.FlexAssy_33_IPQC_OQC_Service.GetByIndi(IPQC_Mounting_Search, out string ItemCode, out string ProductionLine,
				//    out DateTime ShiftStart, out DateTime ShiftEnd, connectionStringOption = connectionStringOk, out connectionStringOk);
				//model.DataFlexAssy.FlexAssy_33_IPQC_OQC_ORT_OBA = Singleton_02_FlexAssy.FlexAssy_33_IPQC_OQC_ORT_OBAService.GetByIndi(IPQC_Mounting_Search, out string ItemCode2, out string ProductionLine2,
				//    out DateTime ShiftStart2, out DateTime ShiftEnd2, connectionStringOption = connectionStringOk, out connectionStringOk);
				model.DataFlexAssy.FlexAssy_33_IPQC = Singleton_02_FlexAssy.FlexAssy_33_IPQC_OQC_ORT_OBAService.GetAllInputTestData(IPQC_Mounting_Search, pcsID, invoiceID, blockID, itemName, "IPQC", connectionStringOk, out connectionStringOk, model.DataFlexAssy.FlexAssy_10_XrayInspection);
				model.DataFlexAssy.FlexAssy_32_ORT = Singleton_02_FlexAssy.FlexAssy_33_IPQC_OQC_ORT_OBAService.GetAllInputTestData(IPQC_Mounting_Search, pcsID, invoiceID, blockID, itemName, "ORT", connectionStringOk, out connectionStringOk);
				model.DataFlexAssy.FlexAssy_34_OQC = Singleton_02_FlexAssy.FlexAssy_33_IPQC_OQC_ORT_OBAService.GetAllInputTestData(IPQC_Mounting_Search, pcsID, invoiceID, blockID, itemName, "OQC", connectionStringOk, out connectionStringOk);
				model.DataFlexAssy.FlexAssy_35_OBA = Singleton_02_FlexAssy.FlexAssy_33_IPQC_OQC_ORT_OBAService.GetAllInputTestData(IPQC_Mounting_Search, pcsID, invoiceID, blockID, itemName, "OBA", connectionStringOk, out connectionStringOk);

				model.DataFlexAssy.FlexAssy_ListLotNumber = Singleton_02_FlexAssy.FlexAssy_33_IPQC_OQC_ORT_OBAService.GetListLotNumber2(ListComp, connectionStringOk, out connectionStringOk);
				//model.DataFlexAssy.FlexAssy_36_IQC = Singleton_02_FlexAssy.FlexAssy_33_IPQC_OQC_ORT_OBAService.GetTestByListNotLumberForIQC(model.DataFlexAssy.FlexAssy_ListLotNumber, pcsID, blockID, workOrder, connectionStringOk);

				model.DataFlexAssy.FlexAssy_36_IQC = new DataTable();
				if (pcsID != "" && (model.DataFlexAssy.FlexAssy_05_PickAndPlace_Group_Models?.Count > 0 || model.DataFlexAssy.FlexAssy_ListLotNumber?.Rows.Count > 0))
				{
					DataTable dtLotNumber = new DataTable();
					dtLotNumber.Columns.Add("LotNumber", typeof(string));

					if (model.DataFlexAssy.FlexAssy_05_PickAndPlace_Group_Models?.Count > 0)
					{
						foreach (FlexAssy_05_PickAndPlace_Group_Model item in model.DataFlexAssy.FlexAssy_05_PickAndPlace_Group_Models)
						{
							dtLotNumber.Rows.Add(item.LotCode);
						}

						DataTable dtIQC = Singleton_02_FlexAssy.FlexAssy_33_IPQC_OQC_ORT_OBAService.GetTestByListNotLumber(dtLotNumber, connectionStringOk);
						if (dtIQC?.Rows.Count > 0)
						{
							model.DataFlexAssy.FlexAssy_36_IQC.Merge(dtIQC);
						}
					}

					DataTable dtLotNumber2 = new DataTable();
					DataTable dtInvoice = new DataTable();
					dtLotNumber2.Columns.Add("LotNumber", typeof(string));
					dtInvoice.Columns.Add("InvoiceID", typeof(string));

					if (model.DataFlexAssy.FlexAssy_ListLotNumber?.Rows.Count > 0)
					{
						foreach (DataRow item in model.DataFlexAssy.FlexAssy_ListLotNumber.Rows)
						{
							dtLotNumber2.Rows.Add(item["LotNumber"] + "");
							dtInvoice.Rows.Add(item["InvoiceNumber"] + "");
						}

						DataTable dtIQC = Singleton_02_FlexAssy.FlexAssy_33_IPQC_OQC_ORT_OBAService.GetTestByListNotLumber3(dtLotNumber2, dtInvoice, connectionStringOk);
						if (dtIQC?.Rows.Count > 0)
						{
							model.DataFlexAssy.FlexAssy_36_IQC.Merge(dtIQC);
						}
					}
				}

				DataTable BareFlexLotForIQC = Singleton_02_FlexAssy.IFlexAssy_WorkOrderService.FlexAssy_WorkOrder_GetByWorkOrder(workOrder, connectionStringOk);
				if (BareFlexLotForIQC?.Rows.Count > 0)
				{
					DataTable dtBareFlexLot = new DataTable();
					dtBareFlexLot.Columns.Add("LotNumber", typeof(string));
					if (BareFlexLotForIQC.Rows[0]["BareFlexLot"] + "" != "")
					{
						dtBareFlexLot.Rows.Add(BareFlexLotForIQC.Rows[0]["BareFlexLot"] + "");
					}
					DataTable dtIQC = Singleton_02_FlexAssy.FlexAssy_33_IPQC_OQC_ORT_OBAService.GetTestByListNotLumber(dtBareFlexLot, connectionStringOk);
					if (dtIQC?.Rows.Count > 0)
					{
						model.DataFlexAssy.FlexAssy_36_IQC.Merge(dtIQC);
					}
				}

				if (model.DataFlexAssy.FlexAssy_03_SolderPastePrinting?.Rows.Count > 0 || model.DataFlexAssy.FlexAssy_03_SolderPastePrinting_List?.Rows.Count > 0)
				{
					DataTable dtLotNumberSPA = new DataTable();
					dtLotNumberSPA.Columns.Add("LotNumber", typeof(string));
					if (model.DataFlexAssy.FlexAssy_03_SolderPastePrinting?.Rows.Count > 0)
					{
						foreach (DataRow row in model.DataFlexAssy.FlexAssy_03_SolderPastePrinting.Rows)
						{
							dtLotNumberSPA.Rows.Add(row["SolderPasteLotNumber"] + "");
						}
					}
					if (model.DataFlexAssy.FlexAssy_03_SolderPastePrinting_List?.Rows.Count > 0)
					{
						foreach (DataRow row in model.DataFlexAssy.FlexAssy_03_SolderPastePrinting_List.Rows)
						{
							dtLotNumberSPA.Rows.Add(row["SolderPasteLotNumber"] + "");
						}
					}
					DataTable dtIQC = Singleton_02_FlexAssy.FlexAssy_33_IPQC_OQC_ORT_OBAService.GetTestByListNotLumber(dtLotNumberSPA, connectionStringOk);
					if (dtIQC?.Rows.Count > 0)
					{
						model.DataFlexAssy.FlexAssy_36_IQC.Merge(dtIQC);
					}
				}

				#endregion

				// Set session -> export file
				if (pcsID.Length != 0)
				{
					Session["HomeSearch_" + pcsID] = model;
				}
				else if (blockID.Length != 0)
				{
					Session["HomeSearch_" + blockID] = model;
				}
				else if (workOrder.Length != 0)
				{
					Session["HomeSearch_" + workOrder] = model;
				}
			}

			// create List to load data 
			int pageIndex = 1;
			List<string> blockIDs = new List<string>();
			List<string> pcsIDs = new List<string>();

			if (Session["BlockIDs"] != null)
			{
				blockIDs = Session["BlockIDs"] != null ? (List<string>)Session["BlockIDs"] : new List<string>();
				if (!string.IsNullOrEmpty(blockID))
				{
					if (!blockIDs.Any(x => x == blockID))
					{
						blockIDs.Insert(0, blockID);
						Session["BlockIDs"] = blockIDs;
					}

					int index = blockIDs.FindIndex(x => x == blockID);
					pageIndex = index / 500 + 1;
				}
			}
			else
			{
				pcsIDs = Session["PcsIDs"] != null ? (List<string>)Session["PcsIDs"] : new List<string>();
				if (!string.IsNullOrEmpty(pcsID))
				{
					if (!pcsIDs.Any(x => x == pcsID))
					{
						pcsIDs.Insert(0, pcsID);
						Session["PcsIDs"] = pcsIDs;
					}

					int index = pcsIDs.FindIndex(x => x == pcsID);
					pageIndex = index / 500 + 1;
				}
			}

			if (!string.IsNullOrEmpty(itemName))
			{
				var dtItemNameVendor = Singleton_03_Common.IItemName_VendorService.ItemName_Vendor_GetByitemName(itemName);
				if (dtItemNameVendor != null && dtItemNameVendor.Rows.Count > 0)
				{
					VendorCode = dtItemNameVendor.Rows[0]["VendorCode"] + "";
					ANPCode = dtItemNameVendor.Rows[0]["ANPCode"] + "";
				}
			}

			#region calculator SequencyOfStage 20210219 by itemname
			// Lay cac SequencyOfStage cua cac cong doan duoc Active
			var itemnameF1 = itemName;
			if (model != null && model.DataBareFlex != null && model.DataBareFlex.BareFlex_WorkOrder.Rows.Count > 0)
			{
				itemnameF1 = model.DataBareFlex.BareFlex_WorkOrder.Rows[0]["ItemName"].ToString();
			}
			DataTable dtSequencyOfStage_All = Singleton_03_Common.ISequencyOfStageService.SequencyOfStage_GetByAll(itemnameF1);
			if (dtSequencyOfStage_All != null && dtSequencyOfStage_All.Rows.Count > 0)
			{
				DataTable dtSequencyOfStage = null;
				DataRow[] dtrows = dtSequencyOfStage_All.Select("FrontOrBack like 'Upper-p'");

				if (dtrows.Count() != 0)
				{
					dtSequencyOfStage = dtSequencyOfStage_All.Select("FrontOrBack like 'Upper-p'").CopyToDataTable();
				}
				var sequencyOfStageModels = new SequencyOfStageModels();
				var test = new List<SequencyOfStage>();
				sequencyOfStageModels.SequencyOfStageModel = new List<SequencyOfStage>();

				if (dtSequencyOfStage != null)
				{
					listSequencyOfStage(dtSequencyOfStage, ref test);
					test = test.OrderBy(a => a.Sequency).ToList();
					sequencyOfStageModels.SequencyOfStageModel.AddRange(test);
				}

				DataRow[] dtr = dtSequencyOfStage_All.Select("FrontOrBack like 'Lower-p'");
				if (dtr.Count() != 0)
				{
					dtSequencyOfStage = dtSequencyOfStage_All.Select("FrontOrBack like 'Lower-p'").CopyToDataTable();
				}
				if (dtSequencyOfStage != null)
				{
					test = new List<SequencyOfStage>();
					listSequencyOfStage(dtSequencyOfStage, ref test);
					test = test.OrderBy(a => a.Sequency).ToList();
					sequencyOfStageModels.SequencyOfStageModel.AddRange(test);
				}
				//sequencyOfStageModels.SequencyOfStageModel = sequencyOfStageModels.SequencyOfStageModel.OrderBy(a => a.Sequency).ToList();
				// Tinh toan lai so lan sequency
				var new_sequencyOfStageModels = new List<SequencyOfStage>();
				for (int i = 0; i < sequencyOfStageModels.SequencyOfStageModel.Count(); i++)
				{
					var itemSequence = sequencyOfStageModels.SequencyOfStageModel[i];
					var _countTopSequenceByStage_of_new_sequencyOfStageModels = new_sequencyOfStageModels.Count(a => a.Stage == itemSequence.Stage);
					itemSequence.TopBySequency = _countTopSequenceByStage_of_new_sequencyOfStageModels + 1;
					new_sequencyOfStageModels.Add(itemSequence);
				}
				sequencyOfStageModels.SequencyOfStageModel = new_sequencyOfStageModels;
				//return ViewBag
				ViewBag.SequencyOfStageModels = sequencyOfStageModels;
			}
			else
			{
				ViewBag.SequencyOfStageModels = new SequencyOfStageModels();
				ViewBag.SequencyOfStageModels.SequencyOfStageModel = new List<SequencyOfStage>();
			}
			#endregion

			ViewBag.ItemName = itemName;
			ViewBag.LaneID = "Lane " + laneID;
			ViewBag.IndicationNo = mounting.Contains(",") ? mounting.Split(',')[0].Trim() : mounting;
			ViewBag.PcsID = pcsID;
			ViewBag.BlockID = blockID;
			ViewBag.WorkOrder = workOrder;
			ViewBag.Upper_p = upper_p;
			ViewBag.Lower_p = lower_p;
			ViewBag.Mounting = mounting;
			ViewBag.PageCurrent = pageIndex;
			ViewBag.TotalRecords = blockIDs.Count > 0 ? blockIDs.Count : pcsIDs.Count();
			ViewBag.Factory = connectionStringOk;
			ViewBag.VendorCode = VendorCode;
			ViewBag.ANPCode = ANPCode;
			ExportStage exportStage = new ExportStage();
			string ssExportStage = "";
			if (Session["StageExport"] != null)
			{
				ssExportStage = Session["StageExport"].ToString();
				object ssObject = new JavaScriptSerializer().DeserializeObject(ssExportStage);
				exportStage = new ExportStage(ssObject);
				ViewBag.BareFirstTime = ExportStage.checkAllStatus(exportStage, dtStageActiveBareFlex);
				ViewBag.AssyFirstTime = ExportStage.checkAllStatus(exportStage, dtStageActive);
			}
			else
			{
				Session["StageExport"] = new JavaScriptSerializer().Serialize(exportStage);
				ViewBag.BareFirstTime = 1;
				ViewBag.AssyFirstTime = 1;
			}
			ViewBag.ExportStage = exportStage;

			stop.Stop();
			Console.WriteLine("Time: " + stop.ElapsedMilliseconds);

			return View(model);
		}
		DataTable MergeData(DataTable target, DataTable source)
		{
			if (source != null)
			{
				if (target == null)
					target = source;
				else
					target.Merge(source);
			}
			if (target != null && target.Columns.Contains("DateTime"))
			{
				DataView view = target.DefaultView;
				view.Sort = "DateTime ASC"; 
				target = view.ToTable();
			}
			return target;
		}
		public void GetUpperData(DataAllTrace model, string upper_p)
		{
			if (!string.IsNullOrEmpty(upper_p))
			{
				model.DataBareFlex.BareFlex_01_BoardCutting = MergeData(model.DataBareFlex.BareFlex_01_BoardCutting, Singleton_01_BareFlex.IBareFlex_01_BoardCuttingService.BareFlex_01_BoardCutting_GetByWorkOrder(upper_p));
				model.DataBareFlex.BareFlex_02_NCDrilling = MergeData(model.DataBareFlex.BareFlex_02_NCDrilling, Singleton_01_BareFlex.IBareFlex_02_NCDrillingService.BareFlex_02_NCDrilling_GetByWorkOrder(upper_p));
				model.DataBareFlex.BareFlex_03_LaserDrilling = MergeData(model.DataBareFlex.BareFlex_03_LaserDrilling, Singleton_01_BareFlex.IBareFlex_03_LaserDrillingService.BareFlex_03_LaserDrilling_GetByWorkOrder(upper_p));
				model.DataBareFlex.BareFlex_04_Plasma = MergeData(model.DataBareFlex.BareFlex_04_Plasma, Singleton_01_BareFlex.IBareFlex_04_PlasmaService.BareFlex_04_Plasma_GetByWorkOrder(upper_p));
				model.DataBareFlex.BareFlex_05_BlackHoleShadow = MergeData(model.DataBareFlex.BareFlex_05_BlackHoleShadow, Singleton_01_BareFlex.IBareFlex_05_BlackHoleShadowService.BareFlex_05_BlackHoleShadow_GetByWorkOrder(upper_p));
				model.DataBareFlex.BareFlex_06_HoleAOI = MergeData(model.DataBareFlex.BareFlex_06_HoleAOI, Singleton_01_BareFlex.IBareFlex_06_HoleAOIService.BareFlex_06_HoleAOI_GetByWorkOrder(upper_p));
				model.DataBareFlex.BareFlex_07_1_DryFilmForButtonPlating_LAMI = MergeData(model.DataBareFlex.BareFlex_07_1_DryFilmForButtonPlating_LAMI, Singleton_01_BareFlex.IBareFlex_07_1_DryFilmForButtonPlating_LAMIService.BareFlex_07_1_DryFilmForButtonPlating_LAMI_GetByWorkOrder(upper_p));
				model.DataBareFlex.BareFlex_07_2_DryFilmForButtonPlating_EXPOSING = MergeData(model.DataBareFlex.BareFlex_07_2_DryFilmForButtonPlating_EXPOSING, Singleton_01_BareFlex.IBareFlex_07_2_DryFilmForButtonPlating_EXPOSINGService.BareFlex_07_2_DryFilmForButtonPlating_EXPOSING_GetByWorkOrder(upper_p));
				model.DataBareFlex.BareFlex_07_3_DryFilmForButtonPlating_DEVELOP = MergeData(model.DataBareFlex.BareFlex_07_3_DryFilmForButtonPlating_DEVELOP, Singleton_01_BareFlex.IBareFlex_07_3_DryFilmForButtonPlating_DEVELOPService.BareFlex_07_3_DryFilmForButtonPlating_DEVELOP_GetByWorkOrder(upper_p));
				model.DataBareFlex.BareFlex_07_4_DryFilmForCircuit_DEVELOP = MergeData(model.DataBareFlex.BareFlex_07_4_DryFilmForCircuit_DEVELOP, Singleton_01_BareFlex.IBareFlex_07_4_DryFilmForCircuit_DEVELOPService.BareFlex_07_4_DryFilmForCircuit_DEVELOP_GetByWorkOrder(upper_p));
				model.DataBareFlex.BareFlex_07_5_MicroEtching = MergeData(model.DataBareFlex.BareFlex_07_5_MicroEtching, Singleton_01_BareFlex.IBareFlex_07_5_MicroEtchingService.BareFlex_07_5_MicroEtching_GetByWorkOrder(upper_p));
				model.DataBareFlex.BareFlex_07_6_Stripping = MergeData(model.DataBareFlex.BareFlex_07_6_Stripping, Singleton_01_BareFlex.IBareFlex_07_6_StrippingService.BareFlex_07_6_Stripping_GetByWorkOrder(upper_p));
				model.DataBareFlex.BareFlex_07_7_FBCBaking = MergeData(model.DataBareFlex.BareFlex_07_7_FBCBaking, Singleton_01_BareFlex.IBareFlex_07_7_FBCBakingService.BareFlex_07_7_FBCBaking_GetByWorkOrder(upper_p));
				model.DataBareFlex.BareFlex_07_8_1_Layup = MergeData(model.DataBareFlex.BareFlex_07_8_1_Layup, Singleton_01_BareFlex.IBareFlex_07_8_1_LayupService.BareFlex_07_8_1_Layup_GetByWorkOrder(upper_p));
				model.DataBareFlex.BareFlex_07_8_2_LayupPress = MergeData(model.DataBareFlex.BareFlex_07_8_2_LayupPress, Singleton_01_BareFlex.IBareFlex_07_8_2_LayupPressService.BareFlex_07_8_2_LayupPress_GetByWorkOrder(upper_p));
				model.DataBareFlex.BareFlex_08_2_ElectrolessCopperPlating = MergeData(model.DataBareFlex.BareFlex_08_2_ElectrolessCopperPlating, Singleton_01_BareFlex.IBareFlex_08_2_ElectrolessCopperPlatingService.BareFlex_08_2_ElectrolessCopperPlating_GetByWorkOrder(upper_p));
				model.DataBareFlex.BareFlex_08_CopperPlating = MergeData(model.DataBareFlex.BareFlex_08_CopperPlating, Singleton_01_BareFlex.IBareFlex_08_CopperPlatingService.BareFlex_08_CopperPlating_GetByWorkOrder(upper_p));
				model.DataBareFlex.BareFlex_09_1_DryFilmDES_LAMI = MergeData(model.DataBareFlex.BareFlex_09_1_DryFilmDES_LAMI, Singleton_01_BareFlex.IBareFlex_09_1_DryFilmDES_LAMIService.BareFlex_09_1_DryFilmDES_LAMI_GetByWorkOrder(upper_p));
				model.DataBareFlex.BareFlex_09_2_DryFilmDES_EXPOSING = MergeData(model.DataBareFlex.BareFlex_09_2_DryFilmDES_EXPOSING, Singleton_01_BareFlex.IBareFlex_09_2_DryFilmDES_EXPOSINGService.BareFlex_09_2_DryFilmDES_EXPOSING_GetByWorkOrder(upper_p));
				model.DataBareFlex.BareFlex_09_3_DryFilmDES_ETCHING = MergeData(model.DataBareFlex.BareFlex_09_3_DryFilmDES_ETCHING, Singleton_01_BareFlex.IBareFlex_09_3_DryFilmDES_ETCHINGService.BareFlex_09_3_DryFilmDES_ETCHING_GetByWorkOrder(upper_p));
				model.DataBareFlex.BareFlex_10_AOI = MergeData(model.DataBareFlex.BareFlex_10_AOI, Singleton_01_BareFlex.IBareFlex_10_AOIService.BareFlex_10_AOI_GetByWorkOrder(upper_p));
				model.DataBareFlex.BareFlex_11_MultiLayerLamination = MergeData(model.DataBareFlex.BareFlex_11_MultiLayerLamination, Singleton_01_BareFlex.IBareFlex_11_MultiLayerLaminationService.BareFlex_11_MultiLayerLamination_GetByWorkOrder(upper_p));
				model.DataBareFlex.BareFlex_12_1_SolderMaskPrinting_PRINT = MergeData(model.DataBareFlex.BareFlex_12_1_SolderMaskPrinting_PRINT, Singleton_01_BareFlex.IBareFlex_12_1_SolderMaskPrinting_PRINTService.BareFlex_12_1_SolderMaskPrinting_PRINT_GetByWorkOrder(upper_p));
				model.DataBareFlex.BareFlex_12_2_SolderMaskPrinting_BAKING = MergeData(model.DataBareFlex.BareFlex_12_2_SolderMaskPrinting_BAKING, Singleton_01_BareFlex.IBareFlex_12_2_SolderMaskPrinting_BAKINGService.BareFlex_12_2_SolderMaskPrinting_BAKING_GetByWorkOrder(upper_p));
				model.DataBareFlex.BareFlex_12_3_SolderMaskPrinting_SREXPOSING = MergeData(model.DataBareFlex.BareFlex_12_3_SolderMaskPrinting_SREXPOSING, Singleton_01_BareFlex.IBareFlex_12_3_SolderMaskPrinting_SREXPOSINGService.BareFlex_12_3_SolderMaskPrinting_SREXPOSING_GetByWorkOrder(upper_p));
				model.DataBareFlex.BareFlex_12_4_SolderMaskPrinting_SRDevelopment = MergeData(model.DataBareFlex.BareFlex_12_4_SolderMaskPrinting_SRDevelopment, Singleton_01_BareFlex.IBareFlex_12_4_SolderMaskPrinting_SRDevelopmentService.BareFlex_12_4_SolderMaskPrinting_SRDevelopment_GetByWorkOrder(upper_p));
				model.DataBareFlex.BareFlex_13_CoverlayLamination = MergeData(model.DataBareFlex.BareFlex_13_CoverlayLamination, Singleton_01_BareFlex.IBareFlex_13_CoverlayLaminationService.BareFlex_13_CoverlayLamination_GetByWorkOrder(upper_p));
				model.DataBareFlex.BareFlex_14_ENIG = MergeData(model.DataBareFlex.BareFlex_14_ENIG, Singleton_01_BareFlex.IBareFlex_14_ENIGService.BareFlex_14_ENIG_GetByWorkOrder(upper_p));
				model.DataBareFlex.BareFlex_15_TargetPunch = MergeData(model.DataBareFlex.BareFlex_15_TargetPunch, Singleton_01_BareFlex.IBareFlex_15_TargetPunchService.BareFlex_15_TargetPunch_GetByWorkOrder(upper_p));
				model.DataBareFlex.BareFlex_07_4_4_DryFilmForCircuit_LAMI = MergeData(model.DataBareFlex.BareFlex_07_4_4_DryFilmForCircuit_LAMI, Singleton_01_BareFlex.IBareFlex_07_4_4_DryFilmForCircuit_LAMIService.BareFlex_07_4_4_DryFilmForCircuit_LAMI_GetByWorkOrder(upper_p));
				model.DataBareFlex.BareFlex_07_4_5_DryFilmForCircuit_EXPOSING = MergeData(model.DataBareFlex.BareFlex_07_4_5_DryFilmForCircuit_EXPOSING, Singleton_01_BareFlex.IBareFlex_07_4_5_DryFilmForCircuit_EXPOSINGService.BareFlex_07_4_5_DryFilmForCircuit_EXPOSING_GetByWorkOrder(upper_p));
				model.DataBareFlex.BareFlex_07_4_6_EtchingRR = MergeData(model.DataBareFlex.BareFlex_07_4_6_EtchingRR, Singleton_01_BareFlex.IBareFlex_07_4_6_EtchingRRService.BareFlex_07_4_6_EtchingRR_GetByWorkOrder(upper_p));
				model.DataBareFlex.BareFlex_07_5_2_Copperbrite = MergeData(model.DataBareFlex.BareFlex_07_5_2_Copperbrite, Singleton_01_BareFlex.IBareFlex_07_5_2_CopperbriteService.BareFlex_07_5_2_Copperbrite_GetByWorkOrder(upper_p));
				model.DataBareFlex.BareFlex_11_2_Division_Biku = MergeData(model.DataBareFlex.BareFlex_11_2_Division_Biku, Singleton_01_BareFlex.IBareFlex_11_2_Division_BikuService.BareFlex_11_2_Division_Biku_GetByWorkOrder(upper_p));
				model.DataBareFlex.BareFlex_11_3_Punching_Upper = MergeData(model.DataBareFlex.BareFlex_11_3_Punching_Upper, Singleton_01_BareFlex.IBareFlex_11_3_Punching_UpperService.BareFlex_11_3_Punching_Upper_GetByWorkOrder(upper_p));
				model.DataBareFlex.BareFlex_12_5_SRBaking_PostCure = MergeData(model.DataBareFlex.BareFlex_12_5_SRBaking_PostCure, Singleton_01_BareFlex.IBareFlex_12_5_SRBaking_PostCureService.BareFlex_12_5_SRBaking_PostCure_GetByWorkOrder(upper_p));
				model.DataBareFlex.BareFlex_12_6_UV_Cure = MergeData(model.DataBareFlex.BareFlex_12_6_UV_Cure, Singleton_01_BareFlex.IBareFlex_12_6_UV_CureService.BareFlex_12_6_UV_Cure_GetByWorkOrder(upper_p));
				model.DataBareFlex.BareFlex_13_1_CL_Pasting = MergeData(model.DataBareFlex.BareFlex_13_1_CL_Pasting, Singleton_01_BareFlex.IBareFlex_13_1_CL_PastingService.BareFlex_13_1_CL_Pasting_GetByWorkOrder(upper_p));
				model.DataBareFlex.BareFlex_13_3_UV_Cleaning = MergeData(model.DataBareFlex.BareFlex_13_3_UV_Cleaning, Singleton_01_BareFlex.IBareFlex_13_3_UV_CleaningService.BareFlex_13_3_UV_Cleaning_GetByWorkOrder(upper_p));
				model.DataBareFlex.BareFlex_15_3_De_cap = MergeData(model.DataBareFlex.BareFlex_15_3_De_cap, Singleton_01_BareFlex.IBareFlex_15_3_De_capService.BareFlex_15_3_De_cap_GetByWorkOrder(upper_p));
			}
		}
		public void GetLowerData(DataAllTrace model, string lower_p)
		{
			if (!string.IsNullOrEmpty(lower_p))
			{
				model.DataBareFlex.BareFlex_15_2_Block_Cutting = MergeData(model.DataBareFlex.BareFlex_15_2_Block_Cutting, Singleton_01_BareFlex.IBareFlex_15_2_Block_CuttingService.BareFlex_15_2_Block_Cutting_GetByWorkOrder(lower_p));
				model.DataBareFlex.BareFlex_16_1_EMILamination_PASTEEMI = MergeData(model.DataBareFlex.BareFlex_16_1_EMILamination_PASTEEMI, Singleton_01_BareFlex.IBareFlex_16_1_EMILamination_PASTEEMIService.BareFlex_16_1_EMILamination_PASTEEMI_GetByWorkOrder(lower_p));
				model.DataBareFlex.BareFlex_16_2_EMILamination_PRESSEMI = MergeData(model.DataBareFlex.BareFlex_16_2_EMILamination_PRESSEMI, Singleton_01_BareFlex.IBareFlex_16_2_EMILamination_PRESSEMIService.BareFlex_16_2_EMILamination_PRESSEMI_GetByWorkOrder(lower_p));
				model.DataBareFlex.BareFlex_17_1_StiffenerLamination_PASTESUS = MergeData(model.DataBareFlex.BareFlex_17_1_StiffenerLamination_PASTESUS, Singleton_01_BareFlex.IBareFlex_17_1_StiffenerLamination_PASTESUSService.BareFlex_17_1_StiffenerLamination_PASTESUS_GetByWorkOrder(lower_p));
				model.DataBareFlex.BareFlex_17_2_StiffenerLamination_PRESSSUS = MergeData(model.DataBareFlex.BareFlex_17_2_StiffenerLamination_PRESSSUS, Singleton_01_BareFlex.IBareFlex_17_2_StiffenerLamination_PRESSSUSService.BareFlex_17_2_StiffenerLamination_PRESSSUS_GetByWorkOrder(lower_p));
				model.DataBareFlex.BareFlex_18_1_SilkScreenPrinting = MergeData(model.DataBareFlex.BareFlex_18_1_SilkScreenPrinting, Singleton_01_BareFlex.IBareFlex_18_1_SilkScreenPrintingService.BareFlex_18_1_SilkScreenPrinting_GetByWorkOrder(lower_p));
				model.DataBareFlex.BareFlex_18_2_SilkScreenPrinting_DRY = MergeData(model.DataBareFlex.BareFlex_18_2_SilkScreenPrinting_DRY, Singleton_01_BareFlex.IBareFlex_18_2_SilkScreenPrinting_DRYService.BareFlex_18_2_SilkScreenPrinting_DRY_GetByWorkOrder(lower_p));
				model.DataBareFlex.BareFlex_19_1_OutlinePrepunch_PUNCH1 = MergeData(model.DataBareFlex.BareFlex_19_1_OutlinePrepunch_PUNCH1, Singleton_01_BareFlex.IBareFlex_19_1_OutlinePrepunch_PUNCH1Service.BareFlex_19_1_OutlinePrepunch_PUNCH1_GetByWorkOrder(lower_p));
				model.DataBareFlex.BareFlex_19_2_OutlinePrepunch_PUNCH2 = MergeData(model.DataBareFlex.BareFlex_19_2_OutlinePrepunch_PUNCH2, Singleton_01_BareFlex.IBareFlex_19_2_OutlinePrepunch_PUNCH2Service.BareFlex_19_2_OutlinePrepunch_PUNCH2_GetByWorkOrder(lower_p));
				model.DataBareFlex.BareFlex_20_ET = MergeData(model.DataBareFlex.BareFlex_20_ET, Singleton_01_BareFlex.IBareFlex_20_ETService.BareFlex_20_ET_GetByWorkOrder(lower_p));
				model.DataBareFlex.BareFlex_22_1_RELORL_HotOilTest = MergeData(model.DataBareFlex.BareFlex_22_1_RELORL_HotOilTest, Singleton_01_BareFlex.IBareFlex_22_1_RELORL_HotOilTestService.BareFlex_22_1_RELORL_HotOilTest_GetByWorkOrder(lower_p));
				model.DataBareFlex.BareFlex_22_2_RELORT_HeatCycleTest = MergeData(model.DataBareFlex.BareFlex_22_2_RELORT_HeatCycleTest, Singleton_01_BareFlex.IBareFlex_22_2_RELORT_HeatCycleTestService.BareFlex_22_2_RELORT_HeatCycleTest_GetByWorkOrder(lower_p));
				model.DataBareFlex.BareFlex_16_3_InkjetPrinting = MergeData(model.DataBareFlex.BareFlex_16_3_InkjetPrinting, Singleton_01_BareFlex.IBareFlex_16_3_InkjetPrintingService.BareFlex_16_3_InkjetPrinting_GetByWorkOrder(lower_p));
				model.DataBareFlex.BareFlex_20_1_BackingBack = MergeData(model.DataBareFlex.BareFlex_20_1_BackingBack, Singleton_01_BareFlex.IBareFlex_20_1_BackingBackService.BareFlex_20_1_BackingBack_GetByWorkOrder(lower_p));
				model.DataBareFlex.BareFlex_16_4_InkjetCure = MergeData(model.DataBareFlex.BareFlex_16_4_InkjetCure, Singleton_01_BareFlex.IBareFlex_16_4_InkjetCureService.BareFlex_16_4_InkjetCure_GetByWorkOrder(lower_p));
				model.DataBareFlex.BareFlex_20_1_2DIDLaserMarking = MergeData(model.DataBareFlex.BareFlex_20_1_2DIDLaserMarking, Singleton_01_BareFlex.IBareFlex_20_1_2DIDLaserMarkingService.BareFlex_20_1_2DIDLaserMarking_GetByWorkOrder(lower_p));
			}
		}
		public ActionResult Pismo()
		{
			Stopwatch stop = new Stopwatch();
			stop.Start();

			// Check Brower
			HttpRequest req = System.Web.HttpContext.Current.Request;
			string browserName = req.Browser.Browser;
			if (!browserName.Contains("Chrome") && !browserName.Contains("Firefox"))
			{
				return Redirect("/browser");
			}
			// Lay cac cong doan duoc Active
			DataTable dtStageActive = Singleton_03_Common.IStageSettingActiveViewTraceService.StageSettingActiveViewTrace_GetAll("#");
			ViewBag.StageActive = dtStageActive;
			// Lay cac cong doan duoc Active Bare Flex
			DataTable dtStageActiveBareFlex = Singleton_03_Common.IStageSettingActiveViewTraceService.StageSettingActiveViewTrace_GetAll("F1");
			ViewBag.StageActiveBareFlex = dtStageActiveBareFlex;

			DataTable dtStageActivePismo = Singleton_03_Common.IStageSettingActiveViewTraceService.StageSettingActiveViewTrace_GetAllPismo();
			ViewBag.StageActivePismo = dtStageActivePismo;

			string itemName = null;
			string pcsID = Request.Params["PcsID"] ?? "";
			string blockID = Request.Params["BlockID"] ?? "";
			string workOrder = Request.Params["PanelID"] ?? "";


			string upper_p = ""; // Upper-p: Indi Front F1
			string lower_p = ""; // Lower-p: Indi Back F1
			string mounting = ""; // Mounting: Indi F3,F4
			string invoiceID = ""; // invoiceID
			string IndicationIDAVI = ""; // IndicationIDAVI

			string DatetimeInvoice = ""; // invoiceID
			string connectionStringOption = null;
			string connectionStringOk = null;
			string plasmaID = "";
			string PunchMachineID = string.Empty;
			string VendorCode = string.Empty;
			string ANPCode = string.Empty;
			DataAllTrace model = null;
			if (pcsID.Length != 0 || blockID.Length != 0 || workOrder.Length != 0)
			{
				model = new DataAllTrace();
				if (pcsID.Length != 0)
				{

					model.DataPismoFlexAssy.FlexAssy_01_AVI_Detail = Singleton_07_Pismo.IPismo_01_AVIServices.FlexAssy_01_AVI_Detail_GetByProductID(pcsID);
					model.DataPismoFlexAssy.FlexAssy_01_TraceAOI = Singleton_07_Pismo.IFlexAssy_01_TraceAOIService.FlexAssy_01_TraceAOI_GetByProductID(pcsID, "Pismo");
					model.DataPismoFlexAssy.FlexAssy_02_AVI_SUS_Detail = Singleton_07_Pismo.IPismo_02_AVI_SUSServices.FlexAssy_02_AVI_SUS_Detail_GetByProductID(pcsID);
					model.DataPismoFlexAssy.FlexAssy_03_LaserSUS_Detail = Singleton_07_Pismo.IPismo_03_LaserSUSServices.FlexAssy_03_LaserSUS_Detail_GetByProductID(pcsID);

					// Lấy giá trị BlockID
					if (model.DataPismoFlexAssy.FlexAssy_03_LaserSUS_Detail.Rows.Count > 0)
					{
						int pkid = int.Parse(model.DataPismoFlexAssy.FlexAssy_03_LaserSUS_Detail.Rows[0]["LaserSUSPkid"].ToString());
						var LaserSus = Singleton_07_Pismo.IPismo_03_LaserSUSServices.FlexAssy_03_LaserSUS_GetByPkID(pkid);
						if (LaserSus.Rows.Count > 0)
						{
							blockID = LaserSus.Rows[0]["BlockID"].ToString();
						}
					}
					model.DataPismoFlexAssy.FlexAssy_04_QcGate = Singleton_07_Pismo.IFlexAssy_04_QcgateService.FlexAssy_04_QcGate_GetByProductID(pcsID, "Pismo");
					model.DataPismoFlexAssy.FlexAssy_05_ViaAOI_Detail = Singleton_07_Pismo.IFlexAssy_05_ViaAOIService.FlexAssy_05_ViaAOI_GetByProductID(pcsID, "Pismo");
					model.DataPismoFlexAssy.FlexAssy_06_Cognex_Detail = Singleton_07_Pismo.IFlexAssy_06_CognexService.FlexAssy_06_Cognex_GetByProductID(pcsID, "Pismo");
					model.DataPismoFlexAssy.FlexAssy_07_ECheck_Detail = Singleton_07_Pismo.IFlexAssy_07_ECheckService.FlexAssy_07_ECheck_GetByProductID(pcsID, "Pismo");
				}

				if (workOrder.Length != 0)
				{
					var LaserEmap = Singleton_07_Pismo.IFlexAssy_03_LaserEMapService.FlexAssy_03_LaserEMap_GetByPanelID(workOrder, "Pismo");
					if (LaserEmap.Rows.Count > 0)
					{
						blockID = LaserEmap.Rows[0]["BlockID"].ToString();
					}
				}
				if (blockID.Length != 0)
				{
					if (model.DataPismoFlexAssy.FlexAssy_01_TraceAOI == null)
					{
						model.DataPismoFlexAssy.FlexAssy_01_TraceAOI = Singleton_07_Pismo.IFlexAssy_01_TraceAOIService.FlexAssy_01_TraceAOI_GetByBlockID(blockID, "Pismo");
					}
					model.DataPismoFlexAssy.FlexAssy_03_LaserEMap = Singleton_07_Pismo.IFlexAssy_03_LaserEMapService.FlexAssy_03_LaserEMap_GetByBlockID(blockID, "Pismo");
					model.DataPismoFlexAssy.FlexAssy_01_AVI = Singleton_07_Pismo.IPismo_01_AVIServices.FlexAssy_01_AVI_GetByBlockID(blockID);
					model.DataPismoFlexAssy.FlexAssy_02_AVI_SUS = Singleton_07_Pismo.IPismo_02_AVI_SUSServices.FlexAssy_02_AVI_SUS_GetByBlockID(blockID);
					model.DataPismoFlexAssy.FlexAssy_03_LaserSUS = Singleton_07_Pismo.IPismo_03_LaserSUSServices.FlexAssy_03_LaserSUS_GetByBlockID(blockID);
					model.DataPismoFlexAssy.FlexAssy_05_ViaAOI = Singleton_07_Pismo.IFlexAssy_05_ViaAOIService.FlexAssy_05_ViaAOI_GetByBlockID(blockID, "Pismo");
					model.DataPismoFlexAssy.FlexAssy_06_Cognex = Singleton_07_Pismo.IFlexAssy_06_CognexService.FlexAssy_06_Cognex_GetByBlockID(blockID, "Pismo");
					model.DataPismoFlexAssy.FlexAssy_07_ECheck = Singleton_07_Pismo.IFlexAssy_07_ECheckService.FlexAssy_07_ECheck_GetByBlockID(blockID, "Pismo");
					model.DataPismoFlexAssy.FlexAssy_Coper = Singleton_07_Pismo.IFlexAssy_CoperService2025.FlexAssy_Coper_GetByBlockID(blockID, "Pismo");
					model.DataPismoFlexAssy.FlexAssy_Laser = Singleton_07_Pismo.IFlexAssy_LaserService2025.FlexAssy_Laser_GetByBlockID(blockID, "Pismo");
				}
			}
			if (Request.Params["PanelID"] != null && Request.Params["PanelID"] != "")
			{
				if (Session["WorkOrder_Value"] == null)
				{
					connectionStringOk = BlockID_FromPanel(Request.Params["PanelID"], connectionStringOk);
				}
				else if ((string)Session["WorkOrder_Value"] != Request.Params["PanelID"])
				{
					connectionStringOk = BlockID_FromPanel(Request.Params["PanelID"], connectionStringOk);
				}
				else if (Session["BlockIDs"] == null)
				{
					connectionStringOk = BlockID_FromPanel(Request.Params["PanelID"], connectionStringOk);
				}
			}
			if (Request.Params["BlockID"] != null && Request.Params["BlockID"] != "")
			{
				if (Session["BlockID_Value"] == null)
				{
					connectionStringOk = PcsID_FromBlockIDPismo(blockID, "Pismo");
				}
				else if ((string)Session["BlockID_Value"] != Request.Params["BlockID"])
				{
					connectionStringOk = PcsID_FromBlockIDPismo(blockID, "Pismo");
				}
				else if (Session["PcsIDs"] == null)
				{
					connectionStringOk = PcsID_FromBlockIDPismo(blockID, "Pismo");
				}
			}
			ExportStage exportStage = new ExportStage();
			string ssExportStage = ""; // create List to load data 
			int pageIndex = 1;
			List<string> blockIDPismos = new List<string>();
			List<string> pcsIDPismos = new List<string>();
			List<string> panelIDPismos = new List<string>();
			if (Session["BlockIDs"] != null)
			{
				blockIDPismos = Session["BlockIDs"] != null ? (List<string>)Session["BlockIDs"] : new List<string>();
				if (!string.IsNullOrEmpty(blockID))
				{
					if (!blockIDPismos.Any(x => x == blockID))
					{
						blockIDPismos.Insert(0, blockID);
						Session["BlockIDs"] = blockIDPismos;
					}

					int index = blockIDPismos.FindIndex(x => x == blockID);
					pageIndex = index / 500 + 1;
				}
			}
			else
			{
				pcsIDPismos = Session["PcsIDs"] != null ? (List<string>)Session["PcsIDs"] : new List<string>();
				if (!string.IsNullOrEmpty(pcsID))
				{
					if (!pcsIDPismos.Any(x => x == pcsID))
					{
						pcsIDPismos.Insert(0, pcsID);
						Session["PcsIDs"] = pcsIDPismos;
					}

					int index = pcsIDPismos.FindIndex(x => x == pcsID);
					pageIndex = index / 500 + 1;
				}
			}
			ViewBag.TotalRecords = blockIDPismos.Count > 0 ? blockIDPismos.Count : pcsIDPismos.Count();
			ViewBag.PcsID = pcsID;
			ViewBag.BlockID = blockID;
			ViewBag.WorkOrder = workOrder;
			ViewBag.PageCurrent = pageIndex;
			if (Session["StageExport"] != null)
			{
				ssExportStage = Session["StageExport"].ToString();
				object ssObject = new JavaScriptSerializer().DeserializeObject(ssExportStage);
				exportStage = new ExportStage(ssObject);
				ViewBag.BareFirstTime = ExportStage.checkAllStatus(exportStage, dtStageActiveBareFlex);
				ViewBag.AssyFirstTime = ExportStage.checkAllStatus(exportStage, dtStageActive);
			}
			else
			{
				Session["StageExport"] = new JavaScriptSerializer().Serialize(exportStage);
				ViewBag.BareFirstTime = 1;
				ViewBag.AssyFirstTime = 1;
			}
			ViewBag.ExportStage = exportStage;
			return View(model);
		}
		public ActionResult Brower()
		{
			return View();
		}

		public ActionResult LogFileProduct_ICT(string productID, string fileName)
		{
			var sb = new StringBuilder();

			if (!string.IsNullOrEmpty(productID))
			{
				if (fileName.ToLower().EndsWith(".dat"))
				{
					string rs = Singleton_02_FlexAssy.IFlexAssy_22_ICTService.FlexAssy_22_ICT_SplitFileDatNew(productID, fileName, "");
					if (rs != "")
					{
						byte[] fileBytes = System.IO.File.ReadAllBytes(rs);
						string fileNameNew = Path.GetFileName(rs);
						string pathFile = Path.GetDirectoryName(rs);
						if (Directory.Exists(pathFile))
						{
							Directory.Delete(pathFile, true);
						}
						return File(fileBytes, System.Net.Mime.MediaTypeNames.Application.Octet, fileNameNew);
					}
				}
				if (fileName.ToLower().EndsWith(".csv"))
				{
					string rs = Singleton_02_FlexAssy.IFlexAssy_22_ICTService.FlexAssy_22_ICT_SplitFileCSV_New(productID, fileName, "");
					if (rs != "")
					{
						byte[] fileBytes = System.IO.File.ReadAllBytes(rs);
						string fileNameNew = Path.GetFileName(rs);
						string pathFile = Path.GetDirectoryName(rs);
						if (Directory.Exists(pathFile))
						{
							Directory.Delete(pathFile, true);
						}
						return File(fileBytes, System.Net.Mime.MediaTypeNames.Application.Octet, fileNameNew);
					}
				}
			}
			return this.File(new UTF8Encoding().GetBytes(sb.ToString()), "text/csv", string.Format("Log-{0}.csv", DateTime.Now.ToString("g").Replace("/", "-").Replace(":", "_").Replace(" ", "-")));
		}
		public ActionResult LogFileProduct_AOI(string componentBlockName, string fileName)
		{
			var sb = new StringBuilder();

			if (!string.IsNullOrEmpty(componentBlockName))
			{
				if (fileName.ToLower().EndsWith(".txt"))
				{
					string rs = Singleton_02_FlexAssy.IFlexAssy_06_PreReflowAOIService.FlexAssy_06_PreReflowAOI_SplitDateFileTXT_New(componentBlockName, fileName, "");
					if (rs != "")
					{
						byte[] fileBytes = System.IO.File.ReadAllBytes(rs);
						string fileNameNew = Path.GetFileName(rs);
						string pathFile = Path.GetDirectoryName(rs);
						if (Directory.Exists(pathFile))
						{
							Directory.Delete(pathFile, true);
						}
						return File(fileBytes, System.Net.Mime.MediaTypeNames.Application.Octet, fileNameNew);
					}
				}
				if (fileName.ToLower().EndsWith(".csv"))
				{
					string rs = Singleton_02_FlexAssy.IFlexAssy_06_PreReflowAOIService.FlexAssy_06_PreReflowAOI_SplitDateFileCSV_New(componentBlockName, fileName, "");
					if (rs != "")
					{
						byte[] fileBytes = System.IO.File.ReadAllBytes(rs);
						string fileNameNew = Path.GetFileName(rs);
						string pathFile = Path.GetDirectoryName(rs);
						if (Directory.Exists(pathFile))
						{
							Directory.Delete(pathFile, true);
						}
						return File(fileBytes, System.Net.Mime.MediaTypeNames.Application.Octet, fileNameNew);
					}
				}
			}
			return this.File(new UTF8Encoding().GetBytes(sb.ToString()), "text/csv", string.Format("Log-{0}.csv", DateTime.Now.ToString("g").Replace("/", "-").Replace(":", "_").Replace(" ", "-")));
		}
		public void listSequencyOfStage(DataTable dtSequencyOfStage, ref List<SequencyOfStage> SequencyOfStageModel)
		{

			for (int i = 0; i < dtSequencyOfStage.Rows.Count; i++)
			{
				var _sequencyOfStageModel = new SequencyOfStage();
				_sequencyOfStageModel.ID = int.Parse(dtSequencyOfStage.Rows[i]["ID"].ToString());
				_sequencyOfStageModel.Sequencys = dtSequencyOfStage.Rows[i]["Sequency"].ToString().Trim();
				_sequencyOfStageModel.Stage = dtSequencyOfStage.Rows[i]["Stage"].ToString().Trim();
				_sequencyOfStageModel.StageName = dtSequencyOfStage.Rows[i]["StageName"].ToString().Trim();
				if (_sequencyOfStageModel.Sequencys.Contains(","))
				{
					var squencysSplit = _sequencyOfStageModel.Sequencys.Split(',');
					for (int j = 0; j < squencysSplit.Length; j++)
					{
						var _sequencyOfStageModel_Childrent = new SequencyOfStage();
						_sequencyOfStageModel_Childrent.Sequency = int.Parse(squencysSplit[j].ToString().Trim());
						_sequencyOfStageModel_Childrent.ID = _sequencyOfStageModel.ID;
						_sequencyOfStageModel_Childrent.Sequencys = _sequencyOfStageModel.Sequencys;
						_sequencyOfStageModel_Childrent.Stage = _sequencyOfStageModel.Stage;
						_sequencyOfStageModel_Childrent.ID = _sequencyOfStageModel.ID;
						_sequencyOfStageModel_Childrent.StageName = _sequencyOfStageModel.StageName;
						// check exists before add to list
						if (SequencyOfStageModel != null && SequencyOfStageModel.FindLastIndex(a => a.Sequency == _sequencyOfStageModel_Childrent.Sequency) <= 0)
						{
							SequencyOfStageModel.Add(_sequencyOfStageModel_Childrent);
						}
					}
				}
				else
				{
					_sequencyOfStageModel.Sequency = DataConvert.ConvertToInt(dtSequencyOfStage.Rows[i]["Sequency"].ToString().Trim());
					// check exists before add to list
					if (SequencyOfStageModel != null && SequencyOfStageModel.FindLastIndex(a => a.Sequency == _sequencyOfStageModel.Sequency) <= 0)
					{
						SequencyOfStageModel.Add(_sequencyOfStageModel);
					}
				}
			}
		}

		public string PcsID_FromBlockID(string blockID, string connectionStringOption)
		{
			List<string> pcsIDs = new List<string>();

			DataTable dt = Singleton_02_FlexAssy.IFlexAssy_08_LaserMarkingService.FlexAssy_08_LaserMarking_GetListProductIDByBlockID(blockID, connectionStringOption, out string connectionStringOk);
			if (dt?.Rows.Count > 0)
			{
				for (int i = 0; i < dt.Rows.Count; i++)
				{
					pcsIDs.Add(dt.Rows[i]["ProductID"].ToString());
				}
			}
			//else
			//{
			//    DataTable dt_F1 = Singleton_01_BareFlex.ISource_BareFlex_ErpData_Service.Tbl2DHeatMap_GetDataWith_Process_Pcs(blockID);
			//    if (dt_F1?.Rows.Count > 0)
			//    {
			//        for (int i = 0; i < dt_F1.Rows.Count; i++)
			//        {
			//            pcsIDs.Add(dt_F1.Rows[i]["ProductID"].ToString());
			//        }
			//    }
			//}

			Session["BlockID_Value"] = blockID;
			Session["PcsIDs"] = pcsIDs.Distinct().ToList();
			Session["BlockIDs"] = null;

			return connectionStringOk;
		}

		public string PcsID_FromBlockIDPismo(string blockID, string connectionStringOption)
		{
			List<string> pcsIDs = new List<string>();
			string connectionStringOk = string.Empty;
			DataTable dt = Singleton_07_Pismo.IPismo_03_LaserSUSServices.FlexAssy_03_LaserSUS_Detail_GetAllPCSByBlockID(blockID);
			if (dt?.Rows.Count > 0)
			{
				for (int i = 0; i < dt.Rows.Count; i++)
				{
					pcsIDs.Add(dt.Rows[i]["ProductID"].ToString());
				}
			}

			Session["BlockID_Value"] = blockID;
			Session["PcsIDs"] = pcsIDs.Distinct().ToList();
			Session["BlockIDs"] = null;

			return connectionStringOk;
		}
		public string BlockID_FromPanel(string workOrder, string connectionStringOption)
		{
			List<string> blockIDs = new List<string>();
			string connectionStringOk = null;
			var LaserEmap = Singleton_07_Pismo.IFlexAssy_03_LaserEMapService.FlexAssy_03_LaserEMap_GetByPanelID(workOrder, "Pismo");
			if (LaserEmap?.Rows.Count > 0)
			{
				for (int i = 0; i < LaserEmap.Rows.Count; i++)
				{
					blockIDs.Add(LaserEmap.Rows[i]["BlockID"].ToString());
				}
			}

			Session["WorkOrder_Value"] = workOrder;
			Session["BlockIDs"] = blockIDs.Distinct().ToList();
			Session["PcsIDs"] = null;

			return connectionStringOption;
		}
		public string BlockID_FromWorkOrder(string workOrder, string connectionStringOption)
		{
			List<string> blockIDs = new List<string>();
			string connectionStringOk = null;
			DataTable dt = Singleton_02_FlexAssy.IFlexAssy_BlockIDService.FlexAssy_BlockID_GetByIndicationNumber(workOrder, connectionStringOk, out connectionStringOption);
			if (dt?.Rows.Count > 0)
			{
				for (int i = 0; i < dt.Rows.Count; i++)
				{
					blockIDs.Add(dt.Rows[i]["BlockID"].ToString());
				}
			}
			// 2024-08-01 tạm thời bỏ đi đợi logic lấy từ bảng mới của anh Luân
			//else
			//{
			//    //phuc vu cho F1 E-CHECK 20220421
			//    DataTable dt_F1 = Singleton_01_BareFlex.ISource_BareFlex_ErpData_Service.Tbl2DHeatMap_GetDataWith_Process_Block(workOrder);
			//    if (dt_F1?.Rows.Count > 0)
			//    {
			//        for (int i = 0; i < dt_F1.Rows.Count; i++)
			//        {
			//            blockIDs.Add(dt_F1.Rows[i]["block_id"].ToString());
			//        }
			//    }
			//    else
			//    {
			//        //AVI 20220421
			//        //DataTable dt_AVI_F1 = Singleton_01_BareFlex.ISource_BareFlex_ErpData_Service.AVI_GetDataWith_Process_Block(workOrder);
			//        //if (dt_AVI_F1?.Rows.Count > 0)
			//        //{
			//        //    for (int i = 0; i < dt_AVI_F1.Rows.Count; i++)
			//        //    {
			//        //        blockIDs.Add(dt_AVI_F1.Rows[i]["blockid"].ToString());
			//        //    }
			//        //}
			//    }
			//}

			Session["WorkOrder_Value"] = workOrder;
			Session["BlockIDs"] = blockIDs.Distinct().ToList();
			Session["PcsIDs"] = null;

			return connectionStringOption;
		}
		public string PcsID_FromWorkOrder(string workOrder, string connectionStringOption)
		{
			List<string> pcsIDs = new List<string>();

			string connectionStringOk;
			DataTable blockIDs = Singleton_02_FlexAssy.IFlexAssy_BlockIDService.FlexAssy_BlockID_GetByIndicationNumber(workOrder, connectionStringOption, out connectionStringOk);
			if (blockIDs?.Rows.Count > 0)
			{
				DataTable dt = Singleton_02_FlexAssy.IFlexAssy_08_LaserMarkingService.FlexAssy_08_LaserMarking_GetListProductIDByListBlockID(blockIDs, connectionStringOk);
				if (dt?.Rows.Count > 0)
				{
					for (int i = 0; i < dt.Rows.Count; i++)
					{
						pcsIDs.Add(dt.Rows[i]["ProductID"].ToString());
					}
				}
			}

			Session["WorkOrder_Value"] = workOrder;
			Session["PcsIDs"] = pcsIDs.Distinct().ToList();
			Session["BlockIDs"] = null;

			return connectionStringOk;
		}

		public JsonResult DataSearchPaging(string type, int pageNumber, int pageSize)
		{
			List<string> lists = new List<string>();
			switch (type)
			{
				case "PcsIDs":
					if (Session["PcsIDs"] != null)
					{
						lists = (List<string>)Session["PcsIDs"];
					}
					break;

				case "BlockIDs":
					if (Session["BlockIDs"] != null)
					{
						lists = (List<string>)Session["BlockIDs"];
					}
					break;
			}

			return Json(lists.Skip((pageNumber - 1) * pageSize).Take(pageSize), JsonRequestBehavior.AllowGet);
		}

		public ActionResult ClearSession(string typeSearch, string urlCurrent)
		{
			switch (typeSearch)
			{
				case "WorkOrder":
					if (Session["WorkOrder_Value"] != null)
					{
						Session.Remove("WorkOrder_Value");
					}
					break;

				case "BlockID":
					if (Session["BlockID_Value"] != null)
					{
						Session.Remove("BlockID_Value");
					}
					break;
			}

			if (Session["PcsIDs"] != null)
			{
				Session.Remove("PcsIDs");
			}

			if (Session["BlockIDs"] != null)
			{
				Session.Remove("BlockIDs");
			}

			return Redirect(urlCurrent);
		}
		public string ExportStageChange(string FlexStep, string ChangeValue)
		{
			string strResult = "";
			DataTable dtStageActive = Singleton_03_Common.IStageSettingActiveViewTraceService.StageSettingActiveViewTrace_GetAll("#");
			ViewBag.StageActive = dtStageActive;
			DataTable dtStageActiveBareFlex = Singleton_03_Common.IStageSettingActiveViewTraceService.StageSettingActiveViewTrace_GetAll("F1");

			DataTable dtStageActivePismo = Singleton_03_Common.IStageSettingActiveViewTraceService.StageSettingActiveViewTrace_GetAllPismo();
			ViewBag.StageActivePismo = dtStageActivePismo;
			ExportStage ExportStageObject = new ExportStage();
			if (Session["StageExport"] != null)
			{
				string strExportStage = Session["StageExport"].ToString();
				object ssObject = new JavaScriptSerializer().DeserializeObject(strExportStage);
				ExportStageObject = new ExportStage(ssObject);
				int index = Array.IndexOf(ExportStageObject.Stage, FlexStep);
				if (index >= 0)
				{
					ExportStageObject.Checked[index] = ChangeValue;
				}
				Session["StageExport"] = new JavaScriptSerializer().Serialize(ExportStageObject);
				if (ExportStage.checkAllStatus(ExportStageObject, dtStageActiveBareFlex) == 0)
				{
					strResult = "Bare";
				}
				else if (ExportStage.checkAllStatus(ExportStageObject, dtStageActive) == 0)
				{
					strResult = "Assy";
				}
				else if (ExportStage.checkAllStatus(ExportStageObject, dtStageActivePismo) == 0)
				{
					strResult = "Pismo";
				}
			}
			return strResult;
		}
		public void exportFilterChange(Boolean changeValue)
		{
			Session["exportFilterChange"] = changeValue;
		}
		public void CheckAllChange(Boolean ChangeValue, string Type)
		{
			string ExportStage = "";
			ExportStage ExportStageObject = new ExportStage();
			DataTable dtStageActive = new DataTable();
			if (Type == "Bare")
			{
				dtStageActive = Singleton_03_Common.IStageSettingActiveViewTraceService.StageSettingActiveViewTrace_GetAll("F1");
			}
			else if (Type == "Assy")
			{
				dtStageActive = Singleton_03_Common.IStageSettingActiveViewTraceService.StageSettingActiveViewTrace_GetAll("#");
			}
			else if (Type == "Pismo")
			{
				dtStageActive = Singleton_03_Common.IStageSettingActiveViewTraceService.StageSettingActiveViewTrace_GetAllPismo();
			}

			if (ChangeValue)
			{
				if (Session["StageExport"] != null)
				{
					ExportStage = Session["StageExport"].ToString();
					object ssObject = new JavaScriptSerializer().DeserializeObject(ExportStage);
					ExportStageObject = new ExportStage(ssObject);
				}
				if (dtStageActive != null)
				{
					foreach (DataRow row in dtStageActive.Rows)
					{
						int index = Array.IndexOf(ExportStageObject.Stage, row["NameUrl"].ToString());
						if (index >= 0)
						{
							ExportStageObject.Checked.SetValue("true", index);
						}
					}
				}
				if (Type == "Bare")
				{
					ViewBag.BareFirstTime = 1;
				}
				else if (Type == "Assy")
				{
					ViewBag.AssyFirstTime = 1;
				}
				else if (Type == "Pismo")
				{
					ViewBag.AssyFirstTime = 1;
				}
				Session["StageExport"] = new JavaScriptSerializer().Serialize(ExportStageObject);
			}
			else
			{
				if (Session["StageExport"] != null)
				{
					ExportStage = Session["StageExport"].ToString();
					object ssObject = new JavaScriptSerializer().DeserializeObject(ExportStage);
					ExportStageObject = new ExportStage(ssObject);
				}
				if (dtStageActive != null)
				{
					foreach (DataRow row in dtStageActive.Rows)
					{
						int index = Array.IndexOf(ExportStageObject.Stage, row["NameUrl"].ToString());
						if (index >= 0)
						{
							ExportStageObject.Checked.SetValue("false", Array.IndexOf(ExportStageObject.Stage, row["NameUrl"].ToString()));
						}
					}
				}
				if (Type == "Bare")
				{
					ViewBag.BareFirstTime = 0;
				}
				else if (Type == "Assy")
				{
					ViewBag.AssyFirstTime = 0;
				}
				else if (Type == "Pismo")
				{
					ViewBag.AssyFirstTime = 0;
				}
				Session["StageExport"] = new JavaScriptSerializer().Serialize(ExportStageObject);
			}
		}
	}
}