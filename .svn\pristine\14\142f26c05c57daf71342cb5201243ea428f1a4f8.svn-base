﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Drawing;
using System.Text;
using System.Text.RegularExpressions;
using System.Windows.Forms;
using Trace_AbilitySystem.Libs;

namespace Trace_AbilitySystem_FlexAssy_Interface_30_OBADefine
{
    public partial class FrmOQCDefine : Form
    {
        private readonly string _serviceName = "Trace-AbilitySystem_FlexAssy_Interface_30_OQCDefine";
        private string _connectionString1;
        private string _connectionString2;
        private string _pathCsvLocal;
        private string _pathCsvServer;
        private bool _isEdit = false;
        private int _lengthProductID = 17;

        //  Manager for serial port Handy + Fix
        private string _strHandy;
        private readonly SerialPortManager _spManagerHandyManager;

        private string _strFix;
        private readonly SerialPortManager _spManagerFixManager;

        public FrmOQCDefine()
        {
            if (Common.IsProcessOpen(_serviceName))
            {
                DialogHelper.Info("Process " + _serviceName + " is running");
                Environment.Exit(0);
            }
            else
            {
                InitializeComponent();

                //Com Handy
                _spManagerHandyManager = new SerialPortManager();
                _spManagerHandyManager.NewSerialDataRecieved += SpManager_NewSerialDataRecieved_Handy;

                //Com Fix
                _spManagerFixManager = new SerialPortManager();
                _spManagerFixManager.NewSerialDataRecieved += SpManager_NewSerialDataRecieved_Fix;

                InitGridDataTable(dataGridView);
            }
        }

        private void FrmOBADefine_Load(object sender, EventArgs e)
        {
            try
            {
                lbItemName.Text = "---";
                _connectionString1 = ConfigurationManager.ConnectionStrings["ConnectionString1"]?.ConnectionString;
                _connectionString2 = ConfigurationManager.ConnectionStrings["ConnectionString2"]?.ConnectionString;

                if (!int.TryParse(ConfigurationManager.AppSettings["LengthProductID"], out _lengthProductID))
                {
                    _lengthProductID = 17;
                }

                txtProductId.MaxLength = _lengthProductID;
                _pathCsvLocal = ConfigurationManager.AppSettings["PathCsvLocal"];
                _pathCsvServer = ConfigurationManager.AppSettings["PathCsvServer"];
                lbPathCsvLocal.Text = "(Local) Path Export CSV: " + _pathCsvLocal;
                lbPathCsvServer.Text = "(Server) Path Export CSV: " + _pathCsvServer;

                _spManagerHandyManager.StartListening("Handy");
                _spManagerFixManager.StartListening("Fix");
            }
            catch (Exception ex)
            {
                DialogHelper.Error(ex.Message);
            }
        }

        private void InitGridDataTable(DataGridView dtGridView)
        {
            try
            {
                var columnHeaders = new List<string> { "ProductID", "ItemName", "IndicationNumber", "OperatorID", "CreatedDate", "Action", "IsFromDB" };
                dtGridView.ColumnCount = columnHeaders.Count;
                dtGridView.ColumnHeadersVisible = true;

                // Thiết lập Style cho Header
                DataGridViewCellStyle columnHeaderStyle = new DataGridViewCellStyle
                {
                    BackColor = Color.Azure,
                    Font = new Font("Tahoma", 11, FontStyle.Bold),
                    Alignment = DataGridViewContentAlignment.MiddleCenter
                };

                dtGridView.ColumnHeadersDefaultCellStyle = columnHeaderStyle;
                dtGridView.ColumnHeadersDefaultCellStyle.BackColor = Color.Azure;
                dtGridView.EnableHeadersVisualStyles = false;
                dtGridView.RowHeadersVisible = false;
                dtGridView.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
                dtGridView.ColumnHeadersHeight = 30;

                for (int i = 0; i < columnHeaders.Count; i++)
                {
                    dtGridView.Columns[i].HeaderText = columnHeaders[i];
                    dtGridView.Columns[i].SortMode = DataGridViewColumnSortMode.NotSortable;
                }
                dtGridView.Columns[0].Width = 300;
                dtGridView.Columns[1].Width = 100;
                dtGridView.Columns[2].Width = 300;
                dtGridView.Columns[3].Width = 100;
                dtGridView.Columns[4].Width = 200;
                dtGridView.Columns[5].Visible = false;
                dtGridView.Columns[6].Visible = false;

                // Remove row default
                dtGridView.AllowUserToAddRows = false;
                dtGridView.AutoGenerateColumns = false;

                // Auto resize height multi line cell
                dtGridView.DefaultCellStyle.WrapMode = DataGridViewTriState.True;

                dtGridView.RowsDefaultCellStyle.SelectionBackColor = Color.DeepSkyBlue;
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp(ex.Message);
            }
        }

        private void AddRowOQC(DataGridView dtGridView, string productID, string itemName, string indicationNumber, string operatorID, DateTime dateTime, bool isFromDB)
        {
            dtGridView.Rows.Insert(0, new string[] {
                productID,
                itemName,
                indicationNumber,
                operatorID,
                dateTime.ToString("yyyy/MM/dd HH:mm:ss"),
                "Delete",
                isFromDB.ToString()
            });

            DataGridViewLinkCell linkCell = new DataGridViewLinkCell
            {
                Value = "Delete"
            };
            dataGridView[5, 0] = linkCell;

            dtGridView.Rows[0].Height = 30;
            dtGridView.Rows[0].DefaultCellStyle.Font = new Font("Tahoma", 10, FontStyle.Regular);
            dtGridView.Rows[0].DefaultCellStyle.BackColor = Color.White;
            dtGridView.Rows[0].ReadOnly = true;
            dtGridView.Rows[0].DefaultCellStyle.BackColor = !isFromDB ? Color.Linen : Color.White;
        }

        private void SpManager_NewSerialDataRecieved_Handy(object sender, SerialDataEventArgs e)
        {
            if (InvokeRequired)
            {
                // Using this.Invoke causes deadlock when closing serial port, and BeginInvoke is good practice anyway.
                BeginInvoke(new EventHandler<SerialDataEventArgs>(SpManager_NewSerialDataRecieved_Handy), sender, e);
                return;
            }

            // This application is connected to a GPS sending ASCCI characters, so data is converted to text
            var tbData = Encoding.ASCII.GetString(e.Data);
            _strHandy += tbData;

            // Đọc dữ liệu cho tới khi gặp ký tự '\r'
            if (_strHandy.Contains("\r"))
            {
                _strHandy = _strHandy.Replace("\r", "").Replace("\n", "").Trim();
                txtOperatorID.Text = _strHandy;
                _strHandy = "";

                if (txtOperatorID.Text.Trim().Length != 5)
                {
                    DialogHelper.Warning("OperatorID không đúng định dạng/ OperatorID wrong format");
                    return;
                }

                if (txtNumber.Text.Trim().Length == 0 || txtNumber.Text.Trim() == "0")
                {
                    txtNumber.Enabled = true;
                    txtNumber.Focus();
                }
            }
        }

        private void SpManager_NewSerialDataRecieved_Fix(object sender, SerialDataEventArgs e)
        {
            if (InvokeRequired)
            {
                // Using this.Invoke causes deadlock when closing serial port, and BeginInvoke is good practice anyway.
                BeginInvoke(new EventHandler<SerialDataEventArgs>(SpManager_NewSerialDataRecieved_Fix), sender, e);
                return;
            }

            // This application is connected to a GPS sending ASCCI characters, so data is converted to text
            var tbData = Encoding.ASCII.GetString(e.Data);
            _strFix += tbData;

            // Đọc dữ liệu cho tới khi gặp ký tự '\r'
            if (_strFix.Contains("\r"))
            {
                if (txtOperatorID.Text.Trim().Length == 0 || txtNumber.Text.Trim().Length == 0 || txtIndiNumber.Text.Trim().Length == 0)
                {
                    lbStatusProduct.Text = @"Chưa nhập đủ thông tin đầu vào/ Not enough input information yet";
                    _strFix = "";

                    return;
                }

                _strFix = _strFix.Replace("\r", "").Replace("\n", "").Trim();
                txtProductId.Text = _strFix;
                _strFix = "";

                if (txtProductId.Text.Trim().Length == _lengthProductID)
                {
                    CheckProductID();
                }
                else
                {
                    lbStatusProduct.Text = @"ProductID có độ dài: " + txtProductId.Text.Trim().Length + " != " + _lengthProductID + "/ ProductID has length: " + txtProductId.Text.Trim().Length + " != " + _lengthProductID;
                    lbStatusProduct.ForeColor = Color.Red;
                }
            }
        }

        private void TxtOperatorID_TextChanged(object sender, EventArgs e)
        {
            var cursorPosition = txtOperatorID.SelectionStart;
            txtOperatorID.Text = Regex.Replace(txtOperatorID.Text, "[^0-9]", "");
            txtOperatorID.SelectionStart = cursorPosition;
        }

        private void TxtOperatorID_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                if (txtOperatorID.Text.Trim().Length != 5)
                {
                    DialogHelper.Warning("OperatorID không đúng định dạng/ OperatorID wrong format");
                    txtOperatorID.Focus();

                    return;
                }

                txtOperatorID.Enabled = false;
                txtNumber.Enabled = true;
                txtNumber.Focus();
            }
        }

        private void TxtNumber_TextChanged(object sender, EventArgs e)
        {
            var cursorPosition = txtNumber.SelectionStart;
            txtNumber.Text = Regex.Replace(txtNumber.Text, "[^0-9]", "");
            txtNumber.SelectionStart = cursorPosition;
        }

        private void TxtNumber_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                Check_TextNumber();
            }
        }

        private bool Check_TextNumber()
        {
            if (txtOperatorID.Text.Trim().Length != 5)
            {
                DialogHelper.Warning("OperatorID không đúng định dạng/ OperatorID wrong format");
                return false;
            }

            if (txtNumber.Text.Trim().Length == 0 || txtNumber.Text.Trim() == "0")
            {
                DialogHelper.Warning("Số lượng ProductID cần định nghĩa OQC phải > 0/ The number of ProductIDs needed to define OQC must be > 0");
                txtNumber.SelectAll();
                txtNumber.Focus();

                return false;
            }

            if (_isEdit)
            {
                if (int.Parse(txtNumber.Text) < int.Parse(lbTotalInput.Text))
                {
                    DialogHelper.Warning("Số lượng ProductID cần sửa không được nhỏ hơn số lượng bạn vừa nhập vào/ The number of ProductID to fix must not be less than the number you just entered");
                    txtNumber.SelectAll();
                    txtNumber.Focus();

                    return false;
                }

                if (int.Parse(txtNumber.Text) == int.Parse(lbTotalInput.Text))
                {
                    DialogHelper.Info("Đã nhập đủ số lượng: " + txtNumber.Text + " ProductID cần định nghĩa OQC/ Entered sufficient quantities: " + txtNumber.Text + " ProductID needs OQC definition");
                    Reset();

                    return false;
                }

                _isEdit = false;
                txtNumber.Enabled = false;
                txtProductId.Focus();

                return true;
            }

            _isEdit = false;
            txtNumber.Enabled = false;
            txtIndiNumber.Enabled = true;
            txtIndiNumber.Focus();

            return true;
        }

        private void TxtIndiNumber_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                Check_TxtIndicationNumber();
            }
        }

        private bool Check_TxtIndicationNumber()
        {
            if (!Check_TextNumber())
                return false;

            dataGridView.ClearSelection();
            dataGridView.Rows.Clear();
            dataGridView.Refresh();

            //txtProductId.Text = "";
            //lbStatusProduct.Text = "";
            //lbTotal.Text = "0";

            if (txtIndiNumber.Text.Trim().Length == 0)
            {
                txtIndiNumber.Focus();
                return false;
            }

            // Check indi exists db
            int rs = Singleton_02_FlexAssy.ISource_FlexAssy_BoardRegistration_Service.IsCheckExistsIndi(txtIndiNumber.Text.Trim(), out string itemName, _connectionString2);
            if (rs == -9)
            {
                DialogHelper.Warning("Mất kết nối với Database BoardRegistration/ Connection to Database BoardRegistration lost");
                return false;
            }

            if (rs <= 0)
            {
                DialogHelper.Warning("IndicationNumber: " + txtIndiNumber.Text + " không tồn tại trong Database BoardRegistration/ IndicationNumber: " + txtIndiNumber.Text + " does not exist in Database BoardRegistration");
                return false;
            }

            lbItemName.Text = itemName;
            if (string.IsNullOrEmpty(itemName) || itemName == "---")
            {
                DialogHelper.Warning("IndicationNumber: " + txtIndiNumber.Text + " không có giá trị ItemName/ IndicationNumber: " + txtIndiNumber.Text + " has no ItemName value");
                txtIndiNumber.Focus();
                return false;
            }

            // Nếu xử dụng cần kiểm tra lại phần xóa vì đang hiển thị số lượng sai khi xóa
            DataTable dt = Singleton_02_FlexAssy.ISource_FlexAssy_SEI_VCDTrace_Service.OQC_Define_GetByIndicationNumber(txtIndiNumber.Text.Trim(), _connectionString1);
            if (dt?.Rows.Count > 0)
            {
                dt.DefaultView.Sort = "CreatedDate asc";
                dt = dt.DefaultView.ToTable();

                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    AddRowOQC(dataGridView, dt.Rows[i]["ProductID"].ToString(), dt.Rows[i]["ItemName"].ToString(), dt.Rows[i]["IndicationNumber"].ToString(), dt.Rows[i]["OperatorID"].ToString(), Convert.ToDateTime(dt.Rows[i]["CreatedDate"]), true);
                }

                lbTotal.Text = dt.Rows.Count.ToString();
            }

            txtIndiNumber.Enabled = false;
            txtProductId.Focus();

            return true;
        }

        private void TxtProductId_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                CheckProductID();
            }
        }

        private void CheckProductID()
        {
            if (txtOperatorID.Text.Trim().Length != 5)
            {
                DialogHelper.Warning("OperatorID không đúng định dạng/ OperatorID wrong format");
                txtOperatorID.Focus();

                return;
            }

            if (!Check_TextNumber())
                return;

            if (!Check_TxtIndicationNumber())
                return;

            if (int.Parse(txtNumber.Text) == int.Parse(lbTotalInput.Text))
            {
                DialogHelper.Info("Đã nhập đủ số lượng: " + txtNumber.Text + " ProductID cần định nghĩa OQC/ Entered sufficient quantities: " + txtNumber.Text + " ProductID needs OQC definition");
                Reset();

                return;
            }

            if (txtProductId.Text.Trim().Length == 0)
            {
                DialogHelper.Warning("ProductID không được bỏ trống/ ProductID must not be blank");
                txtProductId.Focus();

                return;
            }
            else
            {
                if (txtProductId.Text.Length != _lengthProductID)
                {
                    lbStatusProduct.Text = @"ProductID có độ dài: " + txtProductId.Text.Trim().Length + " != " + _lengthProductID + "/ ProductID has length: " + txtProductId.Text.Trim().Length + " != " + _lengthProductID;
                    lbStatusProduct.ForeColor = Color.Red;

                    return;
                }

                if (Singleton_02_FlexAssy.ISource_FlexAssy_SEI_VCDTrace_Service.OQC_Define_CheckExistsProductID(txtProductId.Text.Trim(), _connectionString1) == 1)
                {
                    lbStatusProduct.Text = "ProductID:" + txtProductId.Text.Trim() + " đã tồn tại/ ProductID: " + txtProductId.Text.Trim() + " already exists";
                    lbStatusProduct.ForeColor = Color.Red;
                    txtProductId.Text = "";
                    txtProductId.Focus();
                }
                else
                {
                    int rs = Singleton_02_FlexAssy.ISource_FlexAssy_SEI_VCDTrace_Service.OQC_Define_Insert(txtProductId.Text.Trim(), lbItemName.Text.Trim(), txtIndiNumber.Text.Trim(), txtOperatorID.Text.Trim(), _connectionString1);
                    if (rs > 0)
                    {
                        AddRowOQC(dataGridView, txtProductId.Text.Trim(), lbItemName.Text.Trim(), txtIndiNumber.Text.Trim(), txtOperatorID.Text.Trim(), DateTime.Now, false);
                        lbTotal.Text = (int.Parse(lbTotal.Text) + 1).ToString();
                        lbTotalInput.Text = (int.Parse(lbTotalInput.Text) + 1).ToString();

                        DataTable table = new DataTable();
                        table.Columns.Add("ProductID", typeof(string));
                        table.Columns.Add("IndicationNumber", typeof(string));
                        table.Columns.Add("OperatorID", typeof(string));
                        table.Columns.Add("CreatedDate", typeof(DateTime));

                        table.Rows.Add(txtProductId.Text.Trim(), txtIndiNumber.Text.Trim(), txtOperatorID.Text.Trim(), DateTime.Now);

                        Common.OQCDefine_WriteCsv(table, "OQCDefine" , _pathCsvLocal);
                        Common.OQCDefine_WriteCsv(table, "OQCDefine", _pathCsvServer);

                        lbStatusProduct.Text = "Thêm mới thành công / Add productID Success.";
                        lbStatusProduct.ForeColor = Color.Green;
                        txtProductId.Text = "";
                        txtProductId.Focus();

                        if (int.Parse(txtNumber.Text) == int.Parse(lbTotalInput.Text))
                        {
                            DialogHelper.Info("Đã nhập đủ số lượng: " + txtNumber.Text + " ProductID cần định nghĩa OQC/ Entered sufficient quantities: " + txtNumber.Text + " ProductID needs OQC definition");
                            Reset();
                        }
                    }
                }
            }
        }

        private void DataGridView_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.ColumnIndex == 5 && e.RowIndex != -1)
            {
                if (MessageBox.Show("Bạn có muốn xóa không?", "Đang xóa...", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                {
                    int rowIndex = e.RowIndex;
                    string productId = dataGridView.Rows[rowIndex].Cells[0].Value.ToString();
                    bool isFromDB = bool.Parse(dataGridView.Rows[rowIndex].Cells[5].Value.ToString());

                    int rs = Singleton_02_FlexAssy.ISource_FlexAssy_SEI_VCDTrace_Service.OQC_Define_Delete(productId, _connectionString1);
                    if (rs == -9)
                    {
                        DialogHelper.Warning("Lỗi kết nối");
                        return;
                    }

                    lbTotal.Text = (int.Parse(lbTotal.Text) - 1).ToString();
                    lbTotalInput.Text = !isFromDB ? (int.Parse(lbTotalInput.Text) - 1).ToString() : lbTotalInput.Text;
                    dataGridView.Rows.RemoveAt(rowIndex);
                    DialogHelper.Success("ProductID đã được xóa khỏi Database/ ProductID has been removed from Database");
                }
            }
        }

        private void BtnReset_Click(object sender, EventArgs e)
        {
            if (txtOperatorID.Text.Trim().Length == 5)
            {
                Reset();
            }
            else
            {
                Reset();
                txtNumber.Enabled = false;
            }
        }

        private void Reset()
        {
            dataGridView.ClearSelection();
            dataGridView.Rows.Clear();
            dataGridView.Refresh();

            txtNumber.Text = "";
            txtIndiNumber.Text = "";
            txtProductId.Text = "";
            lbStatusProduct.Text = "";
            lbTotal.Text = "0";
            lbTotalInput.Text = "0";
            lbItemName.Text = "---";
            txtSearch.Text = "";

            txtNumber.Enabled = true;
            txtIndiNumber.Enabled = false;
            txtNumber.Focus();
        }

        private void LbResetOperatorID_Click(object sender, EventArgs e)
        {
            txtOperatorID.Text = "";
        }

        private void LbEdit_Click(object sender, EventArgs e)
        {
            if (txtOperatorID.Text.Trim().Length == 0 || txtNumber.Text.Trim().Length == 0 || txtIndiNumber.Text.Trim().Length == 0)
            {
                DialogHelper.Warning("Bạn không thể sửa số lượng do chưa nhập đủ thông tin đầu vào/ You cannot edit the quantity because you have not entered enough information");
                return;
            }

            _isEdit = true;
            txtNumber.Enabled = true;
            txtNumber.SelectAll();
            txtNumber.Focus();
        }

        private void TxtSearch_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                if (txtNumber.Text.Length > 0 && (int.Parse(txtNumber.Text) != int.Parse(lbTotalInput.Text)))
                {
                    DialogHelper.Warning("Đang định nghĩa hàng OQC, không thể sử dụng chức năng tìm kiếm/ Defining OQC, can't use search function");
                    return;
                }

                if (txtSearch.Text.Trim().Length == 0)
                    return;

                DataTable dt = Singleton_02_FlexAssy.ISource_FlexAssy_SEI_VCDTrace_Service.OQC_Define_GetByProductID(txtSearch.Text.Trim(), _connectionString1);
                if (dt?.Rows.Count == 0)
                {
                    dt = Singleton_02_FlexAssy.ISource_FlexAssy_SEI_VCDTrace_Service.OQC_Define_GetByIndicationNumber(txtSearch.Text.Trim(), _connectionString1);
                }

                if (dt?.Rows.Count > 0)
                {
                    dt.DefaultView.Sort = "CreatedDate asc";
                    dt = dt.DefaultView.ToTable();

                    dataGridView.ClearSelection();
                    dataGridView.Rows.Clear();
                    dataGridView.Refresh();

                    for (int i = 0; i < dt.Rows.Count; i++)
                    {
                        AddRowOQC(dataGridView, dt.Rows[i]["ProductID"].ToString(), dt.Rows[i]["ItemName"].ToString(), dt.Rows[i]["IndicationNumber"].ToString(), dt.Rows[i]["OperatorID"].ToString(), Convert.ToDateTime(dt.Rows[i]["CreatedDate"]), true);
                    }

                    lbTotal.Text = dt.Rows.Count.ToString();
                }
                else
                {
                    DialogHelper.Info("Không tìm thấy dữ liệu nào/ Data not found");
                }
            }
        }

        private void FrmOBADefine_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (e.CloseReason == CloseReason.UserClosing)
            {
                DialogResult result = MessageBox.Show(@"Thoát chương trình/ Exit Program?.", @"Thoát/Exit", MessageBoxButtons.YesNo);
                if (result == DialogResult.Yes)
                {
                    _spManagerHandyManager.StopListening();
                    _spManagerFixManager.StopListening();

                    //Environment.Exit(0);
                    Application.Exit();
                }
                else
                {
                    e.Cancel = true;
                }
            }
            else
            {
                e.Cancel = true;
            }
        }

        private void txtProductId_TextChanged(object sender, EventArgs e)
        {

        }
    }
}