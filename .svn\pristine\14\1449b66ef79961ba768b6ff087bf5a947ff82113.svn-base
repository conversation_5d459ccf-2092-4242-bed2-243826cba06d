﻿@using System.Data;
@using Trace_AbilitySystem.Libs.DTOClass;
@model Trace_AbilitySystem.Libs.DTOClass.DataAllTrace
@{
    DataTable dtStageActive = (DataTable)ViewBag.StageActive;
    ExportStage exportStage = (ExportStage)ViewBag.ExportStage;
    var user = (DataTable)Session["account"];
}
@if (Model != null)
{
    <h2 class="bd-title" id="" style="font-weight: bold; font-size: 20px; color: #0081cc;margin-top:50px; text-decoration:none">III.IQC/IPQC/OQC/OBA/REL/ORT</h2>
    <hr />
}

<div class="divAssyStage" style='display:@(exportStage.Checked[Array.IndexOf(exportStage.Stage,"#30-oba")] == "true" ? "block" : "none")' id='div#30-oba'>
    @(Html.Partial("~/Views/FlexAssy/FlexAssy_30_OBA.cshtml"))
</div>
<div class="divAssyStage" style='display:@(exportStage.Checked[Array.IndexOf(exportStage.Stage,"#30-oba-ict")] == "true" ? "block" : "none")' id='div#30-oba'>
    @(Html.Partial("~/Views/FlexAssy/FlexAssy_30_OBA_ICT_New.cshtml"))
</div>
<div class="divAssyStage" style='display:@(exportStage.Checked[Array.IndexOf(exportStage.Stage,"#31-rel-ort")] == "true" ? "block" : "none")' id='div#31-rel-ort'>
    @(Html.Partial("~/Views/FlexAssy/FlexAssy_31_RELORT.cshtml"))
</div>
<div class="divAssyStage" style='display:@(exportStage.Checked[Array.IndexOf(exportStage.Stage,"#32-ort-thermal")] == "true" ? "block" : "none")' id='div#32-ort-thermal'>
    @(Html.Partial("~/Views/FlexAssy/FlexAssy_32_1_ORT_ThermalCycling.cshtml"))
</div>
<div class="divAssyStage" style='display:@(exportStage.Checked[Array.IndexOf(exportStage.Stage,"#33-ort-heatsoak")] == "true" ? "block" : "none")' id='div#33-ort-heatsoak'>
    @(Html.Partial("~/Views/FlexAssy/FlexAssy_32_2_ORT_HeatsoakAndRecovery.cshtml"))
</div>
<div class="divAssyStage" style='display:@(exportStage.Checked[Array.IndexOf(exportStage.Stage,"#34-ort-thermalshock")] == "true" ? "block" : "none")' id='div#34-ort-thermalshock'>
    @(Html.Partial("~/Views/FlexAssy/FlexAssy_32_3_ORT_ThermalShock.cshtml"))
</div>
<div class="divAssyStage" style='display:@(exportStage.Checked[Array.IndexOf(exportStage.Stage,"#35-ort-thermalcyclingflexbending")] == "true" ? "block" : "none")' id='div#35-ort-thermalcyclingflexbending'>
    @(Html.Partial("~/Views/FlexAssy/FlexAssy_32_4_ORT_ThermalCyclingAndFlexBending.cshtml"))
</div>
<div class="divAssyStage" style='display:@(exportStage.Checked[Array.IndexOf(exportStage.Stage,"#36-ort-flexbending")] == "true" ? "block" : "none")' id='div#36-ort-flexbending'>
    @(Html.Partial("~/Views/FlexAssy/FlexAssy_32_5_ORT_FlexBending.cshtml"))
</div>
<div class="divAssyStage" style='display:@(exportStage.Checked[Array.IndexOf(exportStage.Stage,"#37-ort-heatsoakflexbending")] == "true" ? "block" : "none")' id='div#37-ort-heatsoakflexbending'>
    @(Html.Partial("~/Views/FlexAssy/FlexAssy_32_6_ORT_HeatsoakAndFlexBending.cshtml"))
</div>
<div class="divAssyStage" style='display:@(exportStage.Checked[Array.IndexOf(exportStage.Stage,"#38-ipqc-b2bxsection")] == "true" ? "block" : "none")' id='div#38-ipqc-b2bxsection'>
    @(Html.Partial("~/Views/FlexAssy/FlexAssy_33_1_IPQC_B2BXsection.cshtml"))
</div>
<div class="divAssyStage" style='display:@(exportStage.Checked[Array.IndexOf(exportStage.Stage,"#39-ipqc-b2bpeelingtest")] == "true" ? "block" : "none")' id='div#39-ipqc-b2bpeelingtest'>
    @(Html.Partial("~/Views/FlexAssy/FlexAssy_33_2_IPQC_B2BPeelingTest.cshtml"))
</div>
<div class="divAssyStage" style='display:@(exportStage.Checked[Array.IndexOf(exportStage.Stage,"#40-ipqc-b2bxray")] == "true" ? "block" : "none")' id='div#40-ipqc-b2bxray'>
    @(Html.Partial("~/Views/FlexAssy/FlexAssy_33_3_IPQC_B2BXRay.cshtml"))
</div>
<div class="divAssyStage" style='display:@(exportStage.Checked[Array.IndexOf(exportStage.Stage,"#41-ipqc-punching")] == "true" ? "block" : "none")' id='div#41-ipqc-punching'>
    @(Html.Partial("~/Views/FlexAssy/FlexAssy_33_4_IPQC_OutlinePunchingAndDimension.cshtml"))
</div>
<div class="divAssyStage" style='display:@(exportStage.Checked[Array.IndexOf(exportStage.Stage,"#42-ipqc-wetting")] == "true" ? "block" : "none")' id='div#42-ipqc-wetting'>
    @(Html.Partial("~/Views/FlexAssy/FlexAssy_33_5_IPQC_PlasmaACFWettingAngle.cshtml"))
</div>
<div class="divAssyStage" style='display:@(exportStage.Checked[Array.IndexOf(exportStage.Stage,"#43-oqc-fai-measure")] == "true" ? "block" : "none")' id='div#43-oqc-fai-measure'>
    @(Html.Partial("~/Views/FlexAssy/FlexAssy_34_1_OQC_FAISPCMeasurement.cshtml"))
</div>
<div class="divAssyStage" style='display:@(exportStage.Checked[Array.IndexOf(exportStage.Stage,"#44-oqc-b2bpeeling")] == "true" ? "block" : "none")' id='div#44-oqc-b2bpeeling'>
    @(Html.Partial("~/Views/FlexAssy/FlexAssy_34_2_OQC_B2BPeelingTest.cshtml"))
</div>
<div class="divAssyStage" style='display:@(exportStage.Checked[Array.IndexOf(exportStage.Stage,"#45-oqc-b2bpulling")] == "true" ? "block" : "none")' id='div#45-oqc-b2bpulling'>
    @(Html.Partial("~/Views/FlexAssy/FlexAssy_34_3_OQC_B2BPullingTest.cshtml"))
</div>
<div class="divAssyStage" style='display:@(exportStage.Checked[Array.IndexOf(exportStage.Stage,"#46-oqc-b2bshearing")] == "true" ? "block" : "none")' id='div#46-oqc-b2bshearing'>
    @(Html.Partial("~/Views/FlexAssy/FlexAssy_34_4_OQC_B2BShearingTest.cshtml"))
</div>
<div class="divAssyStage" style='display:@(exportStage.Checked[Array.IndexOf(exportStage.Stage,"#47-oqc-acfflattness")] == "true" ? "block" : "none")' id='div#47-oqc-acfflattness'>
    @(Html.Partial("~/Views/FlexAssy/FlexAssy_34_5_OQC_ACFFlassness.cshtml"))
</div>
<div class="divAssyStage" style='display:@(exportStage.Checked[Array.IndexOf(exportStage.Stage,"#48-oqc-acfroughness")] == "true" ? "block" : "none")' id='div#48-oqc-acfroughness'>
    @(Html.Partial("~/Views/FlexAssy/FlexAssy_34_6_OQC_ACFRoughness.cshtml"))
</div>
<div class="divAssyStage" style='display:@(exportStage.Checked[Array.IndexOf(exportStage.Stage,"#49-oqc-acfbonding")] == "true" ? "block" : "none")' id='div#49-oqc-acfbonding'>
    @(Html.Partial("~/Views/FlexAssy/FlexAssy_34_7_OQC_ACFBonding.cshtml"))
</div>
<div class="divAssyStage" style='display:@(exportStage.Checked[Array.IndexOf(exportStage.Stage,"#50-oqc-hotoil")] == "true" ? "block" : "none")' id='div#50-oqc-hotoil'>
    @(Html.Partial("~/Views/FlexAssy/FlexAssy_34_8_OQC_HotOil.cshtml"))
</div>
<div class="divAssyStage" style='display:@(exportStage.Checked[Array.IndexOf(exportStage.Stage,"#51-oqc-heating4w")] == "true" ? "block" : "none")' id='div#51-oqc-heating4w'>
    @(Html.Partial("~/Views/FlexAssy/FlexAssy_34_9_OQC_Heating4W.cshtml"))
</div>
<div class="divAssyStage" style='display:@(exportStage.Checked[Array.IndexOf(exportStage.Stage,"#52-oqc-xsection")] == "true" ? "block" : "none")' id='div#52-oqc-xsection'>
    @(Html.Partial("~/Views/FlexAssy/FlexAssy_34_99_OQC_XSection.cshtml"))
</div>
<div class="divAssyStage" style='display:@(exportStage.Checked[Array.IndexOf(exportStage.Stage,"#32-ort")] == "true" ? "block" : "none")' id='div#32-ort'>
    @(Html.Partial("~/Views/FlexAssy/FlexAssy_32_ORT.cshtml"))
</div>
<div class="divAssyStage" style='display:@(exportStage.Checked[Array.IndexOf(exportStage.Stage,"#33-ipqc")] == "true" ? "block" : "none")' id='div#33-ipqc'>
    @(Html.Partial("~/Views/FlexAssy/FlexAssy_33_IPQC.cshtml"))
</div>
<div class="divAssyStage" style='display:@(exportStage.Checked[Array.IndexOf(exportStage.Stage,"#34-oqc")] == "true" ? "block" : "none")' id='div#34-oqc'>
    @(Html.Partial("~/Views/FlexAssy/FlexAssy_34_OQC.cshtml"))
</div>
<div class="divAssyStage" style='display:@(exportStage.Checked[Array.IndexOf(exportStage.Stage,"#35-oba")] == "true" ? "block" : "none")' id='div#35-oba'>
    @(Html.Partial("~/Views/FlexAssy/FlexAssy_35_OBA.cshtml"))
</div>
<div class="divAssyStage" style='display:@(exportStage.Checked[Array.IndexOf(exportStage.Stage,"#36-iqc")] == "true" ? "block" : "none")' id='div#36-iqc'>
    @(Html.Partial("~/Views/FlexAssy/FlexAssy_36_IQC.cshtml"))
</div>


@if (Model != null)
{
    <script type="text/javascript">
        $(document).ready(function () {
            try {
                var AreaSelect = window.localStorage.getItem("AreaSelect");
                if (AreaSelect != undefined) {
                    if (AreaSelect.includes("#")) {
                        var Item = AreaSelect + "anchor"
                        window.document.getElementById(Item).classList.add("anchorActive");
                        element = document.getElementById(AreaSelect.replace('#', ''));
                        var positiony = element.offsetTop - element.scrollTop;
                        $('html, body').animate({
                            scrollTop: positiony
                        }, 200);
                    }
                }
            }
            catch (err) {
                console.log(err.message);
            }
        });
    </script>
}
@*
    @(!bool.Parse(dtStageActive.Rows[0]["IsActive"].ToString()) ? null : Html.Partial("~/Views/FlexAssy/FlexAssy_01_FPCBaking.cshtml"))
    @(!bool.Parse(dtStageActive.Rows[1]["IsActive"].ToString()) ? null : Html.Partial("~/Views/FlexAssy/FlexAssy_02_SMTFPCPanelLoading.cshtml"))
    @(!bool.Parse(dtStageActive.Rows[2]["IsActive"].ToString()) ? null : Html.Partial("~/Views/FlexAssy/FlexAssy_03_SolderPastePrinting.cshtml"))
    @(!bool.Parse(dtStageActive.Rows[3]["IsActive"].ToString()) ? null : Html.Partial("~/Views/FlexAssy/FlexAssy_04_SolderPasteInspection.cshtml"))
    @(!bool.Parse(dtStageActive.Rows[4]["IsActive"].ToString()) ? null : Html.Partial("~/Views/FlexAssy/FlexAssy_05_PickAndPlace.cshtml"))
    @(!bool.Parse(dtStageActive.Rows[5]["IsActive"].ToString()) ? null : Html.Partial("~/Views/FlexAssy/FlexAssy_06_PreReflowAOI.cshtml"))
    @(!bool.Parse(dtStageActive.Rows[6]["IsActive"].ToString()) ? null : Html.Partial("~/Views/FlexAssy/FlexAssy_07_Reflow.cshtml"))
    @(!bool.Parse(dtStageActive.Rows[7]["IsActive"].ToString()) ? null : Html.Partial("~/Views/FlexAssy/FlexAssy_08_LaserMarking.cshtml"))
    @(!bool.Parse(dtStageActive.Rows[8]["IsActive"].ToString()) ? null : Html.Partial("~/Views/FlexAssy/FlexAssy_09_PostReflowAOI.cshtml"))
    @(!bool.Parse(dtStageActive.Rows[9]["IsActive"].ToString()) ? null : Html.Partial("~/Views/FlexAssy/FlexAssy_10_XrayInspection.cshtml"))
    @(!bool.Parse(dtStageActive.Rows[10]["IsActive"].ToString()) ? null : Html.Partial("~/Views/FlexAssy/FlexAssy_11_GlueDispensing.cshtml"))
    @(!bool.Parse(dtStageActive.Rows[11]["IsActive"].ToString()) ? null : Html.Partial("~/Views/FlexAssy/FlexAssy_12_GluePreCuringInspection.cshtml"))
    @(!bool.Parse(dtStageActive.Rows[12]["IsActive"].ToString()) ? null : Html.Partial("~/Views/FlexAssy/FlexAssy_13_GlueCuringHeatCure.cshtml"))
    @(!bool.Parse(dtStageActive.Rows[13]["IsActive"].ToString()) ? null : Html.Partial("~/Views/FlexAssy/FlexAssy_14_GlueCuringUVCure.cshtml"))
    @(!bool.Parse(dtStageActive.Rows[14]["IsActive"].ToString()) ? null : Html.Partial("~/Views/FlexAssy/FlexAssy_15_GluePostCuringInspection.cshtml"))
    @(!bool.Parse(dtStageActive.Rows[15]["IsActive"].ToString()) ? null : Html.Partial("~/Views/FlexAssy/FlexAssy_16_BarcodePSATSAStiffenerPasting.cshtml"))
    @(!bool.Parse(dtStageActive.Rows[16]["IsActive"].ToString()) ? null : Html.Partial("~/Views/FlexAssy/FlexAssy_17_BarcodePSATSAStiffenerInspection.cshtml"))
    @(!bool.Parse(dtStageActive.Rows[17]["IsActive"].ToString()) ? null : Html.Partial("~/Views/FlexAssy/FlexAssy_18_PanelAndSingleFPCBarcodeLink.cshtml"))
    @(!bool.Parse(dtStageActive.Rows[18]["IsActive"].ToString()) ? null : Html.Partial("~/Views/FlexAssy/FlexAssy_19_Punching.cshtml"))
    @(!bool.Parse(dtStageActive.Rows[19]["IsActive"].ToString()) ? null : Html.Partial("~/Views/FlexAssy/FlexAssy_20_Prebending.cshtml"))
    @(!bool.Parse(dtStageActive.Rows[20]["IsActive"].ToString()) ? null : Html.Partial("~/Views/FlexAssy/FlexAssy_21_RoboticSoldering.cshtml"))
    @(!bool.Parse(dtStageActive.Rows[21]["IsActive"].ToString()) ? null : Html.Partial("~/Views/FlexAssy/FlexAssy_22_ICT.cshtml"))
    @(!bool.Parse(dtStageActive.Rows[22]["IsActive"].ToString()) ? null : Html.Partial("~/Views/FlexAssy/FlexAssy_23_FCT.cshtml"))
    @(!bool.Parse(dtStageActive.Rows[23]["IsActive"].ToString()) ? null : Html.Partial("~/Views/FlexAssy/FlexAssy_24_AutoPickup.cshtml"))
    @(!bool.Parse(dtStageActive.Rows[24]["IsActive"].ToString()) ? null : Html.Partial("~/Views/FlexAssy/FlexAssy_25_FVI.cshtml"))
    @(!bool.Parse(dtStageActive.Rows[25]["IsActive"].ToString()) ? null : Html.Partial("~/Views/FlexAssy/FlexAssy_26_QcGate.cshtml"))
    @(!bool.Parse(dtStageActive.Rows[26]["IsActive"].ToString()) ? null : Html.Partial("~/Views/FlexAssy/FlexAssy_27_Plasma.cshtml"))
    @(!bool.Parse(dtStageActive.Rows[27]["IsActive"].ToString()) ? null : Html.Partial("~/Views/FlexAssy/FlexAssy_28_ContactAngle.cshtml"))
    @(!bool.Parse(dtStageActive.Rows[28]["IsActive"].ToString()) ? null : Html.Partial("~/Views/FlexAssy/FlexAssy_29_PackingBarcodeScanning.cshtml"))
    @(!bool.Parse(dtStageActive.Rows[29]["IsActive"].ToString()) ? null : Html.Partial("~/Views/FlexAssy/FlexAssy_30_OBA.cshtml"))
    @(!bool.Parse(dtStageActive.Rows[30]["IsActive"].ToString()) ? null : Html.Partial("~/Views/FlexAssy/FlexAssy_31_RELORT.cshtml"))
*@