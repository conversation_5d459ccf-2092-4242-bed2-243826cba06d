﻿using System;
using System.Data;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Trace_AbilitySystem.Libs.ITrace_07_Pismo_Service
{
    public interface IFlexAssy_05_ViaAOIService
    {
        DataTable ViaAOI_GetDataWithCreatedDate(DateTime createdDate, int minutes, string connectionStringOption);
        Task<int> FlexAssy_ViaAOI_ProcessData(string factory, DateTime createdDateSearch, int minutes, string connectionStringOption);
        int FlexAssy_ViaAOI_Insert(int PKID, string BlockID, DateTime dateTime, string MachineID, string Model, string Layer,string LotNumber, string ErrorMap, string FullName, string GetPCSID, DateTime LocalCreatedDate, string connectionStringOption);
        DataTable ViaAOI_Detail_GetDataWithCreatedDate(DateTime createdDate, int minutes, string connectionStringOption);
        Task<int> FlexAssy_ViaAOI_Detail_ProcessData(string factory, DateTime createdDateSearch, int minutes, string connectionStringOption);
        int FlexAssy_ViaAOI_Detail_Insert(int PKID, int ViaAOIId, DateTime dateTime, int Location, string ProductID, string Result, string DefectCode, string connectionStringOption);
        DataTable FlexAssy_05_ViaAOI_GetByProductID(string productID, string connectionStringOption);
        DataTable FlexAssy_05_ViaAOI_GetByBlockID(string blockID, string connectionStringOption);
        DataTable FlexAssy_05_ViaAOI_GetByPkID(int PKID);
    }

}
