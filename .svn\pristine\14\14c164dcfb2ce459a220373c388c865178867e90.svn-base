﻿using Trace_AbilitySystem.Libs.SPC_Commom_Service;
using Trace_AbilitySystem.Libs.ISPC_Commom_Service;

namespace Trace_AbilitySystem.Libs
{
    public static class Singleton_06_SPC
    {
        private static ISPC_01_SPI_Service ISPC_01_SPI_Service;
        private static ISPC_02_ICTService ISPC_02_ICTService;
        private static ISPC_02_ICTServiceDefect ISPC_02_ICTServiceDefect;
        private static ISPC_03_FAIService ISPC_03_FAIService;
        private static ISPC_03_FVIServiceDefect ISPC_03_FVIServiceDefect;
        private static ISPC_CommonService ISPC_CommonService;

        private static readonly object SyncLock = new object();
        public static ISPC_01_SPI_Service iSPC_01_SPI_Service
        {
            get
            {
                if (ISPC_01_SPI_Service == null)
                {
                    lock (SyncLock)
                    {
                        if (ISPC_01_SPI_Service == null)
                            ISPC_01_SPI_Service = new SPC_01_SPI_Service();
                    }
                }
                return ISPC_01_SPI_Service;
            }
        }
        public static ISPC_02_ICTService iSPC_02_ICTService
        {
            get
            {
                if (ISPC_02_ICTService == null)
                {
                    lock (SyncLock)
                    {
                        if (ISPC_02_ICTService == null)
                            ISPC_02_ICTService = new SPC_02_ICTService();
                    }
                }
                return ISPC_02_ICTService;
            }
        }
        public static ISPC_CommonService iSPC_CommonService
        {
            get
            {
                if (ISPC_CommonService == null)
                {
                    lock (SyncLock)
                    {
                        if (ISPC_CommonService == null)
                            ISPC_CommonService = new SPC_CommonService();
                    }
                }
                return ISPC_CommonService;
            }
        }

        public static ISPC_02_ICTServiceDefect iSPC_02_ICTServiceDefect
        {
            get
            {
                if (ISPC_02_ICTServiceDefect == null)
                {
                    lock (SyncLock)
                    {
                        if (ISPC_02_ICTServiceDefect == null)
                            ISPC_02_ICTServiceDefect = new SPC_02_ICTDefectService();
                    }
                }
                return ISPC_02_ICTServiceDefect;
            }
        }
        public static ISPC_03_FAIService iSPC_03_FAIService
        {
            get
            {
                if (ISPC_03_FAIService == null)
                {
                    lock (SyncLock)
                    {
                        if (ISPC_03_FAIService == null)
                            ISPC_03_FAIService = new SPC_03_FAIService();
                    }
                }
                return ISPC_03_FAIService;
            }
        }
        public static ISPC_03_FVIServiceDefect iSPC_03_FVIServiceDefect
        {
            get
            {
                if (ISPC_03_FVIServiceDefect == null)
                {
                    lock (SyncLock)
                    {
                        if (ISPC_03_FVIServiceDefect == null)
                            ISPC_03_FVIServiceDefect = new SPC_03_FVIDefectService();
                    }
                }
                return ISPC_03_FVIServiceDefect;
            }
        }
    }
}