﻿using System.Collections.Generic;

namespace Trace_AbilitySystem.Libs.ITrace_05_Local_Services
{
    public interface IAccountService
    {
        //int Insert(string operatorID, string passWord, string fullName,string roles, string rolesAOI,string CreateBy, string CreateByID, string CreatByName);
        int Insert(string operatorID, string passWord, string fullName, string roles, string CreateBy, string CreateByID, string CreatByName, string ConnectionString);
        int Update(int id, string operatorID, string passWord, string fullName,  string role, string ConnectionString);
        int Delete(int id, string ConnectionString);
        Account GetByOperatorIDAndPassWord(string operatorID, string passWord, string ConnectionString);
        Account GetByOperatorID(string operatorID, string ConnectionString);
        Account GetById(int id, string ConnectionString);
        List<Account> GetAll(string key, string ConnectionString);
    }
}
