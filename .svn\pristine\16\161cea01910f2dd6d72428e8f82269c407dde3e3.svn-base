﻿using System;
using System.Data;

namespace Trace_AbilitySystem.Libs.ITrace_02_FlexAssy_Services
{
    public interface ISource_FlexAssy_Trace_AbilityInterfaceDB_Service
    {
        DataTable FlexAssy_28_ContactAngle_GetDataWithCreatedDate(DateTime createdDateSearch, string connectionStringOption);
        DataTable FlexAssy_28_ContactAngle_Pattern_GetDataWithCreatedDate(DateTime createdDateSearch, string connectionStringOption);


        int FlexAssy_28_ContactAngle_Insert(string plasmaID, string afterOrBefore, int times, string machineNo, string itemName, string itemCode, string itemLot, string plasmaMachineNo, string operatorID, int measurementQty, string connectionStringOption);
        int FlexAssy_28_ContactAngle_Pattern_Insert(string plasmaID, string afterOrBefore, string aCFOrGND, string patternName, int times, string patternValue, string fileNameLocal, string fileNameRoot, string fileName, string connectionStringOption);
        int FlexAssy_28_ContactAngle_Pattern_Insert_v2(string plasmaID, string afterOrBefore, string aCFOrGND, string patternName, int times, string patternValue, string productID, string fileNameLocal, string fileNameRoot, string fileName, string connectionStringOption);
        int IPQC_OQC_ORT_OBA_Data_Insert(string LineID, string MachineID, string ItemName, string IndicationNumber, string Quantity, string TestName, int TestID, string InputOperator, string connectionStringOption);
        int FlexAssy_28_ContactAngle_CountPlasmaWithAfterOrBefore(string afterOrBefore, string itemCode, string itemLot, string connectionStringOption);
        DataTable FlexAssy_28_ContactAngle_GetOEEDataWithCreatedDate(DateTime createdDateSearch, int minute, string connectionStringOption);
    }
}