﻿using System.Data;
using System.Web;
using System.Web.Mvc;

namespace Trace_AbilitySystem
{
	public class PermissionRequired : ActionFilterAttribute
	{
		private HttpSessionStateBase _session;

		protected DataTable CurrUser
		{
			get { return _session["account"] == null ? null : (DataTable)_session["account"]; }
			set { _session["account"] = value; }
		}

		public override void OnActionExecuting(ActionExecutingContext filterContext)
		{
			HttpContextBase context = filterContext.HttpContext;
			HttpRequestBase _request = context.Request;
			_session = context.Session;

			if (CurrUser?.Rows.Count > 0)
			{
				if (CurrUser?.Rows[0]["Role"].ToString() != "3")
				{
					//old
					//if (CurrUser?.Rows[0]["Role"].ToString() != "1" && (_request.CurrentExecutionFilePath == "/server-management" ||
					//_request.CurrentExecutionFilePath == "/stage-search-value" || _request.CurrentExecutionFilePath == "/account-management"))

					//new HieuND update 2024-09-18
					if (CurrUser?.Rows[0]["Role"].ToString() != "1" && (_request.CurrentExecutionFilePath == "/server-management" ||
			   _request.CurrentExecutionFilePath == "/stage-search-value"))
					{
						filterContext.Result = new RedirectResult("/log-path-condition-management");
						return;
					}
					else
						return;
				}
			}
			else
			{
				filterContext.Result = new RedirectResult("/login");
				return;
			}

			if (_request.Url != null)
				filterContext.Result = new RedirectResult("/notpermission");
		}
	}
}