﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Trace_AbilitySystem.Libs.SPC_Utils
{
    public class XbarConstant
    {
        public List<Dictionary<string, double>> ChartConstant;
        public XbarConstant()
        {
            ChartConstant = new List<Dictionary<string, double>>();
            Dictionary<string, double> item = new Dictionary<string, double>();
            item.Add("A2", 0);
            item.Add("D3", 0);
            item.Add("D4", 0);
            item.Add("D2", 0);
            item.Add("A3", 2.659);
            item.Add("B3", 0);
            item.Add("B4", 3.267);
            item.Add("E2", 2.669);
            ChartConstant.Add(item);
            item = new Dictionary<string, double>();
            item.Add("A2", 1.880);
            item.Add("D3", 0);
            item.Add("D4", 3.267);
            item.Add("D2", 1.128);
            item.Add("A3", 2.659);
            item.Add("B3", 0);
            item.Add("B4", 3.267);
            item.Add("E2", 2.669);
            ChartConstant.Add(item);
            item = new Dictionary<string, double>();
            item.Add("A2", 1.023);
            item.Add("D3", 0);
            item.Add("D4", 2.574);
            item.Add("D2", 1.693);
            item.Add("A3", 1.954);
            item.Add("B3", 0);
            item.Add("B4", 2.568);
            item.Add("E2", 1.772);
            ChartConstant.Add(item);
            item = new Dictionary<string, double>();
            item.Add("A2", 0.729);
            item.Add("D3", 0);
            item.Add("D4", 2.282);
            item.Add("D2", 2.059);
            item.Add("A3", 1.628);
            item.Add("B3", 0);
            item.Add("B4", 2.266);
            item.Add("E2", 1.457);
            ChartConstant.Add(item);
            item = new Dictionary<string, double>();
            item.Add("A2", 0.577);
            item.Add("D3", 0);
            item.Add("D4", 2.114);
            item.Add("D2", 2.326);
            item.Add("A3", 1.427);
            item.Add("B3", 0);
            item.Add("B4", 2.089);
            item.Add("E2", 1.290);
            ChartConstant.Add(item);
            item = new Dictionary<string, double>();
            item.Add("A2", 0.483);
            item.Add("D3", 0);
            item.Add("D4", 2.004);
            item.Add("D2", 2.534);
            item.Add("A3", 1.287);
            item.Add("B3", 0.03);
            item.Add("B4", 1.97);
            item.Add("E2", 1.184);
            ChartConstant.Add(item);
            item = new Dictionary<string, double>();
            item.Add("A2", 0.419);
            item.Add("D3", 0.076);
            item.Add("D4", 1.924);
            item.Add("D2", 2.704);
            item.Add("A3", 1.182);
            item.Add("B3", 0.118);
            item.Add("B4", 1.882);
            item.Add("E2", 1.109);
            ChartConstant.Add(item);
            item = new Dictionary<string, double>();
            item.Add("A2", 0.373);
            item.Add("D3", 0.136);
            item.Add("D4", 1.864);
            item.Add("D2", 2.847);
            item.Add("A3", 1.099);
            item.Add("B3", 0.185);
            item.Add("B4", 1.815);
            item.Add("E2", 1.054);
            ChartConstant.Add(item);
            item = new Dictionary<string, double>();
            item.Add("A2", 0.377);
            item.Add("D3", 0.184);
            item.Add("D4", 1.816);
            item.Add("D2", 2.970);
            item.Add("A3", 1.032);
            item.Add("B3", 0.239);
            item.Add("B4", 1.761);
            item.Add("E2", 1.010);
            ChartConstant.Add(item);
            item = new Dictionary<string, double>();
            item.Add("A2", 0.308);
            item.Add("D3", 0.223);
            item.Add("D4", 1.777);
            item.Add("D2", 3.078);
            item.Add("A3", 0.975);
            item.Add("B3", 0.284);
            item.Add("B4", 1.716);
            item.Add("E2", 0.975);
            ChartConstant.Add(item);
            item = new Dictionary<string, double>();
            item.Add("A2", 0.285);
            item.Add("D3", 0.256);
            item.Add("D4", 1.774);
            item.Add("D2", 3.173);
            item.Add("A3", 0.927);
            item.Add("B3", 0.321);
            item.Add("B4", 1.679);
            item.Add("E2", 0.945);
            ChartConstant.Add(item);
            item = new Dictionary<string, double>();
            item.Add("A2", 0.266);
            item.Add("D3", 0.284);
            item.Add("D4", 1.716);
            item.Add("D2", 3.258);
            item.Add("A3", 0.886);
            item.Add("B3", 0.354);
            item.Add("B4", 1.646);
            item.Add("E2", 0.921);
            ChartConstant.Add(item);
            item = new Dictionary<string, double>();
            item.Add("A2", 0.249);
            item.Add("D3", 0.308);
            item.Add("D4", 1.692);
            item.Add("D2", 3.336);
            item.Add("A3", 0.85);
            item.Add("B3", 0.382);
            item.Add("B4", 1.618);
            item.Add("E2", 0.899);
            ChartConstant.Add(item);
            item = new Dictionary<string, double>();
            item.Add("A2", 0.235);
            item.Add("D3", 0.329);
            item.Add("D4", 1.671);
            item.Add("D2", 3.407);
            item.Add("A3", 0.817);
            item.Add("B3", 0.406);
            item.Add("B4", 1.594);
            item.Add("E2", 0.881);
            ChartConstant.Add(item);
            item = new Dictionary<string, double>();
            item.Add("A2", 0.223);
            item.Add("D3", 0.348);
            item.Add("D4", 1.652);
            item.Add("D2", 3.472);
            item.Add("A3", 0.789);
            item.Add("B3", 0.428);
            item.Add("B4", 1.572);
            item.Add("E2", 0.864);
            ChartConstant.Add(item);
            item = new Dictionary<string, double>();
            item.Add("A2", 0.212);
            item.Add("D3", 0.364);
            item.Add("D4", 1.636);
            item.Add("D2", 3.532);
            item.Add("A3", 0.763);
            item.Add("B3", 0.448);
            item.Add("B4", 1.552);
            item.Add("E2", 0.849);
            ChartConstant.Add(item);
            item = new Dictionary<string, double>();
            item.Add("A2", 0.203);
            item.Add("D3", 0.379);
            item.Add("D4", 1.621);
            item.Add("D2", 3.588);
            item.Add("A3", 0.739);
            item.Add("B3", 0.466);
            item.Add("B4", 1.534);
            item.Add("E2", 0.836);
            ChartConstant.Add(item);
            item = new Dictionary<string, double>();
            item.Add("A2", 0.194);
            item.Add("D3", 0.392);
            item.Add("D4", 1.608);
            item.Add("D2", 3.640);
            item.Add("A3", 0.718);
            item.Add("B3", 0.482);
            item.Add("B4", 1.518);
            item.Add("E2", 0.824);
            ChartConstant.Add(item);
            item = new Dictionary<string, double>();
            item.Add("A2", 0.187);
            item.Add("D3", 0.404);
            item.Add("D4", 1.596);
            item.Add("D2", 3.689);
            item.Add("A3", 0.698);
            item.Add("B3", 0.497);
            item.Add("B4", 1.503);
            item.Add("E2", 0.813);
            ChartConstant.Add(item);
            item = new Dictionary<string, double>();
            item.Add("A2", 0.180);
            item.Add("D3", 0.414);
            item.Add("D4", 1.586);
            item.Add("D2", 3.735);
            item.Add("A3", 0.68);
            item.Add("B3", 0.51);
            item.Add("B4", 1.49);
            item.Add("E2", 0.803);
            ChartConstant.Add(item);
            item = new Dictionary<string, double>();
            item.Add("A2", 0.173);
            item.Add("D3", 0.425);
            item.Add("D4", 1.575);
            item.Add("D2", 3.778);
            item.Add("A3", 0.663);
            item.Add("B3", 0.523);
            item.Add("B4", 1.477);
            item.Add("E2", 0.794);
            ChartConstant.Add(item);
            item = new Dictionary<string, double>();
            item.Add("A2", 0.167);
            item.Add("D3", 0.434);
            item.Add("D4", 1.566);
            item.Add("D2", 3.819);
            item.Add("A3", 0.647);
            item.Add("B3", 0.534);
            item.Add("B4", 1.466);
            item.Add("E2", 0.786);
            ChartConstant.Add(item);
            item = new Dictionary<string, double>();
            item.Add("A2", 0.162);
            item.Add("D3", 0.443);
            item.Add("D4", 1.557);
            item.Add("D2", 3.858);
            item.Add("A3", 0.633);
            item.Add("B3", 0.545);
            item.Add("B4", 1.455);
            item.Add("E2", 0.778);
            ChartConstant.Add(item);
            item = new Dictionary<string, double>();
            item.Add("A2", 0.157);
            item.Add("D3", 0.452);
            item.Add("D4", 1.548);
            item.Add("D2", 3.895);
            item.Add("A3", 0.619);
            item.Add("B3", 0.555);
            item.Add("B4", 1.445);
            item.Add("E2", 0.770);
            ChartConstant.Add(item);
            item = new Dictionary<string, double>();
            item.Add("A2", 0.153);
            item.Add("D3", 0.459);
            item.Add("D4", 1.541);
            item.Add("D2", 3.931);
            item.Add("A3", 0.606);
            item.Add("B3", 0.565);
            item.Add("B4", 1.435);
            item.Add("E2", 0.763);
            ChartConstant.Add(item);
        }
    }
}
