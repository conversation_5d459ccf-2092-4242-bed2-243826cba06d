﻿using System;
using System.Data;
using System.Data.SqlClient;
using Trace_AbilitySystem.Libs.Dataconnect;
using Trace_AbilitySystem.Libs.ITrace_03_Common_Services;

namespace Trace_AbilitySystem.Libs.Trace_03_Common_Services
{
    public class OperatorOfStageService : IOperatorOfStageService
    {
        private readonly DbExecute _db;
        private readonly string connectionStringOption = "Common";

        public OperatorOfStageService()
        {
            _db = new SqlExecute();
        }

        public int OperatorOfStage_Insert(string operatorID, string stage, string factory, string typeBarcode)
        {
            var paras = new SqlParameter[4];
            paras[0] = new SqlParameter("@OperatorID", operatorID ?? (object)DBNull.Value);
            paras[1] = new SqlParameter("@Stage", stage ?? (object)DBNull.Value);
            paras[2] = new SqlParameter("@Factory", factory ?? (object)DBNull.Value);
            paras[3] = new SqlParameter("@TypeBarcode", typeBarcode ?? (object)DBNull.Value);

            return _db.Execute_Modify("sp_sms_OperatorOfStage_Insert", paras, CommandType.StoredProcedure, connectionStringOption);
        }

        public DataTable OperatorOfStage_GetByOperatorID(string operatorID)
        {
            var paras = new SqlParameter[1];
            paras[0] = new SqlParameter("@OperatorID", operatorID ?? (object)DBNull.Value);

            return _db.Execute_Table("sp_sms_OperatorOfStage_GetByOperatorID", paras, CommandType.StoredProcedure, connectionStringOption);
        }
    }
}