﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Threading;
using System.Net;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Web.ModelBinding;
using Trace_AbilitySystem.Libs.Dataconnect;
using Trace_AbilitySystem.Libs.DTOClass;
using Trace_AbilitySystem.Libs.DTOClass.FlexAssyModel;
using Trace_AbilitySystem.Libs.ITrace_02_FlexAssy_Services;
using Trace_AbilitySystem.Libs.Trace_01_BareFlex_Services;
using System.Globalization;

namespace Trace_AbilitySystem.Libs.Trace_02_FlexAssy_Services
{
	public class FlexAssy_22_ICTService : IFlexAssy_22_ICTService
	{
		private readonly DbExecute _db;

		public FlexAssy_22_ICTService()
		{
			_db = new SqlExecute();
		}

		public async Task<int> FlexAssy_22_ICT_ProcessData(string factory, DateTime createdDateSearch, int minutes, string connectionStringOption)
		{
			string stage = "FlexAssy_22_ICT";
			string typeBarcode = "PcsID";

			try
			{
				DateTime dateNow = DateTime.Now;
				DateTime timeLast = createdDateSearch;
				int recordNeedSyn = 0;
				int recordSyned = 0;
				DataAllTrace dataAllTrace = new DataAllTrace();
				DataTable dtPcsID = new DataTable();
				dtPcsID.Columns.Add("ProductID", typeof(string));

				DataTable dt = Singleton_02_FlexAssy.ISource_FlexAssy_SEI_VCDTrace_Service.TblIct_GetDataWithCreatedDate(factory, timeLast, minutes, connectionStringOption);
				if (dt == null)
					return -1;

				//if (dt.Rows?.Count > 0)
				//{
				//    for (int i = 0; i < dt.Rows.Count; i++)
				//    {
				//        string pcsID = dt.Rows[i]["ProductID"].ToString();
				//        dtPcsID.Rows.Add(pcsID);
				//    }
				//    dataAllTrace.DataFlexAssy.FlexAssy_08_LaserMarking = Singleton_02_FlexAssy.IFlexAssy_08_LaserMarkingService.FlexAssy_08_LaserMarking_GetByListProductID(dtPcsID, factory, out factory);
				//}

				if (dt.Rows.Count > 0)
				{
					List<MachineMaintenance> machineMaintenances = new List<MachineMaintenance>();
					List<ProductionCondition> productionConditions = new List<ProductionCondition>();
					List<FirstPieceBuyOffControl> firstPieceBuyOffControls = new List<FirstPieceBuyOffControl>();
					List<MachineOfStage> machineOfStages = new List<MachineOfStage>();
					List<OperatorOfStage> operatorOfStages = new List<OperatorOfStage>();

					string connectionStringOptionMachine = Singleton_03_Common.IDBInfoService.ConnectionStringOption(factory, "MachineDB");

					recordNeedSyn = dt.Rows.Count;
					for (int i = 0; i < recordNeedSyn; i++)
					{
						string productID = dt.Rows[i]["ProductID"].ToString().Trim();
						DateTime dateTime = Convert.ToDateTime(dt.Rows[i]["DateTime"]);
						string machineID = dt.Rows[i]["MachineID"].ToString().Trim();
						string operatorID = dt.Rows[i]["OperatorID"].ToString().Trim();
						string facilityID = factory;// dt.Rows[i]["FacilityID"].ToString().Trim();
						string jigID = dt.Rows[i]["JigID"].ToString().Trim();
						string pCB = dt.Rows[i]["PCB"].ToString().Trim();
						int location = int.Parse(dt.Rows[i]["Location"].ToString());
						string testProgram = dt.Rows[i]["ProgramName"].ToString().Trim();
						string result = dt.Rows[i]["Result"].ToString().Trim();

						//string blockID = Common.FindBlockIDByPcsID(productID, dataAllTrace.DataFlexAssy.FlexAssy_08_LaserMarking);
						//if (blockID == "")
						//{
						//    ManageLog.WriteErrorApp(stage + "\n" + "Dont find blockID by pcsID to find FPB_Checking Data");
						//}

						// Find info machine maintenance date
						List<MachineMaintenance> machineMaintenancesOther = Singleton_03_Common.ICommon_CommonService.GetMachine_Maintenance(null, machineID, dateTime, out DateTime? machineMaintenanceDate, out int? machineMaintenanceID, machineMaintenances, connectionStringOptionMachine);
						if (machineMaintenancesOther != null)
							machineMaintenances.AddRange(machineMaintenancesOther);

						// Find info production condition
						ProductionCondition productionCondition = Singleton_03_Common.ICommon_CommonService.GetMachine_Condition(machineID, dateTime, testProgram, out string productionConditionResult, out int? productionConditionID, productionConditions, connectionStringOptionMachine);
						if (productionCondition != null)
							productionConditions.Add(productionCondition);

						//// find infor first piece buy off controll
						//FirstPieceBuyOffControl firstPieceBuyOffControl = Singleton_03_Common.ICommon_CommonService.GetMachine_FirstPieceBuyOff(factory, machineID, blockID, dateTime, out DateTime? firstPieceBuyoffControlDate, out int? firstPieceBuyoffControlID, firstPieceBuyOffControls, connectionStringOptionMachine);
						//if (firstPieceBuyOffControl != null)
						//    firstPieceBuyOffControls.Add(firstPieceBuyOffControl);

						int rs = FlexAssy_22_OQC_OBA_H4W_ORT_ICT_Save(productID, dateTime, machineID, operatorID, facilityID, jigID, pCB, location, testProgram, result, machineMaintenanceDate, machineMaintenanceID,
							productionConditionResult, productionConditionID, null, null, factory);
						if (rs == -9)
							return -1;

						// machineID, OperatorID definition makes at the stage
						MachineOfStage machineOfStage = Singleton_03_Common.ICommon_CommonService.GetMachineOfStage(machineID, null, stage, factory, typeBarcode, machineOfStages);
						if (machineOfStage != null)
							machineOfStages.Add(machineOfStage);

						OperatorOfStage operatorOfStage = Singleton_03_Common.ICommon_CommonService.GetOperatorOfStage(operatorID, null, stage, factory, typeBarcode, operatorOfStages);
						if (operatorOfStage != null)
							operatorOfStages.Add(operatorOfStage);

						// update the time that the record is synchronized
						timeLast = Convert.ToDateTime(dt.Rows[i]["CreatedDate"]);

						// count the number of synchronized records
						recordSyned++;
					}

					// Update Value Search
					Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, stage, timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff"));
				}
				else
				{
					timeLast = timeLast.AddMinutes(minutes) > dateNow ? timeLast : timeLast.AddMinutes(minutes);

					// Update ValueSearch
					Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, stage, timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff"));
				}

				ManageLog.WriteProcess(true, null, recordNeedSyn, recordSyned);
			}
			catch (Exception ex)
			{
				ManageLog.WriteErrorApp(stage + "\n" + ex.Message);

				return -1;
			}

			await Task.Delay(500);

			return 1;
		}
		public async Task<int> FlexAssy_22_ICT_Testprogram_History_ProcessDataSyn(string factory, DateTime createdDateSearch, int minutes)
		{
			string stage = "FlexAssy_22_ICT_Testprogram_History";
			try
			{
				DateTime dateNow = DateTime.Now;
				DateTime timeLast = createdDateSearch;
				DateTime? dt = FlexAssy_22_ICT_Testprogram_History_ProcessDataSyn(timeLast, minutes, factory);
				if (dt == null)
					return -1;
				Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, stage, ((DateTime)dt).ToString("yyyy-MM-dd HH:mm:ss.fff"));
			}
			catch (Exception ex)
			{
				ManageLog.WriteErrorApp(stage + "\n" + ex.Message);
				return -1;
			}

			await Task.Delay(500);

			return 1;
		}
		public DateTime? FlexAssy_22_ICT_Testprogram_History_ProcessDataSyn(DateTime dateTime, int minute, string connectionStringOption)
		{
			DateTime? result = null;
			try
			{
				var paras = new SqlParameter[2];
				paras[0] = new SqlParameter("@DateTime", dateTime);
				paras[1] = new SqlParameter("@Minute", minute);

				var dt = _db.Execute_Table("sp_sms_FlexAssy_22_ICT_Testprogram_History_DataSyn", paras, CommandType.StoredProcedure, connectionStringOption);
				if (dt?.Rows.Count >= 0)
				{
					result = DataConvert.ToDateTimeApp(dt.Rows[0][0].ToString());
				}
			}
			catch (Exception ex)
			{
				ManageLog.WriteErrorWeb("FlexAssy_22_ICT_Testprogram_History: " + ex.ToString());
			}
			return result;
		}

		public async Task FlexAssy_22_ICT_Sample_ProcessData(string factory, DateTime createdDateSearch, string connectionStringOption)
		{
			string stage = "FlexAssy_22_ICT_Sample";

			try
			{
				DateTime dateNow = DateTime.Now;
				DateTime timeLast = createdDateSearch;
				int recordNeedSyn = 0;
				int recordSyned = 0;

				DataTable dt = Singleton_02_FlexAssy.ISource_FlexAssy_SEI_VCDTrace_Service.TblIctSample_GetDataWithCreatedDate(timeLast, connectionStringOption);
				if (dt == null)
					return;

				if (dt.Rows.Count > 0)
				{
					recordNeedSyn = dt.Rows.Count;
					for (int i = 0; i < recordNeedSyn; i++)
					{
						string productID = dt.Rows[i]["ProductID"].ToString().Trim();
						string msName = dt.Rows[i]["MsName"].ToString().Trim();
						string msType = dt.Rows[i]["MsType"].ToString().Trim();
						string createdBy = dt.Rows[i]["CreatedBy"].ToString().Trim();
						DateTime dateTime = Convert.ToDateTime(dt.Rows[i]["CreatedDate"]);

						int rs = FlexAssy_22_ICT_Sample_Insert(productID, dateTime, msName, msType, createdBy, factory);
						if (rs == -9)
							break;

						timeLast = Convert.ToDateTime(dt.Rows[i]["CreatedDate"]);
						recordSyned++;
					}

					// Update Value Search
					Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, stage, timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff"));
				}

				ManageLog.WriteProcess(true, null, recordNeedSyn, recordSyned);
			}
			catch (Exception ex)
			{
				ManageLog.WriteErrorApp(stage + "\n" + ex.Message);
			}

			await Task.Delay(500);
		}

		#region ICT SPC
		public async Task<int> SPC_02_ICT_ProcessData(string factory, DateTime createdDateSearch, string stage)
		{
			try
			{
				DateTime dateNow = DateTime.Now;
				DateTime timeLast = createdDateSearch;
				string ipServerShopFloor = string.Empty;
				string connectionStringOption = "SPC_" + factory;

				string strWhere = "1=1 AND [MPECalculateTime] > '" + timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff") + "'";
				if (factory == "F3")
				{
					ipServerShopFloor = "************";
				}
				else
				{
					ipServerShopFloor = "************";
				}
				string SqlData = " SELECT TOP 1000 FPCBADateTested,[ItemName],[FPCBALot],([TotalICTNG] + [TotalICTOK]) as [TotalICT],[TotalICTNG],[TotalICTOK],[BareFlexDateTested],MPECalculateTime,CreatedDate " +
										 " FROM [" + ipServerShopFloor + "].[BoardRegistration_" + factory + "].[dbo].[YieldRate] WHERE " + strWhere +
										 " ORDER BY [MPECalculateTime] ASC";
				DataTable dt = _db.Execute_Table(SqlData, null, CommandType.Text, connectionStringOption);
				if (dt != null && dt.Rows.Count > 0)
				{
					//string strInsertQuery = "DECLARE @RC bigint, @Rs bigint ";
					for (int a = 0; a < dt.Rows.Count; a++)
					{
						if (!string.IsNullOrEmpty(dt.Rows[a]["FPCBADateTested"] + ""))
						{
							string ItemName = dt.Rows[a]["ItemName"] + "";
							string IndiNumber = dt.Rows[a]["FPCBALot"] + "";
							int TotalNG = Convert.ToInt32(string.IsNullOrEmpty(dt.Rows[a]["TotalICTNG"] + "") ? "0" : dt.Rows[a]["TotalICTNG"].ToString());
							int TotalOK = Convert.ToInt32(string.IsNullOrEmpty(dt.Rows[a]["TotalICTOK"] + "") ? "0" : dt.Rows[a]["TotalICTOK"].ToString());
							int TotalQuantity = TotalNG + TotalOK;
							DateTime dateTime = DataConvert.ToDateTimeApp(dt.Rows[a]["FPCBADateTested"] + "");
							SPC_02_ICT_Insert(ItemName, IndiNumber, TotalNG, TotalOK, TotalQuantity, dateTime, connectionStringOption);
							timeLast = DataConvert.ObjectToDateTime(dt.Rows[a]["MPECalculateTime"]);
						}
					}
				}
				//timeLast = SPC_02_ICT_GETLASTTIME(connectionStringOption, factory, stage);
				Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, stage, timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff"));

			}
			catch (Exception ex)
			{
				ManageLog.WriteErrorApp(stage + "\n" + ex.Message);

				return -1;
			}

			await Task.Delay(500);

			return 1;
		}

		public int SPC_02_ICT_Insert(string ItemName, string Indi, int TotalNG, int TotalOK, int TotalQuantity, DateTime dateTime, string connectionStringOption)
		{
			var paras = new SqlParameter[6];
			paras[0] = new SqlParameter("@ItemName", ItemName);
			paras[1] = new SqlParameter("@IndiNumber", Indi);
			paras[2] = new SqlParameter("@TotalQuantity", TotalQuantity);
			paras[3] = new SqlParameter("@TotalNG", TotalNG);
			paras[4] = new SqlParameter("@TotalOK", TotalOK);
			paras[5] = new SqlParameter("@CreatedDate", dateTime);
			return _db.Execute_Modify("sp_SPC_02_ICT_DEFECT_DATA_Save", paras, CommandType.StoredProcedure, connectionStringOption);
		}

		public DateTime SPC_02_ICT_GETLASTTIME(string connection, string factory, string stage)
		{
			int minute;
			DateTime dateTime = DataConvert.ToDateTimeApp(Singleton_03_Common.IStageSettingSearchValueService.ValueLast(factory, stage, out minute));

			string sql = "SELECT TOP(1) [CreatedDate] FROM [dbo].[SPC_02_ICT_DEFECT_DATA] ORDER BY [CreatedDate] DESC";
			DataTable dt = _db.Execute_Table(sql, null, CommandType.Text, connection);
			if (dt != null && dt.Rows.Count > 0)
			{
				dateTime = DataConvert.ToDateTimeApp(dt.Rows[0]["CreatedDate"] + "");
			}
			return dateTime;
		}

		#endregion

		public int FlexAssy_22_ICT_Insert(string productID, DateTime dateTime, string machineID, string operatorID, string facilityID, string jigID, string pCB, int location, string testProgram, string result,
			 DateTime? machineMaintenanceDate, int? machineMaintenanceID, string connectionStringOption)
		{
			var paras = new SqlParameter[12];
			paras[0] = new SqlParameter("@ProductID", productID);
			paras[1] = new SqlParameter("@DateTime", dateTime);
			paras[2] = new SqlParameter("@MachineID", machineID ?? (object)DBNull.Value);
			paras[3] = new SqlParameter("@OperatorID", operatorID ?? (object)DBNull.Value);
			paras[4] = new SqlParameter("@FacilityID", facilityID ?? (object)DBNull.Value);
			paras[5] = new SqlParameter("@JigID", jigID ?? (object)DBNull.Value);
			paras[6] = new SqlParameter("@PCB", pCB ?? (object)DBNull.Value);
			paras[7] = new SqlParameter("@Location", location);
			paras[8] = new SqlParameter("@TestProgram", testProgram ?? (object)DBNull.Value);
			paras[9] = new SqlParameter("@Result", result ?? (object)DBNull.Value);
			paras[10] = new SqlParameter("@MachineMaintenanceDate", machineMaintenanceDate ?? (object)DBNull.Value);
			paras[11] = new SqlParameter("@MachineMaintenanceID", machineMaintenanceID ?? (object)DBNull.Value);

			return _db.Execute_Modify("sp_sms_FlexAssy_22_ICT_Insert", paras, CommandType.StoredProcedure, connectionStringOption);
		}
		public int FlexAssy_22_OQC_OBA_ICT_Save(string productID, DateTime dateTime, string machineID, string operatorID, string facilityID, string jigID, string pCB, int location, string testProgram, string result,
			 DateTime? machineMaintenanceDate, int? machineMaintenanceID, string connectionStringOption)
		{
			var paras = new SqlParameter[12];
			paras[0] = new SqlParameter("@ProductID", productID);
			paras[1] = new SqlParameter("@DateTime", dateTime);
			paras[2] = new SqlParameter("@MachineID", machineID ?? (object)DBNull.Value);
			paras[3] = new SqlParameter("@OperatorID", operatorID ?? (object)DBNull.Value);
			paras[4] = new SqlParameter("@FacilityID", facilityID ?? (object)DBNull.Value);
			paras[5] = new SqlParameter("@JigID", jigID ?? (object)DBNull.Value);
			paras[6] = new SqlParameter("@PCB", pCB ?? (object)DBNull.Value);
			paras[7] = new SqlParameter("@Location", location);
			paras[8] = new SqlParameter("@TestProgram", testProgram ?? (object)DBNull.Value);
			paras[9] = new SqlParameter("@Result", result ?? (object)DBNull.Value);
			paras[10] = new SqlParameter("@MachineMaintenanceDate", machineMaintenanceDate ?? (object)DBNull.Value);
			paras[11] = new SqlParameter("@MachineMaintenanceID", machineMaintenanceID ?? (object)DBNull.Value);

			return _db.Execute_Modify("sp_sms_FlexAssy_22_OQC_OBA_ICT_Save", paras, CommandType.StoredProcedure, connectionStringOption);
		}
		public int FlexAssy_22_OQC_OBA_H4W_ORT_ICT_Save(string productID, DateTime dateTime, string machineID, string operatorID, string facilityID, string jigID, string pCB, int location, string testProgram, string result,
		 DateTime? machineMaintenanceDate, int? machineMaintenanceID, string productionConditionResult, int? productionConditionID, DateTime? firstPieceBuyoffControlDate, int? firstPieceBuyoffControlID, string connectionStringOption)
		{
			var paras = new SqlParameter[16];
			paras[0] = new SqlParameter("@ProductID", productID);
			paras[1] = new SqlParameter("@DateTime", dateTime);
			paras[2] = new SqlParameter("@MachineID", machineID ?? (object)DBNull.Value);
			paras[3] = new SqlParameter("@OperatorID", operatorID ?? (object)DBNull.Value);
			paras[4] = new SqlParameter("@FacilityID", facilityID ?? (object)DBNull.Value);
			paras[5] = new SqlParameter("@JigID", jigID ?? (object)DBNull.Value);
			paras[6] = new SqlParameter("@PCB", pCB ?? (object)DBNull.Value);
			paras[7] = new SqlParameter("@Location", location);
			paras[8] = new SqlParameter("@TestProgram", testProgram ?? (object)DBNull.Value);
			paras[9] = new SqlParameter("@Result", result ?? (object)DBNull.Value);
			paras[10] = new SqlParameter("@MachineMaintenanceDate", machineMaintenanceDate ?? (object)DBNull.Value);
			paras[11] = new SqlParameter("@MachineMaintenanceID", machineMaintenanceID ?? (object)DBNull.Value);
			paras[12] = new SqlParameter("@ProductionConditionResult", productionConditionResult ?? (object)DBNull.Value);
			paras[13] = new SqlParameter("@ProductionConditionID", productionConditionID ?? (object)DBNull.Value);
			paras[14] = new SqlParameter("@FirstPieceBuyoffControlDate", firstPieceBuyoffControlDate ?? (object)DBNull.Value);
			paras[15] = new SqlParameter("@FirstPieceBuyoffControlID", firstPieceBuyoffControlID ?? (object)DBNull.Value);

			//return _db.Execute_Modify("sp_sms_FlexAssy_22_OQC_OBA_H4W_ORT_ICT_Save", paras, CommandType.StoredProcedure, connectionStringOption);
			//return _db.Execute_Modify("sp_sms_FlexAssy_22_OQC_OBA_H4W_ORT_ICT_Save_2021_11_25", paras, CommandType.StoredProcedure, connectionStringOption);
			return _db.Execute_Modify("sp_sms_FlexAssy_22_OQC_OBA_H4W_ORT_ICT_Save_FPB2022", paras, CommandType.StoredProcedure, connectionStringOption);
		}

		public int FlexAssy_22_ICT_Sample_Insert(string productID, DateTime dateTime, string msName, string msType, string createdBy, string connectionStringOption)
		{
			var paras = new SqlParameter[5];
			paras[0] = new SqlParameter("@ProductID", productID);
			paras[1] = new SqlParameter("@MsName", msName ?? (object)DBNull.Value);
			paras[2] = new SqlParameter("@MsType", msType ?? (object)DBNull.Value);
			paras[3] = new SqlParameter("@CreatedBy", createdBy ?? (object)DBNull.Value);
			paras[4] = new SqlParameter("@DateTime", dateTime);

			return _db.Execute_Modify("sp_sms_FlexAssy_22_ICT_Sample_Insert", paras, CommandType.StoredProcedure, connectionStringOption);
		}

		public async Task FlexAssy_22_ICT_ProcessCopyLog(string factory, string pathSaveLog, string userName, string passWord, string pathSaveLog_Medium, string userName_Medium, string password_Medium, int ICTGetDay = 1)
		{
			if (string.IsNullOrEmpty(pathSaveLog))
				return;

			ManageLog.WriteLogApp("traceLog.txt", "FlexAssy_22_ICT_copylog: Step -1" + pathSaveLog);
			if (userName.Length > 0)
			{
				ManageLog.WriteLogApp("traceLog.txt", "FlexAssy_22_ICT_copylog: Step -2" + pathSaveLog);
				NetworkCredential credentials_Server = new NetworkCredential(userName, passWord);
				try
				{
					DataTable dt = Singleton_03_Common.ILogPathService.LogPath_GetAllByFactoryAndStage(factory, "FlexAssy_22_ICT");
					if (dt == null)
						return;

					if (dt.Rows.Count > 0)
					{
						for (int i = 0; i < dt.Rows.Count; i++)
						{
							int idLog = int.Parse(dt.Rows[i]["ID"].ToString());
							string machineID = dt.Rows[i]["MachineID"].ToString().Trim();
							string machineName = dt.Rows[i]["MachineName"].ToString().Trim();
							string LogType = DataConvert.ConvertToString(dt.Rows[i]["LogType"]).Trim();
							DateTime lastTime = Convert.ToDateTime(dt.Rows[i]["LastTime"]);
							string networkPath = dt.Rows[i]["LogAddress"].ToString().Trim();
							string RC_PathMove = (dt.Rows[i]["LogNGImageAddress"] + "").Trim();
							if (string.IsNullOrEmpty(RC_PathMove))
							{
								RC_PathMove = networkPath + @"\RC_Move";
							}
							int IsActive = DataConvert.ConvertToInt(dt.Rows[i]["IsActive"]);
							string jigID = string.Empty;
							if (IsActive == 0)
							{
								if (LogType.ToUpper().Trim() != "ICT_OIMS")
								{
									continue;
								}
							}
							if (factory == "F4")
							{
								jigID = dt.Rows[i]["JigID"].ToString().Trim();
							}
							ManageLog.WriteLogApp("traceLog.txt", "FlexAssy_22_ICT_copylog: Step 1" + networkPath);

							if (!Common.CheckAddressExists(networkPath))
								continue;

							if (dt.Rows[i]["Username"].ToString().Length > 0)
							{
								NetworkCredential credentials = new NetworkCredential(dt.Rows[i]["Username"].ToString(), dt.Rows[i]["Password"].ToString());
								try
								{
									using (new ConnectToSharedFolder(networkPath, credentials))
									{
										await ProcessLogShare_Security(idLog, networkPath, factory, lastTime, pathSaveLog, machineID, machineName, pathSaveLog_Medium, LogType, jigID, RC_PathMove, ICTGetDay);
									}
								}
								catch (Exception ex)
								{
									ManageLog.WriteErrorApp("FlexAssy_22_ICT: " + networkPath + ";" + ex.Message);

									await ProcessLogShare_Security(idLog, networkPath, factory, lastTime, pathSaveLog, machineID, machineName, pathSaveLog_Medium, LogType, jigID, RC_PathMove, ICTGetDay);
								}
							}
							else
							{
								//await ProcessLogShare_Security(idLog, networkPath, factory, lastTime, pathSaveLog, machineID, machineName);

								NetworkCredential credentials = new NetworkCredential("a", "");
								try
								{
									using (new ConnectToSharedFolder(networkPath, credentials))
									{
										await ProcessLogShare_Security(idLog, networkPath, factory, lastTime, pathSaveLog, machineID, machineName, pathSaveLog_Medium, LogType, jigID, RC_PathMove, ICTGetDay);
									}
								}
								catch (Exception ex)
								{
									ManageLog.WriteErrorApp("FlexAssy_22_ICT" + "\n" + ex.Message);

									await ProcessLogShare_Security(idLog, networkPath, factory, lastTime, pathSaveLog, machineID, machineName, pathSaveLog_Medium, LogType, jigID, RC_PathMove, ICTGetDay);
								}
							}
						}
					}
				}
				catch (Exception ex)
				{
					ManageLog.WriteLogApp("traceLog.txt", "FlexAssy_22_ICT_copylog: Step -3" + pathSaveLog);
					ManageLog.WriteErrorApp("FlexAssy_22_ICT: " + pathSaveLog + ";" + ex.Message);
				}
			}
			else
			{
				ManageLog.WriteLogApp("traceLog.txt", "FlexAssy_22_ICT_copylog: Step -2" + pathSaveLog);
				DataTable dt = Singleton_03_Common.ILogPathService.LogPath_GetByFactoryAndStage(factory, "FlexAssy_22_ICT");
				if (dt == null)
					return;

				if (dt.Rows.Count > 0)
				{
					for (int i = 0; i < dt.Rows.Count; i++)
					{
						int idLog = int.Parse(dt.Rows[i]["ID"].ToString());
						string machineID = dt.Rows[i]["MachineID"].ToString().Trim();
						string machineName = dt.Rows[i]["MachineName"].ToString().Trim();
						DateTime lastTime = Convert.ToDateTime(dt.Rows[i]["LastTime"]);
						string networkPath = dt.Rows[i]["LogAddress"].ToString().Trim();
						string RC_PathMove = (dt.Rows[i]["LogNGImageAddress"] + "").Trim();
						if (string.IsNullOrEmpty(RC_PathMove))
						{
							RC_PathMove = networkPath + @"\RC_Move";
						}
						string LogType = DataConvert.ConvertToString(dt.Rows[i]["LogType"]).Trim();
						int iDPathLog = int.Parse(dt.Rows[i]["ID_PathLog"].ToString());
						string jigID = dt.Rows[i]["JigID"].ToString().Trim();

						if (!Common.CheckAddressExists(networkPath))
							continue;
						ManageLog.WriteLogApp("traceLog.txt", "FlexAssy_22_ICT_copylog: Step -3" + pathSaveLog);

						if (dt.Rows[i]["Username"].ToString().Length > 0)
						{
							NetworkCredential credentials = new NetworkCredential(dt.Rows[i]["Username"].ToString(), dt.Rows[i]["Password"].ToString());
							try
							{
								using (new ConnectToSharedFolder(networkPath, credentials))
								{
									await ProcessLogShare_Security(idLog, networkPath, factory, lastTime, pathSaveLog, machineID, machineName, pathSaveLog_Medium, LogType, jigID, RC_PathMove, ICTGetDay);
								}
							}
							catch (Exception ex)
							{
								ManageLog.WriteErrorApp("FlexAssy_22_ICT: " + networkPath + ";" + ex.Message);

								await ProcessLogShare_Security(idLog, networkPath, factory, lastTime, pathSaveLog, machineID, machineName, pathSaveLog_Medium, LogType, jigID, RC_PathMove, ICTGetDay);
							}
						}
						else
						{
							//await ProcessLogShare_Security(idLog, networkPath, factory, lastTime, pathSaveLog, machineID, machineName);

							NetworkCredential credentials = new NetworkCredential("a", "");
							try
							{
								using (new ConnectToSharedFolder(networkPath, credentials))
								{
									await ProcessLogShare_Security(idLog, networkPath, factory, lastTime, pathSaveLog, machineID, machineName, pathSaveLog_Medium, LogType, jigID, RC_PathMove, ICTGetDay);
								}
							}
							catch (Exception ex)
							{
								ManageLog.WriteErrorApp("FlexAssy_22_ICT" + "\n" + ex.Message);

								await ProcessLogShare_Security(idLog, networkPath, factory, lastTime, pathSaveLog, machineID, machineName, pathSaveLog_Medium, LogType, jigID, RC_PathMove, ICTGetDay);
							}
						}
					}
				}
			}

			await Task.Delay(500);
		}
		public void FlexAssy_22_ICT_ProcessCopyLog_Main(string fileNameMedium, string pathSaveLogNew, string pathSaveLog,
			int iD_LogFile, string factory, string machineID, string fileNameRoot, int LineRead)
		{
			//ManageLog.WriteLog("iD_LogFile: " + iD_LogFile);
			//System.Threading.Thread.Sleep(100);
			// Copy file du lieu ve F2
			string fileName_F2 = null;
			try
			{
				// Copy file du lieu ve F2
				fileName_F2 = Common.CopyFileToPcFromServer(new FileInfo(fileNameMedium), pathSaveLogNew);
				//fileName_F2 = Common.CopyFileICTToPcFromServer(fileNameMedium, pathSaveLogNew, factory);
				if (fileName_F2 == null)
				{
					return;
				}
			}
			catch (Exception)
			{
				//ManageLog.WriteErrorApp("continues");
				//ManageLog.WriteErrorApp("FlexAssy_22_ICT_ProcessReadLog: " + fileNameMedium + "\n" + ex.Message);
				return;
			}

			string fileNameSample = Common.Replace_PathSaveLog(pathSaveLog, fileName_F2);
			// Update duong dan ICT F2
			int rs = FlexAssy_22_ICT_LogFile_UpdateFileName(iD_LogFile, fileNameSample, factory);
			if (rs == -9)
				return;

			string fileName = fileName_F2;// $"{ConfigurationManager.AppSettings["FolderLog"]}{dt.Rows[i]["FileName"].ToString().Replace("/", "\\")}";

			try
			{
				File.Delete(fileNameMedium);
			}
			catch (Exception ex)
			{
				ManageLog.WriteErrorApp("ICT ReadLOG Delete File Error" + ex.ToString());
			}

		}
		public void FlexAssy_22_ICT_ProcessReadLog_Main(string fileNameMedium, string pathSaveLogNew, string pathSaveLog,
			int iD_LogFile, string factory, string machineID, string fileNameRoot, int LineRead)
		{
			string fileName_F2 = null;
			try
			{
				// Copy file du lieu ve F2
				fileName_F2 = Common.CopyFileToPcFromServer(new FileInfo(fileNameMedium), pathSaveLogNew);
				if (fileName_F2 == null)
				{
					return;
				}
			}
			catch (Exception ex)
			{
				ManageLog.WriteErrorApp("continues");
				ManageLog.WriteErrorApp("FlexAssy_22_ICT_ProcessReadLog: " + fileNameMedium + "\n" + ex.Message);
				return;
			}

			string fileNameSample = Common.Replace_PathSaveLog(pathSaveLog, fileName_F2);
			int rs = 0;

			if (fileName_F2.ToLower().EndsWith(".txt") || fileName_F2.ToLower().EndsWith(".log"))
			{
				if (factory.ToUpper().Equals("F4"))
				{
					rs = FlexAssy_22_ICT_ReadFileTxtNew(factory, machineID, iD_LogFile, fileName_F2, fileNameRoot, fileNameSample);
					if (rs == -9)
						return;
				}
				else
				{
					rs = FlexAssy_22_ICT_ReadFileTxt(factory, machineID, iD_LogFile, fileName_F2, fileNameRoot, fileNameSample);
					if (rs == -9)
						return;
				}
			}
			else if (fileName_F2.ToLower().EndsWith(".dat"))
			{
				FlexAssy_22_ICT_ReadFileDat(factory, machineID, iD_LogFile, fileName_F2, fileNameRoot, fileNameSample);
			}
			else if (fileName_F2.ToLower().EndsWith(".csv"))
			{
				rs = FlexAssy_22_ICT_ReadFileCsv(factory, machineID, iD_LogFile, fileName_F2, fileNameRoot, fileNameSample, LineRead);
				if (rs == -9)
					return;
			}
			try
			{
				File.Delete(fileNameMedium);
			}
			catch (Exception ex)
			{
				ManageLog.WriteErrorApp("ICT ReadLOG Delete File Error" + ex.ToString());
			}

		}
		public void FlexAssy_22_ICT_ProcessReadLog_Main_resyn2(string fileNameMedium, string pathSaveLogNew, string pathSaveLog,
			int iD_LogFile, string factory, string machineID, string fileNameRoot, int LineRead)
		{
			//ManageLog.WriteLog("iD_LogFile: " + iD_LogFile);
			//System.Threading.Thread.Sleep(100);
			// Copy file du lieu ve F2
			string fileName_F2 = null;
			try
			{
				// Copy file du lieu ve F2
				fileName_F2 = Common.GetFileNameExist(new FileInfo(fileNameMedium), pathSaveLogNew);
				//fileName_F2 = Common.CopyFileICTToPcFromServer(fileNameMedium, pathSaveLogNew, factory);
				if (fileName_F2 == null)
				{
					return;
				}
			}
			catch (Exception ex)
			{
				ManageLog.WriteErrorApp("continues");
				ManageLog.WriteErrorApp("FlexAssy_22_ICT_ProcessReadLog: " + fileNameMedium + "\n" + ex.Message);
				return;
			}

			string fileNameSample = Common.Replace_PathSaveLog(pathSaveLog, fileName_F2);
			int rs = 0;
			//// Update duong dan ICT F2
			//int rs = FlexAssy_22_ICT_LogFile_UpdateFileName(iD_LogFile, fileNameSample, factory);
			//if (rs == -9)
			//    return;

			//string fileName = fileName_F2;// $"{ConfigurationManager.AppSettings["FolderLog"]}{dt.Rows[i]["FileName"].ToString().Replace("/", "\\")}";

			if (fileName_F2.ToLower().EndsWith(".txt") || fileName_F2.ToLower().EndsWith(".log"))
			{
				if (factory.ToUpper().Equals("F4"))
				{
					rs = FlexAssy_22_ICT_ReadFileTxtNew(factory, machineID, iD_LogFile, fileName_F2, fileNameRoot, fileNameSample);
					if (rs == -9)
						return;
					//System.Threading.Thread t = new System.Threading.Thread(() => FlexAssy_22_ICT_ReadFileTxtNew(factory, machineID, iD_LogFile, fileName_F2, fileNameRoot, fileNameSample));
					//t.Start();
				}
				else
				{
					rs = FlexAssy_22_ICT_ReadFileTxt(factory, machineID, iD_LogFile, fileName_F2, fileNameRoot, fileNameSample);
					if (rs == -9)
						return;
					//System.Threading.Thread t = new System.Threading.Thread(() => FlexAssy_22_ICT_ReadFileTxt(factory, machineID, iD_LogFile, fileName_F2));
					//t.Start();
				}
			}
			else if (fileName_F2.ToLower().EndsWith(".dat"))
			{
				FlexAssy_22_ICT_ReadFileDat(factory, machineID, iD_LogFile, fileName_F2, fileNameRoot, fileNameSample);
			}
			else if (fileName_F2.ToLower().EndsWith(".csv"))
			{
				rs = FlexAssy_22_ICT_ReadFileCsv(factory, machineID, iD_LogFile, fileName_F2, fileNameRoot, fileNameSample, LineRead);
				if (rs == -9)
					return;
			}
			// update the ID that the record is synchronized
			//lastID = int.Parse(dt.Rows[i]["ID"].ToString());

			// Update Value Search
			//Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, "FlexAssy_22_ICT_ProcessReadLog", iD_LogFile.ToString());
			try
			{
				File.Delete(fileNameMedium);
			}
			catch (Exception ex)
			{
				ManageLog.WriteErrorApp("ICT ReadLOG Delete File Error" + ex.ToString());
			}

		}
		public void FlexAssy_22_ICT_ProcessReadLog_Main2(string fileName_F2, string pathSaveLogNew, string pathSaveLog,
			int iD_LogFile, string factory, string machineID, string fileNameRoot, string fileNameSample, int LineRead, string jigID, string connect1, string connect2)
		{
			int rs;
			if (fileName_F2.ToLower().EndsWith(".txt") || fileName_F2.ToLower().EndsWith(".log"))
			{
				if (factory.ToUpper().Equals("F4"))
				{
					rs = FlexAssy_22_ICT_ReadFileTxtNew(factory, machineID, iD_LogFile, fileName_F2, fileNameRoot, fileNameSample);
					if (rs == -9)
						return;
					//System.Threading.Thread t = new System.Threading.Thread(() => FlexAssy_22_ICT_ReadFileTxtNew(factory, machineID, iD_LogFile, fileName_F2, fileNameRoot, fileNameSample));
					//t.Start();
				}
				else
				{
					rs = FlexAssy_22_ICT_ReadFileTxt(factory, machineID, iD_LogFile, fileName_F2, fileNameRoot, fileNameSample);
					if (rs == -9)
						return;
					//System.Threading.Thread t = new System.Threading.Thread(() => FlexAssy_22_ICT_ReadFileTxt(factory, machineID, iD_LogFile, fileName_F2));
					//t.Start();
				}
			}
			else if (fileName_F2.ToLower().EndsWith(".dat"))
			{
				FlexAssy_22_ICT_ReadFileDat2(factory, machineID, iD_LogFile, fileName_F2, fileNameRoot, fileNameSample, jigID, connect1, connect2);
			}
			else if (fileName_F2.ToLower().EndsWith(".csv"))
			{
				if (factory == "F3")
				{
					rs = FlexAssy_22_ICT_ReadFileCsv(factory, machineID, iD_LogFile, fileName_F2, fileNameRoot, fileNameSample, LineRead);
					if (rs == -9)
						return;
				}
			}
		}
		private static SemaphoreSlim semaphore = new SemaphoreSlim(5); // Allows up to 5 threads at a time
		public async Task FlexAssy_22_ICT_ProcessReadLog_Resyn2(string factory, int iD, string pathSaveLog, string pathSynMedium, string pathLinkTo_Medium)
		{
			try
			{
				int lastID = iD;
				int recordNeedSyn = 0;
				int recordSyned = 0;

				DataTable dt = FlexAssy_22_ICT_LogFile_GetDataWithIDRange(iD, 318394034, factory);
				if (dt == null)
					return;

				if (dt.Rows.Count > 0)
				{
					//number of records to sync
					recordNeedSyn = dt.Rows.Count;
					for (int i = 0; i < recordNeedSyn; i++)
					{
						if (dt.Rows[i]["FileNameMedium"] == DBNull.Value)
						{
							// update the ID that the record is synchronized
							lastID = int.Parse(dt.Rows[i]["ID"].ToString());
							// count the number of synchronized records
							recordSyned++;
							continue;
						}

						int iD_LogFile = int.Parse(dt.Rows[i]["ID"].ToString());
						int LineRead = DataConvert.ConvertToInt(dt.Rows[i]["LineRead"]);
						string machineID = dt.Rows[i]["MachineID"].ToString().Trim();
						string fileNameRoot = dt.Rows[i]["FileNameRoot"].ToString().Trim();
						string fileNameMedium = dt.Rows[i]["FileNameMedium"].ToString().Replace(pathSynMedium, pathLinkTo_Medium); //\\10.126.22.249\ContactAngle\LOGS_NO_DELETE => \\10.212.7.120\ContactAngle\LOGS_NO_DELETE

						//if (!Common.DirectoryExistsTimeout(Path.GetDirectoryName(fileNameMedium)))
						//{
						//	ManageLog.WriteErrorApp("FlexAssy_22_ICT_ProcessReadLog: " + fileNameMedium + "is not exist");
						//	continue;
						//}
						if (!File.Exists(fileNameMedium))
						{
							// update the ID that the record is synchronized
							lastID = iD_LogFile;
							string pathSaveLogNew = $"{pathSaveLog}{Path.GetDirectoryName(fileNameMedium).Replace(pathLinkTo_Medium, "")}";
							if (i % 8 > 0)
							{
								System.Threading.Thread t = new System.Threading.Thread(() =>
								FlexAssy_22_ICT_ProcessReadLog_Main_resyn2(fileNameMedium, pathSaveLogNew, pathSaveLog,
							iD_LogFile, factory, machineID, fileNameRoot, LineRead));
								t.Start();
							}
							else
							{
								FlexAssy_22_ICT_ProcessReadLog_Main_resyn2(fileNameMedium, pathSaveLogNew, pathSaveLog,
								iD_LogFile, factory, machineID, fileNameRoot, LineRead);
							}
							Thread.Sleep(10);
							recordSyned++;
						}
						else
						{
							lastID = iD_LogFile;
							string pathSaveLogNew = $"{pathSaveLog}{Path.GetDirectoryName(fileNameMedium).Replace(pathLinkTo_Medium, "")}";
							if (i % 8 > 0)
							{
								System.Threading.Thread t = new System.Threading.Thread(() =>
								FlexAssy_22_ICT_ProcessReadLog_Main(fileNameMedium, pathSaveLogNew, pathSaveLog,
							iD_LogFile, factory, machineID, fileNameRoot, LineRead));
								t.Start();
							}
							else
							{
								FlexAssy_22_ICT_ProcessReadLog_Main(fileNameMedium, pathSaveLogNew, pathSaveLog,
								iD_LogFile, factory, machineID, fileNameRoot, LineRead);
							}
							Thread.Sleep(40);
							recordSyned++;
						}
						//File.Delete(fileNameMedium);

					}

					// Update Value Search
					Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, "FlexAssy_22_ICT_ProcessReadLog_Resyn", lastID.ToString());
				}

				ManageLog.WriteProcess(true, "LogID: " + lastID, recordNeedSyn, recordSyned);
			}
			catch (Exception ex)
			{
				ManageLog.WriteErrorApp("FlexAssy_22_ICT_ProcessReadLog_Resyn: " + iD + "\n" + ex.Message);
			}

			await Task.Delay(500);
		}
		public async Task FlexAssy_22_ICT_ProcessReadLog(string factory, int iD, string pathSaveLog, string pathSynMedium, string pathLinkTo_Medium)
		{
			try
			{
				int lastID = iD;
				int recordNeedSyn = 0;
				int recordSyned = 0;

				DataTable dt = FlexAssy_22_ICT_LogFile_GetDataWithID(iD, factory);
				if (dt == null)
					return;

				if (dt.Rows.Count > 0)
				{
					//number of records to sync
					recordNeedSyn = dt.Rows.Count;
					for (int i = 0; i < recordNeedSyn; i++)
					{
						if (dt.Rows[i]["FileNameMedium"] == DBNull.Value)
						{
							// update the ID that the record is synchronized
							lastID = int.Parse(dt.Rows[i]["ID"].ToString());
							// count the number of synchronized records
							recordSyned++;
							continue;
						}

						int iD_LogFile = int.Parse(dt.Rows[i]["ID"].ToString());
						int LineRead = DataConvert.ConvertToInt(dt.Rows[i]["LineRead"]);
						string machineID = dt.Rows[i]["MachineID"].ToString().Trim();
						string fileNameRoot = dt.Rows[i]["FileNameRoot"].ToString().Trim();
						string fileNameMedium = dt.Rows[i]["FileNameMedium"].ToString().Replace(pathSynMedium, pathLinkTo_Medium); //\\10.126.22.249\ContactAngle\LOGS_NO_DELETE => \\10.212.7.120\ContactAngle\LOGS_NO_DELETE

						if (!Common.DirectoryExistsTimeout(Path.GetDirectoryName(fileNameMedium)))
						{
							ManageLog.WriteErrorApp("FlexAssy_22_ICT_ProcessReadLog: " + fileNameMedium + "is not exist");
							continue;
						}
						if (!File.Exists(fileNameMedium))
						{
							// update the ID that the record is synchronized
							lastID = int.Parse(dt.Rows[i]["ID"].ToString());
							// count the number of synchronized records
							recordSyned++;
							// Update Value Search
							Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, "FlexAssy_22_ICT_ProcessReadLog", lastID.ToString());
							continue;
						}
						lastID = iD_LogFile;
						string pathSaveLogNew = $"{pathSaveLog}{Path.GetDirectoryName(fileNameMedium).Replace(pathLinkTo_Medium, "")}";
						if (i % 8 > 0)
						{
							System.Threading.Thread t = new System.Threading.Thread(() =>
							FlexAssy_22_ICT_ProcessReadLog_Main(fileNameMedium, pathSaveLogNew, pathSaveLog,
						iD_LogFile, factory, machineID, fileNameRoot, LineRead));
							t.Start();
						}
						else
						{
							FlexAssy_22_ICT_ProcessReadLog_Main(fileNameMedium, pathSaveLogNew, pathSaveLog,
							iD_LogFile, factory, machineID, fileNameRoot, LineRead);
						}
						Thread.Sleep(40);
						recordSyned++;
						//File.Delete(fileNameMedium);

					}

					// Update Value Search
					Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, "FlexAssy_22_ICT_ProcessReadLog", lastID.ToString());
				}

				ManageLog.WriteProcess(true, "LogID: " + lastID, recordNeedSyn, recordSyned);
			}
			catch (Exception ex)
			{
				ManageLog.WriteErrorApp("FlexAssy_22_ICT_ProcessReadLog: " + iD + "\n" + ex.Message);
			}

			await Task.Delay(500);
		}
		public async Task FlexAssy_22_ICT_ProcessReadLog_ReSyn(string factory, int iD, string pathSaveLog, string pathSynMedium, string pathLinkTo_Medium)
		{
			try
			{
				int lastID = iD;
				int recordNeedSyn = 0;
				int recordSyned = 0;

				DataTable dt = FlexAssy_22_ICT_LogFile_GetNotReadLogWithID(iD, factory);
				if (dt == null)
					return;

				if (dt.Rows.Count > 0)
				{
					//number of records to sync
					recordNeedSyn = dt.Rows.Count;
					for (int i = 0; i < recordNeedSyn; i++)
					{
						if (dt.Rows[i]["FileNameMedium"] == DBNull.Value)
						{
							// update the ID that the record is synchronized
							lastID = int.Parse(dt.Rows[i]["ID"].ToString());
							// count the number of synchronized records
							recordSyned++;
							continue;
						}

						int iD_LogFile = int.Parse(dt.Rows[i]["ID"].ToString());
						int LineRead = DataConvert.ConvertToInt(dt.Rows[i]["LineRead"]);
						string machineID = dt.Rows[i]["MachineID"].ToString().Trim();
						string fileNameRoot = dt.Rows[i]["FileNameRoot"].ToString().Trim();
						string fileNameMedium = dt.Rows[i]["FileNameMedium"].ToString().Replace(pathSynMedium, pathLinkTo_Medium); //\\10.126.22.249\ContactAngle\LOGS_NO_DELETE => \\10.212.7.120\ContactAngle\LOGS_NO_DELETE

						if (!Common.DirectoryExistsTimeout(Path.GetDirectoryName(fileNameMedium)))
						{
							ManageLog.WriteErrorApp("FlexAssy_22_ICT_ProcessReadLog: " + fileNameMedium + "is not exist");
							continue;
						}
						if (!File.Exists(fileNameMedium))
						{
							lastID = int.Parse(dt.Rows[i]["ID"].ToString());
							recordSyned++;
							continue;
						}
						lastID = iD_LogFile;
						string pathSaveLogNew = $"{pathSaveLog}{Path.GetDirectoryName(fileNameMedium).Replace(pathLinkTo_Medium, "")}";
						if (i % 8 > 0)
						{
							System.Threading.Thread t = new System.Threading.Thread(() =>
							FlexAssy_22_ICT_ProcessReadLog_Main(fileNameMedium, pathSaveLogNew, pathSaveLog,
						iD_LogFile, factory, machineID, fileNameRoot, LineRead));
							t.Start();
						}
						else
						{
							FlexAssy_22_ICT_ProcessReadLog_Main(fileNameMedium, pathSaveLogNew, pathSaveLog,
							iD_LogFile, factory, machineID, fileNameRoot, LineRead);
						}
						Thread.Sleep(40);
						recordSyned++;

					}
				}

				ManageLog.WriteProcess(true, "LogID: " + lastID, recordNeedSyn, recordSyned);
			}
			catch (Exception ex)
			{
				ManageLog.WriteErrorApp("FlexAssy_22_ICT_ProcessReadLog: " + iD + "\n" + ex.Message);
			}

			await Task.Delay(500);
		}
		public async Task FlexAssy_22_ICT_ProcessCopyLogToF2(string factory, int iD, string pathSaveLog, string pathSynMedium, string pathLinkTo_Medium)
		{
			try
			{
				int lastID = iD;
				int recordNeedSyn = 0;
				int recordSyned = 0;

				DataTable dt = FlexAssy_22_ICT_LogFile_GetDataWithID(iD, factory);
				if (dt == null)
					return;

				if (dt.Rows.Count > 0)
				{
					//number of records to sync
					recordNeedSyn = dt.Rows.Count;
					for (int i = 0; i < recordNeedSyn; i++)
					{
						if (dt.Rows[i]["FileNameMedium"] == DBNull.Value)
						{
							// update the ID that the record is synchronized
							lastID = int.Parse(dt.Rows[i]["ID"].ToString());
							// count the number of synchronized records
							recordSyned++;
							continue;
						}

						int iD_LogFile = int.Parse(dt.Rows[i]["ID"].ToString());
						int LineRead = DataConvert.ConvertToInt(dt.Rows[i]["LineRead"]);
						string machineID = dt.Rows[i]["MachineID"].ToString().Trim();
						string fileNameRoot = dt.Rows[i]["FileNameRoot"].ToString().Trim();
						string fileNameMedium = dt.Rows[i]["FileNameMedium"].ToString().Replace(pathSynMedium, pathLinkTo_Medium); //\\10.126.22.249\ContactAngle\LOGS_NO_DELETE => \\10.212.7.120\ContactAngle\LOGS_NO_DELETE

						if (!Common.DirectoryExistsTimeout(Path.GetDirectoryName(fileNameMedium)))
						{
							ManageLog.WriteErrorApp("FlexAssy_22_ICT_ProcessReadLog: " + fileNameMedium + "is not exist");
							continue;
						}
						if (!File.Exists(fileNameMedium))
						{
							// update the ID that the record is synchronized
							lastID = int.Parse(dt.Rows[i]["ID"].ToString());
							// count the number of synchronized records
							recordSyned++;
							// Update Value Search
							Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, "FlexAssy_22_ICT_ProcessCopyLogToF2", lastID.ToString());
							continue;
						}
						lastID = iD_LogFile;
						string pathSaveLogNew = $"{pathSaveLog}{Path.GetDirectoryName(fileNameMedium).Replace(pathLinkTo_Medium, "")}";
						if (i % 10 > 0)
						{
							System.Threading.Thread t = new System.Threading.Thread(() =>
							FlexAssy_22_ICT_ProcessCopyLog_Main(fileNameMedium, pathSaveLogNew, pathSaveLog,
						iD_LogFile, factory, machineID, fileNameRoot, LineRead));
							t.Start();
						}
						else
						{
							FlexAssy_22_ICT_ProcessCopyLog_Main(fileNameMedium, pathSaveLogNew, pathSaveLog,
							iD_LogFile, factory, machineID, fileNameRoot, LineRead);
						}
						Thread.Sleep(5);
						//string fileName_F2 = null;
						//try
						//{
						//    // Copy file du lieu ve F2
						//    fileName_F2 = Common.CopyFileToPcFromServer(new FileInfo(fileNameMedium), pathSaveLogNew);
						//    if (fileName_F2 == null)
						//    {
						//        return;
						//    }
						//}
						//catch (Exception ex)
						//{
						//    ManageLog.WriteErrorApp("ICT CopyFileToPcFromServer Error" + ex.ToString());
						//    return;
						//}

						//string fileNameSample = Common.Replace_PathSaveLog(pathSaveLog, fileName_F2);
						//int rsUpdate = FlexAssy_22_ICT_LogFile_UpdateLineRead_FileName(iD_LogFile, 0, true, fileNameSample, factory);
						//if (rsUpdate == -9)
						//    return;
						//try
						//{
						//    File.Delete(fileNameMedium);
						//}
						//catch (Exception ex)
						//{
						//    ManageLog.WriteErrorApp("ICT ReadLOG Delete File Error" + ex.ToString());
						//}
						recordSyned++;
					}
					Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, "FlexAssy_22_ICT_ProcessCopyLogToF2", lastID.ToString());
				}
				ManageLog.WriteProcess(true, "LogID: " + lastID, recordNeedSyn, recordSyned);
			}
			catch (Exception ex)
			{
				ManageLog.WriteErrorApp("FlexAssy_22_ICT_ProcessCopyLogToF2: " + iD + "\n" + ex.Message);
			}
			await Task.Delay(500);
		}
		public async Task FlexAssy_22_ICT_ProcessReadLog2(string factory, int iD, int isReadID, string pathSaveLog, string pathSynMedium, string pathLinkTo_Medium)
		{
			int lastID = iD;
			try
			{
				int ict_isRead_LastID = isReadID;
				int recordNeedSyn = 0;
				int recordSyned = 0;
				DataTable dt = FlexAssy_22_ICT_LogFile_GetDataWithIDRange(iD, ict_isRead_LastID, factory);
				if (dt == null)
					return;

				if (dt.Rows.Count > 0)
				{
					//number of records to sync
					recordNeedSyn = dt.Rows.Count;
					string connectionStringOptionVCD = Singleton_03_Common.IDBInfoService.ConnectionStringOption(factory, "SEI-VCDTraceDB");
					string connectionStringOptionTriCuong = Singleton_03_Common.IDBInfoService.ConnectionStringOption(factory, "TCI");
					for (int i = 0; i < recordNeedSyn; i++)
					{
						if (dt.Rows[i]["FileNameMedium"] == DBNull.Value)
						{
							// update the ID that the record is synchronized
							lastID = int.Parse(dt.Rows[i]["ID"].ToString());
							// count the number of synchronized records
							recordSyned++;
							continue;
						}
						string fileNameF2 = DataConvert.ConvertFieldToString(dt.Rows[i], "FileName");
						if (string.IsNullOrEmpty(fileNameF2))
						{
							lastID = int.Parse(dt.Rows[i]["ID"].ToString());
							// count the number of synchronized records
							recordSyned++;
							continue;
						}
						int iD_LogFile = int.Parse(dt.Rows[i]["ID"].ToString());
						fileNameF2 = Common.CompareFile(pathSaveLog, fileNameF2);
						string machineID = dt.Rows[i]["MachineID"].ToString().Trim();
						int LineRead = DataConvert.ConvertToInt(dt.Rows[i]["LineRead"]);
						string fileNameRoot = dt.Rows[i]["FileNameRoot"].ToString().Trim();
						string fileName = dt.Rows[i]["FileName"].ToString().Trim();
						string jigID = dt.Rows[i]["JigID"].ToString().Trim();
						string fileNameSample = Common.Replace_PathSaveLog2(pathSaveLog, fileNameF2);
						if (i % 13 > 0)
						{
							System.Threading.Thread t = new System.Threading.Thread(() =>
							FlexAssy_22_ICT_ProcessReadLog_Main2(fileNameF2, "", pathSaveLog,
						iD_LogFile, factory, machineID, fileNameRoot, fileNameSample, LineRead, jigID, connectionStringOptionVCD, connectionStringOptionTriCuong));
							t.Start();
						}
						else
						{
							FlexAssy_22_ICT_ProcessReadLog_Main2(fileNameF2, "", pathSaveLog,
							iD_LogFile, factory, machineID, fileNameRoot, fileNameSample, LineRead, jigID, connectionStringOptionVCD, connectionStringOptionTriCuong);
						}
						Thread.Sleep(5);

						// update the ID that the record is synchronized
						lastID = int.Parse(dt.Rows[i]["ID"].ToString());
						// count the number of synchronized records
						recordSyned++;
					}

					// Update Value Search
					Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, "FlexAssy_22_ICT_ProcessReadLog", lastID.ToString());
				}
				else
				{
					Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, "FlexAssy_22_ICT_ProcessReadLog", ict_isRead_LastID.ToString());
				}

				ManageLog.WriteProcess(true, null, recordNeedSyn, recordSyned);
			}
			catch (Exception ex)
			{
				Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, "FlexAssy_22_ICT_ProcessReadLog", lastID.ToString());
				ManageLog.WriteErrorApp("FlexAssy_22_ICT_ProcessReadLog: " + iD + "\n" + ex.Message);
			}

			await Task.Delay(500);
		}
		public async Task FlexAssy_22_ICT_ProcessReadLog_SPCOld(string factory, int iD, int isReadID, string pathSaveLog, string pathSynMedium, string pathLinkTo_Medium)
		{
			int lastID = iD;
			try
			{
				int ict_isRead_LastID = isReadID;
				int recordNeedSyn = 0;
				int recordSyned = 0;
				DataTable dt = FlexAssy_22_ICT_LogFile_GetDataWithIDRange2(iD, ict_isRead_LastID, factory);
				if (dt == null)
					return;

				if (dt.Rows.Count > 0)
				{
					//number of records to sync
					recordNeedSyn = dt.Rows.Count;
					for (int i = 0; i < recordNeedSyn; i++)
					{
						if (dt.Rows[i]["FileNameMedium"] == DBNull.Value)
						{
							// update the ID that the record is synchronized
							lastID = int.Parse(dt.Rows[i]["ID"].ToString());
							// count the number of synchronized records
							recordSyned++;
							continue;
						}
						string fileNameF2 = DataConvert.ConvertFieldToString(dt.Rows[i], "FileName");
						if (string.IsNullOrEmpty(fileNameF2))
						{
							lastID = int.Parse(dt.Rows[i]["ID"].ToString());
							// count the number of synchronized records
							recordSyned++;
							continue;
						}

						int iD_LogFile = int.Parse(dt.Rows[i]["ID"].ToString());
						fileNameF2 = Common.CompareFile(pathSaveLog, fileNameF2);
						string machineID = dt.Rows[i]["MachineID"].ToString().Trim();
						string fileNameRoot = dt.Rows[i]["FileNameRoot"].ToString().Trim();
						string fileNameMedium = dt.Rows[i]["FileNameMedium"].ToString().Replace(pathSynMedium, pathLinkTo_Medium);
						if (fileNameF2.ToLower().EndsWith(".dat"))
						{
							int rs = FlexAssy_22_ICT_ReadFileDat_SPC(factory, machineID, iD_LogFile, fileNameF2, fileNameRoot);
							if (rs == -9)
								break;
						}

						// update the ID that the record is synchronized
						lastID = int.Parse(dt.Rows[i]["ID"].ToString());

						// Update Value Search

						// count the number of synchronized records
						recordSyned++;
					}

					// Update Value Search
					Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, "ReSPC_02_ICT_ProcessReadLog", lastID.ToString());
				}
				else
				{
					Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, "ReSPC_02_ICT_ProcessReadLog", ict_isRead_LastID.ToString());
				}

				ManageLog.WriteProcess(true, null, recordNeedSyn, recordSyned);
			}
			catch (Exception ex)
			{
				Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, "ReSPC_02_ICT_ProcessReadLog", lastID.ToString());
				ManageLog.WriteErrorApp("SPC_02_ICT_ProcessReadLog: " + iD + "\n" + ex.Message);
			}

			await Task.Delay(500);
		}
		public async Task FlexAssy_22_ICT_ProcessReadLog_ExportData(string factory, int iD, int isReadID, string pathSaveLog, string pathSynMedium, string pathLinkTo_Medium, string StageName)
		{
			int lastID = iD;
			try
			{
				int ict_isRead_LastID = isReadID;
				int recordNeedSyn = 0;
				int recordSyned = 0;
				DataTable dt = FlexAssy_22_ICT_LogFile_GetDataWithIDRange(iD, ict_isRead_LastID, factory);
				if (dt == null)
					return;

				if (dt.Rows.Count > 0)
				{
					//number of records to sync
					recordNeedSyn = dt.Rows.Count;
					string connectionStringOptionVCD = Singleton_03_Common.IDBInfoService.ConnectionStringOption(factory, "SEI-VCDTraceDB");
					string connectionStringOptionTriCuong = Singleton_03_Common.IDBInfoService.ConnectionStringOption(factory, "TCI");
					for (int i = 0; i < recordNeedSyn; i++)
					{
						if (dt.Rows[i]["FileNameMedium"] == DBNull.Value)
						{
							// update the ID that the record is synchronized
							lastID = int.Parse(dt.Rows[i]["ID"].ToString());
							// count the number of synchronized records
							recordSyned++;
							continue;
						}
						string fileNameF2 = DataConvert.ConvertFieldToString(dt.Rows[i], "FileName");
						if (string.IsNullOrEmpty(fileNameF2))
						{
							lastID = int.Parse(dt.Rows[i]["ID"].ToString());
							// count the number of synchronized records
							recordSyned++;
							continue;
						}
						ManageLog.WriteTrace(fileNameF2, "LogfileName");

						int iD_LogFile = int.Parse(dt.Rows[i]["ID"].ToString());
						fileNameF2 = Common.CompareFile(pathSaveLog, fileNameF2);
						string machineID = dt.Rows[i]["MachineID"].ToString().Trim();
						string fileNameRoot = dt.Rows[i]["FileNameRoot"].ToString().Trim();
						string fileNameMedium = dt.Rows[i]["FileNameMedium"].ToString().Replace(pathSynMedium, pathLinkTo_Medium);
						if (fileNameF2.ToLower().EndsWith(".dat"))
						{
							if (i % 10 > 0)
							{
								System.Threading.Thread t = new System.Threading.Thread(() =>
								FlexAssy_22_ICT_ReadFileDat_ExportData(factory, machineID, iD_LogFile, fileNameF2, fileNameRoot, connectionStringOptionVCD, connectionStringOptionTriCuong));
								t.Start();
							}
							else
							{
								FlexAssy_22_ICT_ReadFileDat_ExportData(factory, machineID, iD_LogFile, fileNameF2, fileNameRoot, connectionStringOptionVCD, connectionStringOptionTriCuong);
							}
							Thread.Sleep(5);
						}

						// update the ID that the record is synchronized
						lastID = int.Parse(dt.Rows[i]["ID"].ToString());

						// Update Value Search

						// count the number of synchronized records
						recordSyned++;
					}

					// Update Value Search
					Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, StageName, lastID.ToString());
				}
				else
				{
					Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, StageName, ict_isRead_LastID.ToString());
				}

				ManageLog.WriteProcess(true, null, recordNeedSyn, recordSyned);
			}
			catch (Exception ex)
			{
				Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, StageName, lastID.ToString());
				ManageLog.WriteErrorApp(StageName + ": " + iD + "\n" + ex.Message);
			}

			await Task.Delay(500);
		}
		public async Task FlexAssy_22_ICT_ProcessReadLog_SPC(string factory, int iD, int isReadID, string pathSaveLog, string pathSynMedium, string pathLinkTo_Medium)
		{
			int lastID = iD;
			try
			{
				int ict_isRead_LastID = isReadID;
				int recordNeedSyn = 0;
				int recordSyned = 0;
				DataTable dt = FlexAssy_22_ICT_LogFile_GetDataWithIDRange(iD, ict_isRead_LastID, factory);
				if (dt == null)
					return;

				if (dt.Rows.Count > 0)
				{
					//number of records to sync
					recordNeedSyn = dt.Rows.Count;
					string connectionStringOptionVCD = Singleton_03_Common.IDBInfoService.ConnectionStringOption(factory, "SEI-VCDTraceDB");
					string connectionStringOptionTriCuong = Singleton_03_Common.IDBInfoService.ConnectionStringOption(factory, "TCI");
					for (int i = 0; i < recordNeedSyn; i++)
					{
						if (dt.Rows[i]["FileNameMedium"] == DBNull.Value)
						{
							// update the ID that the record is synchronized
							lastID = int.Parse(dt.Rows[i]["ID"].ToString());
							// count the number of synchronized records
							recordSyned++;
							continue;
						}
						string fileNameF2 = DataConvert.ConvertFieldToString(dt.Rows[i], "FileName");
						if (string.IsNullOrEmpty(fileNameF2))
						{
							lastID = int.Parse(dt.Rows[i]["ID"].ToString());
							// count the number of synchronized records
							recordSyned++;
							continue;
						}
						ManageLog.WriteTrace(fileNameF2, "LogfileName");

						int iD_LogFile = int.Parse(dt.Rows[i]["ID"].ToString());
						fileNameF2 = Common.CompareFile(pathSaveLog, fileNameF2);
						string machineID = dt.Rows[i]["MachineID"].ToString().Trim();
						string fileNameRoot = dt.Rows[i]["FileNameRoot"].ToString().Trim();
						string fileNameMedium = dt.Rows[i]["FileNameMedium"].ToString().Replace(pathSynMedium, pathLinkTo_Medium);
						if (fileNameF2.ToLower().EndsWith(".dat"))
						{
							if (i % 10 > 0)
							{
								System.Threading.Thread t = new System.Threading.Thread(() =>
								FlexAssy_22_ICT_ReadFileDat_SPCOld(factory, machineID, iD_LogFile, fileNameF2, fileNameRoot, connectionStringOptionVCD, connectionStringOptionTriCuong));
								t.Start();
							}
							else
							{
								FlexAssy_22_ICT_ReadFileDat_SPCOld(factory, machineID, iD_LogFile, fileNameF2, fileNameRoot, connectionStringOptionVCD, connectionStringOptionTriCuong);
							}
							Thread.Sleep(5);
						}

						// update the ID that the record is synchronized
						lastID = int.Parse(dt.Rows[i]["ID"].ToString());

						// Update Value Search

						// count the number of synchronized records
						recordSyned++;
					}

					// Update Value Search
					Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, "SPC_02_ICT_ProcessReadLog", lastID.ToString());
				}
				else
				{
					Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, "SPC_02_ICT_ProcessReadLog", ict_isRead_LastID.ToString());
				}

				ManageLog.WriteProcess(true, null, recordNeedSyn, recordSyned);
			}
			catch (Exception ex)
			{
				Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, "SPC_02_ICT_ProcessReadLog", lastID.ToString());
				ManageLog.WriteErrorApp("SPC_02_ICT_ProcessReadLog: " + iD + "\n" + ex.Message);
			}

			await Task.Delay(500);
		}
		public async Task FlexAssy_22_ICT_NET_ProcessReadLog(string factory, int iD, int isReadID, string pathSaveLog, string pathSynMedium, string pathLinkTo_Medium)
		{
			int lastID = iD;
			try
			{
				int ict_isRead_LastID = isReadID;
				int recordNeedSyn = 0;
				int recordSyned = 0;
				DataTable dt = FlexAssy_22_ICT_LogFile_GetDataWithIDRange(iD, ict_isRead_LastID, factory);
				if (dt == null)
					return;

				if (dt.Rows.Count > 0)
				{
					//number of records to sync
					recordNeedSyn = dt.Rows.Count;
					string connectionStringOptionVCD = Singleton_03_Common.IDBInfoService.ConnectionStringOption(factory, "SEI-VCDTraceDB");
					string connectionStringOptionTriCuong = Singleton_03_Common.IDBInfoService.ConnectionStringOption(factory, "TCI");
					for (int i = 0; i < recordNeedSyn; i++)
					{
						if (dt.Rows[i]["FileNameMedium"] == DBNull.Value)
						{
							// update the ID that the record is synchronized
							lastID = int.Parse(dt.Rows[i]["ID"].ToString());
							// count the number of synchronized records
							recordSyned++;
							continue;
						}
						string fileNameF2 = DataConvert.ConvertFieldToString(dt.Rows[i], "FileName");
						if (string.IsNullOrEmpty(fileNameF2))
						{
							lastID = int.Parse(dt.Rows[i]["ID"].ToString());
							// count the number of synchronized records
							recordSyned++;
							continue;
						}
						ManageLog.WriteTrace(fileNameF2, "LogfileName");

						int iD_LogFile = int.Parse(dt.Rows[i]["ID"].ToString());
						fileNameF2 = Common.CompareFile(pathSaveLog, fileNameF2);
						string machineID = dt.Rows[i]["MachineID"].ToString().Trim();
						string fileNameRoot = dt.Rows[i]["FileNameRoot"].ToString().Trim();
						string fileNameMedium = dt.Rows[i]["FileNameMedium"].ToString().Replace(pathSynMedium, pathLinkTo_Medium);
						if (fileNameF2.ToLower().EndsWith(".dat"))
						{
							if (i % 15 > 0)
							{
								System.Threading.Thread t = new System.Threading.Thread(() =>
								FlexAssy_22_ICT_ReadFileDat_NetNG(factory, machineID, iD_LogFile, fileNameF2, fileNameRoot, connectionStringOptionVCD, connectionStringOptionTriCuong));
								t.Start();
							}
							else
							{
								FlexAssy_22_ICT_ReadFileDat_NetNG(factory, machineID, iD_LogFile, fileNameF2, fileNameRoot, connectionStringOptionVCD, connectionStringOptionTriCuong);
							}
							Thread.Sleep(5);
						}

						// update the ID that the record is synchronized
						lastID = int.Parse(dt.Rows[i]["ID"].ToString());

						// Update Value Search

						// count the number of synchronized records
						recordSyned++;
					}

					// Update Value Search
					Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, "SPC_02_ICT_NG_ProcessReadLog", lastID.ToString());
				}
				else
				{
					Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, "SPC_02_ICT_NG_ProcessReadLog", ict_isRead_LastID.ToString());
				}

				ManageLog.WriteProcess(true, null, recordNeedSyn, recordSyned);
			}
			catch (Exception ex)
			{
				Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, "SPC_02_ICT_NG_ProcessReadLog", lastID.ToString());
				ManageLog.WriteErrorApp("SPC_02_ICT_NET_ProcessReadLog: " + iD + "\n" + ex.Message);
			}

			await Task.Delay(500);
		}
		public async Task FlexAssy_22_ICT_ProcessSplitLogFile(string factory, int iD, int isReadID, string pathSaveLog, string pathSynMedium, string pathLinkTo_Medium)
		{
			try
			{
				int lastID = iD;
				int ict_isRead_LastID = isReadID;
				int recordNeedSyn = 0;
				int recordSyned = 0;

				DataTable dt = FlexAssy_22_ICT_LogFile_GetDataWithIDRange(iD, ict_isRead_LastID, factory);
				if (dt == null)
					return;

				if (dt.Rows.Count > 0)
				{
					//number of records to sync
					recordNeedSyn = dt.Rows.Count;
					for (int i = 0; i < recordNeedSyn; i++)
					{
						if (dt.Rows[i]["FileNameMedium"] == DBNull.Value)
						{
							// update the ID that the record is synchronized
							lastID = int.Parse(dt.Rows[i]["ID"].ToString());
							// count the number of synchronized records
							recordSyned++;
							continue;
						}
						string fileNameF2 = DataConvert.ConvertFieldToString(dt.Rows[i], "FileName");
						if (string.IsNullOrEmpty(fileNameF2))
						{
							lastID = int.Parse(dt.Rows[i]["ID"].ToString());
							// count the number of synchronized records
							recordSyned++;
							continue;
						}

						int iD_LogFile = int.Parse(dt.Rows[i]["ID"].ToString());
						string pathSaveLogNew = Path.GetDirectoryName(pathSaveLog);
						fileNameF2 = Common.CompareFile(pathSaveLogNew, fileNameF2);
						string machineID = dt.Rows[i]["MachineID"].ToString().Trim();
						string fileNameRoot = dt.Rows[i]["FileNameRoot"].ToString().Trim();
						string fileNameMedium = dt.Rows[i]["FileNameMedium"].ToString().Replace(pathSynMedium, pathLinkTo_Medium);
						int LineRead = DataConvert.ConvertToInt(dt.Rows[i]["LineRead"]);
						if (fileNameF2.ToLower().EndsWith(".dat"))
						{
							int rs = FlexAssy_22_ICT_SplitFileDat(factory, machineID, iD_LogFile, fileNameF2, pathSaveLogNew);
							if (rs == -9)
								break;
						}
						if (fileNameF2.ToLower().EndsWith(".csv"))
						{
							int rs = FlexAssy_22_ICT_SplitFileCSV(factory, machineID, iD_LogFile, fileNameF2, fileNameRoot, LineRead, pathSaveLogNew);
							if (rs == -9)
								break;
						}

						// update the ID that the record is synchronized
						lastID = int.Parse(dt.Rows[i]["ID"].ToString());

						// Update Value Search
						Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, "FlexAssy_22_ICT_ProcessSplitLogFile", lastID.ToString());

						// count the number of synchronized records
						recordSyned++;
					}

					// Update Value Search
					Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, "FlexAssy_22_ICT_ProcessSplitLogFile", lastID.ToString());
				}

				ManageLog.WriteProcess(true, null, recordNeedSyn, recordSyned);
			}
			catch (Exception ex)
			{
				ManageLog.WriteErrorApp("SPC_02_ICT_ProcessReadLog: " + iD + "\n" + ex.Message);
			}

			await Task.Delay(500);
		}
		public string GetF2FilePath(string PathSaveLog, string FileName_F2)
		{
			string str = Path.GetDirectoryName(PathSaveLog);
			if (str == null)
			{
				str = Path.GetFileName(PathSaveLog);
				str = PathSaveLog.Replace(str, "");

				return str + FileName_F2;
			}

			return str + FileName_F2;
		}
		public async Task ProcessLogShare_Security(int idLog, string networkPath, string factory, DateTime lastTime, string pathSaveLog, string machineID, string machineName, string pathSaveLog_Medium, string LogType, string jigID, string RC_Move, int ICTGetDay = 1)
		{
			ManageLog.WriteLogApp("traceLog.txt", "FlexAssy_22_ICT_copylog: Step 2" + networkPath);
			if (!Common.DirectoryExistsTimeout(networkPath))
			{
				ManageLog.WriteErrorApp("FlexAssy_22_ICT: " + networkPath + " is not connect");
				return;
			}

			try
			{
				//lastTime = 2019-01-05 09:23:39.000
				//dateEnd = 2019-01-06 09:23:39.000
				DateTime dateNow = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 0, 0, 0);
				DateTime dateEnd = DateTime.Compare(lastTime.AddDays(1), dateNow) > 0 ? dateNow : lastTime.AddDays(1);
				List<FileInfo> fileInfos = new List<FileInfo>();

				if (LogType == "ICTTURNCSV" && factory.ToUpper().Equals("F4"))
				{
					fileInfos = FlexAssy_22_ICT_GetByDateTime(networkPath, lastTime.AddDays(-ICTGetDay), factory);
					ManageLog.WriteLogApp("traceLog.txt", "FlexAssy_22_ICT_copylog: Step getlog" + networkPath);
					ManageLog.WriteErrorApp("Step1_CopLog_ICT" + fileInfos.Count());
					if (fileInfos.Count > 0)
					{
						foreach (var item in fileInfos)
						{
							string pathSaveLogNew = $"{pathSaveLog_Medium}\\ICT\\{machineID}-{machineName}\\{item.LastWriteTime.ToString("yyyyMM")}\\{item.LastWriteTime.ToString("yyyyMMdd")}";
							// Copy file du lieu ve F2
							int LineRead = 0;

							DataTable LastFile = FlexAssy_22_ICT_LogFile_GetByFileNameRoot(item.FullName, factory);
							if (LastFile?.Rows.Count > 0)
							{
								if (string.IsNullOrEmpty(DataConvert.ConvertToString(LastFile.Rows[0]["FileName"])))
								{
									// File copy từ trước chưa được xử lý bỏ qua
									continue;
								}
								else
								{
									LineRead = DataConvert.ConvertToInt(LastFile.Rows[0]["LineRead"]);
									string FileName_F2 = GetF2FilePath(pathSaveLog, DataConvert.ConvertToString(LastFile.Rows[0]["FileName"]));
									FileInfo file = new FileInfo(FileName_F2);
									if (file.Exists)
									{
										if (file.Length == item.Length)
										{
											// đô dài của file chưa thay đổi => chưa có cập nhật gì nên không copy
											continue;
										}
									}
								}
							}
							ManageLog.WriteLogApp("traceLog.txt", "FlexAssy_22_ICT_copylog: Step 3" + networkPath);

							string fileName = Common.CopyFileToPcFromServer(item, pathSaveLogNew);
							if (fileName == null)
							{
								ManageLog.WriteErrorApp("FlexAssy_22_ICT: Can not Copyfile to F2" + item.FullName);
								break;
							}
							ManageLog.WriteLogApp("traceLog.txt", "FlexAssy_22_ICT_copylog: Step 4" + networkPath);
							int rs = FlexAssy_22_ICT_LogFile_Insert(machineID, item.FullName, fileName, LineRead, jigID, LogType, factory);
							if (rs == -9)
							{
								break;
							}
							if (item.LastWriteTime.Date < DateTime.Now.Date)
							{
								bool isCopy = Common.CopyTo_Trace_READ_OK(networkPath, item, out string fileNameRoot, "ICT", machineID);
								if (!isCopy) // Loi Copy file
								{
									break;
								}

								rs = FlexAssy_22_ICT_LogFile_UpdateFileNameRoot(rs, fileNameRoot, factory);
								if (rs == -9)
								{
									break;
								}
							}
							else
							{
								rs = FlexAssy_22_ICT_LogFile_UpdateFileNameRoot(rs, item.FullName, factory);
								if (rs == -9)
								{
									break;
								}
							}


							lastTime = item.LastWriteTime;

							//await Task.Delay(500);

							Singleton_03_Common.ILogPathService.LogPath_UpdateLastTime(idLog, lastTime); // Update thời gian của file cuối cùng vừa  lấy về
						}
					}
				}
				else
				{
					fileInfos = FlexAssy_22_ICT_GetAll(networkPath, factory);
					ManageLog.WriteErrorApp(networkPath + "Step1_CopLog_ICT" + fileInfos.Count());
					if (fileInfos.Count > 0)
					{
						foreach (var item in fileInfos)
						{
							string checkProgramName = "";
							if (item.FullName.ToLower().EndsWith(".txt") && factory == "F3")
							{
								checkProgramName = CheckProgramNameTxtF3(machineID, item.FullName);
							}
							else if (item.FullName.ToLower().EndsWith(".txt") && factory == "F4")
							{
								checkProgramName = CheckProgramNameTxtF4(machineID, item.FullName);
							}
							else if (item.FullName.ToLower().EndsWith(".dat"))
							{
								checkProgramName = CheckProgramNameDat(machineID, item.FullName);
							}
							if (checkProgramName == "Log Test")
							{
								// Move file to Trace_READ_OK /NG
								bool rsMove = Common.MoveTo_RC_SKIP(RC_Move, item, machineID, checkProgramName);
								if (!rsMove) // Loi move file
								{
									break;
								}
								continue;
							}
							else if (checkProgramName == "NG")
							{
								break;
							}
							string pathSaveLogNew = $"{pathSaveLog_Medium}\\ICT\\{machineID}-{machineName}\\{item.LastWriteTime.ToString("yyyyMM")}\\{item.LastWriteTime.ToString("yyyyMMdd")}";
							// Copy file du lieu ve F2
							string fileName = Common.CopyFileToPcFromServer(item, pathSaveLogNew);
							if (fileName == null)
							{
								ManageLog.WriteErrorApp("FlexAssy_22_ICT: Can not Copyfile to F2" + item.FullName);
								break;
							}
							int rs = FlexAssy_22_ICT_LogFile_Insert(machineID, item.FullName, fileName, 0, jigID, LogType, factory);
							if (rs == -9 || rs <= 0)
							{
								break;
							}

							// Move file to Trace_READ_OK /NG
							string fileNameRoot = null;
							bool isMove = Common.MoveTo_Trace_READ_OK(networkPath, item, out fileNameRoot, "ICT", machineID);
							if (!isMove) // Loi move file
							{
								break;
							}

							// Move file thanh cong
							rs = FlexAssy_22_ICT_LogFile_UpdateFileNameRoot(rs, fileNameRoot, factory);
							if (rs == -9)
							{
								break;
							}

							lastTime = item.LastWriteTime;

							//await Task.Delay(500);

							Singleton_03_Common.ILogPathService.LogPath_UpdateLastTime(idLog, lastTime); // Update thời gian của file cuối cùng vừa  lấy về
						}
					}
				}
			}
			catch (Exception ex)
			{
				ManageLog.WriteErrorApp("FlexAssy_22_ICT: Copyfile to F2 " + ex.Message);
			}

			await Task.Delay(500);
		}

		public List<FileInfo> FlexAssy_22_ICT_GetListFileLogs(string pathLog, DateTime lastTime, DateTime dateEnd)
		{
			List<FileInfo> fileInfos = new List<FileInfo>();
			List<string> exts = new List<string> { ".dat", ".txt", ".log" };
			if (pathLog.Contains("READ_OK"))
			{
				fileInfos.AddRange(Common.GetListFileLogFromAnalysis(".dat", pathLog, lastTime, dateEnd));
			}
			else
			{
				fileInfos = new DirectoryInfo(pathLog).GetFiles("*.*")
					.Where(item => item.LastWriteTime > lastTime && item.LastWriteTime < dateEnd && exts.Any(item.Extension.ToLower().EndsWith)).ToList();
			}

			return fileInfos.OrderBy(x => x.LastWriteTime).ToList();
		}

		public List<FileInfo> FlexAssy_22_ICT_GetAll(string pathLog)
		{
			List<FileInfo> fileInfos = new DirectoryInfo(pathLog).GetFiles("*.*").Where(item => item.Extension.ToLower() == ".dat" || item.Extension.ToLower() == ".txt").ToList();

			return fileInfos.OrderBy(x => x.LastWriteTime).ToList();
		}
		public List<FileInfo> FlexAssy_22_ICT_GetAll(string pathLog, string Factory)
		{
			List<FileInfo> fileInfos;
			if (Factory.ToUpper().Equals("F4"))
			{
				fileInfos = new DirectoryInfo(pathLog).GetFiles("*.*").Where(item => item.Extension.ToLower() == ".dat" || item.Extension.ToLower() == ".txt").ToList();
			}
			else if (Factory.ToUpper().Equals("F3"))
			{
				fileInfos = new DirectoryInfo(pathLog).GetFiles("*.*").Where(item => item.Extension.ToLower() == ".dat").ToList();
			}
			else
			{
				fileInfos = new List<FileInfo>();
				DirectoryInfo[] listDic = new DirectoryInfo(pathLog).GetDirectories();
				foreach (DirectoryInfo dic in listDic)
				{
					fileInfos.AddRange(dic.GetFiles("*.*").Where(item => item.Extension.ToLower() == ".txt").ToList());
				}
			}
			return fileInfos.OrderBy(x => x.LastWriteTime).ToList();
		}
		public List<FileInfo> FlexAssy_22_ICT_GetByDateTime(string pathLog, DateTime Datetime, string Factory)
		{
			List<FileInfo> fileInfos = new List<FileInfo>();
			if (Factory.ToUpper().Equals("F4"))
			{
				fileInfos = new DirectoryInfo(pathLog).GetFiles("*.*").Where(item => item.Extension.ToLower() == ".csv" && item.LastWriteTime >= Datetime).ToList();
			}
			return fileInfos.OrderBy(x => x.LastWriteTime).ToList();
		}

		public int FlexAssy_22_ICT_ReadFileTxt(string factory, string machineID, int iD_LogFile, string fileName, string fileNameRoot, string fileNameSample)
		{
			try
			{
				StreamReader file = new StreamReader(fileName);
				using (var lineReader = new ReadFileText(file))
				{
					IEnumerable<string> lines = lineReader;
					string[] enumerable = lines as string[] ?? lines.ToArray();

					string productID = null;
					DateTime dateTime = DateTime.Now;
					int location = 0;
					string specFile = "";
					bool isFail = false;
					int addSample = 0;

					for (int i = 0; i < enumerable.Length; i++)
					{
						//  0. Test Results Report
						//  1.
						//  2. Assembly Name:  None assigned
						//  3. 
						//  4. UUT Serial Number: 7255514T199ST305EE1Y00137B
						//  5. 
						//  6. Failures:                 0
						//  7. 
						//  8. Report Date:              Feb 12, 2016
						//  9. Time: 06:47:21
						//  10. 
						//  11. Test System:              CheckSum Analyst ems
						//  12. 
						//  13. File Name: 54ZLCD - ICT - EVT1.SPEC
						//  14.
						//  15.
						//  16. =============================================================================
						//  17.
						//  18.
						//  19. From   From     To    To      Test Test      Test       Low     High   Measured
						//  20. Point--Name-- Point--Name-- - Type Range----Title-- - -Limit - -Limit - -Value--
						//  21. 
						//  22.  51            28            Res  9760 SUS - GND       0.0001  5.0000   0.0348                <= START CHECK
						if (enumerable[i].IndexOf("Test Results Report", StringComparison.Ordinal) != -1)
						{
							int line = i + 1;
							string failures = "0";

							i += 4;
							if (enumerable[i].IndexOf("UUT Serial Number:", StringComparison.Ordinal) != -1)
							{
								productID = Regex.Replace(enumerable[i].Replace("UUT Serial Number:", "").Trim(), @"[^0-9a-zA-Z-]+", "");
							}

							i += 2;
							if (enumerable[i].IndexOf("Failures:", StringComparison.Ordinal) != -1)
							{
								failures = enumerable[i].Replace("Failures:", "").Trim();
								isFail = failures != "0";
							}

							i += 2;
							if (enumerable[i].IndexOf("Report Date:", StringComparison.Ordinal) != -1)
							{
								dateTime = Convert.ToDateTime($"{enumerable[i].Replace("Report Date:", "").Trim()} {enumerable[i + 1].Replace("Time:", "").Trim()}");
							}

							i += 5;
							if (enumerable[i].IndexOf("File Name:", StringComparison.Ordinal) != -1)
							{
								location++;
								specFile = enumerable[i].Replace("File Name:", "").Trim();

								string operatorID = "";
								string facilityID = "";
								string jigID = "";
								string pCB = "";
								string testProgram = specFile;
								string result = failures == "0" ? "Pass" : "Fail";
								//int rs = FlexAssy_22_OQC_OBA_ICT_RetestCycle_Save(productID, dateTime, machineID, operatorID, facilityID, jigID, pCB, location, testProgram, result, iD_LogFile, line, factory);
								int rs = FlexAssy_22_OQC_OBA_H4W_ORT_ICT_RetestCycle_Save(productID, dateTime, machineID, operatorID, facilityID, jigID, pCB, location, testProgram, result, "", iD_LogFile, line, factory);
								if (rs == -9)
									return -9;
								Boolean IsSample = false;
								if (addSample == 0)
								{
									if (specFile.Trim().Contains("-MS"))
									{
										rs = FlexAssy_22_ICT_LogFile_Sample_Insert(dateTime, machineID, testProgram, fileNameRoot, fileNameSample, factory);
										IsSample = true;
										if (rs == -9)
											return -9;// tại sao lại Return đằng sau vãn còn pcs khác
									}
									else
									{
										DataTable dt1 = FlexAssy_22_ICT_Sample_GetByProductID(productID, factory);
										if (dt1 == null)
											break;

										if (dt1.Rows.Count > 0)
										{
											rs = FlexAssy_22_ICT_LogFile_Sample_Insert(dateTime, machineID, testProgram, fileNameRoot, fileNameSample, factory);
											IsSample = true;
											if (rs == -9)
												return -9;
										}
									}
									addSample++;
								}
								if (!IsSample)
								{
									string connectionStringOptionBoard = Singleton_03_Common.IDBInfoService.ConnectionStringOption(factory, "SEI-VCDTraceDB");
									string ItemName = Singleton_02_FlexAssy.ISource_FlexAssy_SEI_VCDTrace_Service.GetItemName_IndiByProductID(productID, out string IndicationNumber, connectionStringOptionBoard);
									string Line = "";
									DataTable dtMachineID = Singleton_04_Machine.ItMachineMtnService.tMachineList_GetByID(machineID);
									if (dtMachineID?.Rows.Count > 0)
									{
										Line = dtMachineID.Rows[0]["LineID"] + "";
									}
									DateTime DateTime = DataConvert.ToDateTime(dateTime);
									if (DateTime.Hour < 6)
									{
										DateTime = DateTime.AddDays(-1);
										DateTime = DateTime.Date;
									}
									else
									{
										DateTime = DateTime.Date;
									}
									// Insert data
									if (result.ToUpper() == "PASS")
									{
										rs = Singleton_06_SPC.iSPC_CommonService.OEE_Product_Save(ItemName, IndicationNumber, Line, "ICT", machineID, 1, 0, DataConvert.ToDateTime(dateTime), "NewSPC_" + factory);
									}
									else
									{
										rs = Singleton_06_SPC.iSPC_CommonService.OEE_Product_Save(ItemName, IndicationNumber, Line, "ICT", machineID, 0, 1, DataConvert.ToDateTime(dateTime), "NewSPC_" + factory);
									}
								}
							}

							i += 8;
							continue;
						}
					}
					int rsUpdate = FlexAssy_22_ICT_LogFile_UpdateLineRead_FileName(iD_LogFile, enumerable.Length, true, fileNameSample, factory);
					if (rsUpdate == -9)
						return -9;

					rsUpdate = FlexAssy_22_ICT_LogFile_UpdateLineRead(iD_LogFile, enumerable.Length, true, factory);
					if (rsUpdate == -9)
						return -9;

					return 1;
				}
			}
			catch (Exception ex)
			{
				int rsUpdate = FlexAssy_22_ICT_LogFile_UpdateLineRead(iD_LogFile, -1, false, factory);
				if (rsUpdate == -9)
					return -9;

				ManageLog.WriteErrorApp("FlexAssy_22_ICT_ProcessReadLog: " + iD_LogFile + Environment.NewLine + fileName + "\n" + ex.Message);
				return -2;
			}
		}
		public string CheckProgramNameTxtF3(string machineID, string fileName)
		{
			try
			{
				StreamReader file = new StreamReader(fileName);
				using (var lineReader = new ReadFileText(file))
				{
					IEnumerable<string> lines = lineReader;
					string[] enumerable = lines as string[] ?? lines.ToArray();

					DateTime dateTime = DateTime.Now;
					int location = 0;
					string testProgram = "";
					for (int i = 0; i < enumerable.Length; i++)
					{
						if (enumerable[i].IndexOf("Test Results Report", StringComparison.Ordinal) != -1)
						{
							i += 13;
							if (enumerable[i].IndexOf("File Name:", StringComparison.Ordinal) != -1)
							{
								location++;
								testProgram = enumerable[i].Replace("File Name:", "").Trim();
							}
						}
						if (testProgram.ToUpper().Contains("TEST"))
						{
							return "Log Test";
						}
					}
					return "PASS";
				}
			}
			catch (Exception ex)
			{
				ManageLog.WriteErrorApp("CheckProgramNameTxtF3: " + Environment.NewLine + fileName + "\n" + ex.Message);
				return "NG";
			}
		}
		public string CheckProgramNameTxtF4(string machineID, string fileName)
		{
			try
			{
				StreamReader file = new StreamReader(fileName);
				using (var lineReader = new ReadFileText(file))
				{
					IEnumerable<string> lines = lineReader;
					string[] enumerable = lines as string[] ?? lines.ToArray();
					string testProgram = "";
					string programName = string.Empty;
					for (int i = 0; i < enumerable.Length; i++)
					{
						string[] strArr = enumerable[i].Split(',');
						int line = i + 1;
						string productID = "";
						bool isProduct17 = false;
						// Read log file hàng 17'
						if (strArr.Length == 18)
						{
							if (strArr[0] == "BARCODE")
							{
								isProduct17 = true;
								continue;
							}
							//  Đọc dòng có ProductID
							productID = strArr[0];
							productID = Regex.Replace(strArr[0].Trim(), @"[^0-9a-zA-Z]+", "");
							//Kết quả
							if (!string.IsNullOrEmpty(productID))
							{
								machineID = strArr[4];
								//ProgramName 
								testProgram = strArr[5];
							}
						}
						if (!isProduct17)
						{
							if (enumerable[i].IndexOf("Test Results Report", StringComparison.Ordinal) != -1)
							{
								line = i + 1;
								i += 13;
								testProgram = enumerable[i].Replace("File Name:", "").Trim();
							}
						}
						if (testProgram.ToUpper().Contains("TEST"))
						{
							return "Log Test";
						}
					}
					return "PASS";
				}
			}
			catch (Exception ex)
			{
				ManageLog.WriteErrorApp("CheckProgramNameTxtF4: " + Environment.NewLine + fileName + "\n" + ex.Message);
				return "NG";
			}
		}
		public string CheckProgramNameDat(string machineID, string fileName)
		{
			try
			{
				int addSample = 0;
				StreamReader file = new StreamReader(fileName);
				using (var lineReader = new ReadFileText(file))
				{
					IEnumerable<string> lines = lineReader;
					string[] enumerable = lines as string[] ?? lines.ToArray();

					string productID = null;
					DateTime dateTime = DateTime.Now;
					int location = 0;
					bool isFail = false;
					string operatorID = "";
					string facilityID = "";
					string pCB = "";
					string jigIDLogFile = "";
					string testProgram = "";
					string result = "";
					string failures = "";
					string cavityNo = "";
					string retestCycleOIMS = "";
					string IndicationNumber = "";
					DateTime ShotTime = DateTime.Now;
					int counter = 0;
					FlexAssy_22_ICT_CalculatorDetail_Model lstCal_Ict = new FlexAssy_22_ICT_CalculatorDetail_Model();
					bool isProductID = false;

					for (int i = 0; i < enumerable.Length; i++)
					{
						string[] strArr = enumerable[i].Split(',');
						if (strArr.Length >= 13)
						{
							isProductID = true;
							productID = Regex.Replace(strArr[5].Trim(), @"[^0-9a-zA-Z+]+", "");
							testProgram = strArr[0].Replace("\"", "").Trim();
						}
						if (testProgram.ToUpper().Contains("TEST"))
						{
							return "Log Test";
						}
					}
					return "PASS";
				}
			}
			catch (Exception ex)
			{
				ManageLog.WriteErrorApp("CheckProgramNameDat: " + Environment.NewLine + fileName + "\n" + ex.Message);
				return "NG";
			}
		}
		public int FlexAssy_22_ICT_ReadFileTxtNew(string factory, string machineID, int iD_LogFile, string fileName, string fileNameRoot, string fileNameSample)
		{
			try
			{
				string connectionStringOptionVCD = Singleton_03_Common.IDBInfoService.ConnectionStringOption(factory, "SEI-VCDTraceDB");
				string connectionStringOptionTriCuong = Singleton_03_Common.IDBInfoService.ConnectionStringOption(factory, "TCI");
				StreamReader file = new StreamReader(fileName);
				using (var lineReader = new ReadFileText(file))
				{
					IEnumerable<string> lines = lineReader;
					string[] enumerable = lines as string[] ?? lines.ToArray();
					int addSample = 0;
					string productID = null;
					DateTime dateTime = DateTime.Now;
					int location = 0;
					string specFile = "";
					bool isFail = false;
					Boolean isProduct17 = false;
					//
					string operatorID = "";
					string facilityID = "";
					string jigID = "";
					string PalletJigID = "";
					string pCB = "";
					string testProgram = "";
					string result = "";
					string failures = "";
					string reportDate = string.Empty;
					string time = string.Empty;
					string programName = string.Empty;
					string jigNo = string.Empty;
					for (int i = 0; i < enumerable.Length; i++)
					{
						string[] strArr = enumerable[i].Split(',');
						int line = i + 1;

						// Read log file hàng 17'
						if (strArr.Length == 18)
						{
							if (strArr[0] == "BARCODE")
							{
								isProduct17 = true;
								continue;
							}
							//  Đọc dòng có ProductID
							productID = strArr[0];
							productID = Regex.Replace(strArr[0].Trim(), @"[^0-9a-zA-Z]+", "");
							if (productID.Length == 12)
							{
								DataTable dt = Singleton_02_FlexAssy.ISource_FlexAssy_SEI_VCDTrace_Service.Tbl2DCodeScan_GetPcsIDWithProductICT(factory, productID, connectionStringOptionVCD);
								if (dt == null)
									break;

								if (dt.Rows.Count > 0)
								{
									productID = dt.Rows[0]["ProductID"].ToString();
									PalletJigID = dt.Rows[0]["RFTagID"].ToString();
								}
								else if (dt.Rows.Count == 0 && factory.Equals("F4"))
								{
									dt = Singleton_02_FlexAssy.ISource_FlexAssy_TCI_Service.Tbl2DCode_GetPcsIDWithProductICT(factory, productID, connectionStringOptionTriCuong);
									if (dt == null)
										break;

									if (dt.Rows.Count > 0)
									{
										productID = dt.Rows[0]["ProductID"].ToString();
									}
								}
							}
							//Kết quả
							if (!string.IsNullOrEmpty(productID))
							{
								DataTable dtPl = Singleton_02_FlexAssy.ISource_FlexAssy_SEI_VCDTrace_Service.Tbl2DCodeScan_GetPcsIDWithProductICT(factory, productID, connectionStringOptionVCD);

								if (dtPl?.Rows.Count > 0)
								{
									PalletJigID = dtPl.Rows[0]["RFTagID"].ToString();
								}
								//Kết quả 
								result = strArr[1];
								//  Date Time
								reportDate = strArr[2];
								time = strArr[3];
								dateTime = Convert.ToDateTime($"{reportDate} {time}");
								//MachineID
								machineID = strArr[4];
								//ProgramName 
								testProgram = strArr[5];
								//JigNo
								jigID = strArr[6];
								//OperatorID
								operatorID = strArr[7];
								//FacilityID 
								facilityID = strArr[8];
								location = 1;
								//int rs = FlexAssy_22_OQC_OBA_ICT_RetestCycle_Save(productID, dateTime, machineID, operatorID, facilityID, jigID, pCB, location, testProgram, result, iD_LogFile, line, factory);
								int rs = FlexAssy_22_OQC_OBA_H4W_ORT_ICT_RetestCycle_Save(productID, dateTime, machineID, operatorID, facilityID, jigID, pCB, location, testProgram, result, "", iD_LogFile, line, factory, PalletJigID);
								if (rs == -9)
									return -9;
								Boolean isSample = false;
								if (addSample == 0)
								{
									if (strArr.Length > 13 && strArr[13].Trim().Contains("-MS"))
									{
										rs = FlexAssy_22_ICT_LogFile_Sample_Insert(dateTime, machineID, testProgram, fileNameRoot, fileNameSample, factory);
										isSample = true;
										if (rs == -9)
											return -9;// tại sao lại Return đằng sau vãn còn pcs khác
									}
									else
									{
										DataTable dt1 = FlexAssy_22_ICT_Sample_GetByProductID(productID, factory);
										if (dt1 == null)
											break;

										if (dt1.Rows.Count > 0)
										{
											isSample = true;
											rs = FlexAssy_22_ICT_LogFile_Sample_Insert(dateTime, machineID, testProgram, fileNameRoot, fileNameSample, factory);
											if (rs == -9)
												return -9;
										}
									}
									addSample++;
								}
								if (!isSample)
								{
									string connectionStringOptionBoard = Singleton_03_Common.IDBInfoService.ConnectionStringOption(factory, "SEI-VCDTraceDB");
									string ItemName = Singleton_02_FlexAssy.ISource_FlexAssy_SEI_VCDTrace_Service.GetItemName_IndiByProductID(productID, out string IndicationNumber, connectionStringOptionBoard);
									string Line = "";
									DataTable dtMachineID = Singleton_04_Machine.ItMachineMtnService.tMachineList_GetByID(machineID);
									if (dtMachineID?.Rows.Count > 0)
									{
										Line = dtMachineID.Rows[0]["LineID"] + "";
									}
									DateTime DateTime = DataConvert.ToDateTime(dateTime);
									if (DateTime.Hour < 6)
									{
										DateTime = DateTime.AddDays(-1);
										DateTime = DateTime.Date;
									}
									else
									{
										DateTime = DateTime.Date;
									}
									// Insert data
									if (result.ToUpper() == "PASS")
									{
										rs = Singleton_06_SPC.iSPC_CommonService.OEE_Product_Save(ItemName, IndicationNumber, Line, "ICT", machineID, 1, 0, DataConvert.ToDateTime(dateTime), "NewSPC_" + factory);
									}
									else
									{
										rs = Singleton_06_SPC.iSPC_CommonService.OEE_Product_Save(ItemName, IndicationNumber, Line, "ICT", machineID, 0, 1, DataConvert.ToDateTime(dateTime), "NewSPC_" + factory);
									}
								}
							}
						}
						if (!isProduct17)
						{
							//  0. Test Results Report
							//  1.
							//  2. Assembly Name:  None assigned
							//  3. 
							//  4. UUT Serial Number: 7255514T199ST305EE1Y00137B
							//  5. 
							//  6. Failures:                 0
							//  7. 
							//  8. Report Date:              Feb 12, 2016
							//  9. Time: 06:47:21
							//  10. 
							//  11. Test System:              CheckSum Analyst ems
							//  12. 
							//  13. File Name: 54ZLCD - ICT - EVT1.SPEC
							//  14.
							//  15.
							//  16. =============================================================================
							//  17.
							//  18.
							//  19. From   From     To    To      Test Test      Test       Low     High   Measured
							//  20. Point--Name-- Point--Name-- - Type Range----Title-- - -Limit - -Limit - -Value--
							//  21. 
							//  22.  51            28            Res  9760 SUS - GND       0.0001  5.0000   0.0348                <= START CHECK
							if (enumerable[i].IndexOf("Test Results Report", StringComparison.Ordinal) != -1)
							{
								line = i + 1;
								failures = "0";

								i += 4;
								if (enumerable[i].IndexOf("UUT Serial Number:", StringComparison.Ordinal) != -1)
								{
									productID = Regex.Replace(enumerable[i].Replace("UUT Serial Number:", "").Trim(), @"[^0-9a-zA-Z]+", "");
								}

								i += 2;
								if (enumerable[i].IndexOf("Failures:", StringComparison.Ordinal) != -1)
								{
									failures = enumerable[i].Replace("Failures:", "").Trim();
									isFail = failures != "0";
								}

								i += 2;
								if (enumerable[i].IndexOf("Report Date:", StringComparison.Ordinal) != -1)
								{
									dateTime = Convert.ToDateTime($"{enumerable[i].Replace("Report Date:", "").Trim()} {enumerable[i + 1].Replace("Time:", "").Trim()}");
								}

								i += 5;
								if (enumerable[i].IndexOf("File Name:", StringComparison.Ordinal) != -1)
								{
									if (!string.IsNullOrEmpty(productID))
									{
										DataTable dt = Singleton_02_FlexAssy.ISource_FlexAssy_SEI_VCDTrace_Service.Tbl2DCodeScan_GetPcsIDWithProductICT(factory, productID, connectionStringOptionVCD);
										if (dt?.Rows.Count > 0)
										{
											PalletJigID = dt.Rows[0]["RFTagID"].ToString();
										}

										location++;
										specFile = enumerable[i].Replace("File Name:", "").Trim();
										operatorID = "";
										facilityID = "";
										jigID = "";
										pCB = "";
										testProgram = specFile;
										result = failures == "0" ? "Pass" : "Fail";
										//int rs = FlexAssy_22_OQC_OBA_ICT_RetestCycle_Save(productID, dateTime, machineID, operatorID, facilityID, jigID, pCB, location, testProgram, result, iD_LogFile, line, factory);
										int rs = FlexAssy_22_OQC_OBA_H4W_ORT_ICT_RetestCycle_Save(productID, dateTime, machineID, operatorID, facilityID, jigID, pCB, location, testProgram, result, "", iD_LogFile, line, factory, PalletJigID);
										if (rs == -9)
											return -9;
									}
								}

								i += 8;
								continue;
							}
						}
					}

					int rsUpdate = FlexAssy_22_ICT_LogFile_UpdateLineRead(iD_LogFile, enumerable.Length, true, factory);
					if (rsUpdate == -9)
						return -9;

					return 1;
				}
			}
			catch (Exception ex)
			{
				int rsUpdate = FlexAssy_22_ICT_LogFile_UpdateLineRead(iD_LogFile, -1, false, factory);
				if (rsUpdate == -9)
					return -9;

				ManageLog.WriteErrorApp("FlexAssy_22_ICT_ProcessReadLog: " + iD_LogFile + Environment.NewLine + fileNameRoot + "\n" + ex.Message);
				return -2;
			}
		}
		public int FlexAssy_22_ICT_ReadFileDat(string factory, string machineID, int iD_LogFile, string fileName, string fileNameRoot, string fileNameSample)
		{
			string connectionStringOptionVCD = Singleton_03_Common.IDBInfoService.ConnectionStringOption(factory, "SEI-VCDTraceDB");
			string connectionStringOptionTriCuong = Singleton_03_Common.IDBInfoService.ConnectionStringOption(factory, "TCI");
			try
			{
				int addSample = 0;
				StreamReader file = new StreamReader(fileName);
				using (var lineReader = new ReadFileText(file))
				{
					IEnumerable<string> lines = lineReader;
					string[] enumerable = lines as string[] ?? lines.ToArray();

					string productID = null;
					DateTime dateTime = DateTime.Now;
					int location = 0;
					bool isFail = false;
					string operatorID = "";
					string facilityID = "";
					string jigID = "";
					string pCB = "";
					string testProgram = "";
					string result = "";
					string MPEResult = "";
					string failures = "";
					DateTime ShotTime = DateTime.Now;
					int counter = 0;
					FlexAssy_22_ICT_CalculatorDetail_Model lstCal_Ict = new FlexAssy_22_ICT_CalculatorDetail_Model();
					bool isProductID = false;

					for (int i = 0; i < enumerable.Length; i++)
					{
						string[] strArr = enumerable[i].Split(',');
						if (strArr.Length >= 13)
						{
							isProductID = true;
							//Lưu giá trị đo điện trở ICT
							#region xử lý giá trị đo ICT
							if (!string.IsNullOrEmpty(lstCal_Ict.ProductID))
							{
								int rs_cal = FlexAssy_22_OQC_OBA_H4W_ORT_ICT_RetestCycle_Save_Calculator(lstCal_Ict.ProductID, lstCal_Ict.VolumeMax, lstCal_Ict.VolumeMin, lstCal_Ict.VolumeAve, testProgram, dateTime, factory);
								if (rs_cal == -9)
									return -9;
							}
							#endregion

							location = 0;
							int line = i + 1;
							string dateCreateFile = Path.GetFileName(fileName).Substring(Path.GetFileName(fileName).LastIndexOf('_') + 1, 14);
							// vì hàng 20 ký tự có ký tự + nên sẽ không Regex bỏ ký tự + nữa
							productID = Regex.Replace(strArr[5].Trim(), @"[^0-9a-zA-Z+]+", "");
							if (productID.Length == 12)
							{
								DataTable dt = Singleton_02_FlexAssy.ISource_FlexAssy_SEI_VCDTrace_Service.Tbl2DCodeScan_GetPcsIDWithProductICT(factory, productID, connectionStringOptionVCD);
								if (dt == null)
									break;

								if (dt.Rows.Count > 0)
								{
									productID = dt.Rows[0]["ProductID"].ToString();
								}
								else if (dt.Rows.Count == 0 && factory.Equals("F4"))
								{
									dt = Singleton_02_FlexAssy.ISource_FlexAssy_TCI_Service.Tbl2DCode_GetPcsIDWithProductICT(factory, productID, connectionStringOptionTriCuong);
									if (dt == null)
										break;

									if (dt.Rows.Count > 0)
									{
										productID = dt.Rows[0]["ProductID"].ToString();
									}
								}
							}

							if (strArr[1].Trim().Count() >= 15)
							{
								dateTime = Convert.ToDateTime(strArr[1].Trim().Replace("\"", ""));
							}
							else
							{
								//dateTime = Convert.ToDateTime($"{int.Parse(dateCreateFile.Substring(0, 4))}-{dateCreateFile.Substring(4, 2)}-{dateCreateFile.Substring(6, 2)} {dateCreateFile.Substring(8, 2)}:{dateCreateFile.Substring(10, 2)}:{dateCreateFile.Substring(12, 2)}");
								dateTime = Convert.ToDateTime($"{int.Parse(dateCreateFile.Substring(0, 4))}-{dateCreateFile.Substring(4, 2)}-{dateCreateFile.Substring(6, 2)} {strArr[1].Trim().Replace("\"", "")}");
							}

							failures = strArr[2].Trim();
							isFail = failures != "0";

							operatorID = strArr[10].Replace("\"", "").Trim();
							facilityID = strArr[8].Replace("\"", "").Trim();
							if (factory.ToUpper() == "F3")
							{
								machineID = strArr[8].Replace("\"", "").Trim();
							}
							pCB = strArr[7].Replace("\"", "").Trim();
							jigID = strArr[9].Replace("\"", "").Trim();
							testProgram = strArr[0].Replace("\"", "").Trim();
							result = failures == "0" ? "Pass" : "Fail";
							try
							{
								Singleton_02_FlexAssy.ISource_FlexAssy_SEI_VCDTrace_Service.GetICTResult(productID, dateTime, out string _Result, out string _MPEResult, connectionStringOptionVCD);
								if (_Result == null)
								{
									return -9;
								}
								if (!string.IsNullOrEmpty(_Result))
								{
									result = _Result;
								}
								if (!string.IsNullOrEmpty(_MPEResult))
								{
									MPEResult = _MPEResult;
								}
								if (pCB.Contains("PCB"))
								{
									string strLocation = pCB.Substring(0, pCB.IndexOf('('));
									location = int.Parse(strLocation.Replace("PCB ", ""));
								}
							}
							catch (Exception)
							{
								location = 0;
							}

							if (!string.IsNullOrEmpty(facilityID))
							{
								if (factory.ToUpper() == "F4" && facilityID.Length < 7)
								{
									machineID = factory.Equals("F3") ? $"MOPA{facilityID}-3" : $"MOPA{facilityID}";
								}
								facilityID = factory;
							}
							int rs;
							if (strArr.Length >= 17)
							{
								string cavityNo = strArr[14].ToString();
								string retestCycleOIMS = strArr[15].ToString();

								rs = FlexAssy_22_OQC_OBA_H4W_ORT_ICT_RetestCycle_Save_OIMS(productID, dateTime, machineID, operatorID, facilityID, jigID, pCB, location, testProgram, result, MPEResult, iD_LogFile, line, cavityNo, retestCycleOIMS, factory);
								if (rs == -9)
									return -9;
							}
							else
							{
								rs = FlexAssy_22_OQC_OBA_H4W_ORT_ICT_RetestCycle_Save(productID, dateTime, machineID, operatorID, facilityID, jigID, pCB, location, testProgram, result, MPEResult, iD_LogFile, line, factory);
								if (rs == -9)
									return -9;
							}
							Boolean isSample = false;

							//"4BVCD_EVT_RFID_V3.SPEC","00:00:23",  0,  0, 0,"<94655AQSK54G>",100, "PCB  1( 4 same UUTs/panel)","023","716490-994316","OP30760", "",4BVCD-MS,49A0100B000005E0,
							//"5BVCD-DOE1_EVT_QRCODE_V3-2.SPEC","13:26:00",  0,  0, 0,"<FNJ0114PLV9MQV55Q>",97, "PCB  1( 4 same UUTs/panel)","008","717090-994311","OP06789", "",PE-NGOC-TEST-MAY,717090998160-MS011,
							if (addSample == 0)
							{
								if (strArr.Length > 13 && strArr[13].Trim().Contains("-MS"))
								{
									//rs = FlexAssy_22_ICT_LogFile_Sample_Insert(dateTime, machineID, testProgram.Split('_')[0].Replace("Retest-", "").Replace("RETEST-", ""), fileNameRoot, fileNameSample, factory);
									rs = FlexAssy_22_ICT_LogFile_Sample_Insert(dateTime, machineID, testProgram, fileNameRoot, fileNameSample, factory);
									isSample = true;
									if (rs == -9)
										return -9;// tại sao lại Return đằng sau vãn còn pcs khác
								}
								else
								{
									// Kiem tra pcsID có trong Sample hay không
									DataTable dt1 = FlexAssy_22_ICT_Sample_GetByProductID(productID, factory);
									if (dt1 == null)
										break; // tại sao lại break đằng sau vãn còn pcs khác

									if (dt1.Rows.Count > 0)
									{
										//rs = FlexAssy_22_ICT_LogFile_Sample_Insert(dateTime, machineID, testProgram.Split('_')[0].Replace("Retest-", "").Replace("RETEST-", ""), fileNameRoot, fileNameSample, factory);
										isSample = true;
										rs = FlexAssy_22_ICT_LogFile_Sample_Insert(dateTime, machineID, testProgram, fileNameRoot, fileNameSample, factory);
										if (rs == -9)
											return -9;
									}
								}

								addSample++;
							}
							if (!isSample)
							{
								string connectionStringOptionBoard = Singleton_03_Common.IDBInfoService.ConnectionStringOption(factory, "SEI-VCDTraceDB");
								string ItemName = Singleton_02_FlexAssy.ISource_FlexAssy_SEI_VCDTrace_Service.GetItemName_IndiByProductID(productID, out string IndicationNumber, connectionStringOptionBoard);
								string Line = "";
								DataTable dtMachineID = Singleton_04_Machine.ItMachineMtnService.tMachineList_GetByID(machineID);
								if (dtMachineID?.Rows.Count > 0)
								{
									Line = dtMachineID.Rows[0]["LineID"] + "";
								}
								DateTime DateTime = DataConvert.ToDateTime(dateTime);
								if (DateTime.Hour < 6)
								{
									DateTime = DateTime.AddDays(-1);
									DateTime = DateTime.Date;
								}
								else
								{
									DateTime = DateTime.Date;
								}
								// Insert data
								if (result.ToUpper() == "PASS")
								{
									rs = Singleton_06_SPC.iSPC_CommonService.OEE_Product_Save(ItemName, IndicationNumber, Line, "ICT", machineID, 1, 0, DataConvert.ToDateTime(dateTime), "NewSPC_" + factory);
								}
								else
								{
									rs = Singleton_06_SPC.iSPC_CommonService.OEE_Product_Save(ItemName, IndicationNumber, Line, "ICT", machineID, 0, 1, DataConvert.ToDateTime(dateTime), "NewSPC_" + factory);
								}
							}

							continue;
						}
						else  //tinh gia tri do ICT max, min, average
						{
							if (strArr.Length > 6)
							{
								if (isProductID == false)
								{
									continue;
								}
								else if (strArr[4].Replace("\"", "").Trim() == "MemS")
								{
									continue;
								}
								else if ((strArr[6].Replace("\"", "").Trim() == "OPEN") || (strArr[6].Replace("\"", "").Trim() == "CNT Short"))
								{
									continue;
								}
								else
								{
									if (Regex.IsMatch(strArr[5].Replace("\"", "").ToString(), "[^0-9.]"))
									{
										continue;
									}
									double netValue = DataConvert.ConvertToDouble(strArr[5].Replace("\"", ""));

									counter++;

									string Type = strArr[4].Replace("\"", "").Trim();
									if (Type == "Res")
									{
										FlexAssy_22_ICT_CalculatorDetail_Model.ICT_Calculator(ref lstCal_Ict, productID, counter, netValue);

									}
								}
							}
						}
					}
					//Lưu giá trị đo điện trở ICT
					#region xử lý giá trị đo ICT
					if (!string.IsNullOrEmpty(lstCal_Ict.ProductID))
					{
						int rs_cal = FlexAssy_22_OQC_OBA_H4W_ORT_ICT_RetestCycle_Save_Calculator(lstCal_Ict.ProductID, lstCal_Ict.VolumeMax, lstCal_Ict.VolumeMin, lstCal_Ict.VolumeAve, testProgram, dateTime, factory);
						if (rs_cal == -9)
							return -9;
					}
					#endregion

					int rsUpdate = FlexAssy_22_ICT_LogFile_UpdateLineRead_FileName(iD_LogFile, enumerable.Length, true, fileNameSample, factory);
					if (rsUpdate == -9)
						return -9;
					return 1;
				}
			}
			catch (Exception ex)
			{
				int rsUpdate = FlexAssy_22_ICT_LogFile_UpdateLineRead_FileName(iD_LogFile, -1, false, fileNameSample, factory);
				if (rsUpdate == -9)
					return -9;

				ManageLog.WriteErrorApp("FlexAssy_22_ICT_ProcessReadLog: " + iD_LogFile + Environment.NewLine + fileName + "\n" + ex.Message);
				return -2;
			}
		}
		public int FlexAssy_22_ICT_ReadFileDat2(string factory, string machineID, int iD_LogFile, string fileName, string fileNameRoot, string fileNameSample, string jigID, string connectionStringOptionVCD, string connectionStringOptionTriCuong)
		{
			// Update duong dan ICT F2
			//int rs = FlexAssy_22_ICT_LogFile_UpdateFileName(iD_LogFile, fileNameSample, factory);
			//if (rs == -9)
			//    return -9;
			try
			{
				int addSample = 0;
				StreamReader file = new StreamReader(fileName);
				using (var lineReader = new ReadFileText(file))
				{
					IEnumerable<string> lines = lineReader;
					string[] enumerable = lines as string[] ?? lines.ToArray();

					string productID = null;
					DateTime dateTime = DateTime.Now;
					int location = 0;
					bool isFail = false;
					string operatorID = "";
					string facilityID = "";
					string pCB = "";
					string jigIDLogFile = "";
					string testProgram = "";
					string result = "";
					string MPEResult = "";
					string failures = "";
					string cavityNo = "";
					string retestCycleOIMS = "";
					string IndicationNumber = "";
					string PalletJigID = "";
					DateTime ShotTime = DateTime.Now;
					int counter = 0;
					FlexAssy_22_ICT_CalculatorDetail_Model lstCal_Ict = new FlexAssy_22_ICT_CalculatorDetail_Model();
					bool isProductID = false;

					for (int i = 0; i < enumerable.Length; i++)
					{
						string[] strArr = enumerable[i].Split(',');
						if (strArr.Length >= 13)
						{
							isProductID = true;
							//Lưu giá trị đo điện trở ICT
							#region xử lý giá trị đo ICT
							if (!string.IsNullOrEmpty(lstCal_Ict.ProductID))
							{
								int rs_cal = FlexAssy_22_OQC_OBA_H4W_ORT_ICT_RetestCycle_Save_Calculator(lstCal_Ict.ProductID, lstCal_Ict.VolumeMax, lstCal_Ict.VolumeMin, lstCal_Ict.VolumeAve, testProgram, dateTime, factory);
								if (rs_cal == -9)
									return -9;
							}
							#endregion

							location = 0;
							int line = i + 1;
							string dateCreateFile = Path.GetFileName(fileName).Substring(Path.GetFileName(fileName).LastIndexOf('_') + 1, 14);

							// vì hàng 20 ký tự có ký tự + nên sẽ không Regex bỏ ký tự + nữa
							productID = Regex.Replace(strArr[5].Trim(), @"[^0-9a-zA-Z+]+", "");
							if (productID.Length == 12)
							{
								DataTable dt = Singleton_02_FlexAssy.ISource_FlexAssy_SEI_VCDTrace_Service.Tbl2DCodeScan_GetPcsIDWithProductICT(factory, productID, connectionStringOptionVCD);
								if (dt == null)
									break;

								if (dt.Rows.Count > 0)
								{
									productID = dt.Rows[0]["ProductID"].ToString();
								}
								else if (dt.Rows.Count == 0 && factory.Equals("F4"))
								{
									dt = Singleton_02_FlexAssy.ISource_FlexAssy_TCI_Service.Tbl2DCode_GetPcsIDWithProductICT(factory, productID, connectionStringOptionTriCuong);
									if (dt == null)
										break;

									if (dt.Rows.Count > 0)
									{
										productID = dt.Rows[0]["ProductID"].ToString();
									}
								}
							}

							try
							{
								if (strArr[1].Trim().Count() >= 15)
								{
									dateTime = Convert.ToDateTime(strArr[1].Trim().Replace("\"", ""));
								}
								else
								{
									//dateTime = Convert.ToDateTime($"{int.Parse(dateCreateFile.Substring(0, 4))}-{dateCreateFile.Substring(4, 2)}-{dateCreateFile.Substring(6, 2)} {dateCreateFile.Substring(8, 2)}:{dateCreateFile.Substring(10, 2)}:{dateCreateFile.Substring(12, 2)}");
									dateTime = Convert.ToDateTime($"{int.Parse(dateCreateFile.Substring(0, 4))}-{dateCreateFile.Substring(4, 2)}-{dateCreateFile.Substring(6, 2)} {strArr[1].Trim().Replace("\"", "")}");
								}
							}
							catch (Exception)
							{
							}
							failures = strArr[2].Trim();
							isFail = failures != "0";

							operatorID = strArr[10].Replace("\"", "").Trim();
							facilityID = strArr[8].Replace("\"", "").Trim();
							IndicationNumber = strArr[12].Replace("\"", "").Trim();
							if (strArr.Length >= 17)
							{
								cavityNo = strArr[14].ToString();
								retestCycleOIMS = strArr[15].ToString();
							}
							if (factory.ToUpper() == "F3")
							{
								machineID = strArr[8].Replace("\"", "").Trim();
							}
							pCB = strArr[7].Replace("\"", "").Trim();
							jigIDLogFile = strArr[9].Replace("\"", "").Trim();
							if (string.IsNullOrEmpty(jigIDLogFile))
							{
								jigIDLogFile = jigID;
							}
							testProgram = strArr[0].Replace("\"", "").Trim();
							result = failures == "0" ? "Pass" : "Fail";
							try
							{
								Singleton_02_FlexAssy.ISource_FlexAssy_SEI_VCDTrace_Service.GetICTResult(productID, dateTime, out string _Result, out string _MPEResult, connectionStringOptionVCD);
								if (_Result == null)
								{
									return -9;
								}
								if (!string.IsNullOrEmpty(_Result))
								{
									result = _Result;
								}
								if (!string.IsNullOrEmpty(_MPEResult))
								{
									MPEResult = _MPEResult;
								}
								if (pCB.Contains("PCB"))
								{
									string strLocation = pCB.Substring(0, pCB.IndexOf('('));
									location = int.Parse(strLocation.Replace("PCB ", ""));
								}
							}
							catch (Exception)
							{
								location = 0;
							}

							if (!string.IsNullOrEmpty(facilityID))
							{
								if (factory.ToUpper() == "F4" && facilityID.Length < 7)
								{
									machineID = factory.Equals("F3") ? $"MOPA{facilityID}-3" : $"MOPA{facilityID}";
								}
								facilityID = factory;
							}
							DataTable dtPl = Singleton_02_FlexAssy.ISource_FlexAssy_SEI_VCDTrace_Service.Tbl2DCodeScan_GetPcsIDWithProductICT(factory, productID, connectionStringOptionVCD);

							if (dtPl?.Rows.Count > 0)
							{
								PalletJigID = dtPl.Rows[0]["RFTagID"].ToString();
							}
							if (strArr.Length >= 17)
							{
								int rs = FlexAssy_22_OQC_OBA_H4W_ORT_ICT_RetestCycle_Save_OIMS(productID, dateTime, machineID, operatorID, facilityID, jigIDLogFile, pCB, location, testProgram, result,
									MPEResult, iD_LogFile, line, cavityNo, retestCycleOIMS, factory, PalletJigID);
								if (rs == -9)
									return -9;
							}
							else
							{
								int rs = FlexAssy_22_OQC_OBA_H4W_ORT_ICT_RetestCycle_Save(productID, dateTime, machineID, operatorID, facilityID, jigIDLogFile, pCB, location, testProgram, result, MPEResult, iD_LogFile, line, factory, PalletJigID);
								if (rs == -9)
									return -9;
							}
							//int rs = FlexAssy_22_OQC_OBA_ICT_RetestCycle_Save(productID, dateTime, machineID, operatorID, facilityID, jigID, pCB, location, testProgram, result, iD_LogFile, line, factory);

							Boolean isSample = false;

							//"4BVCD_EVT_RFID_V3.SPEC","00:00:23",  0,  0, 0,"<94655AQSK54G>",100, "PCB  1( 4 same UUTs/panel)","023","716490-994316","OP30760", "",4BVCD-MS,49A0100B000005E0,
							//"5BVCD-DOE1_EVT_QRCODE_V3-2.SPEC","13:26:00",  0,  0, 0,"<FNJ0114PLV9MQV55Q>",97, "PCB  1( 4 same UUTs/panel)","008","717090-994311","OP06789", "",PE-NGOC-TEST-MAY,717090998160-MS011,
							if (addSample == 0)
							{
								if (strArr.Length > 13 && strArr[13].Trim().Contains("-MS"))
								{
									//rs = FlexAssy_22_ICT_LogFile_Sample_Insert(dateTime, machineID, testProgram.Split('_')[0].Replace("Retest-", "").Replace("RETEST-", ""), fileNameRoot, fileNameSample, factory);
									var rs = FlexAssy_22_ICT_LogFile_Sample_Insert(dateTime, machineID, testProgram, fileNameRoot, fileNameSample, factory);
									isSample = true;
									if (rs == -9)
										return -9;// tại sao lại Return đằng sau vãn còn pcs khác
								}
								else
								{
									// Kiem tra pcsID có trong Sample hay không
									DataTable dt1 = FlexAssy_22_ICT_Sample_GetByProductID(productID, factory);
									if (dt1 == null)
										break; // tại sao lại break đằng sau vãn còn pcs khác

									if (dt1.Rows.Count > 0)
									{
										//rs = FlexAssy_22_ICT_LogFile_Sample_Insert(dateTime, machineID, testProgram.Split('_')[0].Replace("Retest-", "").Replace("RETEST-", ""), fileNameRoot, fileNameSample, factory);
										isSample = true;
										var rs = FlexAssy_22_ICT_LogFile_Sample_Insert(dateTime, machineID, testProgram, fileNameRoot, fileNameSample, factory);
										if (rs == -9)
											return -9;
									}
								}

								addSample++;
							}
							if (!isSample)
							{
								string ItemName = Singleton_02_FlexAssy.ISource_FlexAssy_SEI_VCDTrace_Service.GetItemNameByProductID(productID, connectionStringOptionVCD);
								string Line = "";
								DataTable dtMachineID = Singleton_04_Machine.ItMachineMtnService.tMachineList_GetByID(machineID);
								if (dtMachineID?.Rows.Count > 0)
								{
									Line = dtMachineID.Rows[0]["LineID"] + "";
								}
								DateTime DateTime = DataConvert.ToDateTime(dateTime);
								if (DateTime.Hour < 6)
								{
									DateTime = DateTime.AddDays(-1);
									DateTime = DateTime.Date;
								}
								else
								{
									DateTime = DateTime.Date;
								}
								// Insert data
								if (result.ToUpper() == "PASS")
								{
									var rs = Singleton_06_SPC.iSPC_CommonService.OEE_Product_Save(ItemName, IndicationNumber, Line, "ICT", machineID, 1, 0, DataConvert.ToDateTime(dateTime), "NewSPC_" + factory);
								}
								else
								{
									var rs = Singleton_06_SPC.iSPC_CommonService.OEE_Product_Save(ItemName, IndicationNumber, Line, "ICT", machineID, 0, 1, DataConvert.ToDateTime(dateTime), "NewSPC_" + factory);
								}
							}

							continue;
						}
						else  //tinh gia tri do ICT max, min, average
						{
							if (strArr.Length > 6)
							{
								string Type = strArr[4].Replace("\"", "").Trim();
								if (isProductID == false)
								{
									continue;
								}
								else if (Type == "Res")
								{
									if (Regex.IsMatch(strArr[5].Replace("\"", "").ToString(), "[^0-9.]"))
									{
										continue;
									}
									double netValue = DataConvert.ConvertToDouble(strArr[5].Replace("\"", ""));

									counter++;
									FlexAssy_22_ICT_CalculatorDetail_Model.ICT_Calculator(ref lstCal_Ict, productID, counter, netValue);
								}


							}
						}
					}
					//Lưu giá trị đo điện trở ICT
					#region xử lý giá trị đo ICT
					if (!string.IsNullOrEmpty(lstCal_Ict.ProductID))
					{
						int rs_cal = FlexAssy_22_OQC_OBA_H4W_ORT_ICT_RetestCycle_Save_Calculator(lstCal_Ict.ProductID, lstCal_Ict.VolumeMax, lstCal_Ict.VolumeMin, lstCal_Ict.VolumeAve, testProgram, dateTime, factory);
						if (rs_cal == -9)
							return -9;
					}
					#endregion

					int rsUpdate = FlexAssy_22_ICT_LogFile_UpdateLineRead_FileName(iD_LogFile, enumerable.Length, true, fileNameSample, factory);
					if (rsUpdate == -9)
						return -9;
					return 1;
				}
			}
			catch (Exception ex)
			{
				int rsUpdate = FlexAssy_22_ICT_LogFile_UpdateLineRead_FileName(iD_LogFile, -1, false, fileNameSample, factory);
				if (rsUpdate == -9)
					return -9;

				ManageLog.WriteErrorApp("FlexAssy_22_ICT_ProcessReadLog: " + iD_LogFile + Environment.NewLine + fileName + "\n" + ex.Message);
				return -2;
			}
		}
		public int FlexAssy_22_ICT_ReadFileCsv(string factory, string machineID, int iD_LogFile, string fileName, string fileNameRoot, string fileNameSample, int LineRead)
		{
			string connectionStringOptionVCD = Singleton_03_Common.IDBInfoService.ConnectionStringOption(factory, "SEI-VCDTraceDB");
			string connectionStringOptionTriCuong = Singleton_03_Common.IDBInfoService.ConnectionStringOption(factory, "TCI");
			try
			{
				int addSample = 0;
				StreamReader file = new StreamReader(fileName);
				using (var lineReader = new ReadFileText(file))
				{
					IEnumerable<string> lines = lineReader;
					string[] enumerable = lines as string[] ?? lines.ToArray();

					string productID = null;
					DateTime dateTime = DateTime.Now;
					int location = 0;
					string operatorID = "";
					string facilityID = "";
					string jigID = "";
					string pCB = "";
					string testProgram = "";
					string result = "";
					bool isPcsInfor = false;
					DateTime ShotTime = DateTime.Now;
					pCB = Path.GetFileName(fileName).Contains("192.168.0.3") ? "1" : "2";

					for (int i = LineRead; i < enumerable.Length; i++)
					{
						string[] strArr = enumerable[i].Split(',');
						if (!isPcsInfor)
						{
							if (i >= 7)
							{
								isPcsInfor = true;
							}
						}
						if (isPcsInfor)
						{
							if (strArr.Length >= 12)
							{

								location = 0;
								int line = i + 1;

								productID = Regex.Replace(strArr[0].Trim(), @"[^0-9a-zA-Z]+", "");
								if (productID.Length == 12)
								{
									DataTable dt = Singleton_02_FlexAssy.ISource_FlexAssy_SEI_VCDTrace_Service.Tbl2DCodeScan_GetPcsIDWithProductICT(factory, productID, connectionStringOptionVCD);
									if (dt == null)
										break;

									if (dt.Rows.Count > 0)
									{
										productID = dt.Rows[0]["ProductID"].ToString();
									}
									else if (dt.Rows.Count == 0 && factory.Equals("F4"))
									{
										dt = Singleton_02_FlexAssy.ISource_FlexAssy_TCI_Service.Tbl2DCode_GetPcsIDWithProductICT(factory, productID, connectionStringOptionTriCuong);
										if (dt == null)
											break;

										if (dt.Rows.Count > 0)
										{
											productID = dt.Rows[0]["ProductID"].ToString();
										}
									}
								}

								dateTime = Convert.ToDateTime($"{strArr[2]} {strArr[3]}");
								DataTable FixInfor = FlexAssy_22_ICT_Testprogram_History_GetByFileName(fileNameRoot, dateTime, factory);
								if (FixInfor?.Rows.Count > 0)
								{
									machineID = DataConvert.ConvertToString(FixInfor.Rows[0]["MachineID"]);
									jigID = DataConvert.ConvertToString(FixInfor.Rows[0]["JigID"]);
									testProgram = DataConvert.ConvertToString(FixInfor.Rows[0]["TestProgram"]);
									operatorID = DataConvert.ConvertToString(FixInfor.Rows[0]["OperatorID"]);
								}
								result = strArr[1].Trim();

								int rs = FlexAssy_22_OQC_OBA_H4W_ORT_ICT_RetestCycle_Save(productID, dateTime, machineID, operatorID, facilityID, jigID, pCB, location, testProgram, result, "", iD_LogFile, line, factory);
								if (rs == -9)
									return -9;
								Boolean IsSample = false;
								if (addSample == 0)
								{
									if (strArr.Length > 13 && strArr[13].Trim().Contains("-MS"))
									{
										rs = FlexAssy_22_ICT_LogFile_Sample_Insert(dateTime, machineID, testProgram, fileNameRoot, fileNameSample, factory);
										IsSample = true;
										if (rs == -9)
											return -9;
									}
									else
									{
										// Kiem tra pcsID có trong Sample hay không
										DataTable dt1 = FlexAssy_22_ICT_Sample_GetByProductID(productID, factory);
										if (dt1 == null)
											continue;

										if (dt1.Rows.Count > 0)
										{
											IsSample = true;
											rs = FlexAssy_22_ICT_LogFile_Sample_Insert(dateTime, machineID, testProgram, fileNameRoot, fileNameSample, factory);
											if (rs == -9)
												return -9;
										}
									}

									addSample++;
								}
								if (!IsSample)
								{
									string Line = "";
									DataTable dtMachineID = Singleton_04_Machine.ItMachineMtnService.tMachineList_GetByID(machineID);
									if (dtMachineID?.Rows.Count > 0)
									{
										Line = dtMachineID.Rows[0]["LineID"] + "";
									}
									// Insert data
									//if (result.ToUpper() == "PASS")
									//{
									//    rs = Singleton_06_SPC.iSPC_CommonService.OEE_Product_Save(Line, "ICT", machineID, 1, 0, DataConvert.ToDateTime(dateTime), "NewSPC_" + factory);
									//}
									//else
									//{
									//    rs = Singleton_06_SPC.iSPC_CommonService.OEE_Product_Save(Line, "ICT", machineID, 0, 1, DataConvert.ToDateTime(dateTime), "NewSPC_" + factory);
									//}
								}

								double netValue = DataConvert.ConvertToDouble(strArr[4].Trim());
								double netValue1 = DataConvert.ConvertToDouble(strArr[5].Trim());
								double netValue2 = DataConvert.ConvertToDouble(strArr[6].Trim());

								double[] argValue = new double[] { netValue, netValue1, netValue2 };
								double maxValue = argValue.Max();
								double minValue = argValue.Min();
								double averageValue = argValue.Average();

								int rs_cal = FlexAssy_22_OQC_OBA_H4W_ORT_ICT_RetestCycle_Save_Calculator(productID, maxValue, minValue, averageValue, testProgram, dateTime, factory);
								if (rs_cal == -9)
									return -9;

								continue;
							}
						}
					}

					int rsUpdate = FlexAssy_22_ICT_LogFile_UpdateLineRead_FileName(iD_LogFile, enumerable.Length, true, fileNameSample, factory);
					if (rsUpdate == -9)
						return -9;
					return 1;
				}
			}
			catch (Exception ex)
			{
				int rsUpdate = FlexAssy_22_ICT_LogFile_UpdateLineRead_FileName(iD_LogFile, -1, false, fileNameSample, factory);
				if (rsUpdate == -9)
					return -9;

				ManageLog.WriteErrorApp("FlexAssy_22_ICT_ProcessReadLog: " + iD_LogFile + Environment.NewLine + fileName + "\n" + ex.Message);
				return -2;
			}
		}
		public int FlexAssy_22_ICT_ReadFileDat_SPC(string factory, string machineID, int iD_LogFile, string fileName, string fileNameRoot)
		{
			try
			{
				string connectionStringOptionVCD = Singleton_03_Common.IDBInfoService.ConnectionStringOption(factory, "SEI-VCDTraceDB");
				string connectionStringOptionTriCuong = Singleton_03_Common.IDBInfoService.ConnectionStringOption(factory, "TCI");
				StreamReader file = new StreamReader(fileName);
				using (var lineReader = new ReadFileText(file))
				{
					IEnumerable<string> lines = lineReader;
					string[] enumerable = lines as string[] ?? lines.ToArray();

					string productID = null;
					DateTime dateTime = DateTime.Now;
					bool isFail = false;
					string operatorID = "";
					string facilityID = "";
					string jigID = "";
					string pCB = "";
					string testProgram = "";
					string result = "";
					decimal PCSID = 0;
					decimal PCSInforID = 0;
					string indiNumber;
					string ICTDataQuery = "";
					string failures = "";
					string ItemNameNet = "";
					DateTime ShotTime = DateTime.Now;

					for (int i = 0; i < enumerable.Length; i++)
					{
						string[] strArr = enumerable[i].Split(',');
						if (strArr.Length >= 13)
						{
							int line = i + 1;
							string dateCreateFile = Path.GetFileName(fileName).Substring(Path.GetFileName(fileName).LastIndexOf('_') + 1, 8);

							productID = Regex.Replace(strArr[5].Trim(), @"[^0-9a-zA-Z]+", "");
							if (productID.Length == 12)
							{
								DataTable dt = Singleton_02_FlexAssy.ISource_FlexAssy_SEI_VCDTrace_Service.Tbl2DCodeScan_GetPcsIDWithProductICT(factory, productID, connectionStringOptionVCD);
								if (dt == null)
									break;

								if (dt.Rows.Count > 0)
								{
									productID = dt.Rows[0]["ProductID"].ToString();
								}
								else if (dt.Rows.Count == 0 && factory.Equals("F4"))
								{
									dt = Singleton_02_FlexAssy.ISource_FlexAssy_TCI_Service.Tbl2DCode_GetPcsIDWithProductICT(factory, productID, connectionStringOptionTriCuong);
									if (dt == null)
										break;

									if (dt.Rows.Count > 0)
									{
										productID = dt.Rows[0]["ProductID"].ToString();
									}
								}
							}

							dateTime = Convert.ToDateTime($"{int.Parse(dateCreateFile.Substring(0, 4))}-{dateCreateFile.Substring(4, 2)}-{dateCreateFile.Substring(6, 2)} {strArr[1].Trim().Replace("\"", "")}");
							failures = strArr[2].Trim();
							isFail = failures != "0";

							operatorID = strArr[10].Replace("\"", "").Trim();
							facilityID = strArr[8].Replace("\"", "").Trim();
							jigID = strArr[9].Replace("\"", "").Trim();
							string[] arrPCB = strArr[7].Replace("\"", "").Trim().Split('(');
							pCB = arrPCB[0];
							testProgram = strArr[0].Replace("\"", "").Trim();
							result = failures == "0" ? "Pass" : "Fail";
							indiNumber = strArr[12].Replace("\"", "").Trim();
							string itemName = "";
							DataTable dtWorkOrder = Singleton_02_FlexAssy.IFlexAssy_WorkOrderService.FlexAssy_WorkOrder_GetByWorkOrder(indiNumber, factory);
							if (dtWorkOrder?.Rows.Count > 0)
							{
								itemName = dtWorkOrder.Rows[0]["ItemName"] != null ? dtWorkOrder.Rows[0]["ItemName"].ToString() : "";
							}
							DataTable dtConfig = Singleton_06_SPC.iSPC_02_ICTService.GetSPC_SystemConfigValue_GetByConfigName("GetNetName", "ICTNET", itemName, "SPC_" + factory);
							if (dtConfig?.Rows.Count > 0)
							{
								ItemNameNet = "," + (dtConfig.Rows[0]["ValueData"] != null ? dtConfig.Rows[0]["ValueData"].ToString() : "") + ",";
							}
							else
							{
								ItemNameNet = "";
							}
							if (failures == "0")
							{
								PCSInforID = Singleton_06_SPC.iSPC_02_ICTService.PcsInforInsert(itemName, machineID, indiNumber, pCB, jigID, dateTime, productID, "SPC_" + factory);
							}
						}
						else
						{
							if (failures == "0")
							{
								strArr = enumerable[i].Trim().Split(',');
								if (strArr.Length > 6)
								{
									string net = strArr[6].Replace("\"", "").Trim();
									string netValue = strArr[5].Replace("\"", "").Trim();
									string Type = strArr[4].Replace("\"", "").Trim();
									if (Type == "Res")
									{
										if (ItemNameNet.Contains("," + net + ","))
										{
											ICTDataQuery += "EXEC [dbo].[Prc_SPC_02_ICT_NET_DATA_Insert]" + Environment.NewLine;
											ICTDataQuery += "@PCSInforID = " + PCSInforID + ",@NetID='" + net + "',@Value=" + netValue + Environment.NewLine;
										}
									}
								}
							}
						}
					}
					if (ICTDataQuery != "")
					{
						Singleton_06_SPC.iSPC_02_ICTService.ICT_NET_DataInsert(ICTDataQuery, "SPC_" + factory);
					}
					return 1;
				}
			}
			catch (Exception ex)
			{
				ManageLog.WriteErrorApp("SPC_02_ICT_Net_ProcessReadLog: " + iD_LogFile + Environment.NewLine + fileName + "\n" + ex.Message);
				return -2;
			}
		}
		public int FlexAssy_22_ICT_NG_ReadFileDat(string factory, string machineID, int iD_LogFile, string fileName, string fileNameRoot, string connectionStringOptionVCD, string connectionStringOptionTriCuong)
		{
			try
			{
				StreamReader file = new StreamReader(fileName);
				using (var lineReader = new ReadFileText(file))
				{
					IEnumerable<string> lines = lineReader;
					string[] enumerable = lines as string[] ?? lines.ToArray();

					string productID = null;
					DateTime dateTime = DateTime.Now;
					bool isFail = false;
					string operatorID = "";
					string facilityID = "";
					string jigID = "";
					string pCB = "";
					string testProgram = "";
					string result = "";
					decimal PCSID = 0;
					decimal PCSInforID = 0;
					string indiNumber = "";
					string ICTDataQuery = "";
					string ICT_NG_Save_Query = "";
					string failures = "";
					string ItemNameNet = "";
					string itemName = "";
					DateTime ShotTime = DateTime.Now;

					for (int i = 0; i < enumerable.Length; i++)
					{
						string[] strArr = enumerable[i].Split(',');
						if (strArr.Length >= 13)
						{
							int line = i + 1;
							string dateCreateFile = Path.GetFileName(fileName).Substring(Path.GetFileName(fileName).LastIndexOf('_') + 1, 8);
							productID = Regex.Replace(strArr[5].Trim(), @"[^0-9a-zA-Z]+", "");
							if (productID.Length == 12)
							{
								DataTable dt = Singleton_02_FlexAssy.ISource_FlexAssy_SEI_VCDTrace_Service.Tbl2DCodeScan_GetPcsIDWithProductICT(factory, productID, connectionStringOptionVCD);
								if (dt == null)
									break;

								if (dt.Rows.Count > 0)
								{
									productID = dt.Rows[0]["ProductID"].ToString();
								}
								else if (dt.Rows.Count == 0 && factory.Equals("F4"))
								{
									dt = Singleton_02_FlexAssy.ISource_FlexAssy_TCI_Service.Tbl2DCode_GetPcsIDWithProductICT(factory, productID, connectionStringOptionTriCuong);
									if (dt == null)
										break;

									if (dt.Rows.Count > 0)
									{
										productID = dt.Rows[0]["ProductID"].ToString();
									}
								}
							}

							dateTime = Convert.ToDateTime($"{int.Parse(dateCreateFile.Substring(0, 4))}-{dateCreateFile.Substring(4, 2)}-{dateCreateFile.Substring(6, 2)} {strArr[1].Trim().Replace("\"", "")}");
							failures = strArr[2].Trim();
							isFail = failures != "0";
							facilityID = strArr[8].Replace("\"", "").Trim();
							if (factory.ToUpper() == "F3")
							{
								machineID = strArr[8].Replace("\"", "").Trim();
							}
							if (!string.IsNullOrEmpty(facilityID))
							{
								if (factory.ToUpper() == "F4" && facilityID.Length < 7)
								{
									machineID = factory.Equals("F3") ? $"MOPA{facilityID}-3" : $"MOPA{facilityID}";
								}
								facilityID = factory;
							}

							operatorID = strArr[10].Replace("\"", "").Trim();
							jigID = strArr[9].Replace("\"", "").Trim();
							string[] arrPCB = strArr[7].Replace("\"", "").Trim().Split('(');
							pCB = arrPCB[0];
							testProgram = strArr[0].Replace("\"", "").Trim();
							result = failures == "0" ? "Pass" : "Fail";
							indiNumber = strArr[12].Replace("\"", "").Trim();
							itemName = "";
							//DataTable dtWorkOrder = Singleton_02_FlexAssy.IFlexAssy_WorkOrderService.FlexAssy_WorkOrder_GetByWorkOrder(indiNumber, factory);
							//if (dtWorkOrder?.Rows.Count > 0)
							//{
							//    itemName = dtWorkOrder.Rows[0]["ItemName"] != null ? dtWorkOrder.Rows[0]["ItemName"].ToString() : "";
							//}
							//DataTable dtConfig = Singleton_06_SPC.iSPC_02_ICTService.GetSPC_SystemConfigValue_GetByConfigName("GetNetName", "ICTNET", itemName, "SPC_" + factory);
							//if (dtConfig?.Rows.Count > 0)
							//{
							//    ItemNameNet = "," + (dtConfig.Rows[0]["ValueData"] != null ? dtConfig.Rows[0]["ValueData"].ToString() : "") + ",";
							//}
							//else
							//{
							//    ItemNameNet = "";
							//}
							//if (failures == "0")
							//{
							//    PCSInforID = Singleton_06_SPC.iSPC_02_ICTService.PcsInforInsert(itemName, machineID, indiNumber, pCB, jigID, "SPC_" + factory);
							//    PCSID = Singleton_06_SPC.iSPC_02_ICTService.PCSIDInsert(productID, dateTime, "SPC_" + factory);
							//}
						}
						else
						{
							if (failures == "0")
							{
								continue;
							}
							strArr = enumerable[i].Trim().Split(',');
							string NetResult = strArr[2].Replace("\"", "").Trim();
							//if (failures == "0")
							//{
							//    if (strArr.Length > 6)
							//    {
							//        string net = strArr[6].Replace("\"", "").Trim();
							//        string netValue = strArr[5].Replace("\"", "").Trim();
							//        string Type = strArr[4].Replace("\"", "").Trim();
							//        if (Type == "Res")
							//        {
							//            if (ItemNameNet.Contains("," + net + ","))
							//            {
							//                ICTDataQuery += "EXEC [dbo].[Prc_SPC_02_ICT_NET_DATA_Insert]" + Environment.NewLine;
							//                ICTDataQuery += "@PCSID = " + PCSID + ",@PCSInforID = " + PCSInforID + ",@NetID='" + net + "',@Value=" + netValue + Environment.NewLine;
							//            }
							//        }
							//    }
							//}
							//TOP5 issue
							//if (!string.IsNullOrEmpty(indiNumber))
							//{
							if (NetResult == "1")
							{
								if (strArr.Length > 6)
								{
									string net = strArr[6].Replace("\"", "").Trim();
									if (net.ToUpper().Contains("OPEN"))
									{
										net = "OPEN";
									}
									else
									{
										net = "SHORT";
									}
									string netValue = strArr[5].Replace("\"", "").Trim();
									ICT_NG_Save_Query += "EXEC [dbo].[FlexAssy_22_ICT_NG_Save]" + Environment.NewLine;
									ICT_NG_Save_Query += "@ProductID = " + productID + ",@dateTime = '" + dateTime.ToString("yyyy-MM-dd HH:mm:ss.fff") + "',@machineID='" + machineID + "',@result='" + NetResult +
										"',@NGCode='" + net + "',@NGName='" + net + "',@value='" + netValue + "',@ItemName='" + itemName + "',@IndicationNumber='" + indiNumber + "'" + Environment.NewLine;
								}
								//}
							}
						}

					}
					//if (ICTDataQuery != "")
					//{
					//    Singleton_06_SPC.iSPC_02_ICTService.ICT_NET_DataInsert(ICTDataQuery, "SPC_" + factory);
					//}
					if (ICT_NG_Save_Query != "")
					{
						FlexAssy_22_ICT_NG_Save_Query(ICT_NG_Save_Query, "ConnectionStringSMES");
					}
					return 1;
				}
			}
			catch (Exception ex)
			{
				ManageLog.WriteErrorApp("SPC_02_ICT_Net_ProcessReadLog: " + iD_LogFile + Environment.NewLine + fileName + "\n" + ex.Message);
				return -2;
			}
		}
		public int FlexAssy_22_ICT_ReadFileDat_NetNG(string factory, string machineID, int iD_LogFile, string fileName, string fileNameRoot, string connectionStringOptionVCD, string connectionStringOptionTriCuong)
		{
			try
			{
				StreamReader file = new StreamReader(fileName);
				using (var lineReader = new ReadFileText(file))
				{
					IEnumerable<string> lines = lineReader;
					string[] enumerable = lines as string[] ?? lines.ToArray();

					string productID = null;
					DateTime dateTime = DateTime.Now;
					bool isFail = false;
					string operatorID = "";
					string facilityID = "";
					string jigID = "";
					string pCB = "";
					string testProgram = "";
					string result = "";
					decimal PCSID = 0;
					decimal PCSInforID = 0;
					string indiNumber = "";
					string ICTDataQuery = "";
					string ICT_NG_Save_Query = "";
					string failures = "";
					string ItemNameNet = "";
					string itemName = "";
					DateTime ShotTime = DateTime.Now;

					for (int i = 0; i < enumerable.Length; i++)
					{
						string[] strArr = enumerable[i].Split(',');
						if (strArr.Length >= 13)
						{
							int line = i + 1;
							string dateCreateFile = Path.GetFileName(fileName).Substring(Path.GetFileName(fileName).LastIndexOf('_') + 1, 8);
							productID = Regex.Replace(strArr[5].Trim(), @"[^0-9a-zA-Z]+", "");
							if (productID.Length == 12)
							{
								DataTable dt = Singleton_02_FlexAssy.ISource_FlexAssy_SEI_VCDTrace_Service.Tbl2DCodeScan_GetPcsIDWithProductICT(factory, productID, connectionStringOptionVCD);
								if (dt == null)
									break;

								if (dt.Rows.Count > 0)
								{
									productID = dt.Rows[0]["ProductID"].ToString();
								}
								else if (dt.Rows.Count == 0 && factory.Equals("F4"))
								{
									dt = Singleton_02_FlexAssy.ISource_FlexAssy_TCI_Service.Tbl2DCode_GetPcsIDWithProductICT(factory, productID, connectionStringOptionTriCuong);
									if (dt == null)
										break;

									if (dt.Rows.Count > 0)
									{
										productID = dt.Rows[0]["ProductID"].ToString();
									}
								}
							}

							dateTime = Convert.ToDateTime($"{int.Parse(dateCreateFile.Substring(0, 4))}-{dateCreateFile.Substring(4, 2)}-{dateCreateFile.Substring(6, 2)} {strArr[1].Trim().Replace("\"", "")}");
							failures = strArr[2].Trim();
							isFail = failures != "0";
							facilityID = strArr[8].Replace("\"", "").Trim();
							if (factory.ToUpper() == "F3")
							{
								machineID = strArr[8].Replace("\"", "").Trim();
							}
							if (!string.IsNullOrEmpty(facilityID))
							{
								if (factory.ToUpper() == "F4" && facilityID.Length < 7)
								{
									machineID = factory.Equals("F3") ? $"MOPA{facilityID}-3" : $"MOPA{facilityID}";
								}
								facilityID = factory;
							}

							operatorID = strArr[10].Replace("\"", "").Trim();
							jigID = strArr[9].Replace("\"", "").Trim();
							string[] arrPCB = strArr[7].Replace("\"", "").Trim().Split('(');
							pCB = arrPCB[0];
							testProgram = strArr[0].Replace("\"", "").Trim();
							result = failures == "0" ? "Pass" : "Fail";
							indiNumber = strArr[12].Replace("\"", "").Trim();
							itemName = "";
							DataTable dtWorkOrder = Singleton_02_FlexAssy.IFlexAssy_WorkOrderService.FlexAssy_WorkOrder_GetByWorkOrder(indiNumber, factory);
							if (dtWorkOrder?.Rows.Count > 0)
							{
								itemName = dtWorkOrder.Rows[0]["ItemName"] != null ? dtWorkOrder.Rows[0]["ItemName"].ToString() : "";
							}
						}
						else
						{
							strArr = enumerable[i].Trim().Split(',');
							if (strArr.Length > 6)
							{
								string NetResult = strArr[2].Replace("\"", "").Trim();
								//TOP5 issue
								if (NetResult == "1")
								{
									string net = strArr[6].Replace("\"", "").Trim();
									if (net.ToUpper().Contains("OPEN"))
									{
										net = "OPEN";
									}
									else
									{
										net = "SHORT";
									}
									string netValue = strArr[5].Replace("\"", "").Trim();
									ICT_NG_Save_Query += "EXEC [dbo].[FlexAssy_22_ICT_NG_Save]" + Environment.NewLine;
									ICT_NG_Save_Query += "@ProductID = " + productID + ",@dateTime = '" + dateTime.ToString("yyyy-MM-dd HH:mm:ss.fff") + "',@machineID='" + machineID + "',@result='" + NetResult +
										"',@NGCode='" + net + "',@NGName='" + net + "',@value='" + netValue + "',@ItemName='" + itemName + "',@IndicationNumber='" + indiNumber + "'" + Environment.NewLine;

								}
							}
						}

					}
					if (ICT_NG_Save_Query != "")
					{
						FlexAssy_22_ICT_NG_Save_Query(ICT_NG_Save_Query, "ConnectionStringSMES");
					}
					return 1;
				}
			}
			catch (Exception ex)
			{
				ManageLog.WriteErrorApp("ICT_Net_NG_ProcessReadLog: " + iD_LogFile + Environment.NewLine + fileName + "\n" + ex.Message);
				return -2;
			}
		}
		public int getWeek(DateTime date)
		{
			CultureInfo cul = CultureInfo.CurrentCulture;

			// Lấy tuần của năm
			int weekNum = cul.Calendar.GetWeekOfYear(
				date,
				CalendarWeekRule.FirstFourDayWeek,
				DayOfWeek.Monday
			);
			return weekNum;
		}
		public DataTable InitNetDataTable()
		{
			DataTable NetData = new DataTable();
			NetData.Columns.Add("ProductID", typeof(string));
			NetData.Columns.Add("ProgramType", typeof(string));
			NetData.Columns.Add("DateTime", typeof(DateTime));
			NetData.Columns.Add("NetID", typeof(string));
			NetData.Columns.Add("Value", typeof(double));
			NetData.Columns.Add("CreatedDate", typeof(DateTime));
			return NetData;
		}
		public int FlexAssy_22_ICT_ReadFileDat_ExportData(string factory, string machineID, int iD_LogFile, string fileName, string fileNameRoot, string connectionStringOptionVCD, string connectionStringOptionTriCuong)
		{
			try
			{
				StreamReader file = new StreamReader(fileName);
				using (var lineReader = new ReadFileText(file))
				{
					IEnumerable<string> lines = lineReader;
					string[] enumerable = lines as string[] ?? lines.ToArray();

					string productID = null;
					string dateCreateFile = Path.GetFileName(fileName).Substring(Path.GetFileName(fileName).LastIndexOf('_') + 1, 14);
					DateTime dateTime = DateTime.Now;
					bool isFail = false;
					string operatorID = "";
					string facilityID = "";
					string jigID = "";
					string pCB = "";
					string testProgram = "";
					string result = "";
					string indiNumber = "";
					string failures = "";
					string sTableNameNetData = "";
					string ProgramType = "ICT";
					DataTable NetData = InitNetDataTable();
					DateTime ShotTime = DateTime.Now;

					for (int i = 0; i < enumerable.Length; i++)
					{
						string[] strArr = enumerable[i].Split(',');
						if (strArr.Length >= 13)
						{
							int line = i + 1;
							productID = Regex.Replace(strArr[5].Trim(), @"[^0-9a-zA-Z]+", "");
							if (productID.Length == 12)
							{
								DataTable dt = Singleton_02_FlexAssy.ISource_FlexAssy_SEI_VCDTrace_Service.Tbl2DCodeScan_GetPcsIDWithProductICT(factory, productID, connectionStringOptionVCD);
								if (dt == null)
									break;

								if (dt.Rows.Count > 0)
								{
									productID = dt.Rows[0]["ProductID"].ToString();
								}
							}

							if (strArr[1].Trim().Count() >= 15)
							{
								dateTime = Convert.ToDateTime(strArr[1].Trim().Replace("\"", ""));
							}
							else
							{
								dateTime = Convert.ToDateTime($"{int.Parse(dateCreateFile.Substring(0, 4))}-{dateCreateFile.Substring(4, 2)}-{dateCreateFile.Substring(6, 2)} {strArr[1].Trim().Replace("\"", "")}");
							}
							if (NetData.Rows.Count > 0)
							{
								var rs = Singleton_06_SPC.iSPC_02_ICTService.InsertNetDataAsOneRow(NetData, sTableNameNetData, "ICTData_" + factory);
								NetData = InitNetDataTable();
							}

							sTableNameNetData = "ICT_NET_DATA" + $"{dateTime.Year}{dateTime.Month.ToString("D2")}";
							if (!Singleton_06_SPC.iSPC_CommonService.SPC_ICT_CheckTableExist(sTableNameNetData, "ICTData_" + factory))
							{
								Singleton_06_SPC.iSPC_CommonService.ICT_NET_DATA_CreateTableIndex(sTableNameNetData, "ICTData_" + factory);
							}
							failures = strArr[2].Trim();
							isFail = failures != "0";
							facilityID = strArr[8].Replace("\"", "").Trim();
							if (factory.ToUpper() == "F3")
							{
								machineID = strArr[8].Replace("\"", "").Trim();
							}
							if (!string.IsNullOrEmpty(facilityID))
							{
								if (factory.ToUpper() == "F4" && facilityID.Length < 7)
								{
									machineID = factory.Equals("F3") ? $"MOPA{facilityID}-3" : $"MOPA{facilityID}";
								}
								facilityID = factory;
							}

							operatorID = strArr[10].Replace("\"", "").Trim();
							jigID = strArr[9].Replace("\"", "").Trim();
							string[] arrPCB = strArr[7].Replace("\"", "").Trim().Split('(');
							pCB = arrPCB[0];
							testProgram = strArr[0].Replace("\"", "").Trim();
							ProgramType = testProgram.ToUpper().Contains("-OQC") ? "OQC" : testProgram.ToUpper().Contains("-OBA") ? "OBA" : testProgram.ToUpper().Contains("-ORT") ? "ORT"
								: testProgram.ToUpper().Contains("-H4W") ? "H4W" : testProgram.ToUpper().Contains("-RC") ? "RC" : "ICT";
							result = failures == "0" ? "Pass" : "Fail";
							indiNumber = strArr[12].Replace("\"", "").Trim();
						}
						else
						{
							strArr = enumerable[i].Trim().Split(',');
							if (strArr.Length > 6)
							{
								string NetResult = strArr[2].Replace("\"", "").Trim();
								if (failures == "0")
								{
									string net = strArr[6].Replace("\"", "").Trim();
									string netValue = strArr[5].Replace("\"", "").Trim();
									string Type = strArr[4].Replace("\"", "").Trim();
									if (Type == "Res")
									{
										if (!net.Contains("Check") && !netValue.Contains("K") && !netValue.Contains("M"))
										{
											DataRow newRow = NetData.NewRow();
											newRow["ProductID"] = productID;
											newRow["ProgramType"] = ProgramType;
											newRow["DateTime"] = dateTime;
											newRow["NetID"] = net;
											newRow["Value"] = netValue;
											newRow["CreatedDate"] = DateTime.Now;
											NetData.Rows.Add(newRow);

										}
									}
								}
							}
						}
					}
					if (NetData.Rows.Count > 0)
					{
						var rs = Singleton_06_SPC.iSPC_02_ICTService.InsertNetDataAsOneRow(NetData, sTableNameNetData, "ICTData_" + factory);
					}
					return 1;
				}
			}
			catch (Exception ex)
			{
				ManageLog.WriteErrorApp("SPC_02_ICT_Net_ProcessReadLog: " + iD_LogFile + Environment.NewLine + fileName + "\n" + ex.Message);
				return -2;
			}
		}
		public int FlexAssy_22_ICT_ReadFileDat_SPCOld(string factory, string machineID, int iD_LogFile, string fileName, string fileNameRoot, string connectionStringOptionVCD, string connectionStringOptionTriCuong)
		{
			try
			{
				StreamReader file = new StreamReader(fileName);
				using (var lineReader = new ReadFileText(file))
				{
					IEnumerable<string> lines = lineReader;
					string[] enumerable = lines as string[] ?? lines.ToArray();

					string productID = null;
					string dateCreateFile = Path.GetFileName(fileName).Substring(Path.GetFileName(fileName).LastIndexOf('_') + 1, 14);
					DateTime dateTime = DateTime.Now;
					bool isFail = false;
					string operatorID = "";
					string facilityID = "";
					string jigID = "";
					string pCB = "";
					string testProgram = "";
					string result = "";
					decimal PCSID = 0;
					decimal PCSInforID = 0;
					string indiNumber = "";
					string ICTDataQuery = "";
					string ICT_NG_Save_Query = "";
					string failures = "";
					string ItemNameNet = "";
					string itemName = "";
					DateTime ShotTime = DateTime.Now;
					string sTableNamePCSInfo_ = "PCSInfor_" + $"{dateCreateFile.Substring(0, 4)}{dateCreateFile.Substring(4, 2)}";
					string sTableNameNetData = "SPC_02_ICT_NET_DATA" + $"{dateCreateFile.Substring(0, 4)}{dateCreateFile.Substring(4, 2)}";
					if (!Singleton_06_SPC.iSPC_CommonService.SPC_ICT_CheckTableExist(sTableNameNetData, "SPC_" + factory))
					{
						Singleton_06_SPC.iSPC_CommonService.SPC_02_ICT_NET_DATA_CreateTable(sTableNameNetData, "SPC_" + factory);
					}
					if (!Singleton_06_SPC.iSPC_CommonService.SPC_ICT_CheckTableExist(sTableNamePCSInfo_, "SPC_" + factory))
					{
						Singleton_06_SPC.iSPC_CommonService.SPC_ICT_PCSInfo_CreateTable(sTableNamePCSInfo_, "SPC_" + factory);
					}

					for (int i = 0; i < enumerable.Length; i++)
					{
						string[] strArr = enumerable[i].Split(',');
						if (strArr.Length >= 13)
						{
							int line = i + 1;
							productID = Regex.Replace(strArr[5].Trim(), @"[^0-9a-zA-Z]+", "");
							if (productID.Length == 12)
							{
								DataTable dt = Singleton_02_FlexAssy.ISource_FlexAssy_SEI_VCDTrace_Service.Tbl2DCodeScan_GetPcsIDWithProductICT(factory, productID, connectionStringOptionVCD);
								if (dt == null)
									break;

								if (dt.Rows.Count > 0)
								{
									productID = dt.Rows[0]["ProductID"].ToString();
								}
								else if (dt.Rows.Count == 0 && factory.Equals("F4"))
								{
									//dt = Singleton_02_FlexAssy.ISource_FlexAssy_TCI_Service.Tbl2DCode_GetPcsIDWithProductICT(factory, productID, connectionStringOptionTriCuong);
									//if (dt == null)
									//    break;

									//if (dt.Rows.Count > 0)
									//{
									//    productID = dt.Rows[0]["ProductID"].ToString();
									//}
								}
							}

							if (strArr[1].Trim().Count() >= 15)
							{
								dateTime = Convert.ToDateTime(strArr[1].Trim().Replace("\"", ""));
							}
							else
							{
								dateTime = Convert.ToDateTime($"{int.Parse(dateCreateFile.Substring(0, 4))}-{dateCreateFile.Substring(4, 2)}-{dateCreateFile.Substring(6, 2)} {strArr[1].Trim().Replace("\"", "")}");
							}
							failures = strArr[2].Trim();
							isFail = failures != "0";
							facilityID = strArr[8].Replace("\"", "").Trim();
							if (factory.ToUpper() == "F3")
							{
								machineID = strArr[8].Replace("\"", "").Trim();
							}
							if (!string.IsNullOrEmpty(facilityID))
							{
								if (factory.ToUpper() == "F4" && facilityID.Length < 7)
								{
									machineID = factory.Equals("F3") ? $"MOPA{facilityID}-3" : $"MOPA{facilityID}";
								}
								facilityID = factory;
							}

							operatorID = strArr[10].Replace("\"", "").Trim();
							jigID = strArr[9].Replace("\"", "").Trim();
							string[] arrPCB = strArr[7].Replace("\"", "").Trim().Split('(');
							pCB = arrPCB[0];
							testProgram = strArr[0].Replace("\"", "").Trim();
							result = failures == "0" ? "Pass" : "Fail";
							indiNumber = strArr[12].Replace("\"", "").Trim();
							itemName = "";
							DataTable dtWorkOrder = Singleton_02_FlexAssy.IFlexAssy_WorkOrderService.FlexAssy_WorkOrder_GetByWorkOrder(indiNumber, factory);
							if (dtWorkOrder?.Rows.Count > 0)
							{
								itemName = dtWorkOrder.Rows[0]["ItemName"] != null ? dtWorkOrder.Rows[0]["ItemName"].ToString() : "";
							}
							else
							{
								itemName = testProgram.Split('-')[0];
							}
							DataTable dtConfig = Singleton_06_SPC.iSPC_02_ICTService.GetSPC_SystemConfigValue_GetByConfigName("GetNetName", "ICTNET", itemName, "SPC_" + factory);
							if (dtConfig?.Rows.Count > 0)
							{
								ItemNameNet = "," + (dtConfig.Rows[0]["ValueData"] != null ? dtConfig.Rows[0]["ValueData"].ToString() : "") + ",";
							}
							else
							{
								ItemNameNet = "";
							}
							if (failures == "0")
							{
								PCSInforID = Singleton_06_SPC.iSPC_02_ICTService.PCSInfo_DynamicInsert(sTableNamePCSInfo_, productID, dateTime, itemName, machineID, indiNumber, pCB, jigID, "SPC_" + factory);
							}
						}
						else
						{
							strArr = enumerable[i].Trim().Split(',');
							if (strArr.Length > 6)
							{
								string NetResult = strArr[2].Replace("\"", "").Trim();
								if (failures == "0")
								{
									string net = strArr[6].Replace("\"", "").Trim();
									string netValue = strArr[5].Replace("\"", "").Trim();
									string Type = strArr[4].Replace("\"", "").Trim();
									if (Type == "Res")
									{
										if (ItemNameNet.Contains("," + net + ","))
										{
											ICTDataQuery += "EXEC [dbo].[Prc_SPC_02_ICT_NET_DATA_Insert_New2025]" + Environment.NewLine;
											ICTDataQuery += "@PCSInforID = " + PCSInforID + ",@NetID='" + net + "',@Value='" + netValue + "',@TableName='" + sTableNameNetData + "' ,@SPCData_Flag = 1 " + Environment.NewLine;
										}
									}
								}
							}
						}

					}
					if (ICTDataQuery != "")
					{
						Singleton_06_SPC.iSPC_02_ICTService.ICT_NET_DataInsert(ICTDataQuery, "SPC_" + factory);
					}
					return 1;
				}
			}
			catch (Exception ex)
			{
				ManageLog.WriteErrorApp("SPC_02_ICT_Net_ProcessReadLog: " + iD_LogFile + Environment.NewLine + fileName + "\n" + ex.Message);
				return -2;
			}
		}
		public int FlexAssy_22_ICT_NG_Save_Query(string stringQuery, string connectionStringOption)
		{
			return _db.Execute_Modify(stringQuery, null, CommandType.Text, connectionStringOption);
		}
		public int FlexAssy_22_ICT_NG_Save(string productID, DateTime? dateTime, string machineID, string result, string NGCode, string NGName, string value, string ItemName, string IndicationNumber, string connectionStringOption)
		{
			List<SqlParameter> paras = new List<SqlParameter>();
			paras.Add(new SqlParameter("@ProductID", productID));
			paras.Add(new SqlParameter("@dateTime", dateTime));
			paras.Add(new SqlParameter("@machineID", machineID));
			paras.Add(new SqlParameter("@result", result));
			paras.Add(new SqlParameter("@NGCode", NGCode));
			paras.Add(new SqlParameter("@NGName", NGName));
			paras.Add(new SqlParameter("@value", value));
			paras.Add(new SqlParameter("@ItemName", ItemName));
			paras.Add(new SqlParameter("@IndicationNumber", IndicationNumber));
			return _db.Execute_Modify("FlexAssy_22_ICT_NG_Save", paras.ToArray(), CommandType.StoredProcedure, connectionStringOption);
		}
		/// <summary>
		/// tách file log dạng .Dat
		/// </summary>
		/// <param name="factory"></param>
		/// <param name="machineID"></param>
		/// <param name="iD_LogFile"></param>
		/// <param name="fileName"></param>
		/// <param name="PathSaveLog"></param>
		/// <returns></returns>
		public int FlexAssy_22_ICT_SplitFileDat(string factory, string machineID, int iD_LogFile, string fileName, string PathSaveLog)
		{
			try
			{
				string connectionStringOptionVCD = Singleton_03_Common.IDBInfoService.ConnectionStringOption(factory, "SEI-VCDTraceDB");
				string connectionStringOptionTriCuong = Singleton_03_Common.IDBInfoService.ConnectionStringOption(factory, "TCI");
				StreamReader file = new StreamReader(fileName);
				using (var lineReader = new ReadFileText(file))
				{
					IEnumerable<string> lines = lineReader;
					string[] enumerable = lines as string[] ?? lines.ToArray();

					string productID = null;
					string testProgram = "";
					DateTime dateTime = DateTime.Now;
					int CountPcs = 0;
					int StartLine = 0;
					int EndLine = 0;
					string strDateTime = "";

					for (int i = 0; i < enumerable.Length; i++)
					{
						string[] strArr = enumerable[i].Split(',');
						if (strArr.Length >= 13)
						{
							if (CountPcs > 0)
							{
								string PcsFileName = ICT_SplitFile(fileName, productID, strDateTime, enumerable, StartLine, EndLine);
								if (PcsFileName == "")
								{
									return -2;
								}
								else
								{
									int rs = FlexAssy_22_ICT_LogFile_Pcs_Insert(machineID, PcsFileName.Replace(PathSaveLog, ""), factory);
									if (rs == -9)
									{
										return -2;
									}
									rs = FlexAssy_22_OQC_OBA_H4W_ORT_ICT_UpdateLogFilePcs(productID, dateTime, testProgram, rs, factory);
									if (rs == -9)
									{
										return -2;
									}
								}
							}
							CountPcs += 1;
							StartLine = i;
							int line = i + 1;
							string dateCreateFile = Path.GetFileName(fileName).Substring(Path.GetFileName(fileName).LastIndexOf('_') + 1, 8);
							productID = Regex.Replace(strArr[5].Trim(), @"[^0-9a-zA-Z]+", "");
							if (productID.Length == 12)
							{
								DataTable dt = Singleton_02_FlexAssy.ISource_FlexAssy_SEI_VCDTrace_Service.Tbl2DCodeScan_GetPcsIDWithProductICT(factory, productID, connectionStringOptionVCD);
								if (dt == null)
									break;

								if (dt.Rows.Count > 0)
								{
									productID = dt.Rows[0]["ProductID"].ToString();
								}
								else if (dt.Rows.Count == 0 && factory.Equals("F4"))
								{
									dt = Singleton_02_FlexAssy.ISource_FlexAssy_TCI_Service.Tbl2DCode_GetPcsIDWithProductICT(factory, productID, connectionStringOptionTriCuong);
									if (dt == null)
										break;

									if (dt.Rows.Count > 0)
									{
										productID = dt.Rows[0]["ProductID"].ToString();
									}
								}
							}
							testProgram = strArr[0].Replace("\"", "").Trim();
							strDateTime = dateCreateFile + strArr[1].Trim().Replace("\"", "").Replace(":", "");
							dateTime = Convert.ToDateTime($"{int.Parse(dateCreateFile.Substring(0, 4))}-{dateCreateFile.Substring(4, 2)}-{dateCreateFile.Substring(6, 2)} {strArr[1].Trim().Replace("\"", "")}");
						}
						else
						{
							EndLine = i;
						}
					}
					if (CountPcs > 1)
					{
						string PcsFileName = ICT_SplitFile(fileName, productID, strDateTime, enumerable, StartLine, EndLine);
						if (PcsFileName == "")
						{
							return -2;
						}
						else
						{
							int rs = FlexAssy_22_ICT_LogFile_Pcs_Insert(machineID, PcsFileName.Replace(PathSaveLog, ""), factory);
							if (rs == -9)
							{
								return -2;
							}
							rs = FlexAssy_22_OQC_OBA_H4W_ORT_ICT_UpdateLogFilePcs(productID, dateTime, testProgram, rs, factory);
							if (rs == -9)
							{
								return -2;
							}
						}
					}
					return 1;
				}
			}
			catch (Exception ex)
			{
				ManageLog.WriteErrorApp("SPC_02_ICT_Net_ProcessReadLog: " + iD_LogFile + Environment.NewLine + fileName + "\n" + ex.Message);
				return -2;
			}
		}
		public string FlexAssy_22_ICT_SplitFileDatNew(string productID, string fileName, string PathSaveLog)
		{
			try
			{
				string PcsFileName = "";
				string filePath = Common.DownloadFile(fileName, "ICT");
				StreamReader file = new StreamReader(filePath);
				using (var lineReader = new ReadFileText(file))
				{
					IEnumerable<string> lines = lineReader;
					string[] enumerable = lines as string[] ?? lines.ToArray();

					string testProgram = "";
					DateTime dateTime = DateTime.Now;
					int CountPcs = 0;
					int StartLine = 0;
					int EndLine = 0;
					string strDateTime = "";
					string currentProductID = "";

					for (int i = 0; i < enumerable.Length; i++)
					{
						string[] strArr = enumerable[i].Split(',');
						if (strArr.Length >= 13)
						{
							if (currentProductID.Equals(productID) && CountPcs > 0)
							{
								PcsFileName = ICT_SplitFile(filePath, productID, strDateTime, enumerable, StartLine, EndLine);
							}

							CountPcs += 1;
							StartLine = i;
							int line = i + 1;
							currentProductID = Regex.Replace(strArr[5].Trim(), @"[^0-9a-zA-Z]+", "");
							string dateCreateFile = Path.GetFileName(fileName).Substring(Path.GetFileName(fileName).LastIndexOf('_') + 1, 8);
							testProgram = strArr[0].Replace("\"", "").Trim();
							strDateTime = dateCreateFile + strArr[1].Trim().Replace("\"", "").Replace(":", "");
							dateTime = Convert.ToDateTime($"{int.Parse(dateCreateFile.Substring(0, 4))}-{dateCreateFile.Substring(4, 2)}-{dateCreateFile.Substring(6, 2)} {strArr[1].Trim().Replace("\"", "")}");
						}
						else
						{
							EndLine = i;
						}
					}
					if (currentProductID.Equals(productID) && CountPcs > 0)
					{
						PcsFileName = ICT_SplitFile(filePath, productID, strDateTime, enumerable, StartLine, EndLine);
					}
				}

				if (File.Exists(filePath))
				{
					File.Delete(filePath);
				}

				return PcsFileName;

			}
			catch (Exception ex)
			{
				ManageLog.WriteErrorApp("Slip File ICT.DAT from TraceAbility: " + Environment.NewLine + fileName + "\n" + ex.Message);
				return "";
			}
		}
		/// <summary>
		/// tách file log dạng .Csv
		/// </summary>
		/// <param name="factory"></param>
		/// <param name="machineID"></param>
		/// <param name="iD_LogFile"></param>
		/// <param name="fileName"></param>
		/// <param name="PathSaveLog"></param>
		/// <returns></returns>
		public int FlexAssy_22_ICT_SplitFileCSV(string factory, string machineID, int iD_LogFile, string fileName, string fileNameRoot, int LineRead, string PathSaveLog)
		{
			try
			{
				string connectionStringOptionVCD = Singleton_03_Common.IDBInfoService.ConnectionStringOption(factory, "SEI-VCDTraceDB");
				string connectionStringOptionTriCuong = Singleton_03_Common.IDBInfoService.ConnectionStringOption(factory, "TCI");
				StreamReader file = new StreamReader(fileName);
				using (var lineReader = new ReadFileText(file))
				{
					IEnumerable<string> lines = lineReader;
					string[] enumerable = lines as string[] ?? lines.ToArray();
					string productID = null;
					string testProgram = "";
					bool isPcsInfor = false;
					DateTime dateTime = DateTime.Now;
					string strDateTime = "";
					for (int i = LineRead; i < enumerable.Length; i++)
					{
						string[] strArr = enumerable[i].Split(',');
						if (!isPcsInfor)
						{
							if (i >= 7)
							{
								isPcsInfor = true;
							}
						}
						if (isPcsInfor)
						{
							if (strArr.Length >= 12)
							{
								int line = i + 1;

								productID = Regex.Replace(strArr[0].Trim(), @"[^0-9a-zA-Z]+", "");
								if (productID.Length == 12)
								{
									DataTable dt = Singleton_02_FlexAssy.ISource_FlexAssy_SEI_VCDTrace_Service.Tbl2DCodeScan_GetPcsIDWithProductICT(factory, productID, connectionStringOptionVCD);
									if (dt == null)
										break;

									if (dt.Rows.Count > 0)
									{
										productID = dt.Rows[0]["ProductID"].ToString();
									}
									else if (dt.Rows.Count == 0 && factory.Equals("F4"))
									{
										dt = Singleton_02_FlexAssy.ISource_FlexAssy_TCI_Service.Tbl2DCode_GetPcsIDWithProductICT(factory, productID, connectionStringOptionTriCuong);
										if (dt == null)
											break;

										if (dt.Rows.Count > 0)
										{
											productID = dt.Rows[0]["ProductID"].ToString();
										}
									}
								}

								dateTime = Convert.ToDateTime($"{strArr[2]} {strArr[3]}");
								DataTable FixInfor = FlexAssy_22_ICT_Testprogram_History_GetByFileName(fileNameRoot, dateTime, factory);
								if (FixInfor?.Rows.Count > 0)
								{
									machineID = DataConvert.ConvertToString(FixInfor.Rows[0]["MachineID"]);
									testProgram = DataConvert.ConvertToString(FixInfor.Rows[0]["TestProgram"]);
								}
								string PcsFileName = ICT_SplitFile(fileName, productID, strDateTime, enumerable, i, i);
								if (PcsFileName == "")
								{
									return -2;
								}
								else
								{
									int rs = FlexAssy_22_ICT_LogFile_Pcs_Insert(machineID, PcsFileName.Replace(PathSaveLog, ""), factory);
									if (rs == -9)
									{
										return -2;
									}
									rs = FlexAssy_22_OQC_OBA_H4W_ORT_ICT_UpdateLogFilePcs(productID, dateTime, testProgram, rs, factory);
									if (rs == -9)
									{
										return -2;
									}
								}
							}
						}
					}

					return 1;
				}
			}
			catch (Exception ex)
			{
				ManageLog.WriteErrorApp("SPC_02_ICT_Net_ProcessReadLog: " + iD_LogFile + Environment.NewLine + fileName + "\n" + ex.Message);
				return -2;
			}
		}
		public string FlexAssy_22_ICT_SplitFileCSV_New(string productID, string fileName, string PathSaveLog)
		{
			try
			{
				string PcsFileName = "";
				string filePath = Common.DownloadFile(fileName, "ICT");
				StreamReader file = new StreamReader(filePath);
				using (var lineReader = new ReadFileText(file))
				{
					IEnumerable<string> lines = lineReader;
					string[] enumerable = lines as string[] ?? lines.ToArray();
					bool isPcsInfor = false;
					DateTime dateTime = DateTime.Now;
					int StartLine = 0;
					int EndLine = 0;
					string strDateTime = "";
					string currentProductID = "";

					for (int i = 0; i < enumerable.Length; i++)
					{
						string[] strArr = enumerable[i].Split(',');
						if (!isPcsInfor)
						{
							if (i >= 7)
							{
								isPcsInfor = true;
							}
						}
						if (isPcsInfor)
						{
							if (strArr.Length >= 12)
							{
								if (currentProductID.Equals(productID))
								{
									PcsFileName = ICT_SplitFile(filePath, productID, strDateTime, enumerable, StartLine, EndLine);
								}
								int line = i + 1;

								currentProductID = Regex.Replace(strArr[0].Trim(), @"[^0-9a-zA-Z]+", "");
								strDateTime = Path.GetFileName(fileName).Substring(Path.GetFileName(fileName).LastIndexOf('/') + 13, 12);
								StartLine = i;
								EndLine = i;
							}
						}
					}
					if (currentProductID.Equals(productID))
					{
						PcsFileName = ICT_SplitFile(filePath, productID, strDateTime, enumerable, StartLine, EndLine);
					}
				}
				if (File.Exists(filePath))
				{
					File.Delete(filePath);
				}
				return PcsFileName;
			}
			catch (Exception ex)
			{
				ManageLog.WriteErrorApp("SPC_02_ICT_Net_ProcessReadLog: " + Environment.NewLine + fileName + "\n" + ex.Message);
				return "";
			}
		}
		/// <summary>
		/// 
		/// </summary>
		/// <param name="OriFIle"> file gốc</param>
		/// <param name="enumerable">content gốc</param>
		/// <param name="StartLine">dòng bắt đầu cắt</param>
		/// <param name="EndLine">dòng kết thúc</param>
		/// <returns></returns>
		public string ICT_SplitFile(string OriFIle, string ProductID, string Time, string[] enumerable, int StartLine, int EndLine)
		{
			try
			{
				FileInfo OriFileInfor = new FileInfo(OriFIle);
				string NewFilePath = Path.GetDirectoryName(OriFIle) + "\\" + Path.GetFileNameWithoutExtension(OriFIle) + "\\" + ProductID + "_" + Time + OriFileInfor.Extension;
				string Content = "";
				if (OriFIle.ToLower().EndsWith(".dat"))
				{
					//lấy 2 dòng đầu
					Content += enumerable[0];
					Content += Environment.NewLine + enumerable[1];
				}
				else if (OriFIle.ToLower().EndsWith(".csv"))
				{
					//lấy 6 dòng đầu
					Content += enumerable[0];
					Content += Environment.NewLine + enumerable[1];
					Content += Environment.NewLine + enumerable[2];
					Content += Environment.NewLine + enumerable[3];
					Content += Environment.NewLine + enumerable[4];
					Content += Environment.NewLine + enumerable[5];
				}
				for (int i = StartLine; i <= EndLine; i++)
				{
					Content += Environment.NewLine + enumerable[i];
				}
				Common.CreateFile(NewFilePath, Content);
				return NewFilePath;
			}
			catch (Exception ex)
			{
				ManageLog.WriteErrorApp("ICT_SplitFile: " + ex.ToString());
				return "";
			}
		}

		public int FlexAssy_22_ICT_RetestCycle_Insert(string productID, DateTime? dateTime, string machineID, string operatorID, string facilityID, string jigID, string pCB, int? location, string testProgram, string result, long iD_LogFile, int line, string connectionStringOption)
		{
			var paras = new SqlParameter[12];
			paras[0] = new SqlParameter("@ProductID", productID);
			paras[1] = new SqlParameter("@DateTime", dateTime ?? (object)DBNull.Value);
			paras[2] = new SqlParameter("@MachineID", machineID ?? (object)DBNull.Value);
			paras[3] = new SqlParameter("@OperatorID", operatorID ?? (object)DBNull.Value);
			paras[4] = new SqlParameter("@FacilityID", facilityID ?? (object)DBNull.Value);
			paras[5] = new SqlParameter("@JigID", jigID ?? (object)DBNull.Value);
			paras[6] = new SqlParameter("@PCB", pCB ?? (object)DBNull.Value);
			paras[7] = new SqlParameter("@Location", location ?? (object)DBNull.Value);
			paras[8] = new SqlParameter("@TestProgram", testProgram ?? (object)DBNull.Value);
			paras[9] = new SqlParameter("@Result", result ?? (object)DBNull.Value);
			paras[10] = new SqlParameter("@ID_LogFile", iD_LogFile);
			paras[11] = new SqlParameter("@Line", line);

			return _db.Execute_Modify("sp_sms_FlexAssy_22_ICT_RetestCycle_Insert", paras, CommandType.StoredProcedure, connectionStringOption);
		}
		public int FlexAssy_22_OQC_OBA_H4W_ORT_ICT_UpdateLogFilePcs(string productID, DateTime? dateTime, string testProgram, int iD_LogFile, string connectionStringOption)
		{
			var paras = new SqlParameter[4];
			paras[0] = new SqlParameter("@ProductID", productID);
			paras[1] = new SqlParameter("@DateTime", dateTime ?? (object)DBNull.Value);
			paras[2] = new SqlParameter("@TestProgram", testProgram ?? (object)DBNull.Value);
			paras[3] = new SqlParameter("@LogFileID", iD_LogFile);

			return _db.Execute_Modify("sp_sms_FlexAssy_22_OQC_OBA_H4W_ORT_ICT_UpdateLogFilePcs", paras, CommandType.StoredProcedure, connectionStringOption);
		}
		public int FlexAssy_22_ICT_RetestCycle_Save(string productID, DateTime? dateTime, string machineID, string operatorID, string facilityID, string jigID, string pCB, int? location, string testProgram, string result, long iD_LogFile, int line, string connectionStringOption)
		{
			var paras = new SqlParameter[12];
			paras[0] = new SqlParameter("@ProductID", productID);
			paras[1] = new SqlParameter("@DateTime", dateTime ?? (object)DBNull.Value);
			paras[2] = new SqlParameter("@MachineID", machineID ?? (object)DBNull.Value);
			paras[3] = new SqlParameter("@OperatorID", operatorID ?? (object)DBNull.Value);
			paras[4] = new SqlParameter("@FacilityID", facilityID ?? (object)DBNull.Value);
			paras[5] = new SqlParameter("@JigID", jigID ?? (object)DBNull.Value);
			paras[6] = new SqlParameter("@PCB", pCB ?? (object)DBNull.Value);
			paras[7] = new SqlParameter("@Location", location ?? (object)DBNull.Value);
			paras[8] = new SqlParameter("@TestProgram", testProgram ?? (object)DBNull.Value);
			paras[9] = new SqlParameter("@Result", result ?? (object)DBNull.Value);
			paras[10] = new SqlParameter("@ID_LogFile", iD_LogFile);
			paras[11] = new SqlParameter("@Line", line);

			return _db.Execute_Modify("sp_sms_FlexAssy_22_ICT_RetestCycle_Save", paras, CommandType.StoredProcedure, connectionStringOption);
		}
		public int FlexAssy_22_OQC_OBA_ICT_RetestCycle_Save(string productID, DateTime? dateTime, string machineID, string operatorID, string facilityID, string jigID, string pCB, int? location, string testProgram, string result, long iD_LogFile, int line, string connectionStringOption)
		{
			var paras = new SqlParameter[12];
			paras[0] = new SqlParameter("@ProductID", productID);
			paras[1] = new SqlParameter("@DateTime", dateTime ?? (object)DBNull.Value);
			paras[2] = new SqlParameter("@MachineID", machineID ?? (object)DBNull.Value);
			paras[3] = new SqlParameter("@OperatorID", operatorID ?? (object)DBNull.Value);
			paras[4] = new SqlParameter("@FacilityID", facilityID ?? (object)DBNull.Value);
			paras[5] = new SqlParameter("@JigID", jigID ?? (object)DBNull.Value);
			paras[6] = new SqlParameter("@PCB", pCB ?? (object)DBNull.Value);
			paras[7] = new SqlParameter("@Location", location ?? (object)DBNull.Value);
			paras[8] = new SqlParameter("@TestProgram", testProgram ?? (object)DBNull.Value);
			paras[9] = new SqlParameter("@Result", result ?? (object)DBNull.Value);
			paras[10] = new SqlParameter("@ID_LogFile", iD_LogFile);
			paras[11] = new SqlParameter("@Line", line);

			return _db.Execute_Modify("sp_sms_FlexAssy_22_OQC_OBA_ICT_RetestCycle_Save", paras, CommandType.StoredProcedure, connectionStringOption);
		}
		public int FlexAssy_22_OQC_OBA_H4W_ORT_ICT_RetestCycle_Save(string productID, DateTime? dateTime, string machineID, string operatorID, string facilityID, string jigID,
			string pCB, int? location, string testProgram, string result, string MPEResult, long iD_LogFile, int line, string connectionStringOption, string PalletJigID = "")
		{
			var paras = new SqlParameter[14];
			paras[0] = new SqlParameter("@ProductID", productID);
			paras[1] = new SqlParameter("@DateTime", dateTime ?? (object)DBNull.Value);
			paras[2] = new SqlParameter("@MachineID", machineID ?? (object)DBNull.Value);
			paras[3] = new SqlParameter("@OperatorID", operatorID ?? (object)DBNull.Value);
			paras[4] = new SqlParameter("@FacilityID", facilityID ?? (object)DBNull.Value);
			paras[5] = new SqlParameter("@JigID", jigID ?? (object)DBNull.Value);
			paras[6] = new SqlParameter("@PCB", pCB ?? (object)DBNull.Value);
			paras[7] = new SqlParameter("@Location", location ?? (object)DBNull.Value);
			paras[8] = new SqlParameter("@TestProgram", testProgram ?? (object)DBNull.Value);
			paras[9] = new SqlParameter("@Result", result ?? (object)DBNull.Value);
			paras[10] = new SqlParameter("@ID_LogFile", iD_LogFile);
			paras[11] = new SqlParameter("@Line", line);
			paras[12] = new SqlParameter("@MPEResult", MPEResult);
			paras[13] = new SqlParameter("@PalletJigID", PalletJigID);

			return _db.Execute_Modify("sp_sms_FlexAssy_22_OQC_OBA_H4W_ORT_ICT_RetestCycle_Save_2025", paras, CommandType.StoredProcedure, connectionStringOption);
		}
		public int FlexAssy_22_OQC_OBA_H4W_ORT_ICT_RetestCycle_Save_OIMS(string productID, DateTime? dateTime, string machineID, string operatorID, string facilityID, string jigID, string pCB,
			int? location, string testProgram, string result, string MPEResult, long iD_LogFile, int line, string cavityNo, string retestCycleOIMS, string connectionStringOption, string PalletJigID = "")
		{
			var paras = new SqlParameter[16];
			paras[0] = new SqlParameter("@ProductID", productID);
			paras[1] = new SqlParameter("@DateTime", dateTime ?? (object)DBNull.Value);
			paras[2] = new SqlParameter("@MachineID", machineID ?? (object)DBNull.Value);
			paras[3] = new SqlParameter("@OperatorID", operatorID ?? (object)DBNull.Value);
			paras[4] = new SqlParameter("@FacilityID", facilityID ?? (object)DBNull.Value);
			paras[5] = new SqlParameter("@JigID", jigID ?? (object)DBNull.Value);
			paras[6] = new SqlParameter("@PCB", pCB ?? (object)DBNull.Value);
			paras[7] = new SqlParameter("@Location", location ?? (object)DBNull.Value);
			paras[8] = new SqlParameter("@TestProgram", testProgram ?? (object)DBNull.Value);
			paras[9] = new SqlParameter("@Result", result ?? (object)DBNull.Value);
			paras[10] = new SqlParameter("@ID_LogFile", iD_LogFile);
			paras[11] = new SqlParameter("@Line", line);
			paras[12] = new SqlParameter("@CavityNo", cavityNo);
			paras[13] = new SqlParameter("@RetestCycleOIMS", retestCycleOIMS);
			paras[14] = new SqlParameter("@MPEResult", MPEResult);
			paras[15] = new SqlParameter("@PalletJigID", PalletJigID);

			return _db.Execute_Modify("sp_sms_FlexAssy_22_OQC_OBA_H4W_ORT_ICT_RetestCycle_Save_OIMS_2025", paras, CommandType.StoredProcedure, connectionStringOption);
		}
		// Luu du lieu do tinh toan ICT
		public int FlexAssy_22_OQC_OBA_H4W_ORT_ICT_RetestCycle_Save_Calculator(string productID, double max, double min, double average, string testProgram, DateTime? dateTime, string connectionStringOption)
		{
			var paras = new SqlParameter[6];
			paras[0] = new SqlParameter("@ProductID", productID);
			paras[1] = new SqlParameter("@MaxValue", max);
			paras[2] = new SqlParameter("@MinValue", min);
			paras[3] = new SqlParameter("@AverageValue", average);
			paras[4] = new SqlParameter("@TestProgram", testProgram ?? (object)DBNull.Value);
			paras[5] = new SqlParameter("@DateTime", dateTime ?? (object)DBNull.Value);

			return _db.Execute_Modify("sp_sms_FlexAssy_22_OQC_OBA_H4W_ORT_ICT_RetestCycle_Save_Calculator", paras, CommandType.StoredProcedure, connectionStringOption);
		}
		public int FlexAssy_22_ICT_TestTypeError_Insert(string productID, string specFile, DateTime? dateTime, string step, string testType, string testTitle,
			string measuredValue, string lowLimit, string highLimit, long iD_LogFile, int line, string connectionStringOption)
		{
			var paras = new SqlParameter[11];
			paras[0] = new SqlParameter("@ProductID", productID ?? (object)DBNull.Value);
			paras[1] = new SqlParameter("@SpecFile", specFile ?? (object)DBNull.Value);
			paras[2] = new SqlParameter("@DateTime", dateTime ?? (object)DBNull.Value);
			paras[3] = new SqlParameter("@Step", step ?? (object)DBNull.Value);
			paras[4] = new SqlParameter("@TestType", testType ?? (object)DBNull.Value);
			paras[5] = new SqlParameter("@TestTitle", testTitle ?? (object)DBNull.Value);
			paras[6] = new SqlParameter("@MeasuredValue", measuredValue ?? (object)DBNull.Value);
			paras[7] = new SqlParameter("@LowLimit", lowLimit ?? (object)DBNull.Value);
			paras[8] = new SqlParameter("@HighLimit", highLimit ?? (object)DBNull.Value);
			paras[9] = new SqlParameter("@ID_LogFile", iD_LogFile);
			paras[10] = new SqlParameter("@Line", line);

			return _db.Execute_Modify("sp_sms_FlexAssy_22_ICT_TestTypeError_Insert", paras, CommandType.StoredProcedure, connectionStringOption);
		}

		public int FlexAssy_22_ICT_LogFile_Insert(string machineID, string fileNameRoot, string fileNameMedium, int lineRead, string jigID, string logType, string connectionStringOption)
		{
			var paras = new SqlParameter[7];
			paras[0] = new SqlParameter("@Rs", SqlDbType.Int) { Direction = ParameterDirection.Output };
			paras[1] = new SqlParameter("@MachineID", machineID ?? (object)DBNull.Value);
			paras[2] = new SqlParameter("@FileNameRoot", fileNameRoot ?? (object)DBNull.Value);
			paras[3] = new SqlParameter("@FileNameMedium", fileNameMedium ?? (object)DBNull.Value);
			paras[4] = new SqlParameter("@LineRead", lineRead);
			paras[5] = new SqlParameter("@JigID", jigID);
			paras[6] = new SqlParameter("@LogType", logType);

			var rs = _db.Execute_Modify("sp_sms_FlexAssy_22_ICT_LogFile_Insert_19122022", paras, CommandType.StoredProcedure, connectionStringOption);
			if (rs != -9) return (int)paras[0].Value;
			return rs;
		}

		public int FlexAssy_22_ICT_LogFile_Pcs_Insert(string machineID, string FileName, string connectionStringOption)
		{
			var paras = new SqlParameter[3];
			paras[0] = new SqlParameter("@Rs", SqlDbType.Int) { Direction = ParameterDirection.Output };
			paras[1] = new SqlParameter("@MachineID", machineID ?? (object)DBNull.Value);
			paras[2] = new SqlParameter("@FileName", FileName ?? (object)DBNull.Value);
			var rs = _db.Execute_Modify("sp_sms_FlexAssy_22_ICT_LogFile_Pcs_Insert", paras, CommandType.StoredProcedure, connectionStringOption);
			if (rs != -9) return (int)paras[0].Value;
			return rs;
		}

		public int FlexAssy_22_ICT_LogFile_InsertNew(string machineID, string fileNameRoot, string fileNameMedium, int lineRead, string connectionStringOption, int Readtype)
		{
			var paras = new SqlParameter[6];
			paras[0] = new SqlParameter("@Rs", SqlDbType.Int) { Direction = ParameterDirection.Output };
			paras[1] = new SqlParameter("@MachineID", machineID ?? (object)DBNull.Value);
			paras[2] = new SqlParameter("@FileNameRoot", fileNameRoot ?? (object)DBNull.Value);
			paras[3] = new SqlParameter("@FileNameMedium", fileNameMedium ?? (object)DBNull.Value);
			paras[4] = new SqlParameter("@LineRead", lineRead);
			paras[5] = new SqlParameter("@ReadType", Readtype);

			var rs = _db.Execute_Modify("sp_sms_FlexAssy_22_ICT_LogFile_InsertNew", paras, CommandType.StoredProcedure, connectionStringOption);
			if (rs != -9) return (int)paras[0].Value;
			return rs;
		}

		public int FlexAssy_22_ICT_LogFile_UpdateLineRead(int iD, int lineRead, bool isReadOK, string connectionStringOption)
		{
			var paras = new SqlParameter[3];
			paras[0] = new SqlParameter("@ID", iD);
			paras[1] = new SqlParameter("@LineRead", lineRead);
			paras[2] = new SqlParameter("@IsReadOK", isReadOK);

			return _db.Execute_Modify("sp_sms_FlexAssy_22_ICT_LogFile_UpdateLineRead", paras, CommandType.StoredProcedure, connectionStringOption);
		}

		public int FlexAssy_22_ICT_LogFile_UpdateFileNameRoot(int iD, string fileNameRoot, string connectionStringOption)
		{
			var paras = new SqlParameter[2];
			paras[0] = new SqlParameter("@ID", iD);
			paras[1] = new SqlParameter("@FileNameRoot", fileNameRoot);

			return _db.Execute_Modify("sp_sms_FlexAssy_22_ICT_LogFile_UpdateFileNameRoot", paras, CommandType.StoredProcedure, connectionStringOption);
		}
		public int FlexAssy_22_ICT_LogFile_UpdateLineRead_FileName(int iD, int lineRead, bool isReadOK, string fileName, string connectionStringOption)
		{
			var paras = new SqlParameter[4];
			paras[0] = new SqlParameter("@ID", iD);
			paras[1] = new SqlParameter("@LineRead", lineRead);
			paras[2] = new SqlParameter("@IsReadOK", isReadOK);
			paras[3] = new SqlParameter("@FileName", fileName);
			return _db.Execute_Modify("sp_sms_FlexAssy_22_ICT_LogFile_UpdateLineRead_FileName", paras, CommandType.StoredProcedure, connectionStringOption);
		}
		public int FlexAssy_22_ICT_LogFile_UpdateFileName(int iD, string fileName, string connectionStringOption)
		{
			var paras = new SqlParameter[2];
			paras[0] = new SqlParameter("@ID", iD);
			paras[1] = new SqlParameter("@FileName", fileName);

			return _db.Execute_Modify("sp_sms_FlexAssy_22_ICT_LogFile_UpdateFileName", paras, CommandType.StoredProcedure, connectionStringOption);
		}
		public DataTable FlexAssy_22_ICT_LogFile_GetDataWithID(int iD, string connectionStringOption)
		{
			var paras = new SqlParameter[1];
			paras[0] = new SqlParameter("@ID", iD);

			return _db.Execute_Table("sp_sms_FlexAssy_22_ICT_LogFile_GetDataWithID", paras, CommandType.StoredProcedure, connectionStringOption);
		}
		public DataTable FlexAssy_22_ICT_LogFile_GetNotReadLogWithID(int iD, string connectionStringOption)
		{
			var paras = new SqlParameter[1];
			paras[0] = new SqlParameter("@ID", iD);

			return _db.Execute_Table("sp_sms_FlexAssy_22_ICT_LogFile_GetNotReadLogWithID", paras, CommandType.StoredProcedure, connectionStringOption);
		}
		public DataTable FlexAssy_22_ICT_LogFile_GetDataWithIDRange(int FromID, int ToID, string connectionStringOption)
		{
			var paras = new SqlParameter[2];
			paras[0] = new SqlParameter("@FromID", FromID);
			paras[1] = new SqlParameter("@ToID", ToID);

			return _db.Execute_Table("sp_sms_FlexAssy_22_ICT_LogFile_GetDataWithIDRange", paras, CommandType.StoredProcedure, connectionStringOption);
		}
		public DataTable FlexAssy_22_ICT_LogFile_GetDataWithIDRange2(int FromID, int ToID, string connectionStringOption)
		{
			var paras = new SqlParameter[2];
			paras[0] = new SqlParameter("@FromID", FromID);
			paras[1] = new SqlParameter("@ToID", ToID);

			return _db.Execute_Table("sp_sms_FlexAssy_22_ICT_LogFile_GetDataWithIDRange2", paras, CommandType.StoredProcedure, connectionStringOption);
		}
		public DataTable FlexAssy_22_ICT_LogFile_GetDataWithIDRange_MOPA(int FromID, int ToID, string connectionStringOption)
		{
			var paras = new SqlParameter[2];
			paras[0] = new SqlParameter("@FromID", FromID);
			paras[1] = new SqlParameter("@ToID", ToID);

			return _db.Execute_Table("sp_sms_FlexAssy_22_ICT_LogFile_GetDataWithIDRange_MOPA", paras, CommandType.StoredProcedure, connectionStringOption);
		}
		public DataTable FlexAssy_22_ICT_LogFile_GetDataWithIDRange_NotMOPA(int FromID, int ToID, string connectionStringOption)
		{
			var paras = new SqlParameter[2];
			paras[0] = new SqlParameter("@FromID", FromID);
			paras[1] = new SqlParameter("@ToID", ToID);

			return _db.Execute_Table("sp_sms_FlexAssy_22_ICT_LogFile_GetDataWithIDRange_NotMOPA", paras, CommandType.StoredProcedure, connectionStringOption);
		}
		public DataTable FlexAssy_22_ICT_NPI_RetestCycle_GetByProductID(string productID, int retestCycle, string testprogram, int isAccount, string connectionStringOption)
		{
			//retestCycle = retestCycle > 100 ? 100 : retestCycle;

			string program_ICT_Retest = "";
			string program_ICT = "";
			string program_OQC_Retest = "";
			string program_OQC = "";
			var tmpProgram = testprogram.Split('-');

			if (tmpProgram[0].Equals("RETEST")) // hang Retest
			{
				if (tmpProgram[2].Contains("ICT")) //hangf ICT
				{
					program_ICT_Retest = testprogram;
				}
				if (tmpProgram[2].Contains("OQC")) //hangf OQC
				{
					program_OQC_Retest = testprogram;
				}
			}
			else //Xac dinh NPI
			{
				if (tmpProgram[1].Contains("ICT")) //hangf ICT NPI
				{
					program_ICT = testprogram;
				}
				if (tmpProgram[1].Contains("OQC")) //hangf OQC
				{
					program_OQC = testprogram;
				}
			}



			if (testprogram.Contains("ICT"))
			{
				if (testprogram.Contains("RETEST"))
				{
					program_ICT_Retest = testprogram;
					program_ICT = program_ICT_Retest.Replace("RETEST-", "");
					program_OQC_Retest = program_ICT_Retest.Replace("ICT", "OQC");
					program_OQC = program_OQC_Retest.Replace("RETEST-", "");
				}
				else
				{
					program_ICT = testprogram;
					program_ICT_Retest = "RETEST-" + program_ICT;
					program_OQC = program_ICT.Replace("ICT", "OQC");
					program_OQC_Retest = "RETEST-" + program_OQC;
				}
			}
			else
			{
				if (testprogram.Contains("OQC"))
				{
					if (testprogram.Contains("RETEST"))
					{
						program_OQC_Retest = testprogram;
						program_ICT_Retest = program_OQC_Retest.Replace("OQC", "ICT");
						program_ICT = program_ICT_Retest.Replace("RETEST-", "");
						program_OQC = program_OQC_Retest.Replace("RETEST-", "");
					}
					else
					{
						program_OQC = testprogram;
						program_OQC_Retest = "RETEST-" + program_OQC;
						program_ICT = program_OQC.Replace("OQC", "ICT");
						program_ICT_Retest = "RETEST-" + program_ICT;
					}
				}
			}

			var paras = new SqlParameter[6];
			paras[0] = new SqlParameter("@ProductID", productID ?? (object)DBNull.Value);
			paras[1] = new SqlParameter("@RetestCycle", retestCycle);
			paras[2] = new SqlParameter("@program_ICT", program_ICT ?? (object)DBNull.Value);
			paras[3] = new SqlParameter("@program_ICT_retest", program_ICT_Retest ?? (object)DBNull.Value);
			paras[4] = new SqlParameter("@program_OQC", program_OQC ?? (object)DBNull.Value);
			paras[5] = new SqlParameter("@program_OQC_retest", program_OQC_Retest ?? (object)DBNull.Value);


			DataTable dt;
			string procedure = "sp_sms_FlexAssy_22_ICT_NPI_RetestCycle_GetByProductID";



			switch (connectionStringOption)
			{

				//case "F3":
				//    dt = _db.Execute_Table("sp_sms_FlexAssy_22_ICT_RetestCycle_GetByProductID", paras, CommandType.StoredProcedure, connectionStringOption);
				//    break;

				//case "F4":
				//    dt = _db.Execute_Table("sp_sms_FlexAssy_22_ICT_RetestCycle_GetByProductID", paras, CommandType.StoredProcedure, connectionStringOption);
				//    break;

				//default:
				//    dt = _db.Execute_Table("sp_sms_FlexAssy_22_ICT_RetestCycle_GetByProductID", paras, CommandType.StoredProcedure, "F3");
				//    if (dt == null || dt?.Rows.Count == 0)
				//    {
				//        paras = new SqlParameter[2];
				//        paras[0] = new SqlParameter("@ProductID", productID ?? (object)DBNull.Value);
				//        paras[1] = new SqlParameter("@RetestCycle", retestCycle);

				//        dt = _db.Execute_Table("sp_sms_FlexAssy_22_ICT_RetestCycle_GetByProductID", paras, CommandType.StoredProcedure, "F4");
				//    }
				//    break;

				case "F3":
					dt = _db.Execute_Table(procedure, paras, CommandType.StoredProcedure, connectionStringOption);
					break;

				case "F4":
					dt = _db.Execute_Table(procedure, paras, CommandType.StoredProcedure, connectionStringOption);
					break;
				case "F5":
					dt = _db.Execute_Table(procedure, paras, CommandType.StoredProcedure, connectionStringOption);
					break;

				default:
					dt = _db.Execute_Table(procedure, paras, CommandType.StoredProcedure, "F3");
					if (dt == null || dt?.Rows.Count == 0)
					{
						paras = new SqlParameter[2];
						paras[0] = new SqlParameter("@ProductID", productID ?? (object)DBNull.Value);
						paras[1] = new SqlParameter("@RetestCycle", retestCycle);

						dt = _db.Execute_Table(procedure, paras, CommandType.StoredProcedure, "F4");
					}
					break;
			}

			return dt;
		}
		public DataTable FlexAssy_22_ICT_OQC_RetestCycle_GetByProductID(string productID, int retestCycle, string testprogram, int isAccount, string connectionStringOption)
		{
			string program_oqcsampling = "";
			string program_oqcsampling_retest = "";
			if (testprogram.Contains("OQCSAMPLING") && testprogram.Contains("RETEST"))
			{
				program_oqcsampling = testprogram.Replace("RETEST-", "");
				program_oqcsampling_retest = testprogram;
			}
			else
			{
				program_oqcsampling = testprogram;
				program_oqcsampling_retest = "RETEST-" + program_oqcsampling;
			}

			var paras = new SqlParameter[4];
			paras[0] = new SqlParameter("@ProductID", productID ?? (object)DBNull.Value);
			paras[1] = new SqlParameter("@RetestCycle", retestCycle);
			paras[2] = new SqlParameter("@program_oqcsampling", program_oqcsampling ?? (object)DBNull.Value);
			paras[3] = new SqlParameter("@program_oqcsampling_retest", program_oqcsampling_retest ?? (object)DBNull.Value);

			DataTable dt = null;

			//if (isAccount == 1) 

			switch (connectionStringOption)
			{

				//case "F3":
				//    dt = _db.Execute_Table("sp_sms_FlexAssy_22_ICT_RetestCycle_GetByProductID", paras, CommandType.StoredProcedure, connectionStringOption);
				//    break;

				//case "F4":
				//    dt = _db.Execute_Table("sp_sms_FlexAssy_22_ICT_RetestCycle_GetByProductID", paras, CommandType.StoredProcedure, connectionStringOption);
				//    break;

				//default:
				//    dt = _db.Execute_Table("sp_sms_FlexAssy_22_ICT_RetestCycle_GetByProductID", paras, CommandType.StoredProcedure, "F3");
				//    if (dt == null || dt?.Rows.Count == 0)
				//    {
				//        paras = new SqlParameter[2];
				//        paras[0] = new SqlParameter("@ProductID", productID ?? (object)DBNull.Value);
				//        paras[1] = new SqlParameter("@RetestCycle", retestCycle);

				//        dt = _db.Execute_Table("sp_sms_FlexAssy_22_ICT_RetestCycle_GetByProductID", paras, CommandType.StoredProcedure, "F4");
				//    }
				//    break;

				case "F3":
					dt = _db.Execute_Table("sp_sms_FlexAssy_22_ICT_OQC_Program_RetestCycle_GetByProductID", paras, CommandType.StoredProcedure, connectionStringOption);
					break;

				case "F4":
					dt = _db.Execute_Table("sp_sms_FlexAssy_22_ICT_OQC_Program_RetestCycle_GetByProductID", paras, CommandType.StoredProcedure, connectionStringOption);
					break;
				case "F5":
					dt = _db.Execute_Table("sp_sms_FlexAssy_22_ICT_OQC_Program_RetestCycle_GetByProductID", paras, CommandType.StoredProcedure, connectionStringOption);
					break;

				default:
					dt = _db.Execute_Table("sp_sms_FlexAssy_22_ICT_OQC_Program_RetestCycle_GetByProductID", paras, CommandType.StoredProcedure, "F3");
					if (dt == null || dt?.Rows.Count == 0)
					{
						paras = new SqlParameter[2];
						paras[0] = new SqlParameter("@ProductID", productID ?? (object)DBNull.Value);
						paras[1] = new SqlParameter("@RetestCycle", retestCycle);

						dt = _db.Execute_Table("sp_sms_FlexAssy_22_ICT_OQC_Program_RetestCycle_GetByProductID", paras, CommandType.StoredProcedure, "F4");
					}
					break;
			}

			return dt;

		}
		public DataTable FlexAssy_22_ICT_RetestCycle_NoABBRule(string productID, int retestCycle, string connectionStringOption, string isOQC, string isOBA, int isAccount, string npi)
		{
			//retestCycle = retestCycle > 100 ? 100 : retestCycle;

			var paras = new SqlParameter[2];
			paras[0] = new SqlParameter("@ProductID", productID ?? (object)DBNull.Value);
			paras[1] = new SqlParameter("@RetestCycle", retestCycle);

			DataTable dt;
			string procedure = "";
			procedure = "sp_sms_FlexAssy_22_ICT_RetestCycle_GetByProductID";
			switch (connectionStringOption)
			{

				case "F3":
					dt = _db.Execute_Table(procedure, paras, CommandType.StoredProcedure, connectionStringOption);
					break;

				case "F4":
					dt = _db.Execute_Table(procedure, paras, CommandType.StoredProcedure, connectionStringOption);
					break;

				case "F5":
					dt = _db.Execute_Table(procedure, paras, CommandType.StoredProcedure, connectionStringOption);
					break;

				default:
					dt = _db.Execute_Table(procedure, paras, CommandType.StoredProcedure, "F3");
					if (dt == null || dt?.Rows.Count == 0)
					{
						paras = new SqlParameter[2];
						paras[0] = new SqlParameter("@ProductID", productID ?? (object)DBNull.Value);
						paras[1] = new SqlParameter("@RetestCycle", retestCycle);

						dt = _db.Execute_Table(procedure, paras, CommandType.StoredProcedure, "F4");
					}
					break;
			}

			return dt;
		}
		public DataTable FlexAssy_22_ICT_RetestCycle_GetByProductID(string productID, int retestCycle, string connectionStringOption, string isOQC, string isOBA, int isAccount, string npi)
		{
			//retestCycle = retestCycle > 100 ? 100 : retestCycle;

			var paras = new SqlParameter[2];
			paras[0] = new SqlParameter("@ProductID", productID ?? (object)DBNull.Value);
			paras[1] = new SqlParameter("@RetestCycle", retestCycle);

			DataTable dt;
			string procedure = "";

			if (npi == "1")
			{
				procedure = "sp_sms_FlexAssy_22_ICT_RetestCycle_GetByProductID";
			}
			else if (isOBA == "before")
			{
				if (isAccount == 0) procedure = "sp_sms_FlexAssy_22_OBA_ICT_BeforeDefinition_RetestCycle_GetByProductID";
				else procedure = "sp_sms_FlexAssy_22_ICT_RetestCycle_GetByProductID";
			}
			else if (isOBA == "after")
			{
				if (isAccount == 0) procedure = "sp_sms_FlexAssy_22_OBA_ICT_AfterDefinition_RetestCycle_GetByProductID";
				else procedure = "sp_sms_FlexAssy_22_ICT_RetestCycle_GetByProductID";
			}
			else if (isOQC == "1")
			{
				if (isAccount == 1) procedure = "sp_sms_FlexAssy_22_ICT_RetestCycle_GetByProductID";
				else procedure = "sp_sms_FlexAssy_22_ICT_RetestCycle_GetByProductID";
			}
			else
			{
				procedure = "sp_sms_FlexAssy_22_ICT_RetestCycle_GetByProductID";
			}


			switch (connectionStringOption)
			{

				//case "F3":
				//    dt = _db.Execute_Table("sp_sms_FlexAssy_22_ICT_RetestCycle_GetByProductID", paras, CommandType.StoredProcedure, connectionStringOption);
				//    break;

				//case "F4":
				//    dt = _db.Execute_Table("sp_sms_FlexAssy_22_ICT_RetestCycle_GetByProductID", paras, CommandType.StoredProcedure, connectionStringOption);
				//    break;

				//default:
				//    dt = _db.Execute_Table("sp_sms_FlexAssy_22_ICT_RetestCycle_GetByProductID", paras, CommandType.StoredProcedure, "F3");
				//    if (dt == null || dt?.Rows.Count == 0)
				//    {
				//        paras = new SqlParameter[2];
				//        paras[0] = new SqlParameter("@ProductID", productID ?? (object)DBNull.Value);
				//        paras[1] = new SqlParameter("@RetestCycle", retestCycle);

				//        dt = _db.Execute_Table("sp_sms_FlexAssy_22_ICT_RetestCycle_GetByProductID", paras, CommandType.StoredProcedure, "F4");
				//    }
				//    break;

				case "F3":
					dt = _db.Execute_Table(procedure, paras, CommandType.StoredProcedure, connectionStringOption);
					break;

				case "F4":
					dt = _db.Execute_Table(procedure, paras, CommandType.StoredProcedure, connectionStringOption);
					break;

				case "F5":
					dt = _db.Execute_Table(procedure, paras, CommandType.StoredProcedure, connectionStringOption);
					break;
				default:
					dt = _db.Execute_Table(procedure, paras, CommandType.StoredProcedure, "F3");
					if (dt == null || dt?.Rows.Count == 0)
					{
						paras = new SqlParameter[2];
						paras[0] = new SqlParameter("@ProductID", productID ?? (object)DBNull.Value);
						paras[1] = new SqlParameter("@RetestCycle", retestCycle);

						dt = _db.Execute_Table(procedure, paras, CommandType.StoredProcedure, "F4");
					}
					break;
			}

			return dt;
		}
		public DataTable FlexAssy_22_OQC_OBA_ICT_RetestCycle_GetByProductID(string productID, string TestProgram, string connectionStringOption)
		{
			//retestCycle = retestCycle > 100 ? 100 : retestCycle;

			var paras = new SqlParameter[2];
			paras[0] = new SqlParameter("@ProductID", productID ?? (object)DBNull.Value);
			paras[1] = new SqlParameter("@TestProgram", TestProgram);
			DataTable dt;
			switch (connectionStringOption)
			{
				case "F3":
					dt = _db.Execute_Table("sp_sms_FlexAssy_22_OQC_OBA_ICT_RetestCycle_GetByProductID", paras, CommandType.StoredProcedure, connectionStringOption);
					break;

				case "F4":
					dt = _db.Execute_Table("sp_sms_FlexAssy_22_OQC_OBA_ICT_RetestCycle_GetByProductID", paras, CommandType.StoredProcedure, connectionStringOption);
					break;

				case "F5":
					dt = _db.Execute_Table("sp_sms_FlexAssy_22_OQC_OBA_ICT_RetestCycle_GetByProductID", paras, CommandType.StoredProcedure, connectionStringOption);
					break;
				default:
					dt = _db.Execute_Table("sp_sms_FlexAssy_22_OQC_OBA_ICT_RetestCycle_GetByProductID", paras, CommandType.StoredProcedure, "F3");
					if (dt == null || dt?.Rows.Count == 0)
					{
						dt = _db.Execute_Table("sp_sms_FlexAssy_22_OQC_OBA_ICT_RetestCycle_GetByProductID", paras, CommandType.StoredProcedure, "F4");
					}
					break;
			}

			return dt;
		}
		public DataTable FlexAssy_22_OQC_OBA_H4W_ORT_ICT_RetestCycle_GetByProductID(string productID, string TestProgram, string connectionStringOption)
		{
			//retestCycle = retestCycle > 100 ? 100 : retestCycle;

			var paras = new SqlParameter[2];
			paras[0] = new SqlParameter("@ProductID", productID ?? (object)DBNull.Value);
			paras[1] = new SqlParameter("@TestProgram", TestProgram);
			DataTable dt;
			switch (connectionStringOption)
			{
				case "F3":
					//dt = _db.Execute_Table("sp_sms_FlexAssy_22_OQC_OBA_H4W_ORT_ICT_RetestCycle_GetByProductID", paras, CommandType.StoredProcedure, connectionStringOption);
					dt = _db.Execute_Table("sp_sms_FlexAssy_22_OQC_OBA_H4W_ORT_ICT_RetestCycle_GetByProductID_15_10_2021", paras, CommandType.StoredProcedure, connectionStringOption);
					break;

				case "F4":
					dt = _db.Execute_Table("sp_sms_FlexAssy_22_OQC_OBA_H4W_ORT_ICT_RetestCycle_GetByProductID_15_10_2021", paras, CommandType.StoredProcedure, connectionStringOption);
					break;
				case "F5":
					dt = _db.Execute_Table("sp_sms_FlexAssy_22_OQC_OBA_H4W_ORT_ICT_RetestCycle_GetByProductID_15_10_2021", paras, CommandType.StoredProcedure, connectionStringOption);
					break;

				default:
					dt = _db.Execute_Table("sp_sms_FlexAssy_22_OQC_OBA_H4W_ORT_ICT_RetestCycle_GetByProductID_15_10_2021", paras, CommandType.StoredProcedure, "F3");
					if (dt == null || dt?.Rows.Count == 0)
					{
						dt = _db.Execute_Table("sp_sms_FlexAssy_22_OQC_OBA_H4W_ORT_ICT_RetestCycle_GetByProductID_15_10_2021", paras, CommandType.StoredProcedure, "F4");
					}
					break;
			}

			return dt;
		}
		public DataTable FlexAssy_22_ICT_GetByProductID(string productID, string connectionStringOption, out string connectionStringOk)
		{
			var paras = new SqlParameter[1];
			paras[0] = new SqlParameter("@ProductID", productID ?? (object)DBNull.Value);

			string connectionStringDefault = null;
			DataTable dt;
			switch (connectionStringOption)
			{
				case "F3":
					//dt = _db.Execute_Table("sp_sms_FlexAssy_22_ICT_GetByProductID", paras, CommandType.StoredProcedure, connectionStringOption);
					dt = _db.Execute_Table("sp_sms_FlexAssy_22_ICT_GetByProductID_15_10_2021_OIMS", paras, CommandType.StoredProcedure, connectionStringOption);
					break;

				case "F4":
					dt = _db.Execute_Table("sp_sms_FlexAssy_22_ICT_GetByProductID_15_10_2021_OIMS", paras, CommandType.StoredProcedure, connectionStringOption);
					break;

				case "F5":
					dt = _db.Execute_Table("sp_sms_FlexAssy_22_ICT_GetByProductID_15_10_2021", paras, CommandType.StoredProcedure, connectionStringOption);
					break;

				default:
					connectionStringDefault = "F3";
					dt = _db.Execute_Table("sp_sms_FlexAssy_22_ICT_GetByProductID_15_10_2021_OIMS", paras, CommandType.StoredProcedure, "F3");
					if (dt == null || dt?.Rows.Count == 0)
					{
						connectionStringDefault = "F4";
						paras = new SqlParameter[1];
						paras[0] = new SqlParameter("@ProductID", productID ?? (object)DBNull.Value);

						dt = _db.Execute_Table("sp_sms_FlexAssy_22_ICT_GetByProductID_15_10_2021_OIMS", paras, CommandType.StoredProcedure, "F4");
					}
					if (dt == null || dt?.Rows.Count == 0)
					{
						connectionStringDefault = "F5";
						paras = new SqlParameter[1];
						paras[0] = new SqlParameter("@ProductID", productID ?? (object)DBNull.Value);

						dt = _db.Execute_Table("sp_sms_FlexAssy_22_ICT_GetByProductID_15_10_2021", paras, CommandType.StoredProcedure, "F5");
					}
					break;
			}

			connectionStringOk = connectionStringOption ?? (dt?.Rows.Count > 0 ? connectionStringDefault : null);
			return dt;
		}
		public DataTable FlexAssy_22_ICT_Component_GetByProductID(string productID, string connectionStringOption, out string connectionStringOk)
		{
			var paras = new SqlParameter[1];
			paras[0] = new SqlParameter("@ProductID", productID ?? (object)DBNull.Value);

			string connectionStringDefault = null;
			DataTable dt;
			switch (connectionStringOption)
			{

				case "F4":
					dt = _db.Execute_Table("sp_sms_FlexAssy_22_ICT_Component_GetByProductID", paras, CommandType.StoredProcedure, connectionStringOption);
					break;
				default:
					connectionStringDefault = "F4";
					paras = new SqlParameter[1];
					paras[0] = new SqlParameter("@ProductID", productID ?? (object)DBNull.Value);
					dt = _db.Execute_Table("sp_sms_FlexAssy_22_ICT_Component_GetByProductID", paras, CommandType.StoredProcedure, "F4");
					break;
			}

			connectionStringOk = connectionStringOption ?? (dt?.Rows.Count > 0 ? connectionStringDefault : null);
			return dt;
		}
		public DataTable FlexAssy_22_ICT_GetByListProductID(DataTable listProductID, string connectionStringOption, out string connectionStringOk)
		{
			var paras = new SqlParameter[1];
			paras[0] = new SqlParameter("@ListProductID", listProductID ?? (object)DBNull.Value);

			string connectionStringDefault = null;
			DataTable dt;
			switch (connectionStringOption)
			{
				case "F3":
					//dt = _db.Execute_Table("sp_sms_FlexAssy_22_ICT_GetByListProductID", paras, CommandType.StoredProcedure, connectionStringOption);
					dt = _db.Execute_Table("sp_sms_FlexAssy_22_ICT_GetByListProductID_15_10_2021", paras, CommandType.StoredProcedure, connectionStringOption);
					break;
				case "F4":
					dt = _db.Execute_Table("sp_sms_FlexAssy_22_ICT_GetByListProductID_15_10_2021", paras, CommandType.StoredProcedure, connectionStringOption);
					break;
				case "F5":
					dt = _db.Execute_Table("sp_sms_FlexAssy_22_ICT_GetByListProductID_15_10_2021", paras, CommandType.StoredProcedure, connectionStringOption);
					break;

				default:
					connectionStringDefault = "F3";
					dt = _db.Execute_Table("sp_sms_FlexAssy_22_ICT_GetByListProductID_15_10_2021", paras, CommandType.StoredProcedure, "F3");
					if (dt == null || dt?.Rows.Count == 0)
					{
						connectionStringDefault = "F4";
						paras = new SqlParameter[1];
						paras[0] = new SqlParameter("@ListProductID", listProductID ?? (object)DBNull.Value);

						dt = _db.Execute_Table("sp_sms_FlexAssy_22_ICT_GetByListProductID_15_10_2021", paras, CommandType.StoredProcedure, "F4");
					}
					if (dt == null || dt?.Rows.Count == 0)
					{
						connectionStringDefault = "F5";
						paras = new SqlParameter[1];
						paras[0] = new SqlParameter("@ListProductID", listProductID ?? (object)DBNull.Value);

						dt = _db.Execute_Table("sp_sms_FlexAssy_22_ICT_GetByListProductID_15_10_2021", paras, CommandType.StoredProcedure, "F5");
					}
					break;
			}

			connectionStringOk = connectionStringOption ?? (dt?.Rows.Count > 0 ? connectionStringDefault : null);
			return dt;
		}
		public DataTable FlexAssy_22_ICT_NPI_GetByProductID(string productID, string connectionStringOption)
		{
			var paras = new SqlParameter[1];
			paras[0] = new SqlParameter("@ProductID", productID ?? (object)DBNull.Value);

			return _db.Execute_Table("sp_sms_FlexAssy_22_ICT_NPI_GetByProductID", paras, CommandType.StoredProcedure, connectionStringOption);
		}
		public DataTable GetItem_NoABBRule(string factory, string connectionStringOption)
		{
			return _db.Execute_Table("sp_sms_GetItem_NoABBRule", null, CommandType.StoredProcedure, connectionStringOption);
		}
		public DataTable GetItem_ABBRule(string factory, string connectionStringOption)
		{
			return _db.Execute_Table("sp_sms_GetItem_ABBRule", null, CommandType.StoredProcedure, connectionStringOption);
		}

		public DataTable FlexAssy_22_ICT_Sample_GetByProductID(string productID, string connectionStringOption)
		{
			var paras = new SqlParameter[1];
			paras[0] = new SqlParameter("@ProductID", productID ?? (object)DBNull.Value);

			return _db.Execute_Table("sp_sms_FlexAssy_22_ICT_Sample_GetByProductID", paras, CommandType.StoredProcedure, connectionStringOption);
		}

		public DataTable FlexAssy_22_ICT_OQC_GetByProductID(string productID, string connectionStringOption)
		{
			var paras = new SqlParameter[1];
			paras[0] = new SqlParameter("@ProductID", productID ?? (object)DBNull.Value);

			return _db.Execute_Table("sp_sms_FlexAssy_22_ICT_OQC_GetByProductID", paras, CommandType.StoredProcedure, connectionStringOption);
		}

		public DataTable FlexAssy_22_ICT_OBA_GetByProductID(string productID, string connectionStringOption)
		{
			var paras = new SqlParameter[1];
			paras[0] = new SqlParameter("@ProductID", productID ?? (object)DBNull.Value);

			return _db.Execute_Table("sp_sms_FlexAssy_22_ICT_OBA_GetByProductID", paras, CommandType.StoredProcedure, connectionStringOption);
		}

		public int FlexAssy_22_ICT_LogFile_Sample_Insert(DateTime dateTime, string machineID, string itemName, string fileNameRoot, string fileName, string connectionStringOption)
		{
			var paras = new SqlParameter[5];
			paras[0] = new SqlParameter("@DateTime", dateTime);
			paras[1] = new SqlParameter("@MachineID", machineID ?? (object)DBNull.Value);
			paras[2] = new SqlParameter("@ItemName", itemName ?? (object)DBNull.Value);
			paras[3] = new SqlParameter("@FileNameRoot", fileNameRoot ?? (object)DBNull.Value);
			paras[4] = new SqlParameter("@FileName", fileName ?? (object)DBNull.Value);

			return _db.Execute_Modify("sp_sms_FlexAssy_22_ICT_LogFile_Sample_Insert", paras, CommandType.StoredProcedure, connectionStringOption);
		}

		public DataTable FlexAssy_22_ICT_LogFile_Sample_GetDataWithMachineIDAndItemName(string machineID, string itemName, DateTime dateTime, string connectionStringOption)
		{
			var paras = new SqlParameter[3];
			paras[0] = new SqlParameter("@MachineID", machineID ?? (object)DBNull.Value);
			paras[1] = new SqlParameter("@ItemName", itemName ?? (object)DBNull.Value);
			paras[2] = new SqlParameter("@DateTime", dateTime);

			return _db.Execute_Table("sp_sms_FlexAssy_22_ICT_LogFile_Sample_GetDataWithMachineIDAndItemName", paras, CommandType.StoredProcedure, connectionStringOption);
		}

		public DataTable FlexAssy_22_ICT_LogFile_Sample_GetDataWithDateTime(DateTime dateTime, string connectionStringOption)
		{
			var paras = new SqlParameter[1];
			paras[0] = new SqlParameter("@DateTime", dateTime);

			return _db.Execute_Table("sp_sms_FlexAssy_22_ICT_LogFile_Sample_GetDataWithDateTime", paras, CommandType.StoredProcedure, connectionStringOption);
		}
		public DataTable FlexAssy_22_ICT_LogFile_Sample_GetDataWithDateTime_New(DateTime dateTimeStart, DateTime dateTimeEnd, string connectionStringOption)
		{
			var paras = new SqlParameter[2];
			paras[0] = new SqlParameter("@DateTimeStart", dateTimeStart);
			paras[1] = new SqlParameter("@DateTimeEnd", dateTimeEnd);

			return _db.Execute_Table("sp_sms_FlexAssy_22_ICT_LogFile_Sample_GetDataWithDateTime_New", paras, CommandType.StoredProcedure, connectionStringOption);
		}
		public DataTable FlexAssy_22_ICT_Testprogram_History_GetByFileName(string FileName, DateTime dateTime, string connectionStringOption)
		{
			var paras = new SqlParameter[2];
			paras[0] = new SqlParameter("@DateTime", dateTime);
			paras[1] = new SqlParameter("@FileName", FileName);

			return _db.Execute_Table("sp_sms_FlexAssy_22_ICT_Testprogram_History_GetByFileName", paras, CommandType.StoredProcedure, connectionStringOption);
		}
		public DataTable FlexAssy_22_ICT_LogFile_GetByFileNameRoot(string FileNameRoot, string connectionStringOption)
		{
			var paras = new SqlParameter[1];
			paras[0] = new SqlParameter("@FileNameRoot", FileNameRoot);

			return _db.Execute_Table("sp_sms_FlexAssy_22_ICT_LogFile_GetByFileNameRoot", paras, CommandType.StoredProcedure, connectionStringOption);
		}

		// Process Calculator Fisrt Pass Rate _ICT
		public async Task<int> FlexAssy_22_ICT_ProcessData_Calculator_FirstPassRate(string factory, DateTime createdDateSearch, int minutes, string connectionStringOption)
		{
			string stage = "FlexAssy_22_ICT_FirstPassRate_Sync_ErpDate";
			try
			{
				DateTime dateNow = DateTime.Now;
				DateTime timeLast = createdDateSearch;
				int recordNeedSyn = 0;
				int recordSyned = 0;

				DataTable dt = Singleton_02_FlexAssy.ISource_FlexAssy_BoardRegistration_Service.YieldRateFinishHistory(timeLast, minutes, connectionStringOption);

				if (dt == null)
					return -1;

				if (dt.Rows.Count > 0)
				{
					//string connectionStringOptionMachine = Singleton_03_Common.IDBInfoService.ConnectionStringOption(factory, "MachineDB");

					recordNeedSyn = dt.Rows.Count;
					for (int i = 0; i < recordNeedSyn; i++)
					{
						string indiNumber = DataConvert.ConvertToString(dt.Rows[i]["IndicationNumber"]);
						string ItemName = DataConvert.ConvertToString(dt.Rows[i]["ItemName"]);
						// update the time that the record is synchronized
						timeLast = Convert.ToDateTime(dt.Rows[i]["FinishTime"]);

						DataTable dtPcs = FlexAssy_22_ICT_RetestCycle_GetByIndiNumber(indiNumber, factory);
						if (dtPcs?.Rows.Count < 1)
						{
							continue;
						}
						//int rs = FlexAssy_22_ICT_Calculator_FirstPassRate(indiNumber, ItemName, dtPcs, factory);
						//if (rs == -2)
						//{
						//    continue;
						//}
						// count the number of synchronized records
						recordSyned++;
					}

					// Update Value Search
					Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, stage, timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff"));
				}
				else
				{
					timeLast = timeLast.AddMinutes(minutes) > dateNow ? timeLast : timeLast.AddMinutes(minutes);

					// Update ValueSearch
					Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, stage, timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff"));
				}

				ManageLog.WriteProcess(true, null, recordNeedSyn, recordSyned);
			}
			catch (Exception ex)
			{
				ManageLog.WriteErrorApp(stage + "\n" + ex.Message);

				return -1;
			}

			await Task.Delay(500);

			return 1;
		}
		public DataTable FlexAssy_22_ICT_RetestCycle_GetByIndiNumber(string indiNumber, string factory)
		{
			var paras = new SqlParameter[1];
			paras[0] = new SqlParameter("@indiNumber", indiNumber ?? (object)DBNull.Value);
			DataTable dt = null;
			if (factory.ToUpper().Equals("F3"))
			{
				dt = _db.Execute_Table("sp_sms_FlexAssy_22_ICT_RetestCycle_GetByIndiNumber", paras, CommandType.StoredProcedure, "F3");
			}
			if (factory.ToUpper().Equals("F4"))
			{
				dt = _db.Execute_Table("sp_sms_FlexAssy_22_ICT_RetestCycle_GetByIndiNumber", paras, CommandType.StoredProcedure, "F4");
			}
			if (factory.ToUpper().Equals("F5"))
			{
				dt = _db.Execute_Table("sp_sms_FlexAssy_22_ICT_RetestCycle_GetByIndiNumber", paras, CommandType.StoredProcedure, "F5");
			}
			return dt;
		}
		public DataTable ICT_GetFirstPassRateData(string indiNumber, string Connection)
		{
			var paras = new SqlParameter[1];
			paras[0] = new SqlParameter("@indiNumber", indiNumber ?? (object)DBNull.Value);
			DataTable dt = null;
			dt = _db.Execute_Table("sms_tblICT_GetFirstPassRateData", paras, CommandType.StoredProcedure, Connection);
			return dt;
		}
		public DataTable FlexAssy_22_ICT_RetestCycle_GetByIndiNumber_ProgramType(string indiNumber, string ProgramType, string factory)
		{
			var paras = new SqlParameter[2];
			paras[0] = new SqlParameter("@indiNumber", indiNumber ?? (object)DBNull.Value);
			paras[1] = new SqlParameter("@ProgramType", ProgramType ?? (object)DBNull.Value);
			DataTable dt = null;
			dt = _db.Execute_Table("sp_sms_FlexAssy_22_ICT_RetestCycle_GetByIndiNumber_ProgramType", paras, CommandType.StoredProcedure, factory);
			return dt;
		}
		public int FlexAssy_22_ICT_Calculator_FirstPassRate(string indiNumber, string ItemName, DataTable dtPCs, DateTime DateTime, string connectionStringOption)
		{
			try
			{
				if (dtPCs?.Rows.Count > 0)
				{
					double totalPcs = 0;
					double totalOK = 0;
					string Oldproduct = "";
					for (int i = 0; i < dtPCs?.Rows.Count; i++)
					{
						if (Oldproduct == DataConvert.ConvertToString(dtPCs.Rows[i]["ProductID"]).ToUpper())
						{
							Oldproduct = DataConvert.ConvertToString(dtPCs.Rows[i]["ProductID"]).ToUpper();
							continue;
						}
						totalPcs++;
						if ((dtPCs.Rows[i]["Result"].ToString().ToUpper().Trim().Equals("PASS")) || (dtPCs.Rows[i]["Result"].ToString().ToUpper().Trim().Equals("OK")))
						{
							totalOK++;
						}
						Oldproduct = DataConvert.ConvertToString(dtPCs.Rows[i]["ProductID"]).ToUpper();
					}
					double passRate = DataConvert.ConvertToDouble(totalOK / totalPcs) * 100;
					passRate = (double)System.Math.Round(passRate, 2);
					double totalNG = totalPcs - totalOK;
					int rs = FlexAssy_22_ICT_Calculator_FirstPassRate_Insert(indiNumber, ItemName, DateTime, passRate, totalOK, totalNG, totalPcs, connectionStringOption);
					if (rs == -9)
					{
						return -2;
					}
					return 1;
				}
				else
				{
					return -2;
				}
			}
			catch (Exception ex)
			{
				ManageLog.WriteErrorApp("FlexAssy_22_ICT_Calculator_FirstPassRate: " + ex.Message);
				return -2;
			}
		}
		public int FlexAssy_22_ICT_Calculator_FirstPassRate_Insert(string indiNumber, double passRate, string connectionStringOption)
		{
			var paras = new SqlParameter[2];
			paras[0] = new SqlParameter("@IndiNumber", indiNumber);
			paras[1] = new SqlParameter("@PassRate", passRate);

			return _db.Execute_Modify("sp_sms_FlexAssy_22_ICT_FirstPassRate_Insert", paras, CommandType.StoredProcedure, connectionStringOption);
		}
		public int FlexAssy_22_ICT_Calculator_FirstPassRate_Insert(string indiNumber, string ItemName, DateTime DateTime, double passRate, double TotalOK, double TotalNG, double TotalPCS, string connectionStringOption)
		{
			var paras = new SqlParameter[7];
			paras[0] = new SqlParameter("@IndiNumber", indiNumber);
			paras[1] = new SqlParameter("@PassRate", passRate);
			paras[2] = new SqlParameter("@TotalOK", TotalOK);
			paras[3] = new SqlParameter("@TotalNG", TotalNG);
			paras[4] = new SqlParameter("@TotalPCS", TotalPCS);
			paras[5] = new SqlParameter("@ItemName", ItemName);
			paras[6] = new SqlParameter("@DateTime", DateTime);

			return _db.Execute_Modify("sp_sms_FlexAssy_22_ICT_FirstPassRate_Insert", paras, CommandType.StoredProcedure, connectionStringOption);
		}
		public DataTable FlexAssy_22_ICT_FirstPassRate_MPE_GetByIndi(string indiNumber, string connectionStringOption)
		{
			var paras = new SqlParameter[1];
			paras[0] = new SqlParameter("@IndiNumber", indiNumber);

			return _db.Execute_Table("sp_sms_FlexAssy_22_ICT_FirstPassRate_MPE_GetByIndiNumber", paras, CommandType.StoredProcedure, connectionStringOption);
		}
		public DataTable FlexAssy_22_ICT_FirstPassRate_MPE_GetByListIndi(DataTable ListIndi, string connectionStringOption, out string connectionStringOk)
		{
			var paras = new SqlParameter[1];
			paras[0] = new SqlParameter("@ListWorkOrder", ListIndi ?? (object)DBNull.Value);

			string connectionStringDefault = null;
			DataTable dt;
			switch (connectionStringOption)
			{
				case "F3":
					dt = _db.Execute_Table("sp_sms_FlexAssy_22_ICT_FirstPassRate_MPE_GetByListIndiNumber", paras, CommandType.StoredProcedure, connectionStringOption);
					break;
				case "F4":
					dt = _db.Execute_Table("sp_sms_FlexAssy_22_ICT_FirstPassRate_MPE_GetByListIndiNumber", paras, CommandType.StoredProcedure, connectionStringOption);
					break;
				case "F5":
					dt = _db.Execute_Table("sp_sms_FlexAssy_22_ICT_FirstPassRate_MPE_GetByListIndiNumber", paras, CommandType.StoredProcedure, connectionStringOption);
					break;
				default:
					connectionStringDefault = "F3";
					dt = _db.Execute_Table("sp_sms_FlexAssy_22_ICT_FirstPassRate_MPE_GetByListIndiNumber", paras, CommandType.StoredProcedure, "F3");
					if (dt == null || dt?.Rows.Count == 0)
					{
						connectionStringDefault = "F4";
						paras = new SqlParameter[1];
						paras[0] = new SqlParameter("@ListWorkOrder", ListIndi ?? (object)DBNull.Value);
						dt = _db.Execute_Table("sp_sms_FlexAssy_22_ICT_FirstPassRate_MPE_GetByListIndiNumber", paras, CommandType.StoredProcedure, "F4");
					}
					if (dt == null || dt?.Rows.Count == 0)
					{
						connectionStringDefault = "F5";
						paras = new SqlParameter[1];
						paras[0] = new SqlParameter("@ListWorkOrder", ListIndi ?? (object)DBNull.Value);
						dt = _db.Execute_Table("sp_sms_FlexAssy_22_ICT_FirstPassRate_MPE_GetByListIndiNumber", paras, CommandType.StoredProcedure, "F5");
					}
					break;
			}

			connectionStringOk = connectionStringOption ?? (dt?.Rows.Count > 0 ? connectionStringDefault : null);
			return dt;
		}
		public DataTable FlexAssy_22_RC_ICT_GetByProductID(string productID, string connectionStringOption, out string connectionStringOk)
		{
			var paras = new SqlParameter[1];
			paras[0] = new SqlParameter("@ProductID", productID ?? (object)DBNull.Value);

			string connectionStringDefault = null;
			DataTable dt;
			switch (connectionStringOption)
			{
				case "F3":
					dt = _db.Execute_Table("sp_sms_FlexAssy_22_RC_ICT_GetByProductID", paras, CommandType.StoredProcedure, connectionStringOption);
					break;

				case "F4":
					dt = _db.Execute_Table("sp_sms_FlexAssy_22_RC_ICT_GetByProductID", paras, CommandType.StoredProcedure, connectionStringOption);
					break;

				default:
					connectionStringDefault = "F3";
					dt = _db.Execute_Table("sp_sms_FlexAssy_22_RC_ICT_GetByProductID", paras, CommandType.StoredProcedure, "F3");
					if (dt == null || dt?.Rows.Count == 0)
					{
						connectionStringDefault = "F4";
						paras = new SqlParameter[1];
						paras[0] = new SqlParameter("@ProductID", productID ?? (object)DBNull.Value);
						dt = _db.Execute_Table("sp_sms_FlexAssy_22_RC_ICT_GetByProductID", paras, CommandType.StoredProcedure, "F4");
					}
					break;
			}

			connectionStringOk = connectionStringOption ?? (dt?.Rows.Count > 0 ? connectionStringDefault : null);
			return dt;
		}
		public DataTable FlexAssy_22_RC_ICT_GetByListProductID(DataTable listProductID, string connectionStringOption, out string connectionStringOk)
		{
			var paras = new SqlParameter[1];
			paras[0] = new SqlParameter("@ListProductID", listProductID ?? (object)DBNull.Value);

			string connectionStringDefault = null;
			DataTable dt;
			switch (connectionStringOption)
			{
				case "F3":
					dt = _db.Execute_Table("sp_sms_FlexAssy_22_RC_ICT_GetByListProductID", paras, CommandType.StoredProcedure, connectionStringOption);
					break;

				case "F4":
					dt = _db.Execute_Table("sp_sms_FlexAssy_22_RC_ICT_GetByListProductID", paras, CommandType.StoredProcedure, connectionStringOption);
					break;

				default:
					connectionStringDefault = "F3";
					dt = _db.Execute_Table("sp_sms_FlexAssy_22_RC_ICT_GetByListProductID", paras, CommandType.StoredProcedure, "F3");
					if (dt == null || dt?.Rows.Count == 0)
					{
						connectionStringDefault = "F4";
						paras = new SqlParameter[1];
						paras[0] = new SqlParameter("@ListProductID", listProductID ?? (object)DBNull.Value);

						dt = _db.Execute_Table("sp_sms_FlexAssy_22_RC_ICT_GetByListProductID", paras, CommandType.StoredProcedure, "F4");
					}
					break;
			}

			connectionStringOk = connectionStringOption ?? (dt?.Rows.Count > 0 ? connectionStringDefault : null);
			return dt;
		}
	}
}