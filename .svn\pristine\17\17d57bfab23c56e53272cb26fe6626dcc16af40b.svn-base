﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trace_AbilitySystem.Libs.DTOClass.FlexAssyModel
{
    public class FlexAssy_26_QcGate
    {
        public string ProductID { get; set; }
        public DateTime DateTime { get; set; }
        public string MachineID { get; set; }
        public string OperatorID { get; set; }
        public string ProgramName { get; set; }
        public string Result { get; set; }
        public string LaserResult { get; set; }
        public string BlockResult { get; set; }
        public string IctResult { get; set; }
        public string DuplicateResult { get; set; }
        public string CpkResult { get; set; }
        public string FviResult { get; set; }
        public string OqcResult { get; set; }
        public string MpeResult { get; set; }
        public string CodeRuleResult { get; set; }
        public string GradeResult { get; set; }
        public string QrCodePlasma { get; set; }
        public string TrayID { get; set; }
        public DateTime? GoldenSampleCheckingRecord { get; set; }
        public DateTime? MachineMaintenanceDate { get; set; }
        public int? MachineMaintenanceID { get; set; }
        public string ProductionConditionResult { get; set; }
        public int? ProductionConditionID { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? FirstPieceBuyoffControlDate { get; set; }
        public int? FirstPieceBuyoffControlID { get; set; }
        public string HoldingTimeResult { get; set; }
        public string PunchingResult { get; set; }
        public string XrayResult { get; set; }
        public string BadMarkResult { get; set; }
        public string RotationResult { get; set; }
    }
}
