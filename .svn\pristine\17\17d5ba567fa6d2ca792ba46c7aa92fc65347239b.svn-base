﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using Trace_AbilitySystem.Libs.Dataconnect;
using Trace_AbilitySystem.Libs.ITrace_02_FlexAssy_Services;

namespace Trace_AbilitySystem.Libs.Trace_02_FlexAssy_Services
{
    public class Source_FlexAssy_PMTT_TRACE_Service : ISource_FlexAssy_PMTT_TRACE_Service
    {
        private readonly DbExecute _db;

        public Source_FlexAssy_PMTT_TRACE_Service()
        {
            _db = new SqlExecute();
        }

        public DataTable SF_tblParameterBakingGetDataByMachineIDProgramNameDateTime(string MachineID, string ItemName, string GetParamTime, string connectionStringOption)
        {
            var paras = new SqlParameter[3];
            string StoredProcedure = "sms_SF_tblParameterBaking_Info_getDataByMachineID_DateTimeItemName";
            paras[0] = new SqlParameter("@MachineID", MachineID);
            paras[1] = new SqlParameter("@ItemName", ItemName);
            paras[2] = new SqlParameter("@GetParamTime", GetParamTime);
            return _db.Execute_Table(StoredProcedure, paras, CommandType.StoredProcedure, connectionStringOption);
        }

        public DataTable tblParameterBaking_Info_GetByBlock(string sBlockID, string connectionStringOption)
        {
            var paras = new SqlParameter[1];
            string StoredProcedure = "sms_tblParameterBaking_Info_GetByBlock";
            paras[0] = new SqlParameter("BlockID", sBlockID);
            return _db.Execute_Table(StoredProcedure, paras, CommandType.StoredProcedure, connectionStringOption);
        }
        public DataTable master_list_GetDataWithBackingTimeEnd(DateTime backingTimeEnd, int minutes, string connectionStringOption, string factory)
        {
            // Để đảm bảo dữ liệu update vào hệ thống của PMTT chạy sau so với thời gian hiện tại, mỗi lần lấy dữ liệu sẽ lùi lại 2h
            // Ví dụ thời gian hiện tại check ko có dữ liệu nhưng PMTT lại update lên sau

            int revert = -60;
            if (DateTime.Now.AddHours(-2) > backingTimeEnd) revert = 0;
            string strWhere = "BakingTimeEnd > '" + backingTimeEnd.AddMinutes(revert).ToString("yyyy-MM-dd HH:mm:ss.fff") + "' " +
                " AND BakingTimeEnd <= '" + backingTimeEnd.AddMinutes(minutes).ToString("yyyy-MM-dd HH:mm:ss.fff") + "'";
            string sql = null;
            if (factory.Equals("F5"))
            {
                sql = "SELECT BlockID, BakingTimeStart, BakingTimeEnd, BakingMachineID, BakingOperatorID, BakingEndOperatorID, PackingTimeStart, PackingTimeEnd, PackingStartOperatorID, PackingEndOperatorID FROM master_list WHERE " + strWhere + " ORDER BY BakingTimeEnd ASC";
            }
            else
            {
                sql = "SELECT BlockID, BakingTimeStart, BakingTimeEnd, BakingMachineID, BakingOperatorID, BakingEndOperatorID, PackingTimeStart, PackingTimeEnd, PackingStartOperatorID, PackingEndOperatorID, BakingTrayID, ItemName FROM master_list WHERE " + strWhere + " ORDER BY BakingTimeEnd ASC";
            }
            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }
        public DataTable master_list_GetDataByBlockID(string BlockID, string connectionStringOption, string factory)
        {
            // Để đảm bảo dữ liệu update vào hệ thống của PMTT chạy sau so với thời gian hiện tại, mỗi lần lấy dữ liệu sẽ lùi lại 2h
            // Ví dụ thời gian hiện tại check ko có dữ liệu nhưng PMTT lại update lên sau

            string strWhere = "[BlockID] = '" + BlockID + "'";
            string sql = null;
            if (factory.Equals("F5"))
            {
                sql = "SELECT BlockID, BakingTimeStart, BakingTimeEnd, BakingMachineID, BakingOperatorID, BakingEndOperatorID, PackingTimeStart, PackingTimeEnd, PackingStartOperatorID, PackingEndOperatorID FROM master_list WHERE " + strWhere + " ORDER BY BakingTimeEnd ASC";
            }
            else
            {
                sql = "SELECT BlockID, BakingTimeStart, BakingTimeEnd, BakingMachineID, BakingOperatorID, BakingEndOperatorID, PackingTimeStart, PackingTimeEnd, PackingStartOperatorID, PackingEndOperatorID, BakingTrayID, ItemName FROM master_list WHERE " + strWhere + " ORDER BY BakingTimeEnd ASC";
            }
            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }
        public DataTable master_list_GetDataWithBackingTimeEnd2(DateTime backingTimeEnd, int minutes, string connectionStringOption, string factory)
        {
            // Để đảm bảo dữ liệu update vào hệ thống của PMTT chạy sau so với thời gian hiện tại, mỗi lần lấy dữ liệu sẽ lùi lại 2h
            // Ví dụ thời gian hiện tại check ko có dữ liệu nhưng PMTT lại update lên sau

            string strWhere = "BakingTimeEnd > '" + backingTimeEnd.ToString("yyyy-MM-dd HH:mm:ss.fff") + "' AND BakingTimeEnd <= '" + backingTimeEnd.AddMinutes(minutes).ToString("yyyy-MM-dd HH:mm:ss.fff") + "'";
            string sql = null;
            if (factory.Equals("F5"))
            {
                sql = "SELECT BlockID, BakingTimeStart, BakingTimeEnd as DateTime, BakingMachineID as MachineID, ItemName, BakingOperatorID, BakingEndOperatorID, PackingTimeStart, PackingTimeEnd, PackingStartOperatorID, PackingEndOperatorID FROM master_list WHERE " + strWhere + " ORDER BY BakingTimeEnd ASC";
            }
            else
            {
                sql = "SELECT IndicationID, BlockID, BakingTimeStart, BakingTimeEnd as DateTime, BakingMachineID as MachineID, ItemName, BakingOperatorID, BakingEndOperatorID, PackingTimeStart, PackingTimeEnd, PackingStartOperatorID, PackingEndOperatorID, BakingTrayID FROM master_list WHERE " + strWhere + " ORDER BY BakingTimeEnd ASC";
            }
            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }

        public DataTable master_list_GetDataWithPackingTimeEnd(DateTime packingTimeEnd, int minutes, string connectionStringOption, string factory)
        {
            // Để đảm bảo dữ liệu update vào hệ thống của PMTT chạy sau so với thời gian hiện tại, mỗi lần lấy dữ liệu sẽ lùi lại 2h
            // Ví dụ thời gian hiện tại check ko có dữ liệu nhưng PMTT lại update lên sau
            string strWhere = "PackingTimeEnd > '" + packingTimeEnd.AddMinutes(-60).ToString("yyyy-MM-dd HH:mm:ss.fff") + "' AND PackingTimeEnd <= '" + packingTimeEnd.AddMinutes(minutes).ToString("yyyy-MM-dd HH:mm:ss.fff") + "'";
            string sql = null;
            if (factory.Equals("F5"))
            {
                sql = "SELECT IndicationID, BlockID, BakingTimeStart, BakingTimeEnd, BakingMachineID, BakingOperatorID, BakingEndOperatorID, PackingTimeStart, PackingTimeEnd, PackingStartOperatorID, PackingEndOperatorID FROM master_list WHERE " + strWhere + " ORDER BY PackingTimeEnd ASC";
            }
            else
            {
                sql = "SELECT IndicationID, BlockID, BakingTimeStart, BakingTimeEnd, BakingMachineID, BakingOperatorID, BakingEndOperatorID, PackingTimeStart, PackingTimeEnd, PackingStartOperatorID, PackingEndOperatorID, BakingTrayID, ItemName FROM master_list WHERE " + strWhere + " ORDER BY PackingTimeEnd ASC";
            }

            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }
        public DataTable master_list_GetDataWithRayonTimeEnd(DateTime rayonTimeEnd, int minutes, string connectionStringOption)
        {
            // Để đảm bảo dữ liệu update vào hệ thống của PMTT chạy sau so với thời gian hiện tại, mỗi lần lấy dữ liệu sẽ lùi lại 2h
            // Ví dụ thời gian hiện tại check ko có dữ liệu nhưng PMTT lại update lên sau
            string strWhere = " RayonTime > '" + rayonTimeEnd.AddMinutes(-60).ToString("yyyy-MM-dd HH:mm:ss.fff") + "'" +
                " AND RayonTime <= '" + rayonTimeEnd.AddMinutes(minutes).ToString("yyyy-MM-dd HH:mm:ss.fff") + "'";

            string sql = "SELECT BlockID, RayonMachineID, TrayID, RayonTime, RayonOperatorID FROM master_list WHERE " + strWhere + " ORDER BY RayonTime ASC";

            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }
        public DataTable master_list_GetDataWithRayonTimeEnd2(DateTime rayonTimeEnd, int minutes, string connectionStringOption)
        {
            // Để đảm bảo dữ liệu update vào hệ thống của PMTT chạy sau so với thời gian hiện tại, mỗi lần lấy dữ liệu sẽ lùi lại 2h
            // Ví dụ thời gian hiện tại check ko có dữ liệu nhưng PMTT lại update lên sau
            string strWhere = " RayonTime > '" + rayonTimeEnd.ToString("yyyy-MM-dd HH:mm:ss.fff") + "'" +
                " AND RayonTime <= '" + rayonTimeEnd.AddMinutes(minutes).ToString("yyyy-MM-dd HH:mm:ss.fff") + "'";

            string sql = "SELECT BlockID,IndicationID, RayonMachineID, TrayID, RayonTime, RayonOperatorID FROM master_list WHERE " + strWhere + " ORDER BY RayonTime ASC";

            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }
        public DataTable master_list_GetOEEDataWithRayonTimeEnd(DateTime rayonTimeEnd, int minutes, string connectionStringOption)
        {
            // Để đảm bảo dữ liệu update vào hệ thống của PMTT chạy sau so với thời gian hiện tại, mỗi lần lấy dữ liệu sẽ lùi lại 2h
            // Ví dụ thời gian hiện tại check ko có dữ liệu nhưng PMTT lại update lên sau
            string strWhere = " RayonTime > '" + rayonTimeEnd.ToString("yyyy-MM-dd HH:mm:ss.fff") + "'" +
                " AND RayonTime <= '" + rayonTimeEnd.AddMinutes(minutes).ToString("yyyy-MM-dd HH:mm:ss.fff") + "'";

            string sql = "SELECT RayonMachineID as MachineID, IndicationID, RayonTime as DateTime, ItemName FROM master_list WHERE " + strWhere + " ORDER BY RayonTime ASC";

            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }

        public DataTable reflow_info_GetDataWithReflowTimeStart(DateTime reflowTimeStart, int minutes, string connectionStringOption)
        {
            string strWhere = "ReflowTimeStart > '" + reflowTimeStart.ToString("yyyy-MM-dd HH:mm:ss.fff") + "' AND ReflowTimeStart <= '" + reflowTimeStart.AddMinutes(minutes).ToString("yyyy-MM-dd HH:mm:ss.fff") + "'";

            string sql = "SELECT [reflow_info].BlockID, [reflow_info].ReflowMachineID, [reflow_info].ReflowOperatorID, [reflow_info].ReflowTimeStart, " +
                "[reflow_info].LaneID, [reflow_info].ProfileCheckPdfPKID, [reflow_info].ProfileSetupLogPKID," +
                "[reflow_info].PCBSide, [reflow_info].[CoolingStartTime], [reflow_info].[BakingTimeMinute], [reflow_info].[SPIStartTime], " +
                "[master_list].PackingTimeStart, [master_list].PackingTimeEnd FROM reflow_info " +
                "left join [master_list] on [reflow_info].BlockID = master_list.BlockID WHERE " + strWhere + " ORDER BY reflowTimeStart ASC";

            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }

		public DataTable reflow_info_GetDataFromToDate(DateTime fromDate, DateTime toDate, string connectionStringOption)
		{
			string strWhere = "ReflowTimeStart >= '" + fromDate.ToString("yyyy-MM-dd HH:mm:ss.fff") + "' AND ReflowTimeStart < '" + toDate.ToString("yyyy-MM-dd HH:mm:ss.fff") + "'";

			string sql = "SELECT [reflow_info].BlockID, [reflow_info].ReflowMachineID, [reflow_info].ReflowOperatorID, [reflow_info].ReflowTimeStart, " +
				"[reflow_info].LaneID, [reflow_info].ProfileCheckPdfPKID, [reflow_info].ProfileSetupLogPKID," +
				"[reflow_info].PCBSide, [reflow_info].[CoolingStartTime], [reflow_info].[BakingTimeMinute], [reflow_info].[SPIStartTime], " +
				"[master_list].PackingTimeStart, [master_list].PackingTimeEnd FROM reflow_info " +
				"left join [master_list] on [reflow_info].BlockID = master_list.BlockID WHERE " + strWhere + " ORDER BY reflowTimeStart ASC";

			return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
		}

		public DataTable reflow_info_GetOEEDataWithReflowTimeStart(DateTime reflowTimeStart, int minutes, string connectionStringOption)
        {
            string strWhere = "ReflowTimeStart > '" + reflowTimeStart.ToString("yyyy-MM-dd HH:mm:ss.fff") + "' AND ReflowTimeStart <= '" + reflowTimeStart.AddMinutes(minutes).ToString("yyyy-MM-dd HH:mm:ss.fff") + "'";

            string sql = "SELECT ReflowMachineID as MachineID, ReflowTimeStart as DateTime, BlockID FROM reflow_info WHERE " + strWhere + " ORDER BY reflowTimeStart ASC";

            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }
        public DataTable Reflow_Profile_Setup_PDF_GetByPkid(int PkidSync, string connectionStringOption)
        {
            string sql = "SELECT TOP (1000) *  FROM [dbo].[Reflow_Profile_Setup_PDF] where Pkid > " + PkidSync + " order by pkid asc";

            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }
        public DataTable reflow_info_GetDataWithReflowTimeStartAndMachineID(DateTime reflowTimeStart, string MachineID, int minutes, string connectionStringOption)
        {
            string strWhere = "ReflowTimeStart > '" + reflowTimeStart.ToString("yyyy-MM-dd HH:mm:ss.fff") + "' AND ReflowTimeStart <= '" + reflowTimeStart.AddMinutes(minutes).ToString("yyyy-MM-dd HH:mm:ss.fff") + "' AND ReflowMachineID = '" + MachineID + "'";

            string sql = "SELECT BlockID, ReflowMachineID, ReflowOperatorID, ReflowTimeStart, LaneID, ProfileCheckPdfPKID, ProfileSetupLogPKID,PCBSide FROM reflow_info WHERE " + strWhere + " ORDER BY reflowTimeStart ASC";

            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }
        public DataTable reflow_info_GetDataWithReflowTimeStartAndContainMachineID(DateTime reflowTimeStart, string MachineID, string MachineID2, int minutes, string connectionStringOption)
        {
            string strWhere = "ReflowTimeStart > '" + reflowTimeStart.ToString("yyyy-MM-dd HH:mm:ss.fff") + "' AND ReflowTimeStart <= '" + reflowTimeStart.AddMinutes(minutes).ToString("yyyy-MM-dd HH:mm:ss.fff") + "' AND ReflowMachineID in ('" + MachineID + "','" + MachineID2 + "')";

            string sql = "SELECT BlockID, ReflowMachineID, ReflowOperatorID, ReflowTimeStart, LaneID, ProfileCheckPdfPKID, ProfileSetupLogPKID,PCBSide FROM reflow_info WHERE " + strWhere + " ORDER BY reflowTimeStart ASC";

            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }
        public DataTable reflow_info_GetDataWithReflowTimeStartF5(DateTime reflowTimeStart, int minutes, string connectionStringOption)
        {
            string strWhere = "ReflowTimeStart > '" + reflowTimeStart.ToString("yyyy-MM-dd HH:mm:ss.fff") + "' AND ReflowTimeStart <= '" + reflowTimeStart.AddMinutes(minutes).ToString("yyyy-MM-dd HH:mm:ss.fff") + "'";

            string sql = "SELECT BlockID, ReflowMachineID, ReflowOperatorID, ReflowTimeStart, LaneID, ProfileCheckPdfPKID,ProfileCheckPdfPKID_HCure, ProfileSetupLogPKID,PCBSide,ProgramName, CoolingStartTime FROM reflow_info WHERE " + strWhere + " ORDER BY reflowTimeStart ASC";

            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }
        public DataTable reflow_info_GetDataWithCoolingStartTime(DateTime coolingStartTime, int minutes, string connectionStringOption)
        {
            string strWhere = "CoolingStartTime > '" + coolingStartTime.ToString("yyyy-MM-dd HH:mm:ss.fff") + "' AND CoolingStartTime <= '" + coolingStartTime.AddMinutes(minutes).ToString("yyyy-MM-dd HH:mm:ss.fff") + "'";

            string sql = "SELECT BlockID, CoolingMachineID, CoolingOperatorID, CoolingStartTime, LaneID, ProfileCheckPdfPKID, ProfileSetupLogPKID,PCBSide FROM reflow_info WHERE " + strWhere + " ORDER BY CoolingStartTime ASC";

            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }
        public DataTable reflow_info_GetOEEDataWithCoolingStartTime(DateTime coolingStartTime, int minutes, string connectionStringOption)
        {
            string strWhere = "CoolingStartTime > '" + coolingStartTime.ToString("yyyy-MM-dd HH:mm:ss.fff") + "' AND CoolingStartTime <= '" + coolingStartTime.AddMinutes(minutes).ToString("yyyy-MM-dd HH:mm:ss.fff") + "'";

            string sql = "SELECT BlockID, CoolingMachineID as MachineID, CoolingStartTime as DateTime FROM reflow_info WHERE " + strWhere + " ORDER BY CoolingStartTime ASC";

            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }

        public DataTable Reflow_Profile_Setup_log_GetDataWithPkid_FindProgramName(int pkId, string connectionStringOption)
        {
            string sql = "SELECT TOP(1) ProgramName, Datetime FROM Reflow_Profile_Setup_log WHERE Pkid = " + pkId;
            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }

        public DataTable Reflow_Profile_Setup_PDF_GetDataWithPkid_FindProfile(int pkId, string connectionStringOption)
        {
            string sql = "SELECT TOP(1) TimeTest, LinkFile FROM Reflow_Profile_Setup_PDF WHERE Pkid = " + pkId;
            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }

        //fPCStagingTimeControl > 0, <= 240 : OK else NG
        public DataTable reflow_info_GetDataWithBakingTimeMinute(string blockID, string connectionStringOption)
        {
            string sql = "SELECT TOP(1) BakingTimeMinute FROM reflow_info WHERE BlockID = '" + blockID + "'";// AND BakingTimeMinute < 240 AND PCBSide = 0";
            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }

        // Production Condition
        public DataTable Reflow_Profile_Setup_GetDataWithPkid(int pkID, string connectionStringOption)
        {
            string sql = "SELECT [Pkid],[TimeUpload],[ItemName],[MachineID]," +
                "[Zone1U],[Zone2U],[Zone3U],[Zone4U],[Zone5U],[Zone6U],[Zone7U],[Zone8U],[Zone9U],[Zone10U]," +
                "[Cooling1],[Cooling2]," +
                "[Zone1L],[Zone2L],[Zone3L],[Zone4L],[Zone5L],[Zone6L],[Zone7L],[Zone8L],[Zone9L],[Zone10L]," +
                "[Zone1UB],[Zone2UB],[Zone3UB],[Zone4UB],[Zone5UB],[Zone6UB],[Zone7UB],[Zone8UB],[Zone9UB],[Zone10UB]," +
                "[Cooling1UB],[Cooling2UB]," +
                "[Zone1LB],[Zone2LB],[Zone3LB],[Zone4LB],[Zone5LB],[Zone6LB],[Zone7LB],[Zone8LB],[Zone9LB],[Zone10LB]," +
                "[Interval_Timer],[Converyor_Speed],[O2_Concentration] FROM Reflow_Profile_Setup WHERE Pkid > " + pkID + " ORDER BY pkID ASC";

            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }
        public DataTable Reflow_Profile_Setup_GetAllDataWithPkid(int pkID, string connectionStringOption)
        {
            string sql = "SELECT * FROM Reflow_Profile_Setup WHERE Pkid > " + pkID + " ORDER BY pkID ASC";

            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }
        public DataTable Reflow_Profile_Setup_GetDataWithItemNameAndTimeUpload(string itemName, string machineID, DateTime timeUpload, string connectionStringOption)
        {
            string sql = "SELECT TOP(1) [Pkid],[TimeUpload],[ItemName],[MachineID]," +
                "[Zone1U],[Zone2U],[Zone3U],[Zone4U],[Zone5U],[Zone6U],[Zone7U],[Zone8U],[Zone9U],[Zone10U]," +
                "[Cooling1],[Cooling2]," +
                "[Zone1L],[Zone2L],[Zone3L],[Zone4L],[Zone5L],[Zone6L],[Zone7L],[Zone8L],[Zone9L],[Zone10L]," +
                "[Zone1UB],[Zone2UB],[Zone3UB],[Zone4UB],[Zone5UB],[Zone6UB],[Zone7UB],[Zone8UB],[Zone9UB],[Zone10UB]," +
                "[Cooling1UB],[Cooling2UB]," +
                "[Zone1LB],[Zone2LB],[Zone3LB],[Zone4LB],[Zone5LB],[Zone6LB],[Zone7LB],[Zone8LB],[Zone9LB],[Zone10LB]," +
                "[Interval_Timer],[Converyor_Speed],[O2_Concentration] FROM Reflow_Profile_Setup WHERE ItemName = '" + itemName + "' AND MachineID = '" + machineID + "' AND TimeUpload <= '" + timeUpload + "' ORDER BY pkID DESC";

            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }

        public DataTable Reflow_Profile_Setup_log_GetDataWithPkid(int pkID, string connectionStringOption)
        {
            string sql = "SELECT TOP(1000) [Pkid],[Datetime],[ItemName],[LaneID],[ProgramName],[MachineID]," +
                "[Zone1U],[Zone2U],[Zone3U],[Zone4U],[Zone5U],[Zone6U],[Zone7U],[Zone8U],[Zone9U],[Zone10U]," +
                "[Cooling1],[Cooling2]," +
                "[Zone1L],[Zone2L],[Zone3L],[Zone4L],[Zone5L],[Zone6L],[Zone7L],[Zone8L],[Zone9L],[Zone10L]," +
                "[Zone1UB],[Zone2UB],[Zone3UB],[Zone4UB],[Zone5UB],[Zone6UB],[Zone7UB],[Zone8UB],[Zone9UB],[Zone10UB]," +
                "[Cooling1UB],[Cooling2UB]," +
                "[Zone1LB],[Zone2LB],[Zone3LB],[Zone4LB],[Zone5LB],[Zone6LB],[Zone7LB],[Zone8LB],[Zone9LB],[Zone10LB]," +
                "[Interval_Timer],[Converyor_Speed],[O2_Concentration] FROM Reflow_Profile_Setup_log WHERE PkId > " + pkID + " ORDER BY pkID ASC";

            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }
        public DataTable Reflow_Profile_Setup_log_GetDataWithLastTime(string datetime, string connectionStringOption)
        {
            string sql = "SELECT TOP(1000) [Pkid],[Datetime],[ItemName],[LaneID],[ProgramName],[MachineID]," +
                "[Zone1U],[Zone2U],[Zone3U],[Zone4U],[Zone5U],[Zone6U],[Zone7U],[Zone8U],[Zone9U],[Zone10U]," +
                "[Cooling1],[Cooling2]," +
                "[Zone1L],[Zone2L],[Zone3L],[Zone4L],[Zone5L],[Zone6L],[Zone7L],[Zone8L],[Zone9L],[Zone10L]," +
                "[Zone1UB],[Zone2UB],[Zone3UB],[Zone4UB],[Zone5UB],[Zone6UB],[Zone7UB],[Zone8UB],[Zone9UB],[Zone10UB]," +
                "[Cooling1UB],[Cooling2UB]," +
                "[Zone1LB],[Zone2LB],[Zone3LB],[Zone4LB],[Zone5LB],[Zone6LB],[Zone7LB],[Zone8LB],[Zone9LB],[Zone10LB]," +
                "[Interval_Timer],[Converyor_Speed],[O2_Concentration] FROM Reflow_Profile_Setup_log WHERE Datetime > '" + datetime + "' ORDER BY Datetime ASC";

            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }
        public DataTable GradeCheckMain_GetDataWithCheckTime(DateTime checkTime, int minutes, string connectionStringOption)
        {
            // Để đảm bảo dữ liệu update vào hệ thống của PMTT chạy sau so với thời gian hiện tại, mỗi lần lấy dữ liệu sẽ lùi lại 2h
            // Ví dụ thời gian hiện tại check ko có dữ liệu nhưng PMTT lại update lên sau
            string strWhere = " CheckTime > '" + checkTime.ToString("yyyy-MM-dd HH:mm:ss.fff") + "'" +
                " AND CheckTime <= '" + checkTime.AddMinutes(minutes).ToString("yyyy-MM-dd HH:mm:ss.fff") + "'";

            string sql = "SELECT [CheckTime]      ,[Location]     " +
                " ,[QRCode]      ,[GradeLevel]      ,[GradeResult]      ,[ItemName]      ,[IndicationNumber] " +
                "     ,[Operator]      ,[MachineID]      ,[ProductType]      ,[Comment]  FROM[dbo].[GradeCheckMain]  WHERE " + strWhere + " ORDER BY CheckTime ASC";

            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }
        public DataTable GradeCheckMain_GetDataWithCheckTime2(DateTime checkTime, int minutes, string connectionStringOption)
        {
            // Để đảm bảo dữ liệu update vào hệ thống của PMTT chạy sau so với thời gian hiện tại, mỗi lần lấy dữ liệu sẽ lùi lại 2h
            // Ví dụ thời gian hiện tại check ko có dữ liệu nhưng PMTT lại update lên sau
            string strWhere = " CheckTime > '" + checkTime.ToString("yyyy-MM-dd HH:mm:ss.fff") + "' AND CheckTime <= '" + checkTime.AddMinutes(minutes).ToString("yyyy-MM-dd HH:mm:ss.fff") + "'";

            string sql = "SELECT [CheckTime] as DateTime      ,[Location]     " +
                " ,[QRCode] as ProductID      ,[GradeLevel]      ,[GradeResult]      ,[ItemName]      ,[IndicationNumber] " +
                "     ,[Operator]      ,[MachineID]      ,[ProductType]      ,[Comment]  FROM[dbo].[GradeCheckMain]  WHERE " + strWhere + " ORDER BY CheckTime ASC";

            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }
        public DataTable GradeCheckMain_GetDataByProduction(DateTime checkTime, int minutes, string connectionStringOption)
        {
            // Để đảm bảo dữ liệu update vào hệ thống của PMTT chạy sau so với thời gian hiện tại, mỗi lần lấy dữ liệu sẽ lùi lại 2h
            // Ví dụ thời gian hiện tại check ko có dữ liệu nhưng PMTT lại update lên sau
            string strWhere = " CheckTime > '" + checkTime.AddMinutes(-30).ToString("yyyy-MM-dd HH:mm:ss.fff") + "'" +
                " AND CheckTime <= '" + checkTime.AddMinutes(minutes).ToString("yyyy-MM-dd HH:mm:ss.fff") + "'";

            string sql = "SELECT [CheckTime]      ,[Location]     " +
                " ,[QRCode]      ,[GradeLevel]      ,[GradeResult]      ,[ItemName]      ,[IndicationNumber] " +
                "     ,[Operator]      ,[MachineID]      ,[ProductType]      ,[Comment]  FROM[dbo].[GradeCheckMain]  WHERE " + strWhere + " ORDER BY CheckTime ASC";

            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }
        public DataTable AutoTransfer_DetailData_GetDataWithCreatedDate(DateTime dateNow, DateTime createdDate, int minutes, string connectionStringOption)
        {
            var dateRealTime = createdDate.AddMinutes(minutes);
            if (dateRealTime > dateNow)
            {
                dateRealTime = dateNow;
            }
            string strWhere = "CreatedDate > '" + createdDate.ToString("yyyy-MM-dd HH:mm:ss.fff") + "' AND CreatedDate <= '" + dateRealTime.ToString("yyyy-MM-dd HH:mm:ss.fff") + "'";
            //string strWhere = "ProductID='FNJ0414QE2NMTQ4AN'";
            string sql = "SELECT [AutoTransferID] ,[ProductID] ,[Location] ,[ResultICT] ,[PickUp] ,[PickUpResult] ,[CreatedDate] FROM [AutoTransfer_DetailData] WHERE " + strWhere + " ORDER BY CreatedDate ASC";

            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }
        public DataTable AutoTransfer_DetailData_GetOEEDataWithCreatedDate(DateTime dateNow, DateTime createdDate, int minutes, string connectionStringOption)
        {
            string strWhere = "AutoTransfer_Data.CreatedDate > '" + createdDate.ToString("yyyy-MM-dd HH:mm:ss.fff") + "' AND AutoTransfer_Data.CreatedDate <= '" + createdDate.AddMinutes(minutes).ToString("yyyy-MM-dd HH:mm:ss.fff") + "'";
            //string strWhere = "ProductID='FNJ0414QE2NMTQ4AN'";
            string sql = "SELECT top(10000) AutoTransfer_Data.* ,[AutoTransfer_Data].[CreatedDate]  as DateTime FROM AutoTransfer_Data" +
            " WHERE " + strWhere + " ORDER BY AutoTransfer_Data.CreatedDate ASC";

            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }

        public DataTable AutoTransfer_Data_GetDataWithAutoTransferID(long autoTransferID, string connectionStringOption)
        {
            string sql = "SELECT TOP (1) [AutoTransferID] ,[MachineID] ,[OperatorID] ,[ItemName] ,[IndicationNumber] ,[PalletID] ,[LogFileName] ,[CreatedDate] FROM [AutoTransfer_Data] WHERE AutoTransferID = " + autoTransferID;

            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }
        public DataTable GradeCheckMain_GetByListProduct(DataTable ListProductID, string connectionStringOption)
        {
            var paras = new SqlParameter[1];
            paras[0] = new SqlParameter("@ListProductID", ListProductID);
            return _db.Execute_Table("sp_GradeCheckMain_GetByListProduct", paras, CommandType.StoredProcedure, connectionStringOption);
        }
    }
}