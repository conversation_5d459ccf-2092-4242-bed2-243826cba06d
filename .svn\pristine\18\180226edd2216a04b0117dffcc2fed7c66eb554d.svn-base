﻿using System.Web.Optimization;

namespace Trace_AbilitySystem
{
    public class BundleConfig
    {
        // For more information on bundling, visit https://go.microsoft.com/fwlink/?LinkId=301862
        public static void RegisterBundles(BundleCollection bundles)
        {
            BundleTable.EnableOptimizations = false;

            bundles.Add(new ScriptBundle("~/bundles/jquery").Include(
                    "~/Scripts/jquery-3.4.1.min.js",
                    "~/Scripts/bootstrap.bundle.min.js",
                    "~/Scripts/bootstrap-datepicker.min.js",
                    "~/Scripts/jquery.datetimepicker"

                    //"~/Scripts/docsearch.min.js",
                    //"~/Scripts/docs.min.js",
                    //"~/Scripts/pagination.min.js",
                    //"~/Scripts/main.js"
            ));

            bundles.Add(new ScriptBundle("~/bundles/jquery-main").Include(
                    "~/Scripts/pagination.min.js",
                    "~/Scripts/main.js"
            ));

            bundles.Add(new ScriptBundle("~/bundles/jquery-other").Include(
                    "~/Scripts/pagination.min.js",
                    "~/Scripts/other.js"
            ));
            bundles.Add(new ScriptBundle("~/bundles/jquery-pismo").Include(
                    "~/Scripts/pagination.min.js",
                    "~/Scripts/pismo.js"
            ));

            bundles.Add(new StyleBundle("~/bundles/css").Include(
                      "~/Content/styles/bootstrap.min.css",
                      "~/Content/styles/docsearch.min.css",
                      "~/Content/styles/docs.min.css",
                      "~/Content/styles/bootstrap-datepicker3.min.css",
                      "~/Content/styles/pagination.css",
                      "~/Content/styles/jquery.datetimepicker.css", 
                      "~/Content/styles/css.css"
                      ));
            bundles.Add(new ScriptBundle("~/bundles/management").Include(
                      "~/Scripts/management.js"
            ));
        }
    }
}