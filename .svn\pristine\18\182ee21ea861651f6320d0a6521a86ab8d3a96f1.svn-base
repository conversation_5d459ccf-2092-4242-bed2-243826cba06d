﻿using System;
using System.Data;

namespace Trace_AbilitySystem.Libs.ITrace_01_BareFlex_Services
{
    public interface IBareFlex_07_4_6_EtchingRRService
    {
        int BareFlex_07_4_6_EtchingRR_Insert(string workOrder, DateTime dateTime, string machineID, string operatorID, DateTime? machineMaintenanceDate, int? machineMaintenanceID);
        DataTable BareFlex_07_4_6_EtchingRR_GetByWorkOrder(string workOrder);
        DataTable BareFlex_07_4_6_EtchingRR_GetByListWorkOrder(DataTable listWorkOrder);
    }
}