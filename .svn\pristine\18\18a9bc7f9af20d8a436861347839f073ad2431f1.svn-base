﻿@using System.Configuration;
@model Trace_AbilitySystem.Libs.DTOClass.DataAllTrace


@if (Model != null && Model.DataBareFlex.BareFlex_Process_Block?.Rows.Count > 0)
{
    for (int i = 0; i < Model.DataBareFlex.BareFlex_Process_Block.Rows.Count; i++)
    {
        string linkUrlParam = "";
        if (Model.DataBareFlex.BareFlex_Process_Block.Rows[i]["WCSID"] != DBNull.Value)
        {
            linkUrlParam = $"{Model.DataBareFlex.BareFlex_Process_Block.Rows[i]["WCSID"]}";
        }

        <h2 class="bd-title" id="1-board-cutting">
            @Model.DataBareFlex.BareFlex_Process_Block.Rows[i]["Seq_No"] - @Model.DataBareFlex.BareFlex_Process_Block.Rows[i]["Work_Name"]
        </h2>
        <table class="table table-bordered">
            <thead>
                <tr>
                    <th scope="col">Control Item</th>
                    <th scope="col">Control Value</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>WorkOrder</td>
                    <td>@Model.DataBareFlex.BareFlex_Process_Block.Rows[i]["WorkOrder"]</td>
                </tr>
                <tr>
                    <td>DateTime</td>
                    <td>@Model.DataBareFlex.BareFlex_Process_Block.Rows[i]["Input_date"]</td>
                </tr>
                <tr>
                    <td>MachineID</td>
                    <td>@Model.DataBareFlex.BareFlex_Process_Block.Rows[i]["MachineNo"]</td>
                </tr>
                <tr>
                    <td>Operator ID</td>
                    <td>@Model.DataBareFlex.BareFlex_Process_Block.Rows[i]["Input_by"]</td>
                </tr>
                <tr>
                    <td>
                        WCS ID linkage
                    </td>
                    <td>
                        @if (!string.IsNullOrEmpty(linkUrlParam))
                        {
                            string[] WCSIDArr = (linkUrlParam).Trim().Split(';');
                            foreach (string Link in WCSIDArr)
                            {
                                string _linkUrlParam = $"{ConfigurationManager.AppSettings["LinkUrlParam"]}{Link}";
                                <a target="_blank" style="display:block" href="@_linkUrlParam">@_linkUrlParam</a>
                            }
                        }
                    </td>
                </tr>
            </tbody>
        </table>
    }
}