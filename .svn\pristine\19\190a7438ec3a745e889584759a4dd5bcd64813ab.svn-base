﻿using System;
using System.Data;
using System.Data.SqlClient;
using Trace_AbilitySystem.Libs.Dataconnect;
using Trace_AbilitySystem.Libs.ITrace_03_Common_Services;

namespace Trace_AbilitySystem.Libs.Trace_03_Common_Services
{
    public class LogPathService : ILogPathService
    {
        private readonly DbExecute _db;
        private readonly string connectionStringOption = "Common";

        public LogPathService()
        {
            _db = new SqlExecute();
        }

        public int LogPath_Insert(string factory, string stage, int? line, string machineID, string machineName, string logAddress, string logBadMarkAOI_F4, string logNGImageAddress,
            string userName, string password, string dbServer, string dbUser, string dbPassword, DateTime? lastTime, int? isActive)
        {
            var paras = new SqlParameter[15];
            paras[0] = new SqlParameter("@Factory", factory ?? (object)DBNull.Value);
            paras[1] = new SqlParameter("@Stage", stage ?? (object)DBNull.Value);
            paras[2] = new SqlParameter("@Line", line ?? (object)DBNull.Value);
            paras[3] = new SqlParameter("@MachineID", machineID ?? (object)DBNull.Value);
            paras[4] = new SqlParameter("@MachineName", machineName ?? (object)DBNull.Value);
            paras[5] = new SqlParameter("@LogAddress", logAddress ?? (object)DBNull.Value);
            paras[6] = new SqlParameter("@LogBadMarkAOI_F4", logBadMarkAOI_F4 ?? (object)DBNull.Value);
            paras[7] = new SqlParameter("@LogNGImageAddress", logNGImageAddress ?? (object)DBNull.Value);
            paras[8] = new SqlParameter("@Username", userName ?? (object)DBNull.Value);
            paras[9] = new SqlParameter("@Password", password ?? (object)DBNull.Value);
            paras[10] = new SqlParameter("@DBServer", dbServer ?? (object)DBNull.Value);
            paras[11] = new SqlParameter("@DBUser", dbUser ?? (object)DBNull.Value);
            paras[12] = new SqlParameter("@DBPassword", dbPassword ?? (object)DBNull.Value);
            paras[13] = new SqlParameter("@LastTime", lastTime ?? (object)DBNull.Value);
            paras[14] = new SqlParameter("@IsActive", isActive ?? (object)DBNull.Value);

            return _db.Execute_Modify("sp_sms_LogPath_Insert", paras, CommandType.StoredProcedure, connectionStringOption);
        }

        public int LogPath_Insert_WithDB(int iD_PathLog, string factory, string stage, int? line, string machineID, string machineName, string logAddress, int? isActive)
        {
            var paras = new SqlParameter[8];
            paras[0] = new SqlParameter("@ID_PathLog", iD_PathLog);
            paras[1] = new SqlParameter("@Factory", factory ?? (object)DBNull.Value);
            paras[2] = new SqlParameter("@Stage", stage ?? (object)DBNull.Value);
            paras[3] = new SqlParameter("@Line", line ?? (object)DBNull.Value);
            paras[4] = new SqlParameter("@MachineID", machineID ?? (object)DBNull.Value);
            paras[5] = new SqlParameter("@MachineName", machineName ?? (object)DBNull.Value);
            paras[6] = new SqlParameter("@LogAddress", logAddress ?? (object)DBNull.Value);
            paras[7] = new SqlParameter("@IsActive", isActive ?? (object)DBNull.Value);

            return _db.Execute_Modify("sp_sms_LogPath_Insert_WithDB", paras, CommandType.StoredProcedure, connectionStringOption);
        }
        public int LogPath_Save_WithDB(int iD_PathLog, string factory, string stage, int? line, string machineID, string machineName, string logAddress, int? isActive, string LogType,string jigID)
        {
            var paras = new SqlParameter[10];
            paras[0] = new SqlParameter("@ID_PathLog", iD_PathLog);
            paras[1] = new SqlParameter("@Factory", factory ?? (object)DBNull.Value);
            paras[2] = new SqlParameter("@Stage", stage ?? (object)DBNull.Value);
            paras[3] = new SqlParameter("@Line", line ?? (object)DBNull.Value);
            paras[4] = new SqlParameter("@MachineID", machineID ?? (object)DBNull.Value);
            paras[5] = new SqlParameter("@MachineName", machineName ?? (object)DBNull.Value);
            paras[6] = new SqlParameter("@LogAddress", logAddress ?? (object)DBNull.Value);
            paras[7] = new SqlParameter("@IsActive", isActive ?? (object)DBNull.Value);
            paras[8] = new SqlParameter("@LogType", LogType ?? (object)DBNull.Value);
            paras[9] = new SqlParameter("@JigID", jigID ?? (object)DBNull.Value);

            return _db.Execute_Modify("sp_sms_LogPath_Save_WithDB_20122022", paras, CommandType.StoredProcedure, connectionStringOption);
        }

        public int LogPath_Save_WithDBSMes(int iD_PathLog, string factory, string stage,string dataType, int? line, string machineID, string machineName, string logAddress, int? isActive)
        {
            var paras = new SqlParameter[9];
            paras[0] = new SqlParameter("@ID_PathLog", iD_PathLog);
            paras[1] = new SqlParameter("@Factory", factory ?? (object)DBNull.Value);
            paras[2] = new SqlParameter("@Stage", stage ?? (object)DBNull.Value);
            paras[3] = new SqlParameter("@Line", line ?? (object)DBNull.Value);
            paras[4] = new SqlParameter("@MachineID", machineID ?? (object)DBNull.Value);
            paras[5] = new SqlParameter("@MachineName", machineName ?? (object)DBNull.Value);
            paras[6] = new SqlParameter("@LogAddress", logAddress ?? (object)DBNull.Value);
            paras[7] = new SqlParameter("@IsActive", isActive ?? (object)DBNull.Value);
            paras[8] = new SqlParameter("@DataType", dataType ?? (object)DBNull.Value);

            return _db.Execute_Modify("sp_sms_LogPath_Condition_Save_WithDB", paras, CommandType.StoredProcedure, "ConnectionStringSMES");
        }

        public int LogPath_Update(int? iD, string factory, string stage, int? line, string machineID, string machineName, string logAddress, string logBadMarkAOI_F4, string logNGImageAddress,
            string userName, string password, string dbServer, string dbUser, string dbPassword, DateTime? lastTime, int? isActive)
        {
            var paras = new SqlParameter[16];
            paras[0] = new SqlParameter("@ID", iD ?? (object)DBNull.Value);
            paras[1] = new SqlParameter("@Factory", factory ?? (object)DBNull.Value);
            paras[2] = new SqlParameter("@Stage", stage ?? (object)DBNull.Value);
            paras[3] = new SqlParameter("@Line", line ?? (object)DBNull.Value);
            paras[4] = new SqlParameter("@MachineID", machineID ?? (object)DBNull.Value);
            paras[5] = new SqlParameter("@MachineName", machineName ?? (object)DBNull.Value);
            paras[6] = new SqlParameter("@LogAddress", logAddress ?? (object)DBNull.Value);
            paras[7] = new SqlParameter("@LogBadMarkAOI_F4", logBadMarkAOI_F4 ?? (object)DBNull.Value);
            paras[8] = new SqlParameter("@LogNGImageAddress", logNGImageAddress ?? (object)DBNull.Value);
            paras[9] = new SqlParameter("@Username", userName ?? (object)DBNull.Value);
            paras[10] = new SqlParameter("@Password", password ?? (object)DBNull.Value);
            paras[11] = new SqlParameter("@DBServer", dbServer ?? (object)DBNull.Value);
            paras[12] = new SqlParameter("@DBUser", dbUser ?? (object)DBNull.Value);
            paras[13] = new SqlParameter("@DBPassword", dbPassword ?? (object)DBNull.Value);
            paras[14] = new SqlParameter("@LastTime", lastTime ?? (object)DBNull.Value);
            paras[15] = new SqlParameter("@IsActive", isActive ?? (object)DBNull.Value);

            return _db.Execute_Modify("sp_sms_LogPath_Update", paras, CommandType.StoredProcedure, connectionStringOption);
        }

        public int LogPath_Delete(int? iD)
        {
            var paras = new SqlParameter[1];
            paras[0] = new SqlParameter("@ID", iD ?? (object)DBNull.Value);

            return _db.Execute_Modify("sp_sms_LogPath_Delete", paras, CommandType.StoredProcedure, connectionStringOption);
        }

        public int LogPath_UpdateLastTime(int? iD, DateTime lastTime)
        {
            var paras = new SqlParameter[2];
            paras[0] = new SqlParameter("@ID", iD ?? (object)DBNull.Value);
            paras[1] = new SqlParameter("@LastTime", lastTime);

            return _db.Execute_Modify("sp_sms_LogPath_UpdateLastTime", paras, CommandType.StoredProcedure, connectionStringOption);
        }

        public DataTable LogPath_GetByID(int? iD)
        {
            var paras = new SqlParameter[1];
            paras[0] = new SqlParameter("@ID", iD ?? (object)DBNull.Value);

            return _db.Execute_Table("sp_sms_LogPath_GetByID", paras, CommandType.StoredProcedure, connectionStringOption);
        }

        public DataTable LogPath_GetByFactoryAndStage(string factory, string stage)
        {
            var paras = new SqlParameter[2];
            paras[0] = new SqlParameter("@Factory", factory ?? (object)DBNull.Value);
            paras[1] = new SqlParameter("@Stage", stage ?? (object)DBNull.Value);

            return _db.Execute_Table("sp_sms_LogPath_GetByFactoryAndStage", paras, CommandType.StoredProcedure, connectionStringOption);
        }
        public DataTable LogPath_GetAllByFactoryAndStage(string factory, string stage)
        {
            var paras = new SqlParameter[2];
            paras[0] = new SqlParameter("@Factory", factory ?? (object)DBNull.Value);
            paras[1] = new SqlParameter("@Stage", stage ?? (object)DBNull.Value);

            return _db.Execute_Table("sp_sms_LogPath_GetAllByFactoryAndStage", paras, CommandType.StoredProcedure, connectionStringOption);
        }
        public DataTable LogPath_GetByFactoryAndStage(string factory, string stage, int Active)
        {
            var paras = new SqlParameter[3];
            paras[0] = new SqlParameter("@Factory", factory ?? (object)DBNull.Value);
            paras[1] = new SqlParameter("@Stage", stage ?? (object)DBNull.Value);
            paras[2] = new SqlParameter("@Active", Active);

            return _db.Execute_Table("sp_sms_LogPath_GetByFactoryAndStage_Active", paras, CommandType.StoredProcedure, connectionStringOption);
        }

        public DataTable LogPath_GetByFactoryAndStageAndLine(string factory, string stage, int? line)
        {
            var paras = new SqlParameter[3];
            paras[0] = new SqlParameter("@Factory", factory ?? (object)DBNull.Value);
            paras[1] = new SqlParameter("@Stage", stage ?? (object)DBNull.Value);
            paras[2] = new SqlParameter("@Line", line ?? (object)DBNull.Value);

            return _db.Execute_Table("sp_sms_LogPath_GetByFactoryAndStageAndLine", paras, CommandType.StoredProcedure, connectionStringOption);
        }
        public DataTable LogPath_GetByMachine(string factory, string stage, string machine)
        {
            var paras = new SqlParameter[3];
            paras[0] = new SqlParameter("@Factory", factory ?? (object)DBNull.Value);
            paras[1] = new SqlParameter("@Stage", stage ?? (object)DBNull.Value);
            paras[2] = new SqlParameter("@MachineID", machine ?? (object)DBNull.Value);

            return _db.Execute_Table("sp_sms_LogPath_GetByMachine", paras, CommandType.StoredProcedure, connectionStringOption);
        }
        public DataTable LogPath_GetByMachine2(string factory, string stage, string machine)
        {
            var paras = new SqlParameter[3];
            paras[0] = new SqlParameter("@Factory", factory ?? (object)DBNull.Value);
            paras[1] = new SqlParameter("@Stage", stage ?? (object)DBNull.Value);
            paras[2] = new SqlParameter("@MachineID", machine ?? (object)DBNull.Value);

            return _db.Execute_Table("sp_sms_LogPath_GetByMachine2", paras, CommandType.StoredProcedure, connectionStringOption);
        }

        public DataTable LogPath_UpdateAll_Delete(string factory)
        {
            var paras = new SqlParameter[2];
            paras[0] = new SqlParameter("@Factory", factory ?? (object)DBNull.Value);
            paras[1] = new SqlParameter("@Stage", "FlexAssy_22_ICT");

            return _db.Execute_Table("sp_sms_LogPath_UpdateAll_Delete", paras, CommandType.StoredProcedure, connectionStringOption);
        }
    }
}