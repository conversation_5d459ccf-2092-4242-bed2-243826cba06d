﻿using OfficeOpenXml.FormulaParsing.Excel.Functions.DateTime;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Threading.Tasks;
using Trace_AbilitySystem.Libs.Dataconnect;
using Trace_AbilitySystem.Libs.DTOClass;
using Trace_AbilitySystem.Libs.DTOClass.SPCBase;
using Trace_AbilitySystem.Libs.ITrace_02_FlexAssy_Services;
using Trace_AbilitySystem.Libs.SPC_Utils;

namespace Trace_AbilitySystem.Libs.Trace_02_FlexAssy_Services
{
	public class FlexAssy_25_AVIService : IFlexAssy_25_AVIService
	{
		private readonly DbExecute _db;

		public FlexAssy_25_AVIService()
		{
			_db = new SqlExecute();
		}
		public async Task<int> FlexAssy_25_AVI_OEEProcessData(string factory, DateTime DateTime, int minutes, string connectionStringOption)
		{
			string stage = "OEE_25_AVI";
			DateTime timeLast = DateTime;
			try
			{
				DateTime dateNow = DateTime.Now;
				int recordNeedSyn = 0;
				int recordSyned = 0;
				DataTable dt = Singleton_02_FlexAssy.ISource_FlexAssy_SEI_VCDTrace_Service.TblAVI_GetDataWithCreatedDateOEE(factory, timeLast, minutes, connectionStringOption);
				if (dt == null)
					return -1;

				if (dt.Rows.Count > 0)
				{
					string connectionStringOptionBoard = Singleton_03_Common.IDBInfoService.ConnectionStringOption(factory, "BoardRegistrationDB");
					List<DTOClass.SPCBase.OEE_Item_Product> lstOEE_Item_Product = new List<DTOClass.SPCBase.OEE_Item_Product>();
					for (int i = 0; i < dt.Rows.Count; i++)
					{
						string MachineID = DataConvert.ConvertToString(dt.Rows[i]["MachineID"]).Trim();
						string ItemName = DataConvert.ConvertToString(dt.Rows[i]["ItemName"]).Trim().Split('-')[0];
						string IndicationNumber = DataConvert.ConvertToString(dt.Rows[i]["IndicationNumber"]).Trim();
						DateTime? _DateTime = null;
						if (dt.Rows[i]["AVIDate"] != DBNull.Value)
							_DateTime = Convert.ToDateTime(dt.Rows[i]["AVIDate"]);

						if (_DateTime.Value.Hour < 6)
						{
							_DateTime = _DateTime.Value.AddDays(-1);
							_DateTime = _DateTime.Value.Date;
						}
						else
						{
							_DateTime = _DateTime.Value.Date;
						}
						string Line = "";
						DataTable dtMachineID = Singleton_04_Machine.ItMachineMtnService.tMachineList_GetByID(MachineID);
						if (dtMachineID?.Rows.Count > 0)
						{
							Line = dtMachineID.Rows[0]["LineID"] + "";
						}
						int index = lstOEE_Item_Product.FindIndex(item => item.LineID == Line && item.MachineID == MachineID && item.IndicationNumber == IndicationNumber && _DateTime == item.DateTime);
						if (index >= 0)
						{
							lstOEE_Item_Product[index].MachineID = MachineID;
							lstOEE_Item_Product[index].LineID = Line;
							lstOEE_Item_Product[index].ItemName = ItemName;
							lstOEE_Item_Product[index].IndicationNumber = IndicationNumber;
							lstOEE_Item_Product[index].DateTime = _DateTime;
							lstOEE_Item_Product[index].TotalOKDay += 1;
							lstOEE_Item_Product[index].TotalNGDay = 0;
						}
						else
						{
							DTOClass.SPCBase.OEE_Item_Product New_OEE_Item_Product = new DTOClass.SPCBase.OEE_Item_Product();
							New_OEE_Item_Product.MachineID = MachineID;
							New_OEE_Item_Product.LineID = Line;
							New_OEE_Item_Product.ItemName = ItemName;
							New_OEE_Item_Product.IndicationNumber = IndicationNumber;
							New_OEE_Item_Product.DateTime = _DateTime;
							New_OEE_Item_Product.TotalOKDay = 1;
							New_OEE_Item_Product.TotalNGDay = 0;
							lstOEE_Item_Product.Add(New_OEE_Item_Product);
						}

						timeLast = Convert.ToDateTime(dt.Rows[i]["CreatedDate"]);
					}
					//number of records to sync
					for (int i = 0; i < lstOEE_Item_Product.Count; i++)
					{
						int rs = Singleton_06_SPC.iSPC_CommonService.OEE_Product_Save(lstOEE_Item_Product[i].ItemName, lstOEE_Item_Product[i].IndicationNumber, lstOEE_Item_Product[i].LineID, "AVI", lstOEE_Item_Product[i].MachineID,
							lstOEE_Item_Product[i].TotalOKDay, lstOEE_Item_Product[i].TotalNGDay, lstOEE_Item_Product[i].DateTime, "NewSPC_" + factory);
						if (rs == -9)
							return -1;
					}
					Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, stage, timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff"));
				}
				else
				{
					timeLast = timeLast.AddMinutes(minutes) > dateNow ? timeLast : timeLast.AddMinutes(minutes);

					// Update ValueSearch
					Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, stage, timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff"));
				}
				ManageLog.WriteProcess(true, null, recordNeedSyn, recordSyned);
			}
			catch (Exception ex)
			{
				Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, stage, timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff"));
				ManageLog.WriteErrorApp(stage + "\n" + ex.Message);

				return -1;
			}
			await Task.Delay(50);
			return 1;
		}
		public async Task<int> FlexAssy_25_AVI_ProcessData(string factory, DateTime createdDateSearch, int minutes, string connectionStringOption)
		{
			string stage = "FlexAssy_25_AVI";
			string typeBarcode = "ProductID";

			try
			{
				DateTime dateNow = DateTime.Now;
				DateTime timeLast = createdDateSearch;
				int recordNeedSyn = 0;
				int recordSyned = 0;

				// Lấy dữ liệu từ FlexAssy_24_AVI
				DataTable dt = Singleton_02_FlexAssy.ISource_FlexAssy_SEI_VCDTrace_Service.TblAVI_GetDataWithCreatedDate(factory, timeLast, minutes, connectionStringOption);
				if (dt == null)
					return -1;

				if (dt.Rows.Count > 0)
				{
					List<MachineMaintenance> machineMaintenances = new List<MachineMaintenance>();
					List<MachineOfStage> machineOfStages = new List<MachineOfStage>();
					List<OperatorOfStage> operatorOfStages = new List<OperatorOfStage>();
					List<ProductionCondition> productionConditions = new List<ProductionCondition>();

					string connectionStringOptionMachine = Singleton_03_Common.IDBInfoService.ConnectionStringOption(factory, "MachineDB");

					string productionConditionResult = null;
					int? productionConditionID = null;

					recordNeedSyn = dt.Rows.Count;
					for (int i = 0; i < recordNeedSyn; i++)
					{
						DataRow row = dt.Rows[i];
						string productID = DataConvert.ConvertToString(row["ProductID"]).Trim();
						if (string.IsNullOrEmpty(productID))
							continue;

						DateTime dateTime = Convert.ToDateTime(row["AVIDate"]);
						string MachineID = DataConvert.ConvertToString(row["MachineID_Main"]).Trim();
						string ProgramName = DataConvert.ConvertToString(row["ProgramName"]).Trim();
						string ItemName = DataConvert.ConvertToString(row["ItemName"]).Trim();
						string ItemCode = DataConvert.ConvertToString(row["ItemCode"]).Trim();
						string IndicationNumber = DataConvert.ConvertToString(row["IndicationNumber"]).Trim();
						string ProductID = DataConvert.ConvertToString(row["ProductID"]).Trim();
						string JigID = DataConvert.ConvertToString(row["JigID"]).Trim();
						string PositionIndex = DataConvert.ConvertToString(row["PositionIndex"]).Trim();
						string Result = DataConvert.ConvertToString(row["Result"]).Trim();
						string DefectArea = DataConvert.ConvertToString(row["DefectArea"]).Trim();
						string DefectCode = DataConvert.ConvertToString(row["DefectCode"]).Trim();
						string DefectName = DataConvert.ConvertToString(row["DefectName"]).Trim();
						string PictureName = DataConvert.ConvertToString(row["PictureName"]).Trim();
						string FileName = DataConvert.ConvertToString(row["FileName"]).Trim();
						string FullName = DataConvert.ConvertToString(row["FullName"]).Trim();
						string AVIType = DataConvert.ConvertToString(row["AVIType"]).Trim();
						string OperatorID = DataConvert.ConvertToString(row["OperatorID"]).Trim();
						string LaneID = DataConvert.ConvertToString(row["LaneID"]).Trim();
						string LoaderHand = DataConvert.ConvertToString(row["LoaderHand"]).Trim();
						string LoaderBuffer = DataConvert.ConvertToString(row["LoaderBuffer"]).Trim();
						string UnLoaderHand = DataConvert.ConvertToString(row["UnLoaderHand"]).Trim();
						string UnLoaderBuffer = DataConvert.ConvertToString(row["UnLoaderBuffer"]).Trim();

						byte? VRSByPicture = null;
						if (row["VRSByPicture"] + "" != "")
						{
							if (row["VRSByPicture"] + "" == "False")
							{
								VRSByPicture = 0;
							}
							else
							{
								VRSByPicture = 1;
							}
						}
						byte? VRSByCCD = null;
						if (row["VRSByCCD"] + "" != "")
						{
							if (row["VRSByCCD"] + "" == "False")
							{
								VRSByCCD = 0;
							}
							else
							{
								VRSByCCD = 1;
							}
						}
						byte? VRSByMS = null;
						if (row["VRSByMS"] + "" != "")
						{
							if (row["VRSByMS"] + "" == "False")
							{
								VRSByMS = 0;
							}
							else
							{
								VRSByMS = 1;
							}
						}

						// Find info machine maintenance date
						List<MachineMaintenance> machineMaintenancesOther = Singleton_03_Common.ICommon_CommonService.GetMachine_Maintenance(null, MachineID, dateTime, out DateTime? machineMaintenanceDate, out int? machineMaintenanceID, machineMaintenances, connectionStringOptionMachine);
						if (machineMaintenancesOther != null)
							machineMaintenances.AddRange(machineMaintenancesOther);

						// Define of Operator as stage
						OperatorOfStage operatorOfStage = Singleton_03_Common.ICommon_CommonService.GetOperatorOfStage(OperatorID, null, stage, factory, typeBarcode, operatorOfStages);
						if (operatorOfStage != null)
							operatorOfStages.Add(operatorOfStage);

						// machineID, OperatorID definition makes at the stage
						MachineOfStage machineOfStage = Singleton_03_Common.ICommon_CommonService.GetMachineOfStage(MachineID, null, stage, factory, typeBarcode, machineOfStages);
						if (machineOfStage != null)
							machineOfStages.Add(machineOfStage);

						// Find info production condition
						ProductionCondition productionCondition = Singleton_03_Common.ICommon_CommonService.GetMachine_Condition(MachineID, dateTime, ProgramName, out productionConditionResult, out productionConditionID, productionConditions, connectionStringOptionMachine);
						if (productionCondition != null)
							productionConditions.Add(productionCondition);

						string MS_Result = null;
						DateTime? MS_Time = null;

						DataTable dtMS = Singleton_02_FlexAssy.ISource_FlexAssy_SEI_VCDTrace_Service.TblMasterSampleForAVI_GetData(ProgramName, dateTime, connectionStringOption);

						if (dtMS?.Rows.Count > 0)
						{
							MS_Result = DataConvert.ConvertToString(dtMS.Rows[0]["SampleResult"]).Trim();
							MS_Time = Convert.ToDateTime(dtMS.Rows[0]["CreatedDate"]);
							// Chèn dữ liệu vào các bảng liên quan
							int rs = sms_FlexAssy_25_AVI_History_Save(productID, AVIType, dateTime, MachineID, ProgramName, ItemName, ItemCode, IndicationNumber, JigID
								, PositionIndex, Result, DefectArea, DefectCode, DefectName, PictureName, FileName, FullName, OperatorID, machineMaintenanceDate,
								machineMaintenanceID, productionConditionResult, productionConditionID, null, null, MS_Result, MS_Time, VRSByPicture, VRSByCCD, VRSByMS, LaneID, LoaderHand, LoaderBuffer, UnLoaderHand, UnLoaderBuffer, factory);

							if (rs == -9)
								break;

							// Đếm số bản ghi đã đồng bộ
							recordSyned++;
						}
						timeLast = Convert.ToDateTime(row["CreatedDate"]);
					}
					// Cập nhật giá trị tìm kiếm
					Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, stage, timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff"));
				}
				else
				{
					timeLast = timeLast.AddMinutes(minutes) > dateNow ? timeLast : timeLast.AddMinutes(minutes);

					// Cập nhật giá trị tìm kiếm nếu không có dữ liệu
					Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, stage, timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff"));
				}

				ManageLog.WriteProcess(true, null, recordNeedSyn, recordSyned);
			}
			catch (Exception ex)
			{
				ManageLog.WriteErrorApp(stage + "\n" + ex.Message);
				return -1;
			}

			await Task.Delay(500);
			return 1;
		}

		public async Task<int> FlexAssy_25_AVI_LogFile_ProcessData(string factory, DateTime createdDateSearch, int minutes, string connectionStringOption)
		{
			string stage = "FlexAssy_25_AVI_LogFile";
			try
			{
				DateTime dateNow = DateTime.Now;
				DateTime timeLast = createdDateSearch;
				int recordNeedSyn = 0;
				int recordSyned = 0;

				// Lấy dữ liệu từ FlexAssy_24_AVI
				DataTable dt = Singleton_02_FlexAssy.ISource_FlexAssy_SEI_VCDTrace_Service.TblAVI_GetDataWithCreatedDate(factory, timeLast, minutes, connectionStringOption);
				if (dt == null)
					return -1;

				if (dt.Rows.Count > 0)
				{
					recordNeedSyn = dt.Rows.Count;
					for (int i = 0; i < recordNeedSyn; i++)
					{
						DataRow row = dt.Rows[i];
						string productID = DataConvert.ConvertToString(row["ProductID"]).Trim();
						if (string.IsNullOrEmpty(productID))
							continue;

						string FullName = DataConvert.ConvertToString(row["FullName"]).Trim();
						string PictureName = DataConvert.ConvertToString(row["PictureName"]).Trim();
						ManageLog.WriteLogApp("Trace.txt", productID + FullName);
						if (FullName != "")
						{
							string directoryPath = Path.GetDirectoryName(FullName);
							string fileName = Path.GetFileName(FullName);

							string localDirectoryPath = Path.Combine(directoryPath, "SSG_ReadOK");
							string localPath = Path.Combine(localDirectoryPath, fileName);

							string serverPath = directoryPath.Replace("\\\\10.126.21.104\\pc 1\\", "\\\\10.212.8.119\\2.Logs\\LOG_F3_2024\\AVI\\");

							ManageLog.WriteLogApp("Trace.txt", localPath);
							if (File.Exists(localPath))
							{
								string FileNameServer = Common.CopyFileToPcFromServer(localPath, serverPath);
								ManageLog.WriteLogApp("Trace.txt", "CopyTo " + FileNameServer);

								FileNameServer = FileNameServer.Replace(@"\\10.212.8.119\2.Logs", "").Replace(@"\", "/");

								// Chèn dữ liệu vào các bảng liên quan
								int rs = sms_FlexAssy_25_AVI_LogFile_Save(FullName, FileNameServer, factory);

								if (rs == -9)
									break;
							}

							if (PictureName != "" && PictureName != "0")
							{

								string localDirectoryPathImg = Path.Combine(directoryPath, "SSG_Image\\" + productID);
								//string directoryPathImg = Path.GetDirectoryName(PictureName);

								//string fileNameImg = Path.GetFileName(PictureName);
								//string localPathImg = Path.Combine(localDirectoryPathImg, fileNameImg);

								string serverPathImg = directoryPath.Replace("\\\\10.126.21.104\\pc 1\\", "\\\\10.212.8.119\\2.Logs\\LOG_F3_2024\\AVI\\");

								List<FileInfo> fileInfos = new List<FileInfo>();

								fileInfos = Common.GetAllAnyFilesInFolder(localDirectoryPathImg);
								if (fileInfos?.Count > 0)
								{
									foreach (FileInfo fileImg in fileInfos)
									{
										string FileNameServerImg = Common.CopyFileToPcFromServer(fileImg, serverPathImg);

										FileNameServerImg = FileNameServerImg.Replace(@"\\10.212.8.119\2.Logs", "").Replace(@"\", "/");

										// Chèn dữ liệu vào các bảng liên quan
										int rs = sms_FlexAssy_25_AVI_Image_Save(PictureName, FileNameServerImg, factory);

										if (rs == -9)
											break;
									}
								}
							}
						}

						timeLast = Convert.ToDateTime(row["CreatedDate"]);

						// Đếm số bản ghi đã đồng bộ
						recordSyned++;
					}

					// Cập nhật giá trị tìm kiếm
					Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, stage, timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff"));
				}
				else
				{
					timeLast = timeLast.AddMinutes(minutes) > dateNow ? timeLast : timeLast.AddMinutes(minutes);

					// Cập nhật giá trị tìm kiếm nếu không có dữ liệu
					Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, stage, timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff"));
				}

				ManageLog.WriteProcess(true, null, recordNeedSyn, recordSyned);
			}
			catch (Exception ex)
			{
				ManageLog.WriteErrorApp(stage + "\n" + ex.Message);
				return -1;
			}

			await Task.Delay(500);
			return 1;
		}
		public int sms_FlexAssy_25_AVI_LogFile_Save(string FileNameRoot, string FileName, string connectionStringOption = null)
		{
			var paras = new List<SqlParameter>
			{
				new SqlParameter("@FileNameRoot", FileNameRoot),
				new SqlParameter("@FileName", FileName)
			};

			return _db.Execute_Modify("sms_FlexAssy_25_AVI_LogFile_Insert", paras.ToArray(), CommandType.StoredProcedure, connectionStringOption);
		}
		public int sms_FlexAssy_25_AVI_Image_Save(string FileNameRoot, string FileName, string connectionStringOption = null)
		{
			var paras = new List<SqlParameter>
			{
				new SqlParameter("@FileNameRoot", FileNameRoot),
				new SqlParameter("@FileName", FileName)
			};

			return _db.Execute_Modify("sms_FlexAssy_25_AVI_Image_Insert", paras.ToArray(), CommandType.StoredProcedure, connectionStringOption);
		}
		public int sms_FlexAssy_25_AVI_History_Save(
		   string productID,
		   string aviType,
		   DateTime dateTime,
		   string machineID = null,
		   string programName = null,
		   string itemName = null,
		   string itemCode = null,
		   string indicationNumber = null,
		   string jigID = null,
		   string positionIndex = null,
		   string result = null,
		   string defectArea = null,
		   string defectCode = null,
		   string defectName = null,
		   string pictureName = null,
		   string fileName = null,
		   string fullName = null,
		   string operatorID = null,
		   DateTime? machineMaintenanceDate = null,
		   int? machineMaintenanceID = null,
		   string productionConditionResult = null,
		   int? productionConditionID = null,
		   DateTime? firstPieceBuyoffControlDate = null,
		   int? firstPieceBuyoffControlID = null,
		   string MS_Result = null,
		   DateTime? MS_Time = null,
		   byte? VRSByPicture = null,
		   byte? VRSByCCD = null,
		   byte? VRSByMS = null,
		   string LaneID = null,
		   string LoaderHand = null,
		   string LoaderBuffer = null,
		   string UnLoaderHand = null,
		   string UnLoaderBuffer = null,
		   string connectionStringOption = null)
		{
			var paras = new List<SqlParameter>
			{
				new SqlParameter("@ProductID", productID),
				new SqlParameter("@AVIType", aviType),
				new SqlParameter("@DateTime", dateTime),
				new SqlParameter("@MachineID", machineID ?? (object)DBNull.Value),
				new SqlParameter("@ProgramName", programName ?? (object)DBNull.Value),
				new SqlParameter("@ItemName", itemName ?? (object)DBNull.Value),
				new SqlParameter("@ItemCode", itemCode ?? (object)DBNull.Value),
				new SqlParameter("@IndicationNumber", indicationNumber ?? (object)DBNull.Value),
				new SqlParameter("@JigID", jigID ?? (object)DBNull.Value),
				new SqlParameter("@PositionIndex", positionIndex ?? (object)DBNull.Value),
				new SqlParameter("@Result", result ?? (object)DBNull.Value),
				new SqlParameter("@DefectArea", defectArea ?? (object)DBNull.Value),
				new SqlParameter("@DefectCode", defectCode ?? (object)DBNull.Value),
				new SqlParameter("@DefectName", defectName ?? (object)DBNull.Value),
				new SqlParameter("@PictureName", pictureName ?? (object)DBNull.Value),
				new SqlParameter("@FileName", fileName ?? (object)DBNull.Value),
				new SqlParameter("@FullName", fullName ?? (object)DBNull.Value),
				new SqlParameter("@OperatorID", operatorID ?? (object)DBNull.Value),
				new SqlParameter("@MachineMaintenanceDate", machineMaintenanceDate ?? (object)DBNull.Value),
				new SqlParameter("@MachineMaintenanceID", machineMaintenanceID ?? (object)DBNull.Value),
				new SqlParameter("@ProductionConditionResult", productionConditionResult ?? (object)DBNull.Value),
				new SqlParameter("@ProductionConditionID", productionConditionID ?? (object)DBNull.Value),
				new SqlParameter("@FirstPieceBuyoffControlDate", firstPieceBuyoffControlDate ?? (object)DBNull.Value),
				new SqlParameter("@MS_Result", MS_Result ?? (object)DBNull.Value),
				new SqlParameter("@MS_Time", MS_Time ?? (object)DBNull.Value),
				new SqlParameter("@VRSByPicture", VRSByPicture ?? (object)DBNull.Value),
				new SqlParameter("@VRSByCCD", VRSByCCD ?? (object)DBNull.Value),
				new SqlParameter("@VRSByMS", VRSByMS ?? (object)DBNull.Value),
				new SqlParameter("@LaneID", LaneID),
				new SqlParameter("@LoaderHand", LoaderHand),
				new SqlParameter("@LoaderBuffer", LoaderBuffer),
				new SqlParameter("@UnLoaderHand", UnLoaderHand),
				new SqlParameter("@UnLoaderBuffer", UnLoaderBuffer),
				new SqlParameter("@FirstPieceBuyoffControlID", firstPieceBuyoffControlID ?? (object)DBNull.Value)
			};

			return _db.Execute_Modify("sms_FlexAssy_25_AVI_History_Save_New", paras.ToArray(), CommandType.StoredProcedure, connectionStringOption);
		}

		public string FlexAssy_25_AVI_LogFile_GetFileNameLocal(string FileNameLocal, string connectionStringOption)
		{
			var paras = new SqlParameter[1];
			paras[0] = new SqlParameter("@FileNameLocal", FileNameLocal ?? (object)DBNull.Value);

			string FileNameServer = "";
			DataTable dt = null;

			if (connectionStringOption.Equals("F3"))
			{
				dt = _db.Execute_Table("sms_FlexAssy_25_AVI_LogFile_GetFileNameLocal", paras, CommandType.StoredProcedure, connectionStringOption);
				if (dt?.Rows.Count > 0)
				{
					FileNameServer = dt.Rows[0]["FileName"] + "";
				}
			}
			return FileNameServer;
		}

		public DataTable FlexAssy_25_AVI_GetByProductID(string productID, string connectionStringOption, out string connectionStringOk)
		{
			var paras = new SqlParameter[1];
			paras[0] = new SqlParameter("@ProductID", productID ?? (object)DBNull.Value);

			string connectionStringDefault = null;
			DataTable dt = null;
			switch (connectionStringOption)
			{
				case "F3":
					dt = _db.Execute_Table("sms_FlexAssy_25_AVI_GetByProductID", paras, CommandType.StoredProcedure, connectionStringOption);
					break;

				case "F4":
					//dt = _db.Execute_Table("sms_FlexAssy_25_AVI_GetByProductID", paras, CommandType.StoredProcedure, connectionStringOption);
					break;

				case "F5":
					//dt = _db.Execute_Table("sms_FlexAssy_25_AVI_GetByProductID", paras, CommandType.StoredProcedure, connectionStringOption);
					break;

				default:
					connectionStringDefault = "F3";
					dt = _db.Execute_Table("sms_FlexAssy_25_AVI_GetByProductID", paras, CommandType.StoredProcedure, "F3");
					//if (dt == null || dt?.Rows.Count == 0)
					//{
					//    connectionStringDefault = "F4";
					//    paras = new SqlParameter[1];
					//    paras[0] = new SqlParameter("@ProductID", productID ?? (object)DBNull.Value);

					//    dt = _db.Execute_Table("sms_FlexAssy_25_AVI_GetByProductID", paras, CommandType.StoredProcedure, "F4");
					//}
					//if (dt == null || dt?.Rows.Count == 0)
					//{
					//    connectionStringDefault = "F5";
					//    paras = new SqlParameter[1];
					//    paras[0] = new SqlParameter("@ProductID", productID ?? (object)DBNull.Value);

					//    dt = _db.Execute_Table("sms_FlexAssy_25_AVI_GetByProductID", paras, CommandType.StoredProcedure, "F5");
					//}
					break;
			}

			connectionStringOk = connectionStringOption ?? (dt?.Rows.Count > 0 ? connectionStringDefault : null);
			return dt;
		}

		public DataTable FlexAssy_25_AVI_GetByListProductID(DataTable listProductID, string connectionStringOption, out string connectionStringOk)
		{
			var paras = new SqlParameter[1];
			paras[0] = new SqlParameter("@ListProductID", listProductID ?? (object)DBNull.Value);

			string connectionStringDefault = null;
			DataTable dt = null;
			switch (connectionStringOption)
			{
				case "F3":
					dt = _db.Execute_Table("sms_FlexAssy_25_AVI_GetByListProductID", paras, CommandType.StoredProcedure, connectionStringOption);
					break;

				//case "F4":
				//    dt = _db.Execute_Table("sms_FlexAssy_25_AVI_GetByListProductID", paras, CommandType.StoredProcedure, connectionStringOption);
				//    break;

				//case "F5":
				//    dt = _db.Execute_Table("sms_FlexAssy_25_AVI_GetByListProductID", paras, CommandType.StoredProcedure, connectionStringOption);
				//    break;

				default:
					connectionStringDefault = "F3";
					dt = _db.Execute_Table("sms_FlexAssy_25_AVI_GetByListProductID", paras, CommandType.StoredProcedure, "F3");
					//if (dt == null || dt?.Rows.Count == 0)
					//{
					//    connectionStringDefault = "F4";
					//    paras = new SqlParameter[1];
					//    paras[0] = new SqlParameter("@ListProductID", listProductID ?? (object)DBNull.Value);

					//    dt = _db.Execute_Table("sms_FlexAssy_25_AVI_GetByListProductID", paras, CommandType.StoredProcedure, "F4");
					//}
					//if (dt == null || dt?.Rows.Count == 0)
					//{
					//    connectionStringDefault = "F5";
					//    paras = new SqlParameter[1];
					//    paras[0] = new SqlParameter("@ListProductID", listProductID ?? (object)DBNull.Value);

					//    dt = _db.Execute_Table("sms_FlexAssy_25_AVI_GetByListProductID", paras, CommandType.StoredProcedure, "F5");
					//}
					break;
			}

			connectionStringOk = connectionStringOption ?? (dt?.Rows.Count > 0 ? connectionStringDefault : null);
			return dt;
		}
	}
}