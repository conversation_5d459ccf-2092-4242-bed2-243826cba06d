﻿using System;
using System.Data;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Trace_AbilitySystem.Libs.ITrace_07_Pismo_Service
{
    public interface IPismo_BlockIDService
    {
        Task ProcessService(RichTextBox richTextBox, string factory);
        Task<int> FlexAssy_BlockID_ProcessData(string factory, DateTime createdDateSearch, string connectionStringOption);
        DataTable BlockID_GetDataWithCreatedDate(DateTime createdDate, string connectionStringOption);
        int FlexAssy_BlockID_Insert(int blockpkid, int itemlotpkid, DateTime dateTime, string blockID, string itemName, string itemCode, string itemLot, string EMapID_A, string EMapID_B, int operatorPkid, string connectionStringOption);
    }
}
