﻿using System;
using System.Data;

namespace Trace_AbilitySystem.Libs.ITrace_01_BareFlex_Services
{
    public interface IBareFlex_07_4_DryFilmForCircuit_DEVELOPService
    {
        int BareFlex_07_4_DryFilmForCircuit_DEVELOP_Insert(string workOrder, DateTime dateTime, string productName, string machineID, string operatorID, DateTime? firstPieceBuyoffControlDate, int? firstPieceBuyoffControlID, DateTime? machineMaintenanceDate, int? machineMaintenanceID);
        DataTable BareFlex_07_4_DryFilmForCircuit_DEVELOP_GetByWorkOrder(string workOrder);
        DataTable BareFlex_07_4_DryFilmForCircuit_DEVELOP_GetByListWorkOrder(DataTable listWorkOrder);
    }
}