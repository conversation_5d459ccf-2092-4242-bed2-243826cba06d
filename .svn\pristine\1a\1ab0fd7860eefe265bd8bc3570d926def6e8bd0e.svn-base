﻿using System;
using System.Data;
using Trace_AbilitySystem.Libs.Dataconnect;
using Trace_AbilitySystem.Libs.ITrace_02_FlexAssy_Services;

namespace Trace_AbilitySystem.Libs.Trace_02_FlexAssy_Services
{
    public class Source_FlexAssy_TCI_Service : ISource_FlexAssy_TCI_Service
    {
        private readonly DbExecute _db;

        public Source_FlexAssy_TCI_Service()
        {
            _db = new SqlExecute();
        }

        public DataTable Tbl2DCode_GetDataWithCreatedDate(DateTime createdDate, int minutes, string connectionStringOption)
        {
            string strWhere = "CreateDate > '" + createdDate.ToString("yyyy-MM-dd HH:mm:ss.fff") + "' AND CreateDate <= '" + createdDate.AddMinutes(minutes).ToString("yyyy-MM-dd HH:mm:ss.fff") + "'";

            string sql = "SELECT [ProductRFID],[ProductID],[ProductICT],[ItemCode],[ItemLot],[ProductType],[RFTagID],[OperatorID],[Location],[MachineID],[CreateDate] FROM tbl2DCode WHERE " + strWhere + " ORDER BY CreateDate ASC";

            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }

        public DataTable Tbl2DCode_GetPcsIDWithProductICT(string factory, string productICT, string connectionStringOption)
        {
            string sql = "SELECT TOP(1) [ProductID] FROM tbl2DCode WHERE ProductICT = '" + productICT + "'";

            return _db.Execute_Table(sql, null, CommandType.Text, connectionStringOption);
        }
    }
}