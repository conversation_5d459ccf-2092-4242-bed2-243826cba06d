﻿using System;
using System.Data;

namespace Trace_AbilitySystem.Libs.ITrace_01_BareFlex_Services
{
    public interface IBareFlex_07_5_2_CopperbriteService
    {
        int BareFlex_07_5_2_Copperbrite_Insert(string workOrder, DateTime dateTime, string machineID, string operatorID, DateTime? machineMaintenanceDate, int? machineMaintenanceID, string SequencyNumber);
        DataTable BareFlex_07_5_2_Copperbrite_GetByWorkOrder(string workOrder);
        DataTable BareFlex_07_5_2_Copperbrite_GetByListWorkOrder(DataTable listWorkOrder);
    }
}