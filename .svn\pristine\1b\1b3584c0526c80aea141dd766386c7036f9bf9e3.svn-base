﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trace_AbilitySystem.Libs.Dataconnect;
using Trace_AbilitySystem.Libs.ITrace_07_Pismo_Auto_Service;

namespace Trace_AbilitySystem.Libs.Trace_07_Pismo_Auto_Services
{
    public class Pismo_BlockIDServices2025 : IPismo_BlockIDServices2025
    {
        private readonly DbExecute _db;
        private readonly string _connectionStringOption = "PismoTest";

        public Pismo_BlockIDServices2025()
        {
            _db = new SqlExecute();
        }
        public async Task<int> FlexAssy_BlockID_ProcessData(string factory, DateTime createdDateSearch, string connectionStringOption)
        {
            try
            {
                DateTime dateNow = DateTime.Now;
                DateTime timeLast = createdDateSearch;
                int recordNeedSyn = 0;
                int recordSyned = 0;
                int OperatorPkid = 0;
                DataTable dt = Singleton_07_Pismo.IPismo_BlockIDService2025.BlockID_GetDataWithCreatedDate(timeLast, connectionStringOption);
                if (dt == null)
                    return -1;

                if (dt.Rows.Count > 0)
                {
                    recordNeedSyn = dt.Rows.Count;
                    for (int i = 0; i < recordNeedSyn; i++)
                    {
                        int blockpkid = int.Parse(dt.Rows[i]["PKID"].ToString());
                        int itemlotpkid = int.Parse(dt.Rows[i]["ItemLotPkid"].ToString());
                        DateTime CreatedDate = Convert.ToDateTime(dt.Rows[i]["CreatedDate"]);
                        string itemName = dt.Rows[i]["ItemName"].ToString();
                        string itemCode = dt.Rows[i]["ItemCode"].ToString();
                        string itemLot = dt.Rows[i]["ItemLot"].ToString();
                        string blockid = dt.Rows[i]["BlockID"].ToString();
                        string EMapID_A = dt.Rows[i]["EMapID_A"].ToString();
                        string EMapID_B = dt.Rows[i]["EMapID_B"].ToString();
                        OperatorPkid = dt.Rows[i]["OperatorPkid"] == DBNull.Value ? 0 : int.Parse(dt.Rows[i]["OperatorPkid"].ToString());
                        bool RunAVI = dt.Rows[i]["RunAVI"] != DBNull.Value && Convert.ToBoolean(dt.Rows[i]["RunAVI"]);
                        bool RunAVISUS = dt.Rows[i]["RunAVISUS"] != DBNull.Value && Convert.ToBoolean(dt.Rows[i]["RunAVISUS"]);
                        bool RunECheck = dt.Rows[i]["RunECheck"] != DBNull.Value && Convert.ToBoolean(dt.Rows[i]["RunECheck"]);
                        int rs = FlexAssy_BlockID_Insert(blockpkid, itemlotpkid, CreatedDate, blockid, itemName, itemCode, itemLot, EMapID_A, EMapID_B, OperatorPkid, RunAVI, RunAVISUS, RunECheck, "PismoTest");
                        if (rs == -9)
                            return -1;

                        timeLast = Convert.ToDateTime(dt.Rows[i]["CreatedDate"]);
                        recordSyned++;
                    }
                    if (timeLast == createdDateSearch)
                    {
                        timeLast = timeLast.AddMinutes(5) > DateTime.Now ? timeLast : timeLast.AddMinutes(5);
                    }
                    // Update ValueSearch
                    Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, "Pismo_BlockID2025", timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff"));
                }
                else
                {
                    timeLast = timeLast.AddMinutes(5) > dateNow ? timeLast : timeLast.AddMinutes(5);

                    Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_UpdateSearchValue(factory, "Pismo_BlockID2025", timeLast.ToString("yyyy-MM-dd HH:mm:ss.fff"));
                }

                ManageLog.WriteProcess(true, null, recordNeedSyn, recordSyned);
            }
            catch (Exception ex)
            {
                ManageLog.WriteErrorApp("Pismo_BlockID2025" + "\n" + ex.Message);
                return -1;
            }
            await Task.Delay(500);

            return 1;
        }
        public int FlexAssy_BlockID_Insert(int blockpkid, int itemlotpkid, DateTime CreatedDate, string blockID, string itemName, string itemCode, string itemLot, string EMapID_A, string EMapID_B, int operatorPkid, bool RunAVI, bool RunAVISUS, bool RunECheck, string connectionStringOption)
        {
            var paras = new SqlParameter[13];
            paras[0] = new SqlParameter("@BlockPkid", blockpkid);
            paras[1] = new SqlParameter("@ItemlotPkid", itemlotpkid);
            paras[2] = new SqlParameter("@CreatedDate", CreatedDate);
            paras[3] = new SqlParameter("@BlockID", blockID);
            paras[4] = new SqlParameter("@ItemName", itemName);
            paras[5] = new SqlParameter("@ItemCode", itemCode);
            paras[6] = new SqlParameter("@ItemLot", itemLot);
            paras[7] = new SqlParameter("@EMapID_A", EMapID_A);
            paras[8] = new SqlParameter("@EMapID_B", EMapID_B);
            paras[9] = new SqlParameter("@OperatorPkid", operatorPkid);
            paras[10] = new SqlParameter("@RunAVI", RunAVI);
            paras[11] = new SqlParameter("@RunAVISUS", RunAVISUS);
            paras[12] = new SqlParameter("@RunECheck", RunECheck);

            return _db.Execute_Modify("_FlexAssy_BlockID_Insert", paras, CommandType.StoredProcedure, _connectionStringOption);
        }
        public DataTable BlockID_GetDataWithCreatedDate(DateTime createdDate, string connectionStringOption)
        {
            var paras = new SqlParameter[1];
            paras[0] = new SqlParameter("@CreatedDate", createdDate);
            return _db.Execute_Table("sms_FlexAssy_BlockID_GetDataWithCreatedDate", paras, CommandType.StoredProcedure, connectionStringOption);
        }
    }
}
