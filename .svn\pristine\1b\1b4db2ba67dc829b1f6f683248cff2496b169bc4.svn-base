﻿using System;
using System.Data;
using System.Data.SqlClient;
using Trace_AbilitySystem.Libs.Dataconnect;
using Trace_AbilitySystem.Libs.ITrace_03_Common_Services;

namespace Trace_AbilitySystem.Libs.Trace_03_Common_Services
{
    public class StageSettingSearchValueService : IStageSettingSearchValueService
    {
        private readonly DbExecute _db;
        private readonly string connectionStringOption = "Common";

        public StageSettingSearchValueService()
        {
            _db = new SqlExecute();
        }

        public int StageSettingSearchValue_Update(string factory, string stage, string value, int? minute)
        {
            var paras = new SqlParameter[4];
            paras[0] = new SqlParameter("@Factory", factory);
            paras[1] = new SqlParameter("@Stage", stage);
            paras[2] = new SqlParameter("@SearchValue", value ?? (object)DBNull.Value);
            paras[3] = new SqlParameter("@Minute", minute ?? (object)DBNull.Value);

            return _db.Execute_Modify("sp_sms_StageSettingSearchValue_Update", paras, CommandType.StoredProcedure, connectionStringOption);
        }

        public int StageSettingSearchValue_UpdateSearchValue(string factory, string stage, string value)
        {
            var paras = new SqlParameter[3];
            paras[0] = new SqlParameter("@Factory", factory);
            paras[1] = new SqlParameter("@Stage", stage);
            paras[2] = new SqlParameter("@SearchValue", value ?? (object)DBNull.Value);

            return _db.Execute_Modify("sp_sms_StageSettingSearchValue_UpdateSearchValue", paras, CommandType.StoredProcedure, connectionStringOption);
        }

        public DataTable StageSettingSearchValue_GetByFactoryAndStage(string factory, string stage)
        {
            var paras = new SqlParameter[2];
            paras[0] = new SqlParameter("@Stage", stage);
            paras[1] = new SqlParameter("@Factory", factory);

            return _db.Execute_Table("sp_sms_StageSettingSearchValue_GetByFactoryAndStage", paras, CommandType.StoredProcedure, connectionStringOption);
        }

        public DataTable StageSettingSearchValue_GetByFactory(string factory)
        {
            var paras = new SqlParameter[1];
            paras[0] = new SqlParameter("@Factory", factory);

            return _db.Execute_Table("sp_sms_StageSettingSearchValue_GetByFactory", paras, CommandType.StoredProcedure, connectionStringOption);
        }

        public string ValueLast(string factory, string stage, out int minute)
        {
            minute = 5;
            DataTable dt = StageSettingSearchValue_GetByFactoryAndStage(factory, stage);
            if (dt?.Rows.Count > 0)
            {
                if (dt.Rows[0]["Minute"] != DBNull.Value)
                    minute = int.Parse(dt.Rows[0]["Minute"].ToString());

                return dt.Rows[0]["SearchValue"].ToString();
            }

            return null;
        }
    }
}