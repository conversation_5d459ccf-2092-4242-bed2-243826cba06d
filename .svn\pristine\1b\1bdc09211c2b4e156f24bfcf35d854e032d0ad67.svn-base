﻿@using System.Configuration;
@model Trace_AbilitySystem.Libs.DTOClass.DataAllTrace


@if (Model != null && Model.DataBareFlex.BareFlex_12_5_SRBaking_PostCure?.Rows.Count > 0
    && Model.DataBareFlex.BareFlex_12_5_SRBaking_PostCure?.Rows.Count >= Model.DataBareFlex.BareFlex_12_5_SRBaking_PostCure_Top_Display)
{
    var i = Model.DataBareFlex.BareFlex_12_5_SRBaking_PostCure_Top_Display - 1;
    //for (int i = 0; i < Model.DataBareFlex.BareFlex_12_5_SRBaking_PostCure.Rows.Count; i++)
    //{
    string linkUrlParam = "";
    if (Model.DataBareFlex.BareFlex_12_5_SRBaking_PostCure.Rows[i]["WCSID"] != DBNull.Value)
    {
        linkUrlParam = $"{Model.DataBareFlex.BareFlex_12_5_SRBaking_PostCure.Rows[i]["WCSID"]}";
    }

<h2 class="bd-title">
    SR Baking (Post cure)
    / <a href="#" title="Seq_No" id="@Model.DataBareFlex.BareFlex_12_5_SRBaking_PostCure_Sequence.ToString()">@Model.DataBareFlex.BareFlex_12_5_SRBaking_PostCure_Sequence.ToString()</a>
</h2>
    <table class="table table-bordered">
        <thead>
            <tr>
                <th scope="col">Control Item</th>
                <th scope="col">Control Value</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>WorkOrder</td>
                <td>@Model.DataBareFlex.BareFlex_12_5_SRBaking_PostCure.Rows[i]["WorkOrder"]</td>
            </tr>
            <tr>
                <td>DateTime</td>
                <td>@Model.DataBareFlex.BareFlex_12_5_SRBaking_PostCure.Rows[i]["DateTime"]</td>
            </tr>
            <tr>
                <td>Cutter ID</td>
                <td>@Model.DataBareFlex.BareFlex_12_5_SRBaking_PostCure.Rows[i]["MachineID"]</td>
            </tr>
            <tr>
                <td>Operator ID</td>
                <td>@Model.DataBareFlex.BareFlex_12_5_SRBaking_PostCure.Rows[i]["OperatorID"]</td>
            </tr>
            <tr>
                <td>
                    WCS ID linkage
                </td>
                <td>@if (!string.IsNullOrEmpty(linkUrlParam)){string[] WCSIDArr = (linkUrlParam).Trim().Split(';');foreach (string Link in WCSIDArr){string _linkUrlParam = $"{ConfigurationManager.AppSettings["LinkUrlParam"]}{Link}";<a target="_blank" style="display:block" href="@_linkUrlParam">@_linkUrlParam</a>}}</td>
            </tr>

            @*<tr>
                <td>Machine maintenance & repair record</td>
                <td><a onclick="viewDataMachineMaintenance(@Model.DataBareFlex.BareFlex_12_5_SRBaking_PostCure.Rows[i]["MachineMaintenanceID"])" href="javascript:">@Model.DataBareFlex.BareFlex_12_5_SRBaking_PostCure.Rows[i]["MachineMaintenanceDate"]</a></td>
            </tr>
            <tr>
                <td>SR Baking (Post cure) Infor</td>
                <td>@if (!string.IsNullOrEmpty(linkUrlParam)){string[] WCSIDArr = (linkUrlParam).Trim().Split(';');foreach (string Link in WCSIDArr){string _linkUrlParam = $"{ConfigurationManager.AppSettings["LinkUrlParam"]}{Link}";<a target="_blank" style="display:block" href="@_linkUrlParam">@_linkUrlParam</a>}}</td>
            </tr>*@
        </tbody>
    </table>
    //}
}