﻿using System;
using System.Data;

namespace Trace_AbilitySystem.Libs.ITrace_01_BareFlex_Services
{
    public interface IBareFlex_18_1_SilkScreenPrintingService
    {
        int BareFlex_18_1_SilkScreenPrinting_Insert(string workOrder, DateTime dateTime, string machineID, string screenID, string operatorID, DateTime? firstPieceBuyoffControlDate, int? firstPieceBuyoffControlID, DateTime? machineMaintenanceDate, int? machineMaintenanceID);
        DataTable BareFlex_18_1_SilkScreenPrinting_GetByWorkOrder(string workOrder);
        DataTable BareFlex_18_1_SilkScreenPrinting_GetByListWorkOrder(DataTable listWorkOrder);
    }
}