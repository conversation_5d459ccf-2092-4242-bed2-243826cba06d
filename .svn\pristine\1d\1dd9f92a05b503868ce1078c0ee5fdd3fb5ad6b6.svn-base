﻿using System;
using System.Data;

namespace Trace_AbilitySystem.Libs.ITrace_01_BareFlex_Services
{
    public interface IBareFlex_19_1_OutlinePrepunch_PUNCH1Service
    {
        int BareFlex_19_1_OutlinePrepunch_PUNCH1_Insert(string workOrder, DateTime dateTime, string machineID, string operatorID, string toolingID, DateTime? firstPieceBuyoffControlDate, int? firstPieceBuyoffControlID, DateTime? machineMaintenanceDate, int? machineMaintenanceID);
        DataTable BareFlex_19_1_OutlinePrepunch_PUNCH1_GetByWorkOrder(string workOrder);
        DataTable BareFlex_19_1_OutlinePrepunch_PUNCH1_GetByListWorkOrder(DataTable listWorkOrder);
    }
}