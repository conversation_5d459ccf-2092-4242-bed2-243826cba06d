﻿using System;
using System.Data;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Trace_AbilitySystem.Libs.ITrace_07_Pismo_Service
{
    public interface IFlexAssy_04_QcGateService
    {
        Task<int> FlexAssy_QcGate_ProcessData(string factory, DateTime createdDateSearch, int minutes, string connectionStringOption);
        DataTable QcGate_GetDataWithCreatedDate(DateTime createdDate, int minutes, string connectionStringOption);
        int FlexAssy_QcGate_Insert(string AlbagID, string ProductID, DateTime dateTime, string Result, string Indication,
            string ProgramName, string MachineID, string OperatorID, DateTime LocalCreatedDate, string connectionStringOption);
        DataTable FlexAssy_04_QcGate_GetByProductID(string productID, string connectionStringOption);
    }
}
