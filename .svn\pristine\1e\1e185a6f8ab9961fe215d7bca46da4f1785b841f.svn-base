﻿@model Trace_AbilitySystem.Libs.DTOClass.DataAllTrace
@if (Model != null && Model.DataFlexAssy.FlexAssy_32_6_ORT_HeatSoakAndFlexBending?.Rows.Count > 0)
{
    <h2 class="bd-title" id="37-ort-heatsoakflexbending">ORT Heatsoak And Flex Bending</h2>
    <table class="table table-bordered">
        <thead>
            <tr>
                <th scope="col">Control Item</th>
                <th scope="col">Control Value</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>ItemName</td>
                <td>@(ViewBag.ItemName)</td>
            </tr>
            <tr>
                <td>IndicationNo</td>
                <td>@(ViewBag.IndicationNo)</td>
            </tr>
            <tr>
                <td>Production Line</td>
                <td>@(Model.DataFlexAssy.FlexAssy_33_IPQC_OQC?.Rows.Count > 0 ? Model.DataFlexAssy.FlexAssy_33_IPQC_OQC.Rows[0]["ProductionLine"] : ViewBag.WorkOrder)</td>
            </tr>
            <tr>
                <td>Input Test DateTime</td>
                <td>@Model.DataFlexAssy.FlexAssy_32_6_ORT_HeatSoakAndFlexBending.Rows[0]["InputTest"]</td>
            </tr>
            <tr>
                <td>Operator Input</td>
                <td>@Model.DataFlexAssy.FlexAssy_32_6_ORT_HeatSoakAndFlexBending.Rows[0]["InputOperator"]</td>
            </tr>
            <tr>
                <td>Finish Test DateTime</td>
                <td>@Model.DataFlexAssy.FlexAssy_32_6_ORT_HeatSoakAndFlexBending.Rows[0]["FinishTest"]</td>
            </tr>
            <tr>
                <td>Operator Finish</td>
                <td>@Model.DataFlexAssy.FlexAssy_32_6_ORT_HeatSoakAndFlexBending.Rows[0]["FinishOperator"]</td>
            </tr>
            <tr>
                <td>Result</td>
                <td>@Model.DataFlexAssy.FlexAssy_32_6_ORT_HeatSoakAndFlexBending.Rows[0]["Result"]</td>
            </tr>
            <tr>
                <td>Machine Condition</td>
                <td><a href='@Model.DataFlexAssy.FlexAssy_32_6_ORT_HeatSoakAndFlexBending.Rows[0]["TestCondition"]'>@Model.DataFlexAssy.FlexAssy_32_6_ORT_HeatSoakAndFlexBending.Rows[0]["TestCondition"]</a></td>
            </tr>
            <tr>
                <td>Detail ID</td>
                <td>@Model.DataFlexAssy.FlexAssy_32_6_ORT_HeatSoakAndFlexBending.Rows[0]["DetailID"]</td>
            </tr>
        </tbody>
    </table>
}