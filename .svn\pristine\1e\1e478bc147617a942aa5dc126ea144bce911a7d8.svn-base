﻿@using System.Configuration;
@using System.Data;
@using Trace_AbilitySystem.Libs
@model Trace_AbilitySystem.Libs.DTOClass.DataAllTrace

@if (Model != null && Model.DataPismoFlexAssy.FlexAssy_07_ECheck?.Rows.Count > 0)
{
    <h2 class="bd-title" id="26-qcgate">ECheck</h2>
    <table class="table table-bordered">
        <thead>
            <tr>
                <th scope="col">Control Item</th>
                <th scope="col">Control Value</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>BlockID</td>
                <td>@Model.DataPismoFlexAssy.FlexAssy_07_ECheck.Rows[0]["BlockID"]</td>
            </tr>
            <tr>
                <td>ProgramName</td>
                <td>@Model.DataPismoFlexAssy.FlexAssy_07_ECheck.Rows[0]["ProgramName"]</td>
            </tr>
            <tr>
                <td>MachineID</td>
                <td>@Model.DataPismoFlexAssy.FlexAssy_07_ECheck.Rows[0]["MachineID"]</td>
            </tr>
            <tr>
                <td>DateTime</td>
                <td>@Model.DataPismoFlexAssy.FlexAssy_07_ECheck.Rows[0]["DateTime"]</td>
            </tr>
            <tr>
                <td>FileName</td>
                <td>@Model.DataPismoFlexAssy.FlexAssy_07_ECheck.Rows[0]["FileName"]</td>
            </tr>
            <tr>
                <td>ErrorMap</td>
                <td>@Model.DataPismoFlexAssy.FlexAssy_07_ECheck.Rows[0]["ErrorMap"]</td>
            </tr>
            <tr>
                <td>OperatorPkid</td>
                <td>@Model.DataPismoFlexAssy.FlexAssy_07_ECheck.Rows[0]["OperatorPkid"]</td>
            </tr>
            <tr>
                <td>LotNumber</td>
                <td>@Model.DataPismoFlexAssy.FlexAssy_07_ECheck.Rows[0]["LotNumber"]</td>
            </tr>
            <tr>
                <td>PanelNumber</td>
                <td>@Model.DataPismoFlexAssy.FlexAssy_07_ECheck.Rows[0]["PanelNumber"]</td>
            </tr>
            <tr>
                <td>ItemName</td>
                <td>@Model.DataPismoFlexAssy.FlexAssy_07_ECheck.Rows[0]["ItemName"]</td>
            </tr>
            <tr>
                <td>MachineType</td>
                <td>@Model.DataPismoFlexAssy.FlexAssy_07_ECheck.Rows[0]["MachineType"]</td>
            </tr>
            <tr>
                <td>Side</td>
                <td>@Model.DataPismoFlexAssy.FlexAssy_07_ECheck.Rows[0]["Side"]</td>
            </tr>
            <tr>
                <td>ErrorMapConvert</td>
                <td>@Model.DataPismoFlexAssy.FlexAssy_07_ECheck.Rows[0]["ErrorMapConvert"]</td>
            </tr>
            <tr>
                <td>FullName</td>
                <td>@Model.DataPismoFlexAssy.FlexAssy_07_ECheck.Rows[0]["FullName"]</td>
            </tr>
            <tr>
                <td>SyncPcsID</td>
                <td>@Model.DataPismoFlexAssy.FlexAssy_07_ECheck.Rows[0]["SyncPcsID"]</td>
            </tr>
            <tr>
                <td>OperatorID</td>
                <td>@Model.DataPismoFlexAssy.FlexAssy_07_ECheck.Rows[0]["OperatorID"]</td>
            </tr>
            @if (Model != null && Model.DataPismoFlexAssy.FlexAssy_07_ECheck_Detail?.Rows.Count > 0)
            {
                <tr>
                    <td>ProductID</td>
                    <td>@Model.DataPismoFlexAssy.FlexAssy_07_ECheck_Detail.Rows[0]["ProductID"]</td>
                </tr>
                <tr>
                    <td>Result</td>
                    <td>@Model.DataPismoFlexAssy.FlexAssy_07_ECheck_Detail.Rows[0]["Result"]</td>
                </tr>
                <tr>
                    <td>DefectCode</td>
                    <td>@Model.DataPismoFlexAssy.FlexAssy_07_ECheck_Detail.Rows[0]["DefectCode"]</td>
                </tr>
            }
        </tbody>
    </table>
}