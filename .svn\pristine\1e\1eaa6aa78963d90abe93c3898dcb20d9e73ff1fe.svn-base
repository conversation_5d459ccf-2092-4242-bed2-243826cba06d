﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Data;

namespace Trace_AbilitySystem.Libs.ITrace_03_Common_Services
{
    public interface ILineDefinition
    {
        int LineDefinition_Update(string machineID, string lineID, string factory, string Operator, DateTime CreateDate, string connectionString);
        int LineDefinition_Insert(string machineID, string lineID, string factory, string Operator, DateTime CreateDate, string connectionString);
        int LineDefinition_Delete(string machineID, string connectionString);
        string GetLineByMachineID(string machineID, string connectionString);
        DataTable LineDefinition_GetByFactory(string machineID, string connectionString);
    }
}
