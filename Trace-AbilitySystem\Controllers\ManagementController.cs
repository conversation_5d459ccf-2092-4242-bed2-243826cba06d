using Newtonsoft.Json;
using System;
using System.Configuration;
using System.Data;
using System.Diagnostics;
using System.Threading.Tasks;
using System.Web.Mvc;
using Trace_AbilitySystem.Libs;

namespace Trace_AbilitySystem.Controllers
{
	public class ManagementController : Controller
	{
		// GET: Management
		public ActionResult Login()
		{
			if (Session["account"] != null)
			{
				//DataTable user = (DataTable)Session["account"];
				//if (user.Rows[0]["Role"].ToString() == "1")
				//    return Redirect("/server-management");
				//else
				//    return Redirect("/log-path-condition-management");

				return Redirect("/");
			}

			return View();
		}

		[HttpGet]
		[Route("notpermission")]
		public ActionResult NotPermission()
		{
			return View();
		}

		[PermissionRequired()]
		public async Task<ActionResult> ServerManagement()
		{
			Management_Server_DTO model = new Management_Server_DTO()
			{
				DBInfos = Singleton_03_Common.IDBInfoService.DBInfo_GetAll()
			};

			if (model.DBInfos.Rows.Count > 0)
			{
				Stopwatch stopwatch = new Stopwatch();
				stopwatch.Start();

				Task<bool>[] tasks = new Task<bool>[model.DBInfos.Rows.Count];
				bool[] results = new bool[model.DBInfos.Rows.Count];
				for (int i = 0; i < model.DBInfos.Rows.Count; i++)
				{
					//string connectionString = $"data source=\"{model.DBInfos.Rows[i]["DBServer"].ToString()}\"; initial catalog=\"{model.DBInfos.Rows[i]["DBName"].ToString()}\"; user id=\"{model.DBInfos.Rows[i]["UserID"].ToString()}\"; password=\"{model.DBInfos.Rows[i]["Password"].ToString()}\"; connection timeout={model.DBInfos.Rows[i]["Timeout"].ToString()}";
					//tasks[i] = Common.IsServerConnected(connectionString);
					tasks[i] = Common.CheckConnectSql(model.DBInfos.Rows[i]["DBServer"].ToString());
				}
				await Task.WhenAll(tasks);

				for (int i = 0; i < model.DBInfos.Rows.Count; i++)
				{
					results[i] = await tasks[i];
					//or results[i] = tasks[i].Result;
				}

				stopwatch.Stop();
				Console.WriteLine("Time elapsed: {0}", stopwatch.Elapsed);

				model.IsConnecteds = results;
			}

			return View(model);
		}


		[PermissionRequired()]
		public ActionResult LogPath_ProductionConditionManagement()
		{
			string fatory = Request.Params["factory"] ?? "F4";
			string conditionType = Request.Params["conditionType"] ?? "";
			string line = Request.Params["line"] ?? "0";

			try
			{
				int.Parse(line);
			}
			catch (Exception)
			{
				line = "0";
			}

			var model = new Management_Log_DTO
			{
				LogAddress = Singleton_03_Common.ILogPath_ProductionConditionService.LogPath_ProductionCondition_GetByFactoryAndConditionTypeAndLine(fatory == "-" ? "F4" : fatory, conditionType == "-" ? "" : conditionType, int.Parse(line)),
			};

			return View(model);
		}

		[PermissionRequired()]
		public ActionResult CheckProgramNameIsOk()
		{
			string factory = Request.Params["factory"] ?? "F4";
			string conditionType = Request.Params["conditionType"] ?? "SPI";
			string isOk = Request.Params["isReadOk"] ?? "";
			string programName = Request.Params["programName"] ?? "";
			int pageSize = 50;
			int pageNumber = Request.Params["PageNum"] != null ? int.Parse(Request.Params["PageNum"].ToString()) : 1;
			ViewBag.CurrentPage = pageNumber;

			var model = new FlexAssy_ProductionCondition_LogFile_DTO
			{
				FlexAssy_ProductionCondition_LogFile = Singleton_02_FlexAssy.IFlexAssy_ProductionConditionService.FlexAssy_ProductionCondition_LogFile_GetPageByWhere(conditionType, isOk, programName, factory, pageNumber, pageSize)
			};
			if (model.FlexAssy_ProductionCondition_LogFile?.Rows.Count > 0)
			{
				double TotalRow = double.Parse(model.FlexAssy_ProductionCondition_LogFile.Rows[1]["totalRow"].ToString());
				int totalPage = Convert.ToInt32(Math.Ceiling(TotalRow / pageSize));
				ViewBag.CurrentPage = pageNumber;
				ViewBag.TotalPage = totalPage;
			}
			return View(model);
		}

		#region ItemName sequence
		[PermissionRequired()]
		public ActionResult ItemName_Vendor()
		{
			string ItemnName = Request.Params["ItemnName"] ?? "";
			string VendorCode = Request.Params["VendorCode"] ?? "";
			string ANPCode = Request.Params["ANPCode"] ?? "";
			int pageSize = 50;
			int pageNumber = Request.Params["PageNum"] != null ? int.Parse(Request.Params["PageNum"].ToString()) : 1;
			ViewBag.CurrentPage = pageNumber;

			var model = new Management_ItemNameVendor_DTO
			{
				ItemNamevendor = Singleton_03_Common.IItemName_VendorService.ItemName_Vendor_GetPageByWhere(ItemnName, VendorCode, ANPCode, pageNumber, pageSize)
			};
			if (model.ItemNamevendor?.Rows.Count > 0)
			{
				double TotalRow = double.Parse(model.ItemNamevendor.Rows[0]["totalRow"].ToString());
				int totalPage = Convert.ToInt32(Math.Ceiling(TotalRow / pageSize));
				ViewBag.CurrentPage = pageNumber;
				ViewBag.TotalPage = totalPage;
			}
			return View(model);
		}

		[PermissionRequired]
		public JsonResult ItemnNameVendor_Save(string ItemnName, string VendorCode, string APNCode)
		{
			if (ItemnName.Length == 0 || VendorCode.Length == 0 || APNCode.Length == 0)
			{
				return Json(-1, JsonRequestBehavior.AllowGet);
			}

			int rs = Singleton_03_Common.IItemName_VendorService.ItemName_Vendor_Save(ItemnName, VendorCode, APNCode);
			if (rs != -9)
				return Json(1, JsonRequestBehavior.AllowGet);

			return Json(rs, JsonRequestBehavior.AllowGet);
		}

		[PermissionRequired]
		public JsonResult ItemnNameVendor_Delete(string ItemnName)
		{
			if (ItemnName.Length == 0)
			{
				return Json(-1, JsonRequestBehavior.AllowGet);
			}

			int rs = Singleton_03_Common.IItemName_VendorService.ItemName_Vendor_DeleteByItemName(ItemnName);
			if (rs != -9)
				return Json(1, JsonRequestBehavior.AllowGet);

			return Json(rs, JsonRequestBehavior.AllowGet);
		}

		[PermissionRequired]
		public JsonResult ItemnNameVendor_GetByItemName(string ItemnName)
		{
			if (ItemnName.Length == 0)
			{
				return Json(-1, JsonRequestBehavior.AllowGet);
			}

			DataTable dt = Singleton_03_Common.IItemName_VendorService.ItemName_Vendor_GetByitemName(ItemnName);
			if (dt?.Rows.Count > 0)
			{
				return Json(JsonConvert.SerializeObject(dt), JsonRequestBehavior.AllowGet);
			}

			return Json(-2, JsonRequestBehavior.AllowGet);
		}
		#endregion

		[PermissionRequired()]
		public ActionResult LogPathManagement()
		{
			string fatory = Request.Params["factory"] ?? "F3";
			string stage = Request.Params["stage"] ?? "";
			string line = Request.Params["line"] ?? "0";

			try
			{
				int.Parse(line);
			}
			catch (Exception)
			{
				line = "0";
			}

			var model = new Management_Log_DTO
			{
				LogAddress = Singleton_03_Common.ILogPathService.LogPath_GetByFactoryAndStageAndLine(fatory == "-" ? "F3" : fatory, stage == "-" ? "" : stage, int.Parse(line)),
			};

			return View(model);
		}

		[PermissionRequired()]
		public ActionResult MachineOfStage()
		{
			string fatory = Request.Params["factory"] ?? "F1";
			string stage = Request.Params["stage"] ?? "";
			DataTable model = Singleton_03_Common.IMachineOfStageService.MachineOfStage_GetByFactoryAndStage(fatory == "-" ? "F1" : fatory, stage == "-" ? "" : stage);

			return View(model);
		}

		[PermissionRequired()]
		public ActionResult StageActive()
		{
			DataTable model = Singleton_03_Common.IStageSettingActiveViewTraceService.StageSettingActiveViewTrace_GetAll("#");

			return View(model);
		}

		[PermissionRequired()]
		public ActionResult StageSearchValue()
		{
			string fatory = Request.Params["factory"] ?? "F1";
			DataTable model = Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_GetByFactory(fatory == "-" ? "F1" : fatory);

			return View(model);
		}

		[PermissionRequired()]
		public ActionResult SequencyOfStage()
		{
			string itemName = Request.Params["itemname"] ?? "";
			string frontOrBack = Request.Params["frontOrBack"] ?? "";
			DataTable model = Singleton_03_Common.ISequencyOfStageService.SequencyOfStage_GetByFrontOrBackAndItemName(frontOrBack, itemName);

			return View(model);
		}

		[PermissionRequired()]
		public ActionResult AccountManagement()
		{
			string role = Request.Params["role"] ?? "-1";
			try
			{
				int.Parse(role);
			}
			catch (Exception)
			{
				role = "-1";
			}
			DataTable model = Singleton_03_Common.IAccountService.Account_GetByRole(int.Parse(role));

			return View(model);
		}

		public ActionResult Logout()
		{
			if (Session != null)
			{
				Session.Clear();
				Session.Abandon();
			}
			return new RedirectResult("/");
		}

		#region Index
		public JsonResult Account_GetByOperatorIDAndPassword(string operatorID, string password)
		{
			if (operatorID.Length == 0 || password.Length == 0)
			{
				return Json(-1, JsonRequestBehavior.AllowGet);
			}
			DataTable dt = Singleton_03_Common.IAccountService.Account_GetByOperatorIDAndPassword(operatorID, Common.Md5Endcoding(password));
			if (dt?.Rows.Count > 0)
			{
				Session["account"] = dt;
				return Json(1, JsonRequestBehavior.AllowGet);
			}

			return Json(-2, JsonRequestBehavior.AllowGet);
		}
		#endregion

		#region ServerManagement
		[PermissionRequired]
		public JsonResult DBInfo_GetByID(int id)
		{
			DataTable dt = Singleton_03_Common.IDBInfoService.DBInfo_GetByID(id);
			if (dt?.Rows.Count > 0)
			{

				return Json(new
				{
					dbServer = dt.Rows[0]["DBServer"].ToString(),
					dbName = dt.Rows[0]["DBName"].ToString(),
					userID = dt.Rows[0]["UserID"].ToString(),
					password = dt.Rows[0]["Password"].ToString(),
					timeout = dt.Rows[0]["Timeout"].ToString()
				}, JsonRequestBehavior.AllowGet);
			}

			return Json(-1, JsonRequestBehavior.AllowGet);
		}

		[PermissionRequired]
		public JsonResult DBInfo_Update(int id, string dbServer, string dbName, string userID, string password, int timeout)
		{
			if (dbServer.Length == 0 || dbName.Length == 0 || userID.Length == 0 || password.Length == 0 || timeout.ToString().Length == 0)
			{
				return Json(-1, JsonRequestBehavior.AllowGet);
			}

			if (!int.TryParse(timeout.ToString(), out int value))
			{
				return Json(-2, JsonRequestBehavior.AllowGet);
			}

			int rs = Singleton_03_Common.IDBInfoService.DBInfo_Update(id, dbServer, dbName, userID, password, timeout);
			if (rs != -9)
				return Json(1, JsonRequestBehavior.AllowGet);

			return Json(rs, JsonRequestBehavior.AllowGet);
		}
		#endregion

		#region Log Condition Management
		[PermissionRequired]
		public JsonResult LogPath_ProductionCondition_Insert(string factory, string conditionType, int? line, string machineID, string machineName, string logAddress, string userName, string password, string mdbUser, string mdbPassword, DateTime? lastTime, int? isActive)
		{
			if (machineID.Length == 0 || logAddress.Length == 0)
			{
				return Json(-1, JsonRequestBehavior.AllowGet);
			}
			int rs = Singleton_03_Common.ILogPath_ProductionConditionService.LogPath_ProductionCondition_Insert(factory, conditionType, line, machineID, machineName, logAddress, userName, password, mdbUser, mdbPassword, lastTime, isActive);
			if (rs != -9)
				return Json(1, JsonRequestBehavior.AllowGet);

			return Json(rs, JsonRequestBehavior.AllowGet);
		}

		[PermissionRequired]
		public JsonResult LogPath_ProductionCondition_Update(int id, string factory, string conditionType, int? line, string machineID, string machineName, string logAddress, string userName, string password, string mdbUser, string mdbPassword, DateTime? lastTime, int? isActive)
		{
			if (machineID.Length == 0 || logAddress.Length == 0)
			{
				return Json(-1, JsonRequestBehavior.AllowGet);
			}

			int rs = Singleton_03_Common.ILogPath_ProductionConditionService.LogPath_ProductionCondition_Update(id, factory, conditionType, line, machineID, machineName, logAddress, userName, password, mdbUser, mdbPassword, lastTime, isActive);
			if (rs != -9)
				return Json(1, JsonRequestBehavior.AllowGet);

			return Json(rs, JsonRequestBehavior.AllowGet);
		}

		[PermissionRequired]
		public JsonResult LogPath_ProductionCondition_Delete(int? id)
		{
			int rs = Singleton_03_Common.ILogPath_ProductionConditionService.LogPath_ProductionCondition_Delete(id);
			if (rs != -9)
				return Json(1, JsonRequestBehavior.AllowGet);

			return Json(rs, JsonRequestBehavior.AllowGet);
		}

		[PermissionRequired]
		public JsonResult LogPath_ProductionCondition_GetByID(int? id)
		{
			DataTable dt = Singleton_03_Common.ILogPath_ProductionConditionService.LogPath_ProductionCondition_GetByID(id);
			if (dt?.Rows.Count > 0)
			{
				return Json(JsonConvert.SerializeObject(dt), JsonRequestBehavior.AllowGet);
			}

			return Json(-1, JsonRequestBehavior.AllowGet);
		}

		[PermissionRequired]
		public JsonResult FlexAssy_ProductionCondition_LogFile_Reset(int id, string factory)
		{
			return Json(Singleton_02_FlexAssy.IFlexAssy_ProductionConditionService.FlexAssy_ProductionCondition_LogFile_Reset(id, factory), JsonRequestBehavior.AllowGet);
		}

		[PermissionRequired]
		public JsonResult FlexAssy_ProductionCondition_LogFile_Finish(int id, string factory)
		{
			return Json(Singleton_02_FlexAssy.IFlexAssy_ProductionConditionService.FlexAssy_ProductionCondition_LogFile_Finish(id, factory), JsonRequestBehavior.AllowGet);
		}

		#endregion

		#region LogManagement
		[PermissionRequired]
		public JsonResult LogPath_Insert(string factory, string stage, int? line, string machineID, string machineName, string logAddress, string logBadMarkAOI_F4, string logNGImageAddress,
			string userName, string password, string dbServer, string dbUser, string dbPassword, DateTime? lastTime, int? isActive)
		{
			if (machineID.Length == 0 || logAddress.Length == 0)
			{
				return Json(-1, JsonRequestBehavior.AllowGet);
			}

			int rs = Singleton_03_Common.ILogPathService.LogPath_Insert(factory, stage, line, machineID, machineName, logAddress, logBadMarkAOI_F4, logNGImageAddress,
				userName, password, dbServer, dbUser, dbPassword, lastTime, isActive);
			if (rs != -9)
				return Json(1, JsonRequestBehavior.AllowGet);

			return Json(rs, JsonRequestBehavior.AllowGet);
		}

		[PermissionRequired]
		public JsonResult LogPath_Update(int id, string factory, string stage, int? line, string machineID, string machineName, string logAddress, string logBadMarkAOI_F4, string logNGImageAddress,
			string userName, string password, string dbServer, string dbUser, string dbPassword, DateTime? lastTime, int? isActive)
		{
			if (machineID.Length == 0 || logAddress.Length == 0)
			{
				return Json(-1, JsonRequestBehavior.AllowGet);
			}

			int rs = Singleton_03_Common.ILogPathService.LogPath_Update(id, factory, stage, line, machineID, machineName, logAddress, logBadMarkAOI_F4, logNGImageAddress,
				userName, password, dbServer, dbUser, dbPassword, lastTime, isActive);
			if (rs != -9)
				return Json(1, JsonRequestBehavior.AllowGet);

			return Json(rs, JsonRequestBehavior.AllowGet);
		}

		[PermissionRequired]
		public JsonResult LogPath_Delete(int? id)
		{
			int rs = Singleton_03_Common.ILogPathService.LogPath_Delete(id);
			if (rs != -9)
				return Json(1, JsonRequestBehavior.AllowGet);

			return Json(rs, JsonRequestBehavior.AllowGet);
		}

		[PermissionRequired]
		public JsonResult LogPath_GetByID(int? id)
		{
			DataTable dt = Singleton_03_Common.ILogPathService.LogPath_GetByID(id);
			if (dt?.Rows.Count > 0)
			{
				return Json(JsonConvert.SerializeObject(dt), JsonRequestBehavior.AllowGet);
			}

			return Json(-1, JsonRequestBehavior.AllowGet);
		}

		#endregion

		#region MachineDefineStage

		[PermissionRequired]
		public JsonResult MachineOfStage_Insert(string machineID, string machineName, string stage, string factory, string TypeBarcode, string CycleTime, string TemperatureandHumidity, string Cleanliness)
		{
			if (machineID.Length == 0 || machineName.Length == 0 || stage.Length == 0 || factory.Length == 0 || TypeBarcode.Length == 0)
			{
				return Json(-1, JsonRequestBehavior.AllowGet);
			}

			int rs = Singleton_03_Common.IMachineOfStageService.MachineOfStage_Insert(machineID, machineName, stage, factory, TypeBarcode, TemperatureandHumidity, Cleanliness, string.IsNullOrEmpty(CycleTime) ? 0 : int.Parse(CycleTime));
			if (rs != -9)
				return Json(1, JsonRequestBehavior.AllowGet);

			return Json(rs, JsonRequestBehavior.AllowGet);
		}

		[PermissionRequired]
		public JsonResult MachineOfStage_Update(string machineID, string machineName, string stage, string factory, string TypeBarcode, string CycleTime, string TemperatureandHumidity, string Cleanliness)
		{
			if (machineID.Length == 0 || machineName.Length == 0 || stage.Length == 0 || factory.Length == 0 || TypeBarcode.Length == 0)
			{
				return Json(-1, JsonRequestBehavior.AllowGet);
			}

			int rs = Singleton_03_Common.IMachineOfStageService.MachineOfStage_Update(machineID, machineName, stage, factory, TypeBarcode, TemperatureandHumidity, Cleanliness, string.IsNullOrEmpty(CycleTime) ? 0 : int.Parse(CycleTime));
			if (rs != -9)
				return Json(1, JsonRequestBehavior.AllowGet);

			return Json(rs, JsonRequestBehavior.AllowGet);
		}

		[PermissionRequired]
		public JsonResult MachineOfStage_Delete(string machineID)
		{
			if (machineID.Length == 0)
			{
				return Json(-1, JsonRequestBehavior.AllowGet);
			}

			int rs = Singleton_03_Common.IMachineOfStageService.MachineOfStage_Delete(machineID);
			if (rs != -9)
				return Json(1, JsonRequestBehavior.AllowGet);

			return Json(rs, JsonRequestBehavior.AllowGet);
		}

		[PermissionRequired]
		public JsonResult MachineOfStage_GetByMachineID(string machineID)
		{
			if (machineID.Length == 0)
			{
				return Json(-1, JsonRequestBehavior.AllowGet);
			}

			DataTable dt = Singleton_03_Common.IMachineOfStageService.MachineOfStage_GetByMachineID(machineID);
			if (dt?.Rows.Count > 0)
			{
				return Json(JsonConvert.SerializeObject(dt), JsonRequestBehavior.AllowGet);
			}

			return Json(-2, JsonRequestBehavior.AllowGet);
		}

		[PermissionRequired]
		public JsonResult EnvironmentPlace(string typeName, string factory)
		{
			if (typeName.Length == 0)
			{
				return Json(-1, JsonRequestBehavior.AllowGet);
			}
			DataTable dt = Singleton_03_Common.IStageSettingActiveViewTraceService.EnvironmentPlaceName_GetByTypeNameAndFactory(typeName, string.IsNullOrEmpty(factory) || factory == "-" ? "F1" : factory);
			if (dt?.Rows.Count > 0)
			{
				return Json(JsonConvert.SerializeObject(dt), JsonRequestBehavior.AllowGet);
			}

			return Json(-1, JsonRequestBehavior.AllowGet);
		}

		#endregion

		#region StageActive
		[PermissionRequired]
		public JsonResult StageActive_Update(string stage, bool isActive)
		{
			int rs = Singleton_03_Common.IStageSettingActiveViewTraceService.StageSettingActiveViewTrace_UpdateIsActive(stage, isActive ? 1 : 0);
			if (rs != -9)
				return Json(1, JsonRequestBehavior.AllowGet);

			return Json(rs, JsonRequestBehavior.AllowGet);
		}
		#endregion

		#region StageKeySearch
		[PermissionRequired]
		public JsonResult StageSearchValue_Update(string stage, string factory, string value, int? minute)
		{
			if (stage.Length == 0 || factory.Length == 0 || value.Length == 0)
			{
				return Json(-1, JsonRequestBehavior.AllowGet);
			}

			int rs = Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_Update(factory, stage, value, minute);
			if (rs != -9)
				return Json(1, JsonRequestBehavior.AllowGet);

			return Json(rs, JsonRequestBehavior.AllowGet);
		}

		[PermissionRequired]
		public JsonResult StageSearchValue_GetByStageAndFactory(string stage, string factory)
		{
			if (stage.Length == 0 || factory.Length == 0)
			{
				return Json(-1, JsonRequestBehavior.AllowGet);
			}

			DataTable dt = Singleton_03_Common.IStageSettingSearchValueService.StageSettingSearchValue_GetByFactoryAndStage(factory, stage);
			if (dt?.Rows.Count > 0)
			{
				return Json(JsonConvert.SerializeObject(dt), JsonRequestBehavior.AllowGet);
			}

			return Json(-2, JsonRequestBehavior.AllowGet);
		}
		#endregion

		#region SequencyOfStage
		[PermissionRequired]
		public JsonResult SequencyOfStage_Insert(string frontOrBack, string sequency, string stage, string stageName, string machineID, string machineName, string itemName)
		{
			if (frontOrBack.Length == 0 || sequency.Length == 0 || stage.Length == 0 || itemName.Length == 0 || machineID.Length == 0)
			{
				return Json(-1, JsonRequestBehavior.AllowGet);
			}

			int rs = Singleton_03_Common.ISequencyOfStageService.SequencyOfStage_Insert(frontOrBack, sequency, stage, stageName, machineID, machineName, itemName);
			if (rs != -9)
				return Json(1, JsonRequestBehavior.AllowGet);

			return Json(rs, JsonRequestBehavior.AllowGet);
		}

		[PermissionRequired]
		public JsonResult SequencyOfStage_Update(int iD, string frontOrBack, string sequency, string stage, string stageName, string machineID, string machineName, string itemName)
		{
			if (frontOrBack.Length == 0 || sequency.Length == 0 || stage.Length == 0 || itemName.Length == 0 || machineID.Length == 0)
			{
				return Json(-1, JsonRequestBehavior.AllowGet);
			}

			int rs = Singleton_03_Common.ISequencyOfStageService.SequencyOfStage_Update(iD, frontOrBack, sequency, stage, stageName, machineID, machineName, itemName);
			if (rs != -9)
				return Json(1, JsonRequestBehavior.AllowGet);

			return Json(rs, JsonRequestBehavior.AllowGet);
		}

		[PermissionRequired]
		public JsonResult SequencyOfStage_Delete(int iD)
		{
			int rs = Singleton_03_Common.ISequencyOfStageService.SequencyOfStage_Delete(iD);
			if (rs != -9)
				return Json(1, JsonRequestBehavior.AllowGet);

			return Json(rs, JsonRequestBehavior.AllowGet);
		}

		[PermissionRequired]
		public JsonResult SequencyOfStage_GetByID(int iD)
		{
			DataTable dt = Singleton_03_Common.ISequencyOfStageService.SequencyOfStage_GetByID(iD);
			if (dt?.Rows.Count > 0)
			{
				return Json(JsonConvert.SerializeObject(dt), JsonRequestBehavior.AllowGet);
			}

			return Json(-2, JsonRequestBehavior.AllowGet);
		}
		#endregion

		#region AccountManagement

		[PermissionRequired]
		public JsonResult Account_Insert(string operatorID, string password, int role)
		{
			//DataTable account = (DataTable)Session["account"];

			//if (account.Rows[0]["Role"].ToString() != "1" && (account.Rows[0]["Role"].ToString() != "2" || Request.Params["role"] != "3"))
			//	return Json(-2, JsonRequestBehavior.AllowGet);

			if (operatorID.Length == 0 || password.Length == 0)
				return Json(-1, JsonRequestBehavior.AllowGet);

			int rs = Singleton_03_Common.IAccountService.Account_Insert(operatorID, Common.Md5Endcoding(password), role);
			if (rs != -9)
				return Json(1, JsonRequestBehavior.AllowGet);

			return Json(rs, JsonRequestBehavior.AllowGet);
		}

		[PermissionRequired]
		public JsonResult Account_Update(int id, string operatorID, string password, string passwordOld, int role)
		{
			if (operatorID.Length == 0 || password.Length == 0)
				return Json(-1, JsonRequestBehavior.AllowGet);

			int rs = Singleton_03_Common.IAccountService.Account_Update(id, operatorID, password.Equals(passwordOld) ? password : Common.Md5Endcoding(password), role);
			if (rs != -9)
				return Json(1, JsonRequestBehavior.AllowGet);

			return Json(rs, JsonRequestBehavior.AllowGet);
		}

		[PermissionRequired]
		public JsonResult Account_Delete(int id)
		{
			int rs = Singleton_03_Common.IAccountService.Account_Delete(id);
			if (rs != -9)
				return Json(1, JsonRequestBehavior.AllowGet);

			return Json(rs, JsonRequestBehavior.AllowGet);

		}

		/// <summary>
		/// Đổi mật khẩu cho user hiện tại
		/// </summary>
		/// <param name="currentPassword">Mật khẩu hiện tại</param>
		/// <param name="newPassword">Mật khẩu mới</param>
		/// <returns></returns>
		public JsonResult ChangePassword(string currentPassword, string newPassword)
		{
			try
			{
				// Kiểm tra đầu vào
				if (string.IsNullOrEmpty(currentPassword) || string.IsNullOrEmpty(newPassword))
					return Json(-1, JsonRequestBehavior.AllowGet); // Dữ liệu đầu vào không hợp lệ

				// Lấy thông tin user từ session
				DataTable userSession = (DataTable)Session["account"];
				if (userSession?.Rows.Count == 0)
					return Json(-3, JsonRequestBehavior.AllowGet); // Phiên đăng nhập hết hạn

				var currentUser = userSession.Rows[0];
				string operatorID = currentUser["OperatorID"].ToString();
				int userId = Convert.ToInt32(currentUser["Id"]);
				int userRole = Convert.ToInt32(currentUser["Role"]);

				// Validate mật khẩu hiện tại
				DataTable validateUser = Singleton_03_Common.IAccountService.Account_GetByOperatorIDAndPassword(
					operatorID, Common.Md5Endcoding(currentPassword));

				if (validateUser?.Rows.Count == 0)
					return Json(-4, JsonRequestBehavior.AllowGet); // Mật khẩu hiện tại không đúng

				// Cập nhật mật khẩu mới
				int rs = Singleton_03_Common.IAccountService.Account_Update(
					userId, operatorID, Common.Md5Endcoding(newPassword), userRole);

				if (rs > 0)
					return Json(1, JsonRequestBehavior.AllowGet); // Thành công
				else if (rs == -9)
					return Json(-9, JsonRequestBehavior.AllowGet); // Lỗi database
				else
					return Json(-5, JsonRequestBehavior.AllowGet); // Lỗi khác
			}
			catch (Exception)
			{
				return Json(-6, JsonRequestBehavior.AllowGet); // Lỗi hệ thống
			}
		}

		[PermissionRequired]
		public JsonResult Account_GetById(int id)
		{
			DataTable dt = Singleton_03_Common.IAccountService.Account_GetById(id);
			if (dt?.Rows.Count > 0)
			{
				return Json(JsonConvert.SerializeObject(dt), JsonRequestBehavior.AllowGet);
			}

			return Json(-2, JsonRequestBehavior.AllowGet);
		}
		#endregion
	}
}