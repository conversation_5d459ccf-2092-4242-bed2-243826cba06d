@using System.Data;
@using System.Configuration;
@using Trace_AbilitySystem.Libs.DTOClass;
@{
    DataTable dtStageActive = (DataTable)ViewBag.StageActive;
    DataTable dtStageActiveBareFlex = (DataTable)ViewBag.StageActiveBareFlex;
    DataTable dtStageActivePismo = (DataTable)ViewBag.StageActivePismo;
    DataTable dtStageActiveIPQC = (DataTable)ViewBag.StageActiveIPQC;

    var user = (DataTable)Session["account"];
}
<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="Trace-Ability System">
    <meta name="author" content="SaoMaiSoft">
    <title>Trace-Ability System</title>
    @Styles.Render("~/bundles/css")
    <link href="~/Content/styles/fontawesome.css" rel="stylesheet" />
    <script src="~/Scripts/jquery-3.3.1.min.js"></script>
    <script src="~/Scripts/moment.min.js"></script>
    <link rel="icon" href="~/Content/images/favicon.ico">
    <script type="text/javascript">
        var log_Address = '@ConfigurationManager.AppSettings["Log_Address"]';
        var totalRecord = '@ViewBag.TotalRecords';
        var factory = '@ViewBag.Factory';
        var pcsID = '@ViewBag.PcsID';
        var blockID = '@ViewBag.BlockID';
        var workOrder = '@ViewBag.WorkOrder';
        var upper_p = '@ViewBag.Upper_p';
        var lower_p = '@ViewBag.Lower_p';
        var itemName = '@ViewBag.ItemName';
        var mounting = '@ViewBag.Mounting';
        var pageCurrent = '@ViewBag.PageCurrent';
        var urlIQC = '@ConfigurationManager.AppSettings["UrlIQC"]';
    </script>
</head>
<body>
    <a class="skippy sr-only sr-only-focusable" href="#content">
        <span class="skippy-text">Skip to main content</span>
    </a>
    <header class="navbar navbar-expand navbar-dark flex-column flex-md-row bd-navbar" style="padding: 0.3rem 1rem">
        <a class="navbar-brand mr-0 mr-md-2" href="/" aria-label="Trace-Ability System">
            <img src="~/Content/images/logo.png" width="40" />
        </a>

        <div class="navbar-nav-scroll">
            <ul class="navbar-nav bd-navbar-nav flex-row">
                <li class="nav-item">
                    <a class="nav-link " href="/"><strong>Trace-Ability System</strong></a>
                </li>
            </ul>
        </div>

        <ul class="navbar-nav flex-row ml-md-auto d-none d-md-flex"></ul>
        <span class="messageFront" id="messageFront"></span>
        @if (ViewBag.TotalRecords >= 0)
        {
            <a id="pcsIDReport" href="javascript:" class="btn btn-light d-lg-inline-block mb-3 mb-md-0 ml-md-3"><img src="/Content/images/excel-icon.png" width="18" height="18" style="margin-right:5px;">1 PcsID</a>
        }

        @if (user?.Rows.Count > 0)
        {
            <a id="pcsIDsReportAll" href="javascript:" class="btn btn-light d-lg-inline-block mb-3 mb-md-0 ml-md-3"><img src="/Content/images/excel-icon.png" width="18" height="18" style="margin-right:5px;">100k PcsIDs</a>
            <a id="export500k" href="http://************/ExportRequest" class="btn btn-light d-lg-inline-block mb-3 mb-md-0 ml-md-3"><img src="/Content/images/excel-icon.png" width="18" height="18" style="margin-right:5px;">500k PcsIDs</a>
            <style type="text/css">
                .navbar-right a {
                    color: #000;
                }

                    .navbar-right a:hover {
                        color: #0081cc;
                    }
            </style>
            <div class="dropdown" style="background-color:#fff; padding-left: 15px;">
                <div class="dropdown-toggle" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <img src="~/Content/images/no-avatar.png" style="width:30px; border-radius:50%" /> @(user.Rows[0]["OperatorID"].ToString())
                </div>
                <div class="dropdown-menu" aria-labelledby="dropdownMenuButton" style="right: 0; left: auto; top: 40px; border-radius: 0;">
                    @{
                        if (user.Rows[0]["Role"].ToString() != "3")
                        {
                            if (user.Rows[0]["Role"].ToString() == "1")
                            {
                                <a class="dropdown-item" href="/server-management">1. Server management</a>
                                <a class="dropdown-item" href="/log-path-condition-management">2. Address Condition (F4)</a>
                                <a class="dropdown-item" href="/check-programname">3. Check ProgramName (F4)</a>
                                <a class="dropdown-item" href="/log-path-management">4. Address Log</a>
                                <a class="dropdown-item" href="/machine-of-stage">5. Machine of stage</a>
                                <a class="dropdown-item" href="/sequency-of-stage">6. Sequency of stage (F1)</a>
                                <a class="dropdown-item" href="/stage-search-value">7. Stage search value</a>
                                <a class="dropdown-item" href="/account-management">8. Account management</a>
                                <a class="dropdown-item" href="/ItemName-Vendor">9. ItemName Vendor</a>
                                <div class="dropdown-divider"></div>
                            }
                            else
                            {
                                <a class="dropdown-item" href="/log-path-condition-management">1. Address Condition (F4)</a>
                                <a class="dropdown-item" href="/check-programname">2. Check ProgramName (F4)</a>
                                <a class="dropdown-item" href="/log-path-management">3. Address Log</a>
                                <a class="dropdown-item" href="/machine-of-stage">4. Machine of stage</a>
                                <a class="dropdown-item" href="/sequency-of-stage">5. Sequency of stage (F1)</a>
                                <a class="dropdown-item" href="/account-management">6. Account management</a>
                                <a class="dropdown-item" href="/ItemName-Vendor">7. ItemName Vendor</a>
                                <div class="dropdown-divider"></div>
                            }
                        }

                    }
                    <a class="dropdown-item" href="/management/logout">Log out</a>
                    <button class="dropdown-item" id="changePasswordBtn" onclick="changePassword()">Change Password</button>
                </div>
            </div>
        }
        else
        {
            <a href="/login" class="btn btn-primary d-lg-inline-block mb-3 mb-md-0 ml-md-3" style="font-size:14px;">Login</a>
        }
    </header>

    <div class="modal fade" id="changePasswordModal" tabindex="-1" aria-labelledby="changePasswordModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="changePasswordModalLabel">Change Password</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Đóng"></button>
                </div>
                <div class="modal-body">
                    <form>
                        <div class="form-group">
                            <label for="newPassword">New Password</label>
                            <input type="password" class="form-control" id="newPassword" placeholder="New Password">
                        </div>
                        <div class="form-group">
                            <label for="confirmPassword">Confirm Password</label>
                            <input type="password" class="form-control" id="confirmPassword" placeholder="Confirm Passwor">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" id="closeChangePassword"">Close</button>
                    <button type="button" class="btn btn-primary" id="savePassword">Save</button>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <div class="row flex-xl-nowrap">
            <div class="col-12 col-md-3 col-xl-2 bd-sidebar" style="padding-top: 10px; height: calc(100vh - 47px)">
                <ul class="nav nav-tabs">
                    <li class="nav-item">
                        <a class="nav-link active" href="/" style="text-decoration:none">Main</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/other">Other</a>
                    </li>
                    @*<li class="nav-item">
                            <a class="nav-link" href="/Pismo">Pismo</a>
                        </li>*@
                </ul>
                <div class="tab-content">
                    <div class="tab-pane container active" style="padding:0">
                        <div class="bd-search d-flex align-items-center">
                            <form action="javascript:" style="width:100%;">
                                <div class="form-group">
                                    <div class="input-group">
                                        <input id="search-input-pcsID" type="text" class="form-control" placeholder="or a PcsID" style="font-size:14px; padding-left: 35px;" value="@ViewBag.PcsID" title="Find a PcsID">
                                        <div class="input-group-append">
                                            <button id="btnSearch-pcsID" style="font-size:13px;" class="btn btn-outline-secondary" type="button">Search</button>
                                        </div>
                                        <img id="call-upload-txt" src="~/Content/images/txt-icon.png" width="27" height="25" style="position:absolute;left:5px; top:5px; cursor:pointer; z-index:100;" title="Find the list of PcsID" />
                                        <input id="upload-txt" type="file" class="upload d-none" name="UploadedFile" accept=".csv,.txt" />
                                    </div>
                                    <span>
                                        <small style="color:#aaa">Ex: FNJ0254BY56MTQ45M</small>
                                        <small style="line-height:25px;" class="float-right"><a style="color:#aaa" href="~/download/PcsID_Sample.csv">Sample (*.csv)</a></small>
                                    </span>
                                </div>
                                <div class="form-group">
                                    <div class="input-group">
                                        <input id="search-input-blockID" style="font-size:13px;" type="text" class="form-control" placeholder="BlockID" value="@ViewBag.BlockID">
                                        <div class="input-group-append">
                                            <button id="btnSearch-blockID" style="font-size:13px;" class="btn btn-outline-secondary" type="button">Search</button>
                                        </div>
                                    </div>
                                    <small style="color:#aaa">Ex: 717430-00007-056-1</small>
                                </div>
                                <div class="form-group">
                                    <div class="input-group">
                                        <input id="search-input-workOrder" style="font-size:13px;" type="text" class="form-control" placeholder="WorkOrder" value="@ViewBag.WorkOrder">
                                        <div class="input-group-append">
                                            <button id="btnSearch-workOrder" style="font-size:13px;" class="btn btn-outline-secondary" type="button">Search</button>
                                        </div>
                                    </div>
                                    <small style="color: #aaa">Ex: 71743100000B00015</small>
                                </div>
                            </form>
                            <button style="margin-top: -25px;" class="btn btn-link bd-search-docs-toggle d-md-none p-0 ml-3" type="button" data-toggle="collapse" data-target="#bd-docs-nav" aria-controls="bd-docs-nav" aria-expanded="false" aria-label="Toggle docs navigation">
                                <svg xmlns="http://www.w3.org/2000/svg" viewbox="0 0 30 30" width="30" height="30" focusable="false"><title>Menu</title><path stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-miterlimit="10" d="M4 7h22M4 15h22M4 23h22" /></svg>
                            </button>
                        </div>
                    </div>
                </div>
                <style type="text/css">
                    ul li a.active {
                        font-weight: bold;
                        text-decoration: underline;
                    }
                </style>
                <nav class="collapse bd-links" id="bd-docs-nav" style="max-height: calc(100vh - 21rem); overflow-y: auto">
                    <div class="bd-toc-item active">
                        <label title="tích chọn để hiện thị các ô CheckBox lọc dữ liệu download" class="form-check-label lblexportFiter" style="cursor:pointer">
                            <input id="exportFilter" hidden type="checkbox" title="tích chọn để hiện thị các ô CheckBox lọc dữ liệu download"><i class="fas fa-clipboard-list"></i> <span>Export filter</span>
                        </label>
                        <ul id="myUL">
                            <li>
                                <span class="bd-toc-link caret">
                                    @{
                                        if (ViewBag.BareFirstTime == 1)
                                        {
                                            <input class="isDisplay" style="display:none" checked type="checkbox" id="MenuLeftBareCheckAll"> <span>I.Bare Flex</span>
                                        }
                                        else
                                        {
                                            <input class="isDisplay" style="display:none" type="checkbox" id="MenuLeftBareCheckAll"> <span>I.Bare Flex</span>
                                        }
                                    }
                                </span>
                                <ul class="nav bd-sidenav nested">
                                    @if (dtStageActiveBareFlex != null)
                                    {
                                        for (int i = 0; i < dtStageActiveBareFlex.Rows.Count; i++)
                                        {
                                            if (bool.Parse(dtStageActiveBareFlex.Rows[i]["IsActive"].ToString()))
                                            {
                                                ExportStage ExportStage = ViewBag.ExportStage;
                                                string ischecked = "false";
                                                if (ExportStage.Stage.Contains(dtStageActiveBareFlex.Rows[i]["NameUrl"].ToString()))
                                                {
                                                    int index = Array.IndexOf(ExportStage.Stage, dtStageActiveBareFlex.Rows[i]["NameUrl"].ToString());
                                                    if (index >= 0)
                                                    {
                                                        ischecked = ExportStage.Checked[index];
                                                    }
                                                }
                                                <li>
                                                    <a class="MenuLeftAnchor">
                                                        <label class="form-check-label" id="@(dtStageActiveBareFlex.Rows[i]["NameUrl"].ToString()+"anchor")" for="" style="cursor:pointer">
                                                            @if (ischecked == "true")
                                                            {
                                                                <input type="checkbox" checked class="MenuLeftBareCheckBox isDisplay" style="display:none" value="@dtStageActiveBareFlex.Rows[i]["NameUrl"].ToString()"> @(dtStageActiveBareFlex.Rows[i]["NameShow"].ToString())
                                                            }
                                                            else
                                                            {
                                                                <input type="checkbox" class="MenuLeftBareCheckBox isDisplay" style="display:none" value="@dtStageActiveBareFlex.Rows[i]["NameUrl"].ToString()"> @(dtStageActiveBareFlex.Rows[i]["NameShow"].ToString())

                                                            }
                                                        </label>
                                                    </a>
                                                </li>
                                            }
                                        }
                                    }
                                </ul>
                            </li>
                            <li>
                                <span class="bd-toc-link caret">
                                    @{
                                        if (ViewBag.AssyFirstTime == 1)
                                        {
                                            <input class="isDisplay" style="display:none" checked type="checkbox" id="MenuLeftAssyCheckAll"> <span> II.Flex Assy</span>
                                        }
                                        else
                                        {
                                            <input class="isDisplay" style="display:none" type="checkbox" id="MenuLeftAssyCheckAll"> <span> II.Flex Assy</span>
                                        }
                                    }
                                </span>
                                <ul class="nested nav bd-sidenav">
                                    @if (dtStageActive != null)
                                    {
                                        int add = 0;
                                        for (int i = 0; i < dtStageActive.Rows.Count; i++)
                                        {
                                            if (bool.Parse(dtStageActive.Rows[i]["IsActive"].ToString()))
                                            {
                                                ExportStage ExportStage = ViewBag.ExportStage;
                                                string ischecked = "false";
                                                if (ExportStage.Stage.Contains(@dtStageActive.Rows[i]["NameUrl"].ToString()))
                                                {
                                                    int index = Array.IndexOf(ExportStage.Stage, @dtStageActive.Rows[i]["NameUrl"].ToString());
                                                    if (index >= 0)
                                                    {
                                                        ischecked = ExportStage.Checked[Array.IndexOf(ExportStage.Stage, @dtStageActive.Rows[i]["NameUrl"].ToString())];
                                                    }
                                                }
                                                add++;
                                                <li>
                                                    <a class="MenuLeftAnchor">
                                                        <label class="form-check-label" id="@(dtStageActive.Rows[i]["NameUrl"].ToString()+"anchor")" style="cursor:pointer" for="@dtStageActive.Rows[i]["NameUrl"].ToString()">
                                                            @if (ischecked == "true")
                                                            {
                                                                <input type="checkbox" checked class="MenuLeftAssyCheckBox isDisplay" style="display:none" value="@dtStageActive.Rows[i]["NameUrl"].ToString()">
                                                            }
                                                            else
                                                            {
                                                                <input type="checkbox" class="MenuLeftAssyCheckBox isDisplay" style="display:none" value="@dtStageActive.Rows[i]["NameUrl"].ToString()">

                                                            }
                                                            @*//lỗi này do db mình không có menuindex*@
                                                            @(dtStageActive.Rows[i]["MenuIndex"].ToString()).@(dtStageActive.Rows[i]["NameShow"].ToString())
                                                        </label>
                                                    </a>
                                                </li>
                                            }
                                        }
                                    }
                                </ul>
                            </li>
                            <li>
                                <span class="bd-toc-link caret">
                                    @{
                                        if (ViewBag.AssyFirstTime == 1)
                                        {
                                            <input class="isDisplay" style="display:none" checked type="checkbox" id="MenuLeftAssyCheckAll"> <span> III.IQC/IPQC/OQC/OBA/REL/ORT</span>
                                        }
                                        else
                                        {
                                            <input class="isDisplay" style="display:none" type="checkbox" id="MenuLeftAssyCheckAll"> <span> III.IQC/IPQC/OQC/OBA/REL/ORT</span>
                                        }
                                    }
                                </span>
                                <ul class="nested nav bd-sidenav">
                                    @if (dtStageActiveIPQC != null)
                                    {
                                        int add = 0;
                                        for (int i = 0; i < dtStageActiveIPQC.Rows.Count; i++)
                                        {
                                            if (bool.Parse(dtStageActiveIPQC.Rows[i]["IsActive"].ToString()))
                                            {
                                                ExportStage ExportStage = ViewBag.ExportStage;
                                                string ischecked = "false";
                                                if (ExportStage.Stage.Contains(dtStageActiveIPQC.Rows[i]["NameUrl"].ToString()))
                                                {
                                                    int index = Array.IndexOf(ExportStage.Stage, dtStageActiveIPQC.Rows[i]["NameUrl"].ToString());
                                                    if (index >= 0)
                                                    {
                                                        ischecked = ExportStage.Checked[Array.IndexOf(ExportStage.Stage, dtStageActiveIPQC.Rows[i]["NameUrl"].ToString())];
                                                    }
                                                }
                                                add++;
                                                <li>
                                                    <a class="MenuLeftAnchor">
                                                        <label class="form-check-label" id="@(dtStageActiveIPQC.Rows[i]["NameUrl"].ToString()+"anchor")" style="cursor:pointer" for="@dtStageActiveIPQC.Rows[i]["NameUrl"].ToString()">
                                                            @if (ischecked == "true")
                                                            {
                                                                <input type="checkbox" checked class="MenuLeftAssyCheckBox isDisplay" style="display:none" value="@dtStageActiveIPQC.Rows[i]["NameUrl"].ToString()">
                                                            }
                                                            else
                                                            {
                                                                <input type="checkbox" class="MenuLeftAssyCheckBox isDisplay" style="display:none" value="@dtStageActiveIPQC.Rows[i]["NameUrl"].ToString()">

                                                            }
                                                            @*//lỗi này do db mình không có menuindex*@
                                                            @(dtStageActiveIPQC.Rows[i]["MenuIndex"].ToString()).@(dtStageActiveIPQC.Rows[i]["NameShow"].ToString())
                                                        </label>
                                                    </a>
                                                </li>
                                            }
                                        }
                                    }
                                </ul>
                            </li>
                            <li>
                                <span class="bd-toc-link caret">
                                    @{
                                        if (ViewBag.AssyFirstTime == 1)
                                        {
                                            <input class="isDisplay" style="display:none" checked type="checkbox" id="MenuLeftPismoCheckAll"> <span> IV. PISMO FLEX ASSY</span>
                                        }
                                        else
                                        {
                                            <input class="isDisplay" style="display:none" type="checkbox" id="MenuLeftPismoCheckAll"> <span> IV. PISMO FLEX ASSY</span>
                                        }
                                    }
                                </span>
                                <ul class="nested nav bd-sidenav">
                                    @if (dtStageActivePismo != null)
                                    {
                                        int add = 0;
                                        for (int i = 0; i < dtStageActivePismo.Rows.Count; i++)
                                        {
                                            if (bool.Parse(dtStageActivePismo.Rows[i]["IsActive"].ToString()))
                                            {
                                                ExportStage ExportStage = ViewBag.ExportStage;
                                                string ischecked = "false";
                                                if (ExportStage.Stage.Contains(@dtStageActivePismo.Rows[i]["NameUrl"].ToString()))
                                                {
                                                    int index = Array.IndexOf(ExportStage.Stage, @dtStageActivePismo.Rows[i]["NameUrl"].ToString());
                                                    if (index >= 0)
                                                    {
                                                        ischecked = ExportStage.Checked[Array.IndexOf(ExportStage.Stage, @dtStageActivePismo.Rows[i]["NameUrl"].ToString())];
                                                    }
                                                }
                                                add++;
                                                <li>
                                                    <a class="MenuLeftAnchor">
                                                        <label class="form-check-label" id="@(dtStageActivePismo.Rows[i]["NameUrl"].ToString()+"anchor")" style="cursor:pointer" for="@dtStageActivePismo.Rows[i]["NameUrl"].ToString()">
                                                            @if (ischecked == "true")
                                                            {
                                                                <input type="checkbox" checked class="MenuLeftPismoCheckBox isDisplay" style="display:none" value="@dtStageActivePismo.Rows[i]["NameUrl"].ToString()">
                                                            }
                                                            else
                                                            {
                                                                <input type="checkbox" class="MenuLeftPismoCheckBox isDisplay" style="display:none" value="@dtStageActivePismo.Rows[i]["NameUrl"].ToString()">

                                                            }
                                                            @*//lỗi này do db mình không có menuindex*@
                                                            @(dtStageActivePismo.Rows[i]["MenuIndex"].ToString()).@(dtStageActivePismo.Rows[i]["NameShow"].ToString())
                                                        </label>
                                                    </a>
                                                </li>
                                            }
                                        }
                                    }
                                </ul>
                            </li>
                        </ul>
                        <script>
                            var toggler = document.getElementsByClassName("caret");
                            var i;

                            for (i = 0; i < toggler.length; i++) {
                                toggler[i].addEventListener("click", function (event) {
                                    if (event.target.tagName == "INPUT") {
                                        return;
                                    }
                                    this.parentElement.querySelector(".nested").classList.toggle("Menuactive");
                                    this.classList.toggle("caret-down");
                                });
                            }
                        </script>
                        @*<label class="form-check-label lblexportFiter bd-toc-link" id="toggleBare" for="" style="cursor:pointer">
                            </label>*@
                    </div>
                </nav>
            </div>
            <main class="col-12 col-md-7 col-xl-8 py-md-3 pl-md-5 bd-content" role="main">
                @RenderBody()
            </main>

            @*<div class="d-none d-xl-block col-xl-2 bd-toc">*@
            <div class="col-12 col-md-2 col-xl-2 bd-toc">
                <div class="bd-search d-flex align-items-center">
                    <input type="hidden" id="hdfTotalPCS" value="@ViewBag.TotalRecords" />
                    <div style="color: #0081cc; font-weight: 500; text-align:center;width:100%;">
                        BlockIDs/PcsIDs TO SEARCH (@ViewBag.TotalRecords)
                        @if (@ViewBag.TotalRecords > 0)
                        {
                            <br /> <a style="color:#aaa; font-weight:400;" href="/home/<USER>"WorkOrder": ViewBag.BlockID.Length != 0 ? "BlockID": "PcsIDs")&urlCurrent=@(HttpContext.Current.Request.Url.AbsoluteUri)">Clear PcsIDs</a>
                        }

                    </div>
                </div>
                <ul class="section-nav bd-links" style="max-height: calc(100vh - 12rem) !important;height: calc(100vh - 12rem) !important; overflow-y: auto; border-bottom: 1px solid rgba(0,0,0,.05)">
                    <li class="toc-entry toc-h2" id="lstPcsIds">
                    </li>
                </ul>
                <div class="container">
                    <nav aria-label="Page navigation">
                        <ul class="pagination" id="pagination"></ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>
    <div id="isloading" class="showLoading"></div>
    <footer class="bd-footer text-muted">
        <div class="container-fluid p-3 p-md-5" style="padding: 20px !important">
            <ul class="bd-footer-links" style="margin-bottom: 10px;">
                <li><a href="/">Trace-Ability System</a></li>
            </ul>
            <p style="margin-bottom: 10px;">Designed and built by the <a href="http://saomaisoft.com">saomaisoft.com</a>.</p>
            <p>Currently v1.0.0.</p>
        </div>
    </footer>
    <!-- Modal -->
    <div class="modal fade" id="modalData" tabindex="-1" role="dialog" aria-labelledby="modalData" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalLabel">...</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body" style="font-size:14px; overflow-y:scroll; max-height: calc(100vh - 12rem); ">

                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
    <!-- Modal Xray-->
    <div class="modal fade" id="modalDataXray" tabindex="-1" role="dialog" aria-labelledby="modalData" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalLabelXray">...</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-bodyXray" style="font-size:14px; overflow-y:scroll; max-height: calc(100vh - 12rem); ">

                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
    <div id="stop" class="scrollTop">
        <span><a href="javascript:"><img src="~/Content/images/arrow_up.png" width="50" height="50" /></a></span>
    </div>
    @Scripts.Render("~/bundles/jquery")
    @Scripts.Render("~/bundles/jquery-main")
<script>
        function changePassword() {
            $('#changePasswordModal').modal('show');
        }

        document.getElementById("closeChangePassword").addEventListener("click", function () {
            document.getElementById("changePasswordModal").style.display = "none";
        });

        document.getElementById("closeChangePassword").addEventListener("click", function () {
            // Sử dụng Bootstrap modal hide thay vì CSS display
            $('#changePasswordModal').modal('hide');

            // Đảm bảo cleanup hoàn toàn
            setTimeout(function () {
                // Xóa tất cả backdrop
                $('.modal-backdrop').remove();
                // Xóa class modal-open khỏi body
                $('body').removeClass('modal-open');
                // Reset modal display
                $('#changePasswordModal').css('display', '');
            }, 100);
        });

         //Thêm event listener cho backdrop click để đóng modal
        $(document).on('click', '.modal-backdrop', function () {
            $('#changePasswordModal').modal('hide');
        })

    $(document).ready(function () {
        $('#savePassword').click(function () {
            var newPassword = $('#newPassword').val();
            var confirmPassword = $('#confirmPassword').val();
            //var role = $('#UserLogin').data('role'); 

            var allowedRoles = ['Admin', 'Manager', 'Engineer'];

            if (!allowedRoles.includes(role)) {
                alert('Bạn không có quyền đổi mật khẩu!');
                return;
            }

            if (newPassword === '' || confirmPassword === '') {
                alert('Vui lòng điền đầy đủ cả hai trường mật khẩu!');
                return;
            }

            if (newPassword !== confirmPassword) {
                alert('Mật khẩu không khớp!');
                return;
            }

            var userData = {
                UserName: $('#UserLogin').data('username'),
                Role: role,
                PassWordNew: newPassword
            };

            $.ajax({
                url: '/Account/Account_Update',
                type: 'POST',
                contentType: 'application/json;charset=utf-8',
                data: JSON.stringify(userData),
                dataType: 'json',
                success: function (response) {
                    if (response.Code === "00") {
                        alert('Đổi mật khẩu thành công!');
                        $('#changePasswordModal').modal('hide');
                        $('#newPassword').val('');
                        $('#confirmPassword').val('');
                    } else {
                        alert('Thay đổi mật khẩu thất bại: ' + response.Message);
                    }
                },
                error: function (xhr, status, error) {
                    alert('Đã xảy ra lỗi: ' + error);
                    $('#changePasswordModal').modal('hide');
                }
            });
        });

        $('.btn-secondary, .close').click(function () {
            $('#changePasswordModal').modal('hide');
        });

        $("#isloading").hide();
    });


</script>
</body>
</html>

<div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal"></button>
                <h4 class="modal-title" id="myModalLabel">Choose Export Style</h4>
            </div>
            <div class="modal-body">
                <form>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group row">
                                <label for="QtyProduct" class="col-sm-3 col-form-label">Quantity Of Product</label>
                                <div class="col-sm-9">
                                    <label class="form-control" id="lblTotalPCS"></label>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="ExportType" class="col-sm-3 col-form-label">Export Type</label>
                                <div class="col-sm-9">
                                    <select name="dropdown" id="ExportType" placeholder="Export Type" class="form-control">
                                        <option value="ExportHorizontal">Export Horizontal</option>
                                        <option value="ExportVertical">Export Vertical</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" id="btnExport">Export</button>
                <button type="button" class="btn btn-danger" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>